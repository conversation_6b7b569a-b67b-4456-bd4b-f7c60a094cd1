#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员积分管理API
Admin Credits Management API
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from app import db
from app.models.user import User
from app.models.points_log import PointsLog
from app.models.points_config import PointsConfig
from datetime import datetime
from sqlalchemy import func

# 兼容两种路径
admin_credits_bp = Blueprint('admin_credits', __name__, url_prefix='/api/admin/points')
admin_credits_bp_credits = Blueprint('admin_credits_credits', __name__, url_prefix='/api/admin/credits')

# 获取所有积分配置
@admin_credits_bp.route('/config', methods=['GET'])
@admin_credits_bp_credits.route('/config', methods=['GET'])
@jwt_required()
def get_config():
    configs = PointsConfig.query.all()
    return jsonify([{ 'key': c.key, 'value': c.value, 'description': c.description } for c in configs])

# 更新/新增积分配置
@admin_credits_bp.route('/config', methods=['POST'])
@admin_credits_bp_credits.route('/config', methods=['POST'])
@jwt_required()
def set_config():
    data = request.json
    for item in data:
        config = PointsConfig.query.filter_by(key=item['key']).first()
        if not config:
            config = PointsConfig(key=item['key'])
        config.value = item['value']
        config.description = item.get('description', '')
        db.session.add(config)
    db.session.commit()
    return jsonify({'msg': '配置已更新'})

# 获取积分日志
@admin_credits_bp.route('/logs', methods=['GET'])
@admin_credits_bp_credits.route('/logs', methods=['GET'])
@jwt_required()
def get_logs():
    user_id = request.args.get('user_id')
    q = PointsLog.query
    if user_id:
        q = q.filter_by(user_id=user_id)
    logs = q.order_by(PointsLog.created_at.desc()).limit(200).all()
    return jsonify([
        {
            'id': l.id,
            'user_id': l.user_id,
            'change': l.change,
            'type': l.type,
            'remark': l.remark,
            'created_at': l.created_at.isoformat(),
            'balance': l.balance
        } for l in logs
    ])

# 管理员手动调整用户积分
@admin_credits_bp.route('/adjust', methods=['POST'])
@admin_credits_bp_credits.route('/adjust', methods=['POST'])
@jwt_required()
def adjust_points():
    data = request.json
    user = User.query.get(data['user_id'])
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    change = int(data['change'])
    user.points += change
    user.points_used += -change if change < 0 else 0
    log = PointsLog(user_id=user.id, change=change, type='admin', remark=data.get('remark', ''), balance=user.points)
    db.session.add(log)
    db.session.commit()
    return jsonify({'msg': '积分已调整', 'balance': user.points})

@admin_credits_bp.route('/stats', methods=['GET'])
@admin_credits_bp_credits.route('/stats', methods=['GET'])
@jwt_required()
def get_points_stats():
    total_points = db.session.query(func.sum(User.points)).scalar() or 0
    total_users = db.session.query(func.count(User.id)).scalar() or 0
    today = datetime.utcnow().date()
    today_points = db.session.query(func.sum(PointsLog.change)).filter(
        PointsLog.created_at >= today
    ).scalar() or 0
    avg_points = total_points // total_users if total_users else 0
    return jsonify({
        'totalPoints': total_points,
        'totalUsers': total_users,
        'todayPoints': today_points,
        'avgPoints': avg_points
    })
