#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员积分配置API
Admin Credits Configuration API
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from app import db
from app.models.points_config import PointsConfig
from app.models.points_log import PointsLog
from app.models.user import User
import json
import os
import uuid
from datetime import datetime
import traceback
from werkzeug.utils import secure_filename

admin_credits_bp = Blueprint('admin_credits', __name__, url_prefix='/api/admin/credits')

# 获取积分配置
@admin_credits_bp.route('/config', methods=['GET'])
@jwt_required()
def get_credits_config():
    """获取积分配置"""
    try:
        config = PointsConfig.query.first()
        if not config:
            # 创建默认配置
            config = PointsConfig()
            db.session.add(config)
            db.session.commit()
        
        # 解析套餐配置
        packages = []
        if config.payment_packages:
            try:
                packages = json.loads(config.payment_packages)
            except:
                packages = []
        
        return jsonify({
            'success': True,
            'data': {
                'register_reward': config.register_reward,
                'invite_reward': config.invite_reward,
                'ad_reward': config.ad_reward,
                'photo_process_cost': config.photo_process_cost,
                'resume_generate_cost': config.resume_generate_cost,
                'business_card_cost': config.business_card_cost,
                'document_compare_cost': config.document_compare_cost,
                'recharge_ratio': config.recharge_ratio,
                'wechat_enabled': config.wechat_enabled,
                'wechat_account': config.wechat_account,
                'wechat_qr_code': config.wechat_qr_code,
                'wechat_receiver_name': config.wechat_receiver_name,
                'wechat_payment_note': config.wechat_payment_note,
                'alipay_enabled': config.alipay_enabled,
                'alipay_account': config.alipay_account,
                'alipay_qr_code': config.alipay_qr_code,
                'alipay_receiver_name': config.alipay_receiver_name,
                'alipay_payment_note': config.alipay_payment_note,
                'payment_packages': packages
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 保存积分配置
@admin_credits_bp.route('/config', methods=['POST'])
@jwt_required()
def save_credits_config():
    """保存积分配置"""
    try:
        print("=== 保存积分配置请求 ===")
        print(f"请求方法: {request.method}")
        print(f"请求头: {dict(request.headers)}")
        print(f"请求数据: {request.json}")
        
        data = request.json
        if not data:
            print("错误: 没有请求数据")
            return jsonify({'success': False, 'error': '没有请求数据'}), 400
        
        config = PointsConfig.query.first()
        if not config:
            print("创建新的配置记录")
            config = PointsConfig()
            db.session.add(config)
        else:
            print("更新现有配置记录")
        
        # 更新配置
        config.register_reward = data.get('register_reward', 100)
        config.invite_reward = data.get('invite_reward', 50)
        config.ad_reward = data.get('ad_reward', 5)
        config.photo_process_cost = data.get('photo_process_cost', 10)
        config.resume_generate_cost = data.get('resume_generate_cost', 20)
        config.business_card_cost = data.get('business_card_cost', 15)
        config.document_compare_cost = data.get('document_compare_cost', 25)
        config.recharge_ratio = data.get('recharge_ratio', 1.0)
        
        # 微信支付配置
        config.wechat_enabled = data.get('wechat_enabled', False)
        config.wechat_account = data.get('wechat_account', '')
        config.wechat_qr_code = data.get('wechat_qr_code', '')
        config.wechat_receiver_name = data.get('wechat_receiver_name', '')
        config.wechat_payment_note = data.get('wechat_payment_note', '')
        
        # 支付宝配置
        config.alipay_enabled = data.get('alipay_enabled', False)
        config.alipay_account = data.get('alipay_account', '')
        config.alipay_qr_code = data.get('alipay_qr_code', '')
        config.alipay_receiver_name = data.get('alipay_receiver_name', '')
        config.alipay_payment_note = data.get('alipay_payment_note', '')
        
        # 套餐配置
        packages = data.get('payment_packages', [])
        config.payment_packages = json.dumps(packages)
        
        print(f"保存的配置: 微信启用={config.wechat_enabled}, 支付宝启用={config.alipay_enabled}")
        print(f"套餐数量: {len(packages)}")
        
        db.session.commit()
        print("配置保存成功")
        
        return jsonify({'success': True, 'message': '积分配置保存成功'})
    except Exception as e:
        print(f"保存配置异常: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500



# 上传支付二维码
@admin_credits_bp.route('/upload-qr', methods=['POST'])
@jwt_required()
def upload_qr_code():
    """上传支付二维码 - 增强版"""
    try:
        print("=== 二维码上传请求开始 ===")
        
        # 验证文件存在
        if 'file' not in request.files:
            print("错误: 请求中没有文件字段")
            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400
        
        file = request.files['file']
        if not file or file.filename == '':
            print("错误: 没有选择文件")
            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400
        
        # 验证文件类型
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        
        if file_extension not in allowed_extensions:
            print(f"错误: 不支持的文件类型 {file_extension}")
            return jsonify({'success': False, 'error': f'请上传图片文件，支持的格式：{", ".join(allowed_extensions)}'}), 400
        
        # 验证文件大小 (最大5MB)
        max_file_size = 5 * 1024 * 1024  # 5MB
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置文件指针到开头
        
        if file_size > max_file_size:
            print(f"错误: 文件过大 {file_size} bytes")
            return jsonify({'success': False, 'error': '文件大小不能超过5MB'}), 400
        
        if file_size == 0:
            print("错误: 空文件")
            return jsonify({'success': False, 'error': '不能上传空文件'}), 400
        
        # 获取支付类型参数
        payment_type = request.form.get('payment_type', 'unknown')
        if payment_type not in ['wechat', 'alipay']:
            print(f"警告: 未知的支付类型 {payment_type}")
        
        # 生成安全文件名
        filename = secure_filename(file.filename)
        import uuid
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_filename = f"{payment_type}_qr_{timestamp}_{uuid.uuid4().hex[:8]}_{filename}"
        
        # 确保上传目录存在
        upload_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'uploads', 'qr_codes')
        os.makedirs(upload_dir, exist_ok=True)
        
        # 构建完整文件路径
        file_path = os.path.join(upload_dir, unique_filename)
        
        # 保存文件
        file.save(file_path)
        
        # 验证文件是否成功保存
        if not os.path.exists(file_path):
            print(f"错误: 文件保存失败 {file_path}")
            return jsonify({'success': False, 'error': '文件保存失败，请重试'}), 500
        
        # 验证文件大小
        saved_file_size = os.path.getsize(file_path)
        if saved_file_size != file_size:
            print(f"警告: 文件大小不匹配 {file_size} vs {saved_file_size}")
        
        # 生成相对路径
        relative_path = f"/uploads/qr_codes/{unique_filename}"
        
        print(f"文件上传成功: {unique_filename} ({saved_file_size} bytes), 类型: {payment_type}")
        
        return jsonify({
            'success': True,
            'data': {
                'url': relative_path,
                'filename': unique_filename,
                'original_filename': filename,
                'size': saved_file_size,
                'payment_type': payment_type
            },
            'message': '二维码上传成功'
        })
        
    except Exception as e:
        print(f"上传异常: {str(e)}")
        print(f"异常类型: {type(e).__name__}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': '上传失败，请稍后重试'}), 500

# 获取积分日志
@admin_credits_bp.route('/logs', methods=['GET'])
@jwt_required()
def get_points_logs():
    """获取积分日志"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        user_id = request.args.get('user_id', type=int)
        log_type = request.args.get('type', '')
        
        query = PointsLog.query
        
        if user_id:
            query = query.filter(PointsLog.user_id == user_id)
        
        if log_type:
            query = query.filter(PointsLog.type == log_type)
        
        pagination = query.order_by(PointsLog.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        logs = []
        for log in pagination.items:
            log_data = log.to_dict()
            log_data['username'] = log.user.username if log.user else '未知用户'
            logs.append(log_data)
        
        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 调整用户积分
@admin_credits_bp.route('/adjust', methods=['POST'])
@jwt_required()
def adjust_user_points():
    """调整用户积分"""
    try:
        data = request.json
        user_id = data.get('user_id')
        points = data.get('points', 0)
        description = data.get('description', '管理员调整')
        
        if not user_id:
            return jsonify({'success': False, 'error': '用户ID不能为空'}), 400
        
        user = User.query.get(user_id)
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'}), 404
        
        if points > 0:
            user.add_credits(points, description, 'admin_adjust')
        elif points < 0:
            if not user.consume_credits(abs(points), description, 'admin_adjust'):
                return jsonify({'success': False, 'error': '用户积分不足'}), 400
        
        return jsonify({
            'success': True,
            'message': f'积分调整成功，当前积分：{user.points}'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 获取积分统计
@admin_credits_bp.route('/stats', methods=['GET'])
@jwt_required()
def get_points_stats():
    """获取积分统计"""
    try:
        from sqlalchemy import func
        
        # 总积分统计
        total_points = db.session.query(func.sum(User.points)).scalar() or 0
        total_used = db.session.query(func.sum(User.points_used)).scalar() or 0
        
        # 今日积分变动
        from datetime import datetime, date
        today = date.today()
        today_earned = db.session.query(func.sum(PointsLog.points)).filter(
            PointsLog.points > 0,
            func.date(PointsLog.created_at) == today
        ).scalar() or 0
        
        today_consumed = abs(db.session.query(func.sum(PointsLog.points)).filter(
            PointsLog.points < 0,
            func.date(PointsLog.created_at) == today
        ).scalar() or 0)
        
        # 积分类型统计
        type_stats = db.session.query(
            PointsLog.type,
            func.count(PointsLog.id),
            func.sum(PointsLog.points)
        ).group_by(PointsLog.type).all()
        
        return jsonify({
            'success': True,
            'data': {
                'total_points': total_points,
                'total_used': total_used,
                'today_earned': today_earned,
                'today_consumed': today_consumed,
                'type_stats': [
                    {
                        'type': stat[0],
                        'count': stat[1],
                        'points': stat[2] or 0
                    }
                    for stat in type_stats
                ]
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500