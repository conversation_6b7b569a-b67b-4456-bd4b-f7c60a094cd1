<template>
  <div class="card-wrapper avatar-circle" :style="styleVariables">
    <div class="avatar-section">
      <img v-if="cardData.avatar" :src="cardData.avatar" class="avatar-img" alt="头像" />
      <div v-else class="avatar-placeholder">头像</div>
    </div>
    <div class="main-content">
      <div class="identity">
        <h2 class="name" :style="nameStyle">{{ cardData.name || '姓名' }}</h2>
        <p class="title" v-if="cardData.title" :style="titleStyle">{{ cardData.title }}</p>
        <p class="company" v-if="cardData.company" :style="companyStyle">{{ cardData.company }}</p>
      </div>
      <div class="contact-row">
        <span v-if="cardData.phone" class="contact-item" :style="contactStyle">{{ cardData.phone }}</span>
        <span v-if="cardData.email" class="contact-item" :style="contactStyle">{{ cardData.email }}</span>
        <span v-if="cardData.website" class="contact-item" :style="contactStyle">{{ cardData.website }}</span>
      </div>
    </div>
    <div class="qr-code" v-if="qrCodeUrl">
      <img :src="qrCodeUrl" alt="二维码" class="qr-img" />
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';
export default {
  name: 'AvatarCircle',
  props: {
    cardData: {
      type: Object,
      required: true,
    },
    styleConfig: {
      type: Object,
      default: () => ({}),
    },
    qrCodeUrl: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const styleVariables = computed(() => {
      const config = props.styleConfig || {};
      const multiplier = (config.fontSize || 100) / 100;
      return {
        '--primary-color': config.primary || '#409EFF',
        '--secondary-color': config.secondary || '#FFFFFF',
        '--text-color': config.text || '#222',
        '--light-text-color': config.light || '#888',
        '--font-size-name': `${70 * multiplier}px`,
        '--font-size-title': `${38 * multiplier}px`,
        '--font-size-company': `${32 * multiplier}px`,
        '--font-size-contact': `${28 * multiplier}px`,
        '--avatar-size': `${72 * multiplier}px`,
        '--card-radius': `${config.radius || 18}px`,
      };
    });
    const nameStyle = computed(() => ({
      color: props.styleConfig.nameColor || 'var(--primary-color)',
      fontWeight: 'bold',
    }));
    const companyStyle = computed(() => ({
      color: props.styleConfig.companyColor || 'var(--text-color)',
      fontWeight: 500,
    }));
    const titleStyle = computed(() => ({
      color: props.styleConfig.text || 'var(--text-color)',
      fontWeight: 400,
    }));
    const contactStyle = computed(() => ({
      color: props.styleConfig.light || 'var(--light-text-color)',
      fontWeight: 400,
    }));
    return {
      styleVariables,
      nameStyle,
      companyStyle,
      titleStyle,
      contactStyle,
    };
  },
};
</script>

<style scoped>
.card-wrapper {
  width: 100%;
  height: 100%;
  background: var(--secondary-color);
  border-radius: var(--card-radius);
  box-shadow: 0 2px 8px #0001;
  display: flex;
  align-items: center;
  padding: 0 32px;
  position: relative;
  font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}
.avatar-section {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32px;
}
.avatar-img {
  width: var(--avatar-size);
  height: var(--avatar-size);
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-color);
  background: #fff;
}
.avatar-placeholder {
  width: var(--avatar-size);
  height: var(--avatar-size);
  border-radius: 50%;
  background: #f0f0f0;
  color: #bbb;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  border: 2px dashed #ddd;
}
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 0;
}
.identity {
  text-align: center;
}
.name {
  font-size: var(--font-size-name);
  margin: 0 0 4px 0;
  letter-spacing: 1px;
}
.title {
  font-size: var(--font-size-title);
  margin: 0 0 2px 0;
  color: var(--text-color);
  opacity: 0.9;
}
.company {
  font-size: var(--font-size-company);
  margin: 0 0 8px 0;
  color: var(--text-color);
  opacity: 0.8;
}
.contact-row {
  display: flex;
  gap: 18px;
  margin-top: 10px;
  justify-content: center;
}
.contact-item {
  font-size: var(--font-size-contact);
  color: var(--light-text-color);
}
.qr-code {
  position: absolute;
  right: 18px;
  bottom: 18px;
  width: 48px;
  height: 48px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px #0001;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qr-img {
  width: 80%;
  height: 80%;
  object-fit: contain;
  display: block;
}
</style> 