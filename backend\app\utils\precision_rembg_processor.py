#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 rembg 的精准背景替换处理器
提供高精度的人物分割和背景替换功能
"""

import cv2
import numpy as np
from PIL import Image
import io
import time
import logging
from typing import Tuple, Optional, Union
import gc

try:
    from rembg import remove, new_session
    REMBG_AVAILABLE = True
except ImportError:
    REMBG_AVAILABLE = False
    pass  # 静默处理，避免警告

class PrecisionRembgProcessor:
    """基于 rembg 的精准背景替换处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.session = None
        self.fallback_enabled = True
        self._session_loaded = False
        self._last_use_time = 0
        self._cleanup_interval = 300  # 5分钟清理一次
    
    def _lazy_load_session(self):
        """懒加载rembg会话"""
        if self._session_loaded and self.session is not None:
            return
        
        current_time = time.time()
        
        # 如果距离上次使用超过清理间隔，先清理内存
        if current_time - self._last_use_time > self._cleanup_interval:
            self._cleanup_memory()
        
        if REMBG_AVAILABLE and self.session is None:
            try:
                # 初始化 rembg 会话，使用 u2net 模型（人像效果最好）
                self.session = new_session('u2net')
                self._session_loaded = True
                self._last_use_time = current_time
                self.logger.info("rembg u2net 模型懒加载成功")
            except Exception as e:
                self.logger.warning(f"rembg 懒加载失败: {e}，将使用备用算法")
                self.session = None
                self._session_loaded = False
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            if self.session is not None:
                del self.session
                self.session = None
            
            self._session_loaded = False
            
            # 强制垃圾回收
            gc.collect()
            
            self.logger.info("rembg内存清理完成")
            
        except Exception as e:
            self.logger.warning(f"rembg内存清理失败: {e}")
    
    def remove_background_rembg(self, img_cv: np.ndarray) -> Optional[np.ndarray]:
        """
        使用 rembg 移除背景
        
        Args:
            img_cv: OpenCV 格式的输入图像
            
        Returns:
            移除背景后的图像，失败时返回 None
        """
        start_time = time.time()
        
        try:
            # 懒加载会话
            self._lazy_load_session()
            
            if not REMBG_AVAILABLE or self.session is None:
                self.logger.warning("rembg 不可用或会话未加载")
                return None
            
            # 转换为 PIL 图像
            pil_img = Image.fromarray(cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB))
            
            # 使用 rembg 移除背景
            img_no_bg = remove(pil_img, session=self.session)
            
            # 转换回 OpenCV 格式
            if img_no_bg.mode == 'RGBA':
                # 如果有透明通道，转换为 RGB
                img_no_bg = img_no_bg.convert('RGB')
            
            result_cv = cv2.cvtColor(np.array(img_no_bg), cv2.COLOR_RGB2BGR)
            
            processing_time = time.time() - start_time
            self.logger.info(f"rembg 背景移除完成，耗时: {processing_time:.2f}秒")
            
            return result_cv
            
        except Exception as e:
            self.logger.error(f"rembg 处理失败: {e}")
            # 处理失败时清理内存
            self._cleanup_memory()
            return None
        finally:
            # 清理临时变量
            if 'pil_img' in locals():
                del pil_img
            if 'img_no_bg' in locals():
                del img_no_bg
            if 'result_cv' in locals():
                del result_cv
            gc.collect()
    
    def replace_background_precise(self, img_cv: np.ndarray, bg_color: Tuple[int, int, int]) -> Image.Image:
        """
        精准背景替换主函数
        
        Args:
            img_cv: OpenCV 格式的输入图像
            bg_color: 背景颜色 (R, G, B)
            
        Returns:
            PIL Image 对象
        """
        start_time = time.time()
        
        try:
            # 方法1: 尝试使用 rembg
            if REMBG_AVAILABLE:
                result = self._replace_with_rembg(img_cv, bg_color)
                if result is not None:
                    processing_time = time.time() - start_time
                    self.logger.info(f"rembg 精准背景替换完成，耗时: {processing_time:.2f}秒")
                    return result
            
            # 方法2: 备用高精度算法
            if self.fallback_enabled:
                try:
                    result = self._replace_with_advanced_cv(img_cv, bg_color)
                    processing_time = time.time() - start_time
                    self.logger.info(f"高精度CV算法背景替换完成，耗时: {processing_time:.2f}秒")
                    return result
                except Exception as e:
                    self.logger.error(f"备用算法也失败: {e}")
            
            # 方法3: 最后的简单算法
            result = self._replace_with_simple_cv(img_cv, bg_color)
            processing_time = time.time() - start_time
            self.logger.info(f"简单算法背景替换完成，耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            self.logger.error(f"背景替换失败: {e}")
            # 失败时清理内存
            self._cleanup_memory()
            raise
        finally:
            # 清理临时变量
            if 'result' in locals():
                del result
            gc.collect()
    
    def _replace_with_rembg(self, img_cv: np.ndarray, bg_color: Tuple[int, int, int]) -> Optional[Image.Image]:
        """使用 rembg 进行背景替换"""
        # 获取前景蒙版
        foreground_rgba = self.remove_background_rembg(img_cv)
        if foreground_rgba is None:
            return None
        
        height, width = foreground_rgba.shape[:2]
        
        # 创建新背景
        background = np.full((height, width, 3), bg_color, dtype=np.uint8)
        
        # 提取 alpha 通道
        alpha = foreground_rgba[:, :, 3] / 255.0
        alpha = alpha[:, :, np.newaxis]
        
        # 前景（去掉 alpha 通道）
        foreground_rgb = foreground_rgba[:, :, :3]
        
        # Alpha 混合
        result = (foreground_rgb * alpha + background * (1 - alpha)).astype(np.uint8)
        
        # 转换为 PIL Image
        return Image.fromarray(result)
    
    def _replace_with_advanced_cv(self, img_cv: np.ndarray, bg_color: Tuple[int, int, int]) -> Image.Image:
        """高精度 OpenCV 算法"""
        height, width = img_cv.shape[:2]
        
        # 1. 人脸检测
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        # 2. 创建初始蒙版
        mask = np.zeros((height, width), dtype=np.uint8)
        
        if len(faces) > 0:
            # 基于人脸扩展人体区域
            face = faces[0]  # 使用第一个检测到的人脸
            x, y, w, h = face
            
            # 扩展区域估算人体
            body_x = max(0, x - w // 2)
            body_y = max(0, y - h // 4)
            body_w = min(width - body_x, w * 2)
            body_h = min(height - body_y, int(h * 4.5))
            
            # 创建椭圆形人体蒙版
            center = (body_x + body_w // 2, body_y + body_h // 2)
            axes = (body_w // 2, body_h // 2)
            cv2.ellipse(mask, center, axes, 0, 0, 360, 255, -1)
            
            # 3. 使用 GrabCut 精细化
            try:
                rect = (body_x, body_y, body_w, body_h)
                bgd_model = np.zeros((1, 65), np.float64)
                fgd_model = np.zeros((1, 65), np.float64)
                
                # 初始化 GrabCut
                gc_mask = np.zeros((height, width), np.uint8)
                gc_mask[body_y:body_y+body_h, body_x:body_x+body_w] = cv2.GC_PR_FGD
                
                # 运行 GrabCut
                cv2.grabCut(img_cv, gc_mask, rect, bgd_model, fgd_model, 3, cv2.GC_INIT_WITH_RECT)
                
                # 提取前景
                mask2 = np.where((gc_mask == 2) | (gc_mask == 0), 0, 1).astype('uint8')
                mask = mask2 * 255
                
            except Exception as e:
                self.logger.warning(f"GrabCut 失败，使用椭圆蒙版: {e}")
        else:
            # 没有检测到人脸，使用中心区域
            center_x, center_y = width // 2, height // 2
            radius_x, radius_y = width // 3, height // 2
            cv2.ellipse(mask, (center_x, center_y), (radius_x, radius_y), 0, 0, 360, 255, -1)
        
        # 4. 蒙版后处理
        mask = self._post_process_mask(mask)
        
        # 5. 应用背景
        result = self._apply_background(img_cv, mask, bg_color)
        
        return Image.fromarray(cv2.cvtColor(result, cv2.COLOR_BGR2RGB))
    
    def _replace_with_simple_cv(self, img_cv: np.ndarray, bg_color: Tuple[int, int, int]) -> Image.Image:
        """简单 OpenCV 算法（备用）"""
        height, width = img_cv.shape[:2]
        
        # 创建简单的中心椭圆蒙版
        mask = np.zeros((height, width), dtype=np.uint8)
        center = (width // 2, height // 2)
        axes = (width // 3, int(height * 0.4))
        cv2.ellipse(mask, center, axes, 0, 0, 360, 255, -1)
        
        # 应用背景
        result = self._apply_background(img_cv, mask, bg_color)
        
        return Image.fromarray(cv2.cvtColor(result, cv2.COLOR_BGR2RGB))
    
    def _post_process_mask(self, mask: np.ndarray) -> np.ndarray:
        """蒙版后处理"""
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # 高斯模糊边缘
        mask = cv2.GaussianBlur(mask, (5, 5), 0)
        
        return mask
    
    def _apply_background(self, img_cv: np.ndarray, mask: np.ndarray, bg_color: Tuple[int, int, int]) -> np.ndarray:
        """应用背景颜色"""
        height, width = img_cv.shape[:2]
        
        # 创建背景
        background = np.full((height, width, 3), bg_color[::-1], dtype=np.uint8)  # BGR 格式
        
        # 归一化蒙版
        mask_norm = mask.astype(np.float32) / 255.0
        mask_norm = mask_norm[:, :, np.newaxis]
        
        # Alpha 混合
        result = (img_cv * mask_norm + background * (1 - mask_norm)).astype(np.uint8)
        
        return result

# 全局实例
precision_processor = PrecisionRembgProcessor()

def precision_background_replace(img_cv: np.ndarray, bg_color: Union[Tuple[int, int, int], list]) -> Image.Image:
    """
    精准背景替换函数（对外接口）
    
    Args:
        img_cv: OpenCV 格式的输入图像
        bg_color: 背景颜色 (R, G, B)
        
    Returns:
        PIL Image 对象
    """
    if isinstance(bg_color, list):
        bg_color = tuple(bg_color)
    
    return precision_processor.replace_background_precise(img_cv, bg_color)

def test_precision_processor():
    """测试精准处理器"""
    print("🧪 测试精准背景替换处理器")
    print("="*50)
    
    # 创建测试图像
    test_img = np.random.randint(0, 255, (400, 300, 3), dtype=np.uint8)
    
    try:
        # 测试处理
        start_time = time.time()
        result = precision_background_replace(test_img, (255, 255, 255))
        processing_time = time.time() - start_time
        
        print(f"✅ 处理成功，耗时: {processing_time:.2f}秒")
        print(f"📊 输出尺寸: {result.size}")
        print(f"🎯 rembg 可用: {REMBG_AVAILABLE}")
        
        if REMBG_AVAILABLE:
            models = ['u2net', 'u2netp', 'u2net_human_seg', 'silueta', 'isnet-general-use']
            print(f"🔧 支持的模型: {models}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_precision_processor() 