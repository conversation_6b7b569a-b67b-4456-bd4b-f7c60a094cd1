# Web框架
Flask>=2.3.0
Flask-SQLAlchemy>=3.0.0
Flask-Migrate>=4.0.0
Flask-CORS>=4.0.0
Flask-JWT-Extended>=4.5.0

# 数据库
PyMySQL>=1.1.0
redis>=5.0.0

# 图像处理 (基础版本)
opencv-python>=4.8.0
Pillow>=10.0.0
numpy>=1.24.0
scipy>=1.11.0
rembg>=2.0.50
onnxruntime>=1.16.0
scikit-image>=0.21.0
imageio>=2.31.0
matplotlib>=3.7.0
seaborn>=0.12.0

# 异步任务
celery>=5.3.0
kombu>=5.3.0

# 工具库
python-dotenv>=1.0.0
marshmallow>=3.20.0
python-dateutil>=2.8.0
requests>=2.31.0
gunicorn>=21.2.0
qrcode[pil]>=7.4.2

# 文档处理
python-docx>=0.8.11
docx2txt>=0.8
PyMuPDF>=1.23.8
diff-match-patch>=20230430
# textract>=1.6.5  # 暂时注释掉，有版本兼容性问题

# 简历模板渲染
Jinja2>=3.1.0
WeasyPrint>=60.1
docxtpl>=0.16.7

# 开发工具
pytest==7.4.2
pytest-flask==1.3.0

# 注释：以下包在Python 3.13上可能有兼容性问题
# mediapipe>=0.10.0  
# dlib>=19.24.0
# face-recognition>=1.3.0
# segment-anything>=1.0
# transformers>=4.35.0
# torch>=2.0.0
# torchvision>=0.15.0 