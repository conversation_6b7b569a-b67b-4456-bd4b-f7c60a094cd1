#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员用户管理API
Admin User Management API
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from sqlalchemy import or_, and_, func
from datetime import datetime, timedelta
from app import db
from app.models.user import User
from app.models.points_log import PointsLog
from app.models.user_log import UserLog

admin_users_bp = Blueprint('admin_users', __name__, url_prefix='/api/admin/users')

# 获取用户列表
@admin_users_bp.route('', methods=['GET'])
@jwt_required()
def get_users():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    keyword = request.args.get('keyword', '')
    status = request.args.get('status', '')
    vip_level = request.args.get('vip_level', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    
    query = User.query
    
    # 关键词搜索
    if keyword:
        query = query.filter(
            or_(
                User.username.contains(keyword),
                User.email.contains(keyword),
                User.phone.contains(keyword)
            )
        )
    
    # 状态筛选
    if status:
        query = query.filter(User.status == int(status))
    
    # VIP等级筛选
    if vip_level:
        query = query.filter(User.vip_level == int(vip_level))
    
    # 日期范围筛选
    if start_date and end_date:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
        query = query.filter(
            and_(
                User.created_at >= start_dt,
                User.created_at < end_dt
            )
        )
    
    # 分页
    pagination = query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    users = []
    for user in pagination.items:
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'phone': user.phone,
            'nickname': user.nickname,
            'avatar_url': user.avatar_url,
            'status': user.status,
            'vip_level': user.vip_level,
            'points': user.points,
            'points_used': user.points_used,
            'created_at': user.created_at.isoformat(),
            'last_login_time': user.last_login_time.isoformat() if user.last_login_time else None,
            'last_login_ip': user.last_login_ip
        }
        users.append(user_data)
    
    return jsonify({
        'users': users,
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page,
        'per_page': per_page
    })

# 获取用户详情
@admin_users_bp.route('/<int:user_id>', methods=['GET'])
@jwt_required()
def get_user_detail(user_id):
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    user_data = {
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'phone': user.phone,
        'nickname': user.nickname,
        'gender': user.gender,
        'birthday': user.birthday.isoformat() if user.birthday else None,
        'avatar_url': user.avatar_url,
        'status': user.status,
        'vip_level': user.vip_level,
        'vip_expire_time': user.vip_expire_time.isoformat() if user.vip_expire_time else None,
        'points': user.points,
        'points_used': user.points_used,
        'created_at': user.created_at.isoformat(),
        'updated_at': user.updated_at.isoformat(),
        'last_login_time': user.last_login_time.isoformat() if user.last_login_time else None,
        'last_login_ip': user.last_login_ip
    }
    
    return jsonify(user_data)

# 切换用户状态
@admin_users_bp.route('/<int:user_id>/toggle-status', methods=['POST'])
@jwt_required()
def toggle_user_status(user_id):
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 切换状态
    user.status = 2 if user.status == 1 else 1
    db.session.commit()
    
    return jsonify({
        'msg': f'用户状态已切换为{"正常" if user.status == 1 else "禁用"}',
        'status': user.status
    })

# 批量切换用户状态
@admin_users_bp.route('/batch-toggle-status', methods=['POST'])
@jwt_required()
def batch_toggle_user_status():
    data = request.json
    user_ids = data.get('user_ids', [])
    action = data.get('action', '')  # 'enable' or 'disable'
    
    if not user_ids:
        return jsonify({'error': '请选择用户'}), 400
    
    status = 1 if action == 'enable' else 2
    
    users = User.query.filter(User.id.in_(user_ids)).all()
    for user in users:
        user.status = status
    
    db.session.commit()
    
    return jsonify({
        'msg': f'已批量{action} {len(users)} 个用户',
        'affected_count': len(users)
    })

# 调整用户积分
@admin_users_bp.route('/<int:user_id>/adjust-points', methods=['POST'])
@jwt_required()
def adjust_user_points(user_id):
    data = request.json
    change = data.get('change', 0)
    remark = data.get('remark', '管理员调整')
    
    user = User.query.get(user_id)
    if not user:
        return jsonify({'error': '用户不存在'}), 404
    
    # 调整积分
    user.points += change
    if change < 0:
        user.points_used += abs(change)
    
    # 记录积分变动日志
    log = PointsLog(
        user_id=user.id,
        change=change,
        type='admin',
        remark=remark,
        balance=user.points
    )
    db.session.add(log)
    db.session.commit()
    
    return jsonify({
        'msg': '积分调整成功',
        'new_balance': user.points,
        'change': change
    })

# 导出用户数据
@admin_users_bp.route('/export', methods=['GET'])
@jwt_required()
def export_users():
    import pandas as pd
    from io import BytesIO
    
    # 获取筛选参数
    keyword = request.args.get('keyword', '')
    status = request.args.get('status', '')
    vip_level = request.args.get('vip_level', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    
    query = User.query
    
    # 应用筛选条件
    if keyword:
        query = query.filter(
            or_(
                User.username.contains(keyword),
                User.email.contains(keyword),
                User.phone.contains(keyword)
            )
        )
    
    if status:
        query = query.filter(User.status == int(status))
    
    if vip_level:
        query = query.filter(User.vip_level == int(vip_level))
    
    if start_date and end_date:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
        query = query.filter(
            and_(
                User.created_at >= start_dt,
                User.created_at < end_dt
            )
        )
    
    users = query.all()
    
    # 准备数据
    data = []
    for user in users:
        data.append({
            '用户ID': user.id,
            '用户名': user.username,
            '邮箱': user.email,
            '手机号': user.phone or '',
            '昵称': user.nickname or '',
            '状态': '正常' if user.status == 1 else '禁用',
            'VIP等级': user.vip_level,
            '积分': user.points,
            '已用积分': user.points_used,
            '注册时间': user.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            '最后登录': user.last_login_time.strftime('%Y-%m-%d %H:%M:%S') if user.last_login_time else '',
            '最后登录IP': user.last_login_ip or ''
        })
    
    # 创建Excel文件
    df = pd.DataFrame(data)
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='用户数据', index=False)
    
    output.seek(0)
    
    from flask import send_file
    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'用户数据_{datetime.now().strftime("%Y%m%d")}.xlsx'
    ) 

# 获取用户操作日志
@admin_users_bp.route('/logs', methods=['GET'])
@jwt_required()
def get_user_logs():
    """获取用户操作日志"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    user_id = request.args.get('user_id', '')
    action = request.args.get('action', '')
    resource_type = request.args.get('resource_type', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    
    query = UserLog.query.join(User, UserLog.user_id == User.id)
    
    # 用户ID筛选
    if user_id:
        query = query.filter(UserLog.user_id == int(user_id))
    
    # 操作类型筛选
    if action:
        query = query.filter(UserLog.action == action)
    
    # 资源类型筛选
    if resource_type:
        query = query.filter(UserLog.resource_type == resource_type)
    
    # 日期范围筛选
    if start_date and end_date:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
        query = query.filter(
            and_(
                UserLog.created_at >= start_dt,
                UserLog.created_at < end_dt
            )
        )
    
    # 分页
    pagination = query.order_by(UserLog.created_at.desc()).paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    logs = []
    for log in pagination.items:
        log_data = {
            'id': log.id,
            'user_id': log.user_id,
            'username': log.user.username if log.user else '未知用户',
            'action': log.action,
            'resource_type': log.resource_type,
            'resource_id': log.resource_id,
            'ip_address': log.ip_address,
            'user_agent': log.user_agent,
            'request_data': log.request_data,
            'response_status': log.response_status,
            'created_at': log.created_at.isoformat()
        }
        logs.append(log_data)
    
    return jsonify({
        'logs': logs,
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page,
        'per_page': per_page
    })

# 获取用户日志统计
@admin_users_bp.route('/logs/stats', methods=['GET'])
@jwt_required()
def get_user_logs_stats():
    """获取用户日志统计信息"""
    # 今日日志数量
    today = datetime.utcnow().date()
    today_logs = db.session.query(func.count(UserLog.id)).filter(
        UserLog.created_at >= today
    ).scalar() or 0
    
    # 总日志数量
    total_logs = db.session.query(func.count(UserLog.id)).scalar() or 0
    
    # 活跃用户数（今日有操作的用户）
    active_users = db.session.query(func.count(func.distinct(UserLog.user_id))).filter(
        UserLog.created_at >= today
    ).scalar() or 0
    
    # 操作类型统计
    action_stats = db.session.query(
        UserLog.action,
        func.count(UserLog.id)
    ).group_by(UserLog.action).order_by(func.count(UserLog.id).desc()).limit(10).all()
    
    # 最近7天的日志趋势
    seven_days_ago = datetime.utcnow().date() - timedelta(days=7)
    daily_logs = db.session.query(
        func.date(UserLog.created_at).label('date'),
        func.count(UserLog.id).label('count')
    ).filter(
        UserLog.created_at >= seven_days_ago
    ).group_by(func.date(UserLog.created_at)).order_by(func.date(UserLog.created_at)).all()
    
    return jsonify({
        'today_logs': today_logs,
        'total_logs': total_logs,
        'active_users': active_users,
        'action_stats': [{'action': action, 'count': count} for action, count in action_stats],
        'daily_trend': [{'date': str(date), 'count': count} for date, count in daily_logs]
    })

# 导出用户日志
@admin_users_bp.route('/logs/export', methods=['GET'])
@jwt_required()
def export_user_logs():
    """导出用户操作日志"""
    import pandas as pd
    from io import BytesIO
    
    # 获取筛选参数
    user_id = request.args.get('user_id', '')
    action = request.args.get('action', '')
    resource_type = request.args.get('resource_type', '')
    start_date = request.args.get('start_date', '')
    end_date = request.args.get('end_date', '')
    
    query = UserLog.query.join(User, UserLog.user_id == User.id)
    
    # 应用筛选条件
    if user_id:
        query = query.filter(UserLog.user_id == int(user_id))
    
    if action:
        query = query.filter(UserLog.action == action)
    
    if resource_type:
        query = query.filter(UserLog.resource_type == resource_type)
    
    if start_date and end_date:
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
        query = query.filter(
            and_(
                UserLog.created_at >= start_dt,
                UserLog.created_at < end_dt
            )
        )
    
    logs = query.order_by(UserLog.created_at.desc()).limit(10000).all()
    
    # 准备数据
    data = []
    for log in logs:
        data.append({
            '日志ID': log.id,
            '用户ID': log.user_id,
            '用户名': log.user.username if log.user else '未知用户',
            '操作类型': log.action,
            '资源类型': log.resource_type or '',
            '资源ID': log.resource_id or '',
            'IP地址': log.ip_address or '',
            '响应状态': log.response_status or '',
            '操作时间': log.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 创建Excel文件
    df = pd.DataFrame(data)
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='用户操作日志', index=False)
    
    output.seek(0)
    
    from flask import send_file
    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'用户操作日志_{datetime.now().strftime("%Y%m%d")}.xlsx'
    ) 