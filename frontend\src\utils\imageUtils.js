/**
 * 图片URL处理工具函数
 */

/**
 * 构建完整的图片URL
 * @param {string} url - 相对图片URL
 * @returns {string} 完整的图片URL
 */
export function getImageUrl(url) {
  if (!url) return ''
  if (url.startsWith('http')) return url
  const baseUrl = process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000'
  return `${baseUrl}${url}`
}

/**
 * 构建头像URL
 * @param {string} avatarUrl - 头像URL
 * @returns {string} 完整的头像URL
 */
export function getAvatarUrl(avatarUrl) {
  return getImageUrl(avatarUrl)
} 