import os
import io
import logging
import tempfile
from typing import Dict, Any, Optional
from datetime import datetime
from jinja2 import Template, Environment, FileSystemLoader
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn
from app.models.resume import Resume, ResumeTemplate
from app.services.resume_templates.renderer import ResumeRenderer
import traceback

logger = logging.getLogger(__name__)

class ResumeGenerator:
    """
    简历生成器 - 按照me.md文档要求实现多格式简历生成
    支持PDF和DOCX格式的简历导出功能
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.renderer = ResumeRenderer()
        
        # 默认模板样式
        self.default_css = """
        @page {
            size: A4;
            margin: 1in;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'SimSun', <PERSON><PERSON>, sans-serif;
            font-size: 11pt;
            line-height: 1.5;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            border-bottom: 2pt solid #3498db;
            margin-bottom: 16pt;
            padding-bottom: 12pt;
        }
        
        .name {
            font-size: 18pt;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 8pt;
        }
        
        .contact-info {
            font-size: 10pt;
            color: #666;
            line-height: 1.3;
        }
        
        .section {
            margin-bottom: 16pt;
            break-inside: avoid;
        }
        
        .section-title {
            font-size: 12pt;
            font-weight: bold;
            color: #3498db;
            border-bottom: 1pt solid #bdc3c7;
            padding-bottom: 4pt;
            margin-bottom: 8pt;
        }
        
        .education-item, .experience-item, .project-item, .certification-item {
            margin-bottom: 12pt;
            break-inside: avoid;
        }
        
        .item-header {
            font-weight: bold;
            margin-bottom: 4pt;
            font-size: 11pt;
        }
        
        .item-meta {
            color: #666;
            font-size: 10pt;
            margin-bottom: 4pt;
            font-style: italic;
        }
        
        .item-description {
            margin-left: 12pt;
            text-align: justify;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 6pt;
        }
        
        .skill-item {
            background: #f8f9fa;
            border: 1pt solid #dee2e6;
            padding: 4pt 8pt;
            border-radius: 2pt;
            font-size: 10pt;
            display: inline-block;
        }
        
        .photo {
            width: 90pt;
            height: 120pt;
            border: 1pt solid #ddd;
            border-radius: 4pt;
            float: right;
            margin-left: 12pt;
            margin-bottom: 12pt;
        }
        
        /* 打印优化 */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        """
    
    def generate_pdf(self, resume_id: int, output_path: Optional[str] = None) -> bytes:
        """
        生成PDF格式简历（暂时返回HTML内容，等待PDF库修复）
        
        Args:
            resume_id: 简历ID
            output_path: 输出文件路径，如果不指定则生成临时文件
            
        Returns:
            bytes: 生成的HTML内容（字节形式）
        """
        try:
            # 获取简历数据
            resume = Resume.query.get(resume_id)
            if not resume:
                raise ValueError(f"简历不存在: {resume_id}")
            
            # 暂时生成HTML内容，等待PDF库修复
            html_content = self.generate_html(resume)
            
            # 添加打印友好的样式
            print_friendly_html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>{resume.full_name} - 简历</title>
                <style>
                    @media print {{
                        @page {{ size: A4; margin: 0.5in; }}
                        body {{ -webkit-print-color-adjust: exact; }}
                    }}
                    {self.default_css}
                </style>
            </head>
            <body>
                {html_content.split('<body>')[1].split('</body>')[0] if '<body>' in html_content else html_content}
            </body>
            </html>
            """
            
            self.logger.info(f"HTML简历生成成功（PDF功能暂时不可用）")
            return print_friendly_html.encode('utf-8')
            
        except Exception as e:
            self.logger.error(f"简历生成失败: {str(e)}")
            raise ValueError(f"简历生成失败: {str(e)}")
    
    def generate_docx(self, resume_id: int, output_path: Optional[str] = None) -> str:
        """
        生成DOCX格式简历
        
        Args:
            resume_id: 简历ID
            output_path: 输出文件路径，如果不指定则生成临时文件
            
        Returns:
            str: 生成的DOCX文件路径
        """
        try:
            # 获取简历数据
            resume = Resume.query.get(resume_id)
            if not resume:
                raise ValueError(f"简历不存在: {resume_id}")
            
            # 创建文档
            doc = Document()
            
            # 设置文档样式
            self._setup_docx_styles(doc)
            
            # 生成文档内容
            self._generate_docx_content(doc, resume)
            
            # 生成输出路径
            if not output_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"resume_{resume.id}_{timestamp}.docx"
                output_path = os.path.join(tempfile.gettempdir(), filename)
            
            # 保存文档
            doc.save(output_path)
            
            self.logger.info(f"DOCX简历生成成功: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"DOCX简历生成失败: {str(e)}")
            raise ValueError(f"DOCX生成失败: {str(e)}")
    
    def preview_html(self, resume_id: int) -> str:
        """
        生成HTML预览内容
        
        Args:
            resume_id: 简历ID
            
        Returns:
            str: HTML内容
        """
        try:
            self.logger.info(f"开始生成简历预览: resume_id={resume_id}")
            
            resume = Resume.query.get(resume_id)
            if not resume:
                self.logger.error(f"简历不存在: resume_id={resume_id}")
                raise ValueError(f"简历不存在: {resume_id}")
            
            self.logger.info(f"获取简历数据成功: title={resume.title}")
            self.logger.info(f"简历模板信息: template_id={resume.template_id}, template_name={resume.template.name if resume.template else 'None'}")
            
            html_content = self.generate_html(resume)
            self.logger.info("HTML预览生成成功")
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"HTML预览生成失败: {str(e)}\n{traceback.format_exc()}")
            raise ValueError(f"预览生成失败: {str(e)}")
    
    def preview_from_data(self, data: Dict[str, Any]) -> str:
        """
        从数据直接生成HTML预览内容（用于实时预览）
        
        Args:
            data: 简历数据字典
            
        Returns:
            str: HTML内容
        """
        try:
            # 获取模板
            template_id = data.get('template_id')
            template = None
            if template_id:
                template = ResumeTemplate.query.get(template_id)
            
            template_html = self._get_template_html(template)
            template_css = self._get_template_css(template)
            
            # 准备模板变量
            template_vars = {
                'template': template,
                'personal_info': {
                    'name': data.get('full_name', ''),
                    'phone': data.get('phone', ''),
                    'email': data.get('email', ''),
                    'address': data.get('address', ''),
                    'photo_url': data.get('photo_url', '')
                },
                'objective': data.get('objective', ''),
                'summary': data.get('summary', ''),
                'educations': data.get('educations', []),
                'work_experiences': data.get('work_experiences', []),
                'projects': data.get('projects', []),
                'skills': data.get('skills', []),
                'certifications': data.get('certifications', []),
                'css': template_css
            }
            
            # 渲染模板
            template_obj = Template(template_html)
            html_content = template_obj.render(**template_vars)
            
            self.logger.info("数据预览生成成功")
            return html_content
            
        except Exception as e:
            self.logger.error(f"数据预览生成失败: {str(e)}")
            raise ValueError(f"预览生成失败: {str(e)}")
    
    def generate_html(self, resume: Resume) -> str:
        """生成HTML内容

        Args:
            resume (Resume): 简历对象

        Returns:
            str: HTML内容
        """
        try:
            self.logger.info("开始生成HTML内容")
            
            # 获取模板ID，如果没有则使用基础模板
            template_id = 'basic'
            if resume.template_id:
                # 根据数据库模板ID映射到后端模板ID
                template_mapping = {
                    1: 'basic',
                    2: 'modern', 
                    3: 'creative_sidebar',
                    4: 'elegant_business',
                    5: 'basic',  # 极简主义暂时映射到基础模板
                    6: 'creative_icon',
                    7: 'professional_timeline',
                    8: 'simple_ats',
                    9: 'modern_two_column',
                    10: 'classic_one_column'
                }
                template_id = template_mapping.get(resume.template_id, 'basic')
            
            self.logger.info(f"使用模板: {template_id}")
            
            # 准备简历数据
            resume_data = {
                'id': resume.id,
                'full_name': resume.full_name,
                'gender': resume.gender,
                'age': resume.age,
                'phone': resume.phone,
                'email': resume.email,
                'address': resume.address,
                'objective': resume.objective,
                'summary': resume.summary,
                'photo_url': resume.photo_url,
                'educations': [edu.to_dict() for edu in resume.educations],
                'work_experiences': [exp.to_dict() for exp in resume.work_experiences],
                'projects': [proj.to_dict() for proj in resume.projects],
                'skills': [skill.to_dict() for skill in resume.skills],
                'certifications': [cert.to_dict() for cert in resume.certifications]
            }
            
            # 使用新的模板渲染器
            html_content = self.renderer.render_html(resume_data, template_id)
            
            self.logger.info("HTML内容生成成功")
            return html_content
            
        except Exception as e:
            self.logger.error(f"生成HTML内容失败: {str(e)}\n{traceback.format_exc()}")
            # 如果新渲染器失败，回退到旧方法
            return self._generate_html_fallback(resume)
    
    def _generate_html_fallback(self, resume: Resume) -> str:
        """回退方法：使用旧的HTML生成逻辑"""
        try:
            self.logger.info("使用回退方法生成HTML")
            
            # 获取模板
            template_html = self._get_template_html(resume.template)
            template_css = self._get_template_css(resume.template)
            
            # 准备模板变量
            template_vars = {
                'resume': resume,
                'template': resume.template,
                'personal_info': {
                    'name': resume.full_name,
                    'phone': resume.phone,
                    'email': resume.email,
                    'address': resume.address,
                    'photo_url': resume.photo_url
                },
                'objective': resume.objective,
                'summary': resume.summary,
                'educations': [edu.to_dict() for edu in resume.educations],
                'work_experiences': [exp.to_dict() for exp in resume.work_experiences],
                'projects': [proj.to_dict() for proj in resume.projects],
                'skills': [skill.to_dict() for skill in resume.skills],
                'certifications': [cert.to_dict() for cert in resume.certifications],
                'css': template_css
            }
            
            # 渲染模板
            template = Template(template_html)
            html_content = template.render(**template_vars)
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"回退方法也失败了: {str(e)}")
            raise
    
    def _get_template_html(self, template: Optional[ResumeTemplate]) -> str:
        """获取模板HTML"""
        if template and template.template_html:
            return template.template_html
        
        # 默认模板
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{{ personal_info.name }} - 简历</title>
            <style>{{ css }}</style>
        </head>
        <body>
            <div class="header">
                {% if personal_info.photo_url %}
                <img src="{{ personal_info.photo_url }}" class="photo" alt="照片">
                {% endif %}
                <div class="name">{{ personal_info.name }}</div>
                <div class="contact-info">
                    {% if personal_info.phone %}电话：{{ personal_info.phone }} | {% endif %}
                    {% if personal_info.email %}邮箱：{{ personal_info.email }} | {% endif %}
                    {% if personal_info.address %}地址：{{ personal_info.address }}{% endif %}
                </div>
            </div>
            
            {% if objective %}
            <div class="section">
                <div class="section-title">求职意向</div>
                <div>{{ objective }}</div>
            </div>
            {% endif %}
            
            {% if summary %}
            <div class="section">
                <div class="section-title">个人总结</div>
                <div>{{ summary }}</div>
            </div>
            {% endif %}
            
            {% if educations %}
            <div class="section">
                <div class="section-title">教育背景</div>
                {% for edu in educations %}
                <div class="education-item">
                    <div class="item-header">{{ edu.school_name }} - {{ edu.major }}</div>
                    <div class="item-meta">{{ edu.degree }} | {{ edu.start_date }} - {{ edu.end_date }}</div>
                    {% if edu.description %}
                    <div class="item-description">{{ edu.description }}</div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% if work_experiences %}
            <div class="section">
                <div class="section-title">工作经历</div>
                {% for exp in work_experiences %}
                <div class="experience-item">
                    <div class="item-header">{{ exp.company_name }} - {{ exp.position }}</div>
                    <div class="item-meta">{{ exp.start_date }} - {% if exp.is_current %}至今{% else %}{{ exp.end_date }}{% endif %}</div>
                    {% if exp.description %}
                    <div class="item-description">{{ exp.description }}</div>
                    {% endif %}
                    {% if exp.achievements %}
                    <div class="item-description">
                        {% for achievement in exp.achievements %}
                        <div>• {{ achievement }}</div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% if projects %}
            <div class="section">
                <div class="section-title">项目经验</div>
                {% for project in projects %}
                <div class="project-item">
                    <div class="item-header">{{ project.name }}</div>
                    <div class="item-meta">{{ project.role }} | {{ project.start_date }} - {{ project.end_date }}</div>
                    {% if project.description %}
                    <div class="item-description">{{ project.description }}</div>
                    {% endif %}
                    {% if project.technologies %}
                    <div class="item-description">技术栈：{{ project.technologies | join(', ') }}</div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% if skills %}
            <div class="section">
                <div class="section-title">专业技能</div>
                <div class="skills-list">
                    {% for skill in skills %}
                    <div class="skill-item">{{ skill.name }}{% if skill.proficiency %} ({{ skill.proficiency }}){% endif %}</div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
            
            {% if certifications %}
            <div class="section">
                <div class="section-title">证书资质</div>
                {% for cert in certifications %}
                <div class="certification-item">
                    <div class="item-header">{{ cert.name }}</div>
                    {% if cert.issuer %}<div class="item-meta">颁发机构：{{ cert.issuer }}</div>{% endif %}
                    {% if cert.issue_date %}<div class="item-meta">获得时间：{{ cert.issue_date }}</div>{% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </body>
        </html>
        """
    
    def _get_template_css(self, template: Optional[ResumeTemplate]) -> str:
        """获取模板CSS"""
        if template and template.template_css:
            return template.template_css
        return self.default_css
    
    def _setup_docx_styles(self, doc: Document):
        """设置DOCX文档样式"""
        # 设置页面边距
        sections = doc.sections
        for section in sections:
            section.top_margin = Inches(0.5)
            section.bottom_margin = Inches(0.5)
            section.left_margin = Inches(0.75)
            section.right_margin = Inches(0.75)
        
        # 设置默认字体
        style = doc.styles['Normal']
        font = style.font
        font.name = 'Microsoft YaHei'
        font.size = Pt(11)
    
    def _generate_docx_content(self, doc: Document, resume: Resume):
        """生成DOCX文档内容"""
        try:
            # 添加标题（姓名）
            title = doc.add_heading(resume.full_name, 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加联系信息
            contact_info = []
            if resume.phone:
                contact_info.append(f"电话：{resume.phone}")
            if resume.email:
                contact_info.append(f"邮箱：{resume.email}")
            if resume.address:
                contact_info.append(f"地址：{resume.address}")
            
            if contact_info:
                contact_para = doc.add_paragraph(" | ".join(contact_info))
                contact_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 添加求职意向
            if resume.objective:
                doc.add_heading("求职意向", 1)
                doc.add_paragraph(resume.objective)
            
            # 添加个人总结
            if resume.summary:
                doc.add_heading("个人总结", 1)
                doc.add_paragraph(resume.summary)
            
            # 添加教育背景
            if resume.educations:
                doc.add_heading("教育背景", 1)
                for edu in resume.educations:
                    edu_para = doc.add_paragraph()
                    edu_para.add_run(f"{edu.school_name} - {edu.major}").bold = True
                    doc.add_paragraph(f"{edu.degree} | {edu.start_date} - {edu.end_date}")
                    if edu.description:
                        doc.add_paragraph(edu.description)
            
            # 添加工作经历
            if resume.work_experiences:
                doc.add_heading("工作经历", 1)
                for exp in resume.work_experiences:
                    exp_para = doc.add_paragraph()
                    exp_para.add_run(f"{exp.company_name} - {exp.position}").bold = True
                    end_date = "至今" if exp.is_current else exp.end_date
                    doc.add_paragraph(f"{exp.start_date} - {end_date}")
                    if exp.description:
                        doc.add_paragraph(exp.description)
                    if exp.achievements:
                        for achievement in exp.achievements:
                            doc.add_paragraph(f"• {achievement}")
            
            # 添加项目经验
            if resume.projects:
                doc.add_heading("项目经验", 1)
                for project in resume.projects:
                    proj_para = doc.add_paragraph()
                    proj_para.add_run(project.name).bold = True
                    doc.add_paragraph(f"{project.role} | {project.start_date} - {project.end_date}")
                    if project.description:
                        doc.add_paragraph(project.description)
                    if project.technologies:
                        doc.add_paragraph(f"技术栈：{', '.join(project.technologies)}")
            
            # 添加专业技能
            if resume.skills:
                doc.add_heading("专业技能", 1)
                skills_by_category = {}
                for skill in resume.skills:
                    category = skill.category or "其他"
                    if category not in skills_by_category:
                        skills_by_category[category] = []
                    skill_text = skill.name
                    if skill.proficiency:
                        skill_text += f" ({skill.proficiency})"
                    skills_by_category[category].append(skill_text)
                
                for category, skills in skills_by_category.items():
                    doc.add_paragraph(f"{category}：{', '.join(skills)}")
            
            # 添加证书资质
            if resume.certifications:
                doc.add_heading("证书资质", 1)
                for cert in resume.certifications:
                    cert_para = doc.add_paragraph()
                    cert_para.add_run(cert.name).bold = True
                    if cert.issuer:
                        doc.add_paragraph(f"颁发机构：{cert.issuer}")
                    if cert.issue_date:
                        doc.add_paragraph(f"获得时间：{cert.issue_date}")
        except Exception as e:
            self.logger.error(f"生成DOCX内容失败: {str(e)}\n{traceback.format_exc()}")
            raise
    
    def get_supported_formats(self) -> list:
        """获取支持的导出格式"""
        return ['pdf', 'docx', 'html']
    
    def validate_template(self, template_html: str, template_css: str = '') -> Dict[str, Any]:
        """
        验证模板有效性
        
        Args:
            template_html: 模板HTML
            template_css: 模板CSS
            
        Returns:
            Dict: 验证结果
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            # 验证Jinja2模板语法
            template = Template(template_html)
            
            # 尝试渲染模板（使用空数据）
            template.render(
                personal_info={'name': '测试姓名', 'phone': '', 'email': ''},
                objective='',
                summary='',
                educations=[],
                work_experiences=[],
                projects=[],
                skills=[],
                certifications=[],
                css=template_css
            )
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"模板语法错误: {str(e)}")
        
        return result 