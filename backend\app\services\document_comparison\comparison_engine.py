from enum import Enum
from dataclasses import dataclass
from typing import List, Dict, Any, Tuple, Optional
import difflib
import unicodedata
import logging
from diff_match_patch import diff_match_patch
import json
import re

logger = logging.getLogger(__name__)

class ComparisonType(Enum):
    """对比类型枚举"""
    TEXT = "text"               # 纯文本对比
    STRUCTURED = "structured"   # 结构化对比
    VISUAL = "visual"          # 视觉对比
    TABLE = "table"            # 表格对比

class DifferenceType(Enum):
    """差异类型枚举"""
    INSERTION = 1    # 插入
    DELETION = -1    # 删除
    EQUAL = 0        # 相同
    REPLACEMENT = 2  # 替换

@dataclass
class ComparisonOptions:
    """对比选项配置"""
    ignore_whitespace: bool = True
    ignore_case: bool = False
    ignore_punctuation: bool = False
    detailed_report: bool = True
    extract_tables: bool = True
    visual_comparison: bool = False
    similarity_threshold: float = 0.6
    context_lines: int = 3

class ComparisonResult:
    """对比结果"""
    def __init__(self, file1_name: str, file2_name: str, comparison_type: ComparisonType):
        self.file1_name = file1_name
        self.file2_name = file2_name
        self.comparison_type = comparison_type
        self.differences = []
        self.statistics = {}
        self.summary = {}
        self.metadata = {}

class EnhancedComparisonEngine:
    """
    增强的文档对比引擎 - 按about.md技术方案实现
    支持多种对比模式和智能对比策略
    """
    
    def __init__(self):
        # 初始化Google diff-match-patch算法
        self.dmp = diff_match_patch()
        self.logger = logging.getLogger(__name__)
    
    def compare_documents(self, 
                         doc1_data: Dict[str, Any], 
                         doc2_data: Dict[str, Any], 
                         options: ComparisonOptions = None) -> ComparisonResult:
        """
        文档对比主入口 - 支持多种对比模式
        
        Args:
            doc1_data: 文档1的解析数据
            doc2_data: 文档2的解析数据
            options: 对比选项
            
        Returns:
            ComparisonResult: 包含差异、统计和总结的对比结果
        """
        # 一键修正：默认不开启任何归一化，严格逐字对比
        if not options:
            options = ComparisonOptions(
                ignore_whitespace=False,
                ignore_case=False,
                ignore_punctuation=False,
                detailed_report=True,
                extract_tables=True,
                visual_comparison=False
            )
        else:
            options.ignore_whitespace = False
            options.ignore_case = False
            options.ignore_punctuation = False
        
        # 获取文件名
        file1_name = doc1_data.get('file_info', {}).get('name', 'Document 1')
        file2_name = doc2_data.get('file_info', {}).get('name', 'Document 2')
        
        # 确定对比类型
        comparison_type = self._determine_comparison_type(doc1_data, doc2_data, options)
        
        # 初始化结果对象
        result = ComparisonResult(file1_name, file2_name, comparison_type)
        
        try:
            # 根据对比类型选择对比方法
            if comparison_type == ComparisonType.TEXT:
                self._compare_text(doc1_data, doc2_data, options, result)
            elif comparison_type == ComparisonType.STRUCTURED:
                self._compare_structured(doc1_data, doc2_data, options, result)
            elif comparison_type == ComparisonType.TABLE:
                self._compare_tables(doc1_data, doc2_data, options, result)
            elif comparison_type == ComparisonType.VISUAL:
                self._compare_visual(doc1_data, doc2_data, options, result)
            
            # 增强：对比图片
            images1 = doc1_data.get('images', [])
            images2 = doc2_data.get('images', [])
            img_set1 = set([img.get('hash', img.get('name', str(i))) for i, img in enumerate(images1)])
            img_set2 = set([img.get('hash', img.get('name', str(i))) for i, img in enumerate(images2)])
            for img in img_set1 - img_set2:
                result.differences.append({
                    'type': 'image_deletion',
                    'image': img,
                    'detail': f"图片被删除: {img}"
                })
            for img in img_set2 - img_set1:
                result.differences.append({
                    'type': 'image_insertion',
                    'image': img,
                    'detail': f"图片新增: {img}"
                })
            # 增强：对比元数据
            meta1 = doc1_data.get('metadata', {})
            meta2 = doc2_data.get('metadata', {})
            all_keys = set(meta1.keys()) | set(meta2.keys())
            for key in all_keys:
                v1 = meta1.get(key, None)
                v2 = meta2.get(key, None)
                if v1 != v2:
                    result.differences.append({
                        'type': 'metadata_difference',
                        'key': key,
                        'old_value': v1,
                        'new_value': v2,
                        'detail': f"元数据字段 '{key}' 不同: '{v1}' vs '{v2}'"
                    })
            
            # 计算并设置相似度
            self._calculate_and_set_similarity(result, doc1_data, doc2_data, options)
            
            return result
        except Exception as e:
            self.logger.error(f"文档对比失败: {str(e)}")
            raise ValueError(f"对比失败: {str(e)}")
    
    def _determine_comparison_type(self, doc1_data: Dict, doc2_data: Dict, options: ComparisonOptions) -> ComparisonType:
        """
        智能确定最佳对比类型
        """
        # 检查是否为PDF（需要视觉对比）
        if doc1_data.get('format') == 'pdf' and doc2_data.get('format') == 'pdf':
            if options.visual_comparison:
                return ComparisonType.VISUAL
        
        # 检查是否有表格需要对比
        if options.extract_tables and (doc1_data.get('tables') or doc2_data.get('tables')):
            return ComparisonType.TABLE
        
        # 检查是否有结构化内容
        if doc1_data.get('structured_content') and doc2_data.get('structured_content'):
            return ComparisonType.STRUCTURED
        
        # 默认使用文本对比
        return ComparisonType.TEXT
    
    def _compare_text(self, doc1_data: Dict, doc2_data: Dict, options: ComparisonOptions, result: ComparisonResult):
        """
        纯文本对比 - 分段处理，提升细粒度检测能力
        """
        text1 = doc1_data.get('text', '')
        text2 = doc2_data.get('text', '')
        # 分段（按换行或句号）
        def split_segments(text):
            # 按段落或句子分割
            return [seg.strip() for seg in re.split(r'[\n。！？!?.]', text) if seg.strip()]
        segs1 = split_segments(text1)
        segs2 = split_segments(text2)
        differences = []
        from difflib import SequenceMatcher
        matcher = SequenceMatcher(None, segs1, segs2)
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                continue
            elif tag == 'replace':
                for k in range(max(i2-i1, j2-j1)):
                    s1 = segs1[i1+k] if i1+k < i2 else ''
                    s2 = segs2[j1+k] if j1+k < j2 else ''
                    if s1 and s2:
                        differences.append({'type': 2, 'old_content': s1, 'new_content': s2, 'position': i1+k})
                    elif s1:
                        differences.append({'type': -1, 'old_content': s1, 'position': i1+k})
                    elif s2:
                        differences.append({'type': 1, 'new_content': s2, 'position': j1+k})
            elif tag == 'delete':
                for k in range(i1, i2):
                    differences.append({'type': -1, 'old_content': segs1[k], 'position': k})
            elif tag == 'insert':
                for k in range(j1, j2):
                    differences.append({'type': 1, 'new_content': segs2[k], 'position': k})
        result.differences = differences
    
    def _extract_all_text(self, item):
        """递归提取结构化内容所有文本字段"""
        if isinstance(item, dict):
            texts = []
            for k, v in item.items():
                if k in ('text', 'content', 'children'):
                    texts.append(self._extract_all_text(v))
            return ' '.join(texts)
        elif isinstance(item, list):
            return ' '.join(self._extract_all_text(i) for i in item)
        else:
            return str(item)

    def _compare_structured(self, doc1_data: Dict, doc2_data: Dict, options: ComparisonOptions, result: ComparisonResult):
        """
        结构化对比 - 递归比对所有文本相关字段
        """
        content1 = doc1_data.get('structured_content', [])
        content2 = doc2_data.get('structured_content', [])
        from difflib import SequenceMatcher
        norm_content1 = [self._extract_all_text(item) for item in content1]
        norm_content2 = [self._extract_all_text(item) for item in content2]
        matcher = SequenceMatcher(None, norm_content1, norm_content2)
        differences = []
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            if tag == 'equal':
                continue
            elif tag == 'replace':
                for k in range(max(i2-i1, j2-j1)):
                    s1 = norm_content1[i1+k] if i1+k < i2 else ''
                    s2 = norm_content2[j1+k] if j1+k < j2 else ''
                    if s1 and s2:
                        differences.append({'type': 2, 'old_content': s1, 'new_content': s2, 'position': i1+k})
                    elif s1:
                        differences.append({'type': -1, 'old_content': s1, 'position': i1+k})
                    elif s2:
                        differences.append({'type': 1, 'new_content': s2, 'position': j1+k})
            elif tag == 'delete':
                for k in range(i1, i2):
                    differences.append({'type': -1, 'old_content': norm_content1[k], 'position': k})
            elif tag == 'insert':
                for k in range(j1, j2):
                    differences.append({'type': 1, 'new_content': norm_content2[k], 'position': k})
        result.differences = differences
    
    def _compare_tables(self, doc1_data: Dict, doc2_data: Dict, options: ComparisonOptions, result: ComparisonResult):
        """
        表格专用对比算法 - 按about.md建议实现结构化数据对比
        """
        tables1 = doc1_data.get('tables', [])
        tables2 = doc2_data.get('tables', [])
        
        differences = []
        
        # 如果表格数量不同
        if len(tables1) != len(tables2):
            differences.append({
                'type': 'table_count_difference',
                'old_count': len(tables1),
                'new_count': len(tables2),
                'message': f'表格数量从 {len(tables1)} 变为 {len(tables2)}'
            })
        
        # 逐个对比表格
        max_tables = max(len(tables1), len(tables2))
        for i in range(max_tables):
            table1 = tables1[i] if i < len(tables1) else None
            table2 = tables2[i] if i < len(tables2) else None
            
            if table1 is None:
                differences.append({
                    'type': DifferenceType.INSERTION.value,
                    'table_index': i,
                    'new_table': table2,
                    'message': f'新增表格 {i+1}'
                })
            elif table2 is None:
                differences.append({
                    'type': DifferenceType.DELETION.value,
                    'table_index': i,
                    'old_table': table1,
                    'message': f'删除表格 {i+1}'
                })
            else:
                # 对比表格内容
                table_diff = self._compare_single_table(table1, table2, i)
                if table_diff:
                    differences.extend(table_diff)
        
        result.differences = differences
    
    def _compare_single_table(self, table1: Dict, table2: Dict, table_index: int) -> List[Dict]:
        """
        单个表格的详细对比
        """
        differences = []
        
        data1 = table1.get('data', [])
        data2 = table2.get('data', [])
        
        # 检查表格尺寸
        rows1, cols1 = len(data1), len(data1[0]) if data1 else 0
        rows2, cols2 = len(data2), len(data2[0]) if data2 else 0
        
        if rows1 != rows2 or cols1 != cols2:
            differences.append({
                'type': 'table_structure_change',
                'table_index': table_index,
                'old_size': (rows1, cols1),
                'new_size': (rows2, cols2),
                'message': f'表格 {table_index+1} 尺寸从 {rows1}x{cols1} 变为 {rows2}x{cols2}'
            })
        
        # 逐行对比
        max_rows = max(rows1, rows2)
        for row_idx in range(max_rows):
            row1 = data1[row_idx] if row_idx < rows1 else None
            row2 = data2[row_idx] if row_idx < rows2 else None
            
            if row1 is None:
                differences.append({
                    'type': DifferenceType.INSERTION.value,
                    'table_index': table_index,
                    'row_index': row_idx,
                    'new_row': row2,
                    'message': f'表格 {table_index+1} 新增行 {row_idx+1}'
                })
            elif row2 is None:
                differences.append({
                    'type': DifferenceType.DELETION.value,
                    'table_index': table_index,
                    'row_index': row_idx,
                    'old_row': row1,
                    'message': f'表格 {table_index+1} 删除行 {row_idx+1}'
                })
            else:
                # 逐列对比
                max_cols = max(len(row1), len(row2))
                for col_idx in range(max_cols):
                    cell1 = str(row1[col_idx]) if col_idx < len(row1) else ""
                    cell2 = str(row2[col_idx]) if col_idx < len(row2) else ""
                    
                    if cell1 != cell2:
                        differences.append({
                            'type': DifferenceType.REPLACEMENT.value,
                            'table_index': table_index,
                            'row_index': row_idx,
                            'col_index': col_idx,
                            'old_value': cell1,
                            'new_value': cell2,
                            'position': f'表格{table_index+1}[{row_idx+1},{col_idx+1}]'
                        })
        
        return differences
    
    def _compare_visual(self, doc1_data: Dict, doc2_data: Dict, options: ComparisonOptions, result: ComparisonResult):
        """
        PDF视觉对比 - 按about.md建议实现像素级和对象级对比
        """
        # 这个方法需要与DocumentParser的视觉对比功能配合
        # 这里主要处理对比结果的结构化
        differences = []
        
        # 获取页面信息
        pages1 = doc1_data.get('pages', [])
        pages2 = doc2_data.get('pages', [])
        
        max_pages = max(len(pages1), len(pages2))
        
        for page_num in range(max_pages):
            if page_num >= len(pages1):
                differences.append({
                    'type': DifferenceType.INSERTION.value,
                    'page_number': page_num + 1,
                    'message': f'文档2多出页面 {page_num + 1}'
                })
            elif page_num >= len(pages2):
                differences.append({
                    'type': DifferenceType.DELETION.value,
                    'page_number': page_num + 1,
                    'message': f'文档1多出页面 {page_num + 1}'
                })
            else:
                # 这里需要调用DocumentParser的视觉对比功能
                # 或者直接在这里实现简化版的视觉对比
                visual_diff = self._compare_page_visually(pages1[page_num], pages2[page_num])
                if visual_diff:
                    differences.extend(visual_diff)
        
        result.differences = differences
    
    def _compare_page_visually(self, page1_data: Dict, page2_data: Dict) -> List[Dict]:
        """
        单页面视觉对比（简化版）
        """
        differences = []
        
        # 比较文本内容
        text1 = page1_data.get('text', '')
        text2 = page2_data.get('text', '')
        
        if text1 != text2:
            differences.append({
                'type': 'visual_text_difference',
                'page_number': page1_data.get('page_number', 1),
                'old_text_length': len(text1),
                'new_text_length': len(text2),
                'message': f'页面 {page1_data.get("page_number", 1)} 文本内容发生变化'
            })
        
        return differences
    
    def _preprocess_text(self, text: str, options: ComparisonOptions) -> str:
        """
        文本预处理 - 按about.md建议进行规范化
        """
        if not text:
            return ""
        
        # Unicode标准化
        text = unicodedata.normalize('NFKC', text)
        
        # 处理空白字符
        if options.ignore_whitespace:
            text = ' '.join(text.split())
        
        # 大小写处理
        if options.ignore_case:
            text = text.lower()
        
        # 标点符号处理
        if options.ignore_punctuation:
            text = ''.join(ch for ch in text if not unicodedata.category(ch).startswith('P'))
        
        return text
    
    def _convert_dmp_diffs(self, diffs: List[Tuple], text1: str, text2: str) -> List[Dict]:
        """
        转换Google diff-match-patch的差异格式为标准格式
        """
        differences = []
        pos1 = pos2 = 0
        
        for op, text in diffs:
            if op == 0:  # 相同
                pos1 += len(text)
                pos2 += len(text)
            elif op == -1:  # 删除
                differences.append({
                    'type': DifferenceType.DELETION.value,
                    'old_content': text,
                    'position': pos1,
                    'length': len(text)
                })
                pos1 += len(text)
            else:  # 插入
                differences.append({
                    'type': DifferenceType.INSERTION.value,
                    'new_content': text,
                    'position': pos2,
                    'length': len(text)
                })
                pos2 += len(text)
        
        return differences
    
    def _get_context(self, text: str, position: int, length: int, context_size: int = 50) -> Dict[str, str]:
        """
        获取差异的上下文
        """
        start = max(0, position - context_size)
        end = min(len(text), position + length + context_size)
        
        return {
            'before': text[start:position],
            'after': text[position + length:end]
        }
    
    def _generate_statistics(self, differences: List[Dict]) -> Dict[str, Any]:
        """
        生成对比统计信息
        """
        stats = {
            'total_differences': len(differences),
            'insertions': 0,
            'deletions': 0,
            'replacements': 0,
            'table_changes': 0,
            'visual_changes': 0,
            'image_changes': 0,
            'metadata_changes': 0
        }
        for diff in differences:
            diff_type = diff.get('type')
            if diff_type == DifferenceType.INSERTION.value:
                stats['insertions'] += 1
            elif diff_type == DifferenceType.DELETION.value:
                stats['deletions'] += 1
            elif diff_type == DifferenceType.REPLACEMENT.value:
                stats['replacements'] += 1
            elif 'table' in str(diff_type).lower():
                stats['table_changes'] += 1
            elif 'visual' in str(diff_type).lower():
                stats['visual_changes'] += 1
            elif 'image' in str(diff_type).lower():
                stats['image_changes'] += 1
            elif 'metadata' in str(diff_type).lower():
                stats['metadata_changes'] += 1
        return stats
    
    def _generate_summary(self, differences: List[Dict], statistics: Dict) -> Dict[str, Any]:
        """
        生成对比总结
        """
        summary = {
            'total_changes': statistics['total_differences'],
            'change_types': [],
            'significant_changes': [],
            'overview': ''
        }
        
        # 添加变更类型
        if statistics['insertions']:
            summary['change_types'].append(f"新增内容: {statistics['insertions']}处")
        if statistics['deletions']:
            summary['change_types'].append(f"删除内容: {statistics['deletions']}处")
        if statistics['replacements']:
            summary['change_types'].append(f"替换内容: {statistics['replacements']}处")
        if statistics['table_changes']:
            summary['change_types'].append(f"表格变更: {statistics['table_changes']}处")
        if statistics['visual_changes']:
            summary['change_types'].append(f"视觉变更: {statistics['visual_changes']}处")
        
        # 生成重要变更列表
        for diff in differences:
            if diff.get('type') == DifferenceType.REPLACEMENT.value:
                if len(str(diff.get('old_content', ''))) > 100 or len(str(diff.get('new_content', ''))) > 100:
                    summary['significant_changes'].append({
                        'type': '大段内容替换',
                        'position': diff.get('position', 'unknown'),
                        'message': f"在位置 {diff.get('position', 'unknown')} 处发生大段内容变更"
                    })
        
        # 生成总览描述
        summary['overview'] = f"文档共有 {statistics['total_differences']} 处差异，" + \
                            f"包括 {', '.join(summary['change_types'])}。"
        
        return summary
    
    def _generate_metadata(self, doc1_data: Dict, doc2_data: Dict, options: ComparisonOptions) -> Dict[str, Any]:
        """
        生成对比元数据
        """
        return {
            'comparison_time': None,  # 由调用方设置
            'doc1_info': doc1_data.get('file_info', {}),
            'doc2_info': doc2_data.get('file_info', {}),
            'options_used': {
                'ignore_whitespace': options.ignore_whitespace,
                'ignore_case': options.ignore_case,
                'ignore_punctuation': options.ignore_punctuation,
                'detailed_report': options.detailed_report,
                'extract_tables': options.extract_tables,
                'visual_comparison': options.visual_comparison,
                'similarity_threshold': options.similarity_threshold
            }
        } 
    
    def _calculate_and_set_similarity(self, result: ComparisonResult, doc1_data: Dict, doc2_data: Dict, options: ComparisonOptions):
        """
        计算并设置文档相似度
        
        Args:
            result: 对比结果对象
            doc1_data: 文档1数据
            doc2_data: 文档2数据
            options: 对比选项
        """
        try:
            # 根据对比类型计算相似度
            if result.comparison_type == ComparisonType.TEXT:
                similarity = self._calculate_text_similarity(doc1_data, doc2_data, result.differences, options)
            elif result.comparison_type == ComparisonType.STRUCTURED:
                similarity = self._calculate_structured_similarity(doc1_data, doc2_data, result.differences, options)
            elif result.comparison_type == ComparisonType.TABLE:
                similarity = self._calculate_table_similarity(doc1_data, doc2_data, result.differences, options)
            elif result.comparison_type == ComparisonType.VISUAL:
                similarity = self._calculate_visual_similarity(doc1_data, doc2_data, result.differences, options)
            else:
                # 默认文本相似度计算
                similarity = self._calculate_text_similarity(doc1_data, doc2_data, result.differences, options)
            
            # 确保相似度在0-1范围内
            similarity = max(0.0, min(1.0, similarity))
            
            # 设置相似度字段
            result.similarity = similarity
            
            # 生成统计信息
            statistics = self._generate_statistics(result.differences)
            result.statistics = statistics
            
            # 生成总结信息
            summary = self._generate_summary(result.differences, statistics)
            summary['similarity'] = similarity
            summary['similarity_percentage'] = similarity * 100
            result.summary = summary
            
            # 生成元数据
            result.metadata = self._generate_metadata(doc1_data, doc2_data, options)
            
            self.logger.info(f"文档相似度计算完成: {similarity:.2%}")
            
        except Exception as e:
            self.logger.error(f"计算相似度失败: {str(e)}")
            # 设置默认值
            result.similarity = 0.0
            result.summary = {
                'similarity': 0.0,
                'similarity_percentage': 0.0,
                'total_changes': len(result.differences),
                'change_types': [],
                'significant_changes': [],
                'overview': f"文档共有 {len(result.differences)} 处差异，相似度计算失败。"
            }
    
    def _calculate_text_similarity(self, doc1_data: Dict, doc2_data: Dict, differences: List[Dict], options: ComparisonOptions) -> float:
        """
        计算文本相似度
        """
        text1 = doc1_data.get('text', '')
        text2 = doc2_data.get('text', '')
        
        # 如果两个文档都为空，认为完全相似
        if not text1 and not text2:
            return 1.0
        
        # 如果只有一个文档为空，认为完全不相似
        if not text1 or not text2:
            return 0.0
        
        # 文本预处理（与对比时保持一致）
        if options.ignore_whitespace or options.ignore_case or options.ignore_punctuation:
            text1 = self._preprocess_text(text1, options)
            text2 = self._preprocess_text(text2, options)
        
        # 使用difflib计算相似度
        similarity = difflib.SequenceMatcher(None, text1, text2).ratio()
        
        # 根据差异数量调整相似度
        total_length = max(len(text1), len(text2))
        if total_length > 0:
            # 差异惩罚因子：每个差异减少一定相似度
            diff_penalty = len(differences) * 0.01  # 每个差异减少1%
            similarity = max(0.0, similarity - diff_penalty)
        
        return similarity
    
    def _calculate_structured_similarity(self, doc1_data: Dict, doc2_data: Dict, differences: List[Dict], options: ComparisonOptions) -> float:
        """
        计算结构化内容相似度
        """
        content1 = doc1_data.get('structured_content', [])
        content2 = doc2_data.get('structured_content', [])
        
        # 如果两个文档都为空，认为完全相似
        if not content1 and not content2:
            return 1.0
        
        # 如果只有一个文档为空，认为完全不相似
        if not content1 or not content2:
            return 0.0
        
        # 使用difflib计算序列相似度
        def normalize(seq):
            return [json.dumps(item, sort_keys=True, ensure_ascii=False) if isinstance(item, dict) else str(item) for item in seq]
        
        norm_content1 = normalize(content1)
        norm_content2 = normalize(content2)
        
        similarity = difflib.SequenceMatcher(None, norm_content1, norm_content2).ratio()
        
        # 根据差异数量调整相似度
        total_elements = max(len(content1), len(content2))
        if total_elements > 0:
            diff_penalty = len(differences) * 0.02  # 每个差异减少2%
            similarity = max(0.0, similarity - diff_penalty)
        
        return similarity
    
    def _calculate_table_similarity(self, doc1_data: Dict, doc2_data: Dict, differences: List[Dict], options: ComparisonOptions) -> float:
        """
        计算表格相似度
        """
        tables1 = doc1_data.get('tables', [])
        tables2 = doc2_data.get('tables', [])
        
        # 如果两个文档都无表格，认为完全相似
        if not tables1 and not tables2:
            return 1.0
        
        # 如果只有一个文档有表格，认为完全不相似
        if not tables1 or not tables2:
            return 0.0
        
        # 计算表格数量和内容的相似度
        table_count_similarity = 1.0 - abs(len(tables1) - len(tables2)) / max(len(tables1), len(tables2))
        
        # 计算表格内容的相似度
        content_similarity = 0.0
        if tables1 and tables2:
            total_cells = 0
            similar_cells = 0
            
            for i in range(min(len(tables1), len(tables2))):
                table1 = tables1[i]
                table2 = tables2[i]
                
                data1 = table1.get('data', [])
                data2 = table2.get('data', [])
                
                # 计算单元格相似度
                for row_idx in range(min(len(data1), len(data2))):
                    row1 = data1[row_idx]
                    row2 = data2[row_idx]
                    
                    for col_idx in range(min(len(row1), len(row2))):
                        total_cells += 1
                        if str(row1[col_idx]) == str(row2[col_idx]):
                            similar_cells += 1
            
            if total_cells > 0:
                content_similarity = similar_cells / total_cells
        
        # 综合相似度
        similarity = (table_count_similarity + content_similarity) / 2
        
        # 根据差异数量调整相似度
        if len(differences) > 0:
            diff_penalty = len(differences) * 0.015  # 每个差异减少1.5%
            similarity = max(0.0, similarity - diff_penalty)
        
        return similarity
    
    def _calculate_visual_similarity(self, doc1_data: Dict, doc2_data: Dict, differences: List[Dict], options: ComparisonOptions) -> float:
        """
        计算视觉相似度
        """
        pages1 = doc1_data.get('pages', [])
        pages2 = doc2_data.get('pages', [])
        
        # 如果两个文档都无页面，认为完全相似
        if not pages1 and not pages2:
            return 1.0
        
        # 如果只有一个文档有页面，认为完全不相似
        if not pages1 or not pages2:
            return 0.0
        
        # 计算页面数量相似度
        page_count_similarity = 1.0 - abs(len(pages1) - len(pages2)) / max(len(pages1), len(pages2))
        
        # 计算页面内容相似度
        content_similarity = 0.0
        if pages1 and pages2:
            total_pages = min(len(pages1), len(pages2))
            similar_pages = 0
            
            for i in range(total_pages):
                page1 = pages1[i]
                page2 = pages2[i]
                
                text1 = page1.get('text', '')
                text2 = page2.get('text', '')
                
                if text1 == text2:
                    similar_pages += 1
            
            if total_pages > 0:
                content_similarity = similar_pages / total_pages
        
        # 综合相似度
        similarity = (page_count_similarity + content_similarity) / 2
        
        # 根据差异数量调整相似度
        if len(differences) > 0:
            diff_penalty = len(differences) * 0.02  # 每个差异减少2%
            similarity = max(0.0, similarity - diff_penalty)
        
        return similarity 