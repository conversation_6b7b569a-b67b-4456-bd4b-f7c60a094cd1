<template>
  <el-header class="navbar">
    <div class="navbar-brand">
      <router-link to="/" class="brand-link">
        <img src="/logo.png" alt="Logo" class="logo" />
        <span class="brand-text">白泽AI文件助手</span>
      </router-link>
    </div>
    
    <div class="navbar-menu">
      <el-menu
        mode="horizontal"
        :default-active="activeIndex"
        class="navbar-nav"
        @select="handleSelect"
      >
        <el-menu-item index="/">首页</el-menu-item>
        <el-menu-item index="/editor" v-if="authStore.isAuthenticated">证件照制作</el-menu-item>
        <el-menu-item index="/documents" v-if="authStore.isAuthenticated">文档对比</el-menu-item>
        <el-menu-item index="/resumes" v-if="authStore.isAuthenticated">简历管理</el-menu-item>
        <el-menu-item index="/business-cards" v-if="authStore.isAuthenticated">名片管理</el-menu-item>
        <el-menu-item v-if="authStore.user?.username === 'admin'" index="/admin">管理后台</el-menu-item>
        <el-menu-item index="/help">
          使用帮助
        </el-menu-item>
      </el-menu>
    </div>
    
    <div class="navbar-actions">
      <template v-if="authStore.isAuthenticated">
        <el-dropdown @command="handleUserAction">
          <el-button type="link" class="user-dropdown">
            <el-avatar 
              :size="32" 
              :src="authStore.user?.avatar_url ? getImageUrl(authStore.user.avatar_url) : null"
              :icon="!authStore.user?.avatar_url ? 'User' : null"
            >
              {{ authStore.user?.username?.charAt(0).toUpperCase() }}
            </el-avatar>
            <span class="username">{{ authStore.user?.username }}</span>
            <el-icon><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人中心</el-dropdown-item>
              <el-dropdown-item command="credits">积分中心</el-dropdown-item>
              <el-dropdown-item command="orders">我的订单</el-dropdown-item>
              <el-dropdown-item command="settings">设置</el-dropdown-item>
              <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template v-else>
        <el-button @click="$router.push('/login')">登录</el-button>
        <el-button type="primary" @click="$router.push('/register')">注册</el-button>
      </template>
    </div>
  </el-header>
</template>

<script>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ArrowDown } from '@element-plus/icons-vue'
import { getImageUrl } from '@/utils/imageUtils'

export default {
  name: 'NavBar',
  components: {
    ArrowDown
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    
    const activeIndex = computed(() => route.path)
    
    const handleSelect = (index) => {
      if (index !== route.path) {
        // 检查是否需要登录
        const requiresAuth = ['/editor', '/documents', '/resumes', '/business-cards', '/admin'].includes(index)
        if (requiresAuth && !authStore.isAuthenticated) {
          router.push('/login')
          return
        }
        router.push(index)
      }
    }
    
    const handleUserAction = (command) => {
      switch (command) {
        case 'profile':
          router.push('/profile')
          break
        case 'credits':
          router.push('/credits')
          break
        case 'orders':
          router.push('/orders')
          break
        case 'settings':
          router.push('/settings')
          break
        case 'logout':
          authStore.logout()
          router.push('/')
          break
      }
    }
    
    return {
      activeIndex,
      authStore,
      handleSelect,
      handleUserAction,
      getImageUrl
    }
  }
}
</script>

<style scoped>
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: #fff;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  display: flex;
  align-items: center;
}

.brand-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: inherit;
}

.logo {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.brand-text {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
}

.navbar-menu {
  flex: 1;
  margin: 0 40px;
}

.navbar-nav {
  border-bottom: none;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-dropdown {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
}

.username {
  margin-left: 4px;
}
</style>