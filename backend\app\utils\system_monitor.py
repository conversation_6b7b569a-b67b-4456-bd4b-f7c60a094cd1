"""
系统监控工具
"""
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import psutil
import os
import time
import logging
import gc
from functools import wraps

logger = logging.getLogger(__name__)

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.start_time = time.time()
        self.metrics_history = []
        self.max_history_size = 1000
        self.alert_thresholds = {
            'cpu_percent': 80,
            'memory_percent': 80,
            'disk_percent': 90
        }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            cpu = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'system': {
                    'cpu_percent': cpu,
                    'memory_total': memory.total,
                    'memory_used': memory.used,
                    'memory_percent': memory.percent,
                    'disk_total': disk.total,
                    'disk_used': disk.used,
                    'disk_percent': disk.percent
                },
                'process': {
                    'cpu_percent': self.process.cpu_percent(),
                    'memory_rss': self.process.memory_info().rss,
                    'memory_vms': self.process.memory_info().vms,
                    'threads': self.process.num_threads()
                }
            }
            
            # 存储历史数据
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history_size:
                self.metrics_history.pop(0)
            
            return metrics
        except Exception as e:
            logger.error(f"获取系统指标失败: {str(e)}")
            return {}
    
    def get_performance_issues(self) -> List[Dict[str, Any]]:
        """获取性能问题"""
        issues = []
        metrics = self.get_system_metrics()
        
        if not metrics:
            return [{'level': 'error', 'message': '无法获取系统指标'}]
        
        # 检查CPU使用率
        if metrics['system']['cpu_percent'] > self.alert_thresholds['cpu_percent']:
            issues.append({
                'level': 'warning',
                'component': 'cpu',
                'message': f"CPU使用率过高: {metrics['system']['cpu_percent']}%"
            })
        
        # 检查内存使用率
        if metrics['system']['memory_percent'] > self.alert_thresholds['memory_percent']:
            issues.append({
                'level': 'warning',
                'component': 'memory',
                'message': f"内存使用率过高: {metrics['system']['memory_percent']}%"
            })
        
        # 检查磁盘使用率
        if metrics['system']['disk_percent'] > self.alert_thresholds['disk_percent']:
            issues.append({
                'level': 'warning',
                'component': 'disk',
                'message': f"磁盘使用率过高: {metrics['system']['disk_percent']}%"
            })
        
        return issues
    
    def get_metrics_history(self, 
                          time_range: Optional[timedelta] = None) -> List[Dict[str, Any]]:
        """
        获取历史指标
        :param time_range: 时间范围，如果为None则返回所有历史数据
        :return: 历史指标列表
        """
        if not time_range:
            return self.metrics_history
        
        cutoff_time = datetime.now() - time_range
        return [
            metrics for metrics in self.metrics_history
            if datetime.fromisoformat(metrics['timestamp']) > cutoff_time
        ]
    
    def cleanup_old_metrics(self, max_age: timedelta = timedelta(hours=24)):
        """
        清理旧的指标数据
        :param max_age: 最大保留时间
        """
        cutoff_time = datetime.now() - max_age
        self.metrics_history = [
            metrics for metrics in self.metrics_history
            if datetime.fromisoformat(metrics['timestamp']) > cutoff_time
        ]
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        gc.collect()
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        metrics = self.get_system_metrics()
        issues = self.get_performance_issues()
        
        # 确定整体健康状态
        if any(issue['level'] == 'error' for issue in issues):
            status = 'critical'
        elif any(issue['level'] == 'warning' for issue in issues):
            status = 'warning'
        else:
            status = 'healthy'
        
        return {
            'status': status,
            'uptime': time.time() - self.start_time,
            'metrics': metrics,
            'issues': issues
        }

def monitor_performance(context: str = ""):
    """性能监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = psutil.Process(os.getpid()).memory_info().rss
            
            try:
                result = func(*args, **kwargs)
                
                # 计算性能指标
                execution_time = time.time() - start_time
                memory_used = psutil.Process(os.getpid()).memory_info().rss - start_memory
                
                # 记录性能数据
                logger.info(
                    f"性能监控 - {context or func.__name__}:\n"
                    f"执行时间: {execution_time:.2f}秒\n"
                    f"内存使用: {memory_used/1024/1024:.2f}MB"
                )
                
                return result
            except Exception as e:
                logger.error(
                    f"函数执行失败 - {context or func.__name__}:\n"
                    f"错误: {str(e)}"
                )
                raise
        return wrapper
    return decorator

# 创建全局监控实例
system_monitor = None

def init_system_monitor():
    """初始化系统监控"""
    global system_monitor
    if system_monitor is None:
        system_monitor = SystemMonitor()
    return system_monitor

def get_system_monitor() -> SystemMonitor:
    """获取系统监控实例"""
    global system_monitor
    if system_monitor is None:
        system_monitor = init_system_monitor()
    return system_monitor
