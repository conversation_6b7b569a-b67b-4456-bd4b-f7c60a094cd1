import sqlite3
import os

def update_database():
    # 获取数据库文件路径
    db_path = os.path.join('instance', 'photo_maker.db')
    
    # 连接数据库
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 添加style_config列
        cursor.execute('''
        ALTER TABLE business_cards 
        ADD COLUMN style_config TEXT DEFAULT NULL
        ''')
        
        # 提交更改
        conn.commit()
        print("数据库更新成功！")
        
    except sqlite3.OperationalError as e:
        if "duplicate column name" in str(e).lower():
            print("列已存在，跳过添加")
        else:
            print(f"更新失败: {e}")
    
    finally:
        # 关闭连接
        cursor.close()
        conn.close()

if __name__ == '__main__':
    update_database() 