<template>
  <div class="layer-panel">
    <h3>图层管理</h3>
    <ul>
      <li v-for="el in sortedElements" :key="el.id">
        <template v-if="el.type==='group'">
          <span @click="toggleGroup(el.id)">
            {{ expandedGroups.includes(el.id) ? '▼' : '▶' }}
          </span>
          <span @dblclick="editName(el)">{{ el.name || '分组' }}</span>
          <button @click="toggleLock(el)">{{ el.locked ? '解锁' : '锁定' }}</button>
          <button @click="toggleVisible(el)">{{ el.visible ? '隐藏' : '显示' }}</button>
          <button @click="moveUp(el)">上移</button>
          <button @click="moveDown(el)">下移</button>
          <ul v-if="expandedGroups.includes(el.id)">
            <li v-for="child in sortedElements.filter(e=>e.groupId===el.id)" :key="child.id">
              <span @dblclick="editName(child)">{{ child.name || child.type }}</span>
              <button @click="toggleLock(child)">{{ child.locked ? '解锁' : '锁定' }}</button>
              <button @click="toggleVisible(child)">{{ child.visible ? '隐藏' : '显示' }}</button>
              <button @click="moveUp(child)">上移</button>
              <button @click="moveDown(child)">下移</button>
            </li>
          </ul>
        </template>
        <template v-else>
          <span @dblclick="editName(el)">{{ el.name || el.type }}</span>
          <button @click="toggleLock(el)">{{ el.locked ? '解锁' : '锁定' }}</button>
          <button @click="toggleVisible(el)">{{ el.visible ? '隐藏' : '显示' }}</button>
          <button @click="moveUp(el)">上移</button>
          <button @click="moveDown(el)">下移</button>
        </template>
      </li>
    </ul>
  </div>
</template>
<script setup>
import { ref, computed } from 'vue';
const props = defineProps(['elements']);
const emit = defineEmits(['update:elements']);
const expandedGroups = ref([]);
const sortedElements = computed(() => [...props.elements].sort((a, b) => b.zIndex - a.zIndex));
function toggleLock(el) {
  el.locked = !el.locked;
  emit('update:elements', props.elements);
}
function toggleVisible(el) {
  el.visible = !el.visible;
  emit('update:elements', props.elements);
}
function moveUp(el) {
  el.zIndex++;
  emit('update:elements', props.elements);
}
function moveDown(el) {
  if (el.zIndex > 0) el.zIndex--;
  emit('update:elements', props.elements);
}
function editName(el) {
  const name = prompt('重命名图层', el.name || el.type);
  if (name !== null) {
    el.name = name;
    emit('update:elements', props.elements);
  }
}
function toggleGroup(groupId) {
  if (expandedGroups.value.includes(groupId)) {
    expandedGroups.value = expandedGroups.value.filter(id => id !== groupId);
  } else {
    expandedGroups.value.push(groupId);
  }
}
</script>
<style scoped>
.layer-panel { width: 200px; background: #f7f7f7; border: 1px solid #ddd; padding: 8px; }
.layer-panel ul { list-style: none; padding: 0; }
.layer-panel li { display: flex; align-items: center; gap: 4px; margin-bottom: 4px; }
</style> 