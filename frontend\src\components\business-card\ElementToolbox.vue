<template>
  <div class="toolbox">
    <el-button type="primary" size="small" @click="addText">添加文本</el-button>
    <el-button size="small" @click="addImage">添加图片</el-button>
    <el-button size="small" @click="addQRCode">添加二维码</el-button>
  </div>
</template>
<script setup>
import { defineEmits } from 'vue'
const emit = defineEmits(['add'])
const addText = () => {
  emit('add', {
    type: 'text',
    id: Date.now() + '-' + Math.random(),
    config: {
      text: '新文本', x: 100, y: 100, fontSize: 24, fill: '#222', fontFamily: '', zIndex: 1
    }
  })
}
const addImage = () => {
  emit('add', {
    type: 'image',
    id: Date.now() + '-' + Math.random(),
    config: {
      src: '', x: 200, y: 100, width: 120, height: 80, zIndex: 1
    }
  })
}
const addQRCode = () => {
  emit('add', {
    type: 'qrcode',
    id: Date.now() + '-' + Math.random(),
    config: {
      text: 'https://', x: 300, y: 100, size: 80, zIndex: 1
    }
  })
}
</script>
<style scoped>
.toolbox {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 18px;
}
</style> 