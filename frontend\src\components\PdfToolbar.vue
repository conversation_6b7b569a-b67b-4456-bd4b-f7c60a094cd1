<template>
  <nav class="toolbar">
    <div class="toolbar-group">
      <button class="toolbar-btn" @click="$emit('toggle-sidebar')" :title="'显示/隐藏缩略图栏'">
        <svg width="20" height="20" viewBox="0 0 24 24"><rect x="3" y="4" width="4" height="16" rx="1" fill="#3182ce"/><rect x="9" y="4" width="12" height="16" rx="2" fill="#fff" stroke="#3182ce"/></svg>
      </button>
      <label class="upload-label" title="上传PDF">
        <input type="file" accept="application/pdf" style="display:none" @change="onUpload" />
        <span class="toolbar-btn">
          <svg width="20" height="20" viewBox="0 0 24 24"><path d="M12 16V4M8 8l4-4 4 4" stroke="#3182ce" stroke-width="2" fill="none"/><rect x="4" y="16" width="16" height="4" rx="2" fill="#3182ce"/></svg>
        </span>
      </label>
    </div>
    <div class="toolbar-group">
      <button class="toolbar-btn" @click="$emit('zoom-out')" :title="'缩小'">
        <svg width="20" height="20" viewBox="0 0 24 24"><rect x="5" y="11" width="14" height="2" rx="1" fill="#3182ce"/></svg>
      </button>
      <span class="toolbar-label">{{ zoom }}%</span>
      <button class="toolbar-btn" @click="$emit('zoom-in')" :title="'放大'">
        <svg width="20" height="20" viewBox="0 0 24 24"><rect x="5" y="11" width="14" height="2" rx="1" fill="#3182ce"/><rect x="11" y="5" width="2" height="14" rx="1" fill="#3182ce"/></svg>
      </button>
    </div>
    <div class="toolbar-group">
      <button class="toolbar-btn" :class="{active: annotateMode==='pen'}" @click="$emit('annotate', 'pen')" :disabled="!canAnnotate" :title="'画笔批注'">
        <svg width="20" height="20" viewBox="0 0 24 24"><path d="M3 17.25V21h3.75l11.06-11.06-3.75-3.75L3 17.25z" fill="#3182ce"/></svg>
      </button>
      <button class="toolbar-btn" :class="{active: annotateMode==='highlight'}" @click="$emit('annotate', 'highlight')" :disabled="!canAnnotate" :title="'高亮批注'">
        <svg width="20" height="20" viewBox="0 0 24 24"><rect x="4" y="17" width="16" height="2" rx="1" fill="#3182ce"/><rect x="7" y="7" width="10" height="6" rx="2" fill="#3182ce" opacity=".3"/></svg>
      </button>
      <button class="toolbar-btn" :class="{active: annotateMode==='text'}" @click="$emit('annotate', 'text')" :disabled="!canAnnotate" :title="'文本批注'">
        <svg width="20" height="20" viewBox="0 0 24 24"><text x="4" y="18" font-size="16" fill="#3182ce">T</text></svg>
      </button>
    </div>
    <div class="toolbar-group">
      <button class="toolbar-btn" @click="$emit('delete-page')" :disabled="!canDeletePage" :title="'删除当前页'">
        <svg width="20" height="20" viewBox="0 0 24 24"><path d="M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="#3182ce"/></svg>
      </button>
      <button class="toolbar-btn" @click="$emit('export')" :disabled="!canExport" :title="'导出PDF'">
        <svg width="20" height="20" viewBox="0 0 24 24"><path d="M5 20h14v-2H5v2zm7-18C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 14.5h-2v-6h2v6zm0-8h-2V7h2v1.5z" fill="#3182ce"/></svg>
      </button>
    </div>
    <div class="toolbar-group toolbar-group-right">
      <span class="toolbar-label">第 {{ page }}/{{ pageCount }} 页</span>
    </div>
  </nav>
</template>
<script setup>
const props = defineProps({
  canAnnotate: Boolean,
  canDeletePage: Boolean,
  canExport: Boolean,
  zoom: Number,
  page: Number,
  pageCount: Number,
  annotateMode: String
})
function onUpload(e) {
  const file = e.target.files[0];
  if (file) {
    const reader = new FileReader();
    reader.onload = async (evt) => {
      const arrayBuffer = evt.target.result;
      // 生成缩略图等由父组件处理
      const pdfjsLib = await import('pdfjs-dist');
      pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
      let pages = [];
      for (let i = 1; i <= pdf.numPages; i++) {
        const pdfPage = await pdf.getPage(i);
        const viewport = pdfPage.getViewport({ scale: 0.2 });
        const canvas = document.createElement('canvas');
        canvas.width = viewport.width;
        canvas.height = viewport.height;
        const ctx = canvas.getContext('2d');
        await pdfPage.render({ canvasContext: ctx, viewport }).promise;
        const previewUrl = canvas.toDataURL();
        pages.push({ id: `${file.name}_p${i}_${Date.now()}`, previewUrl });
      }
      props.$emit && props.$emit('file-loaded', {
        name: file.name,
        arrayBuffer,
        pages
      });
    };
    reader.readAsArrayBuffer(file);
  }
}
</script>
<style scoped>
.toolbar {
  display: flex;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  padding: 0 1rem;
  min-height: 52px;
  gap: 0.5rem;
  font-size: 15px;
  z-index: 10;
}
.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-right: 1.5rem;
}
.toolbar-group-right {
  margin-left: auto;
}
.toolbar-btn {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  border-radius: 6px;
  padding: 0.4em 0.7em;
  color: #3182ce;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  font-size: 15px;
  position: relative;
}
.toolbar-btn.active, .toolbar-btn:active {
  background: #e3eaff;
  color: #2563eb;
}
.toolbar-btn:disabled {
  color: #bbb;
  background: #f5f5f5;
  cursor: not-allowed;
}
.toolbar-btn:hover:not(:disabled) {
  background: #f0f6ff;
  color: #2563eb;
}
.toolbar-label {
  margin: 0 0.5em;
  color: #888;
  font-size: 14px;
}
.upload-label {
  cursor: pointer;
}
</style> 