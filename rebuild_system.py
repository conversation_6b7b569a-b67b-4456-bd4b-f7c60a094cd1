#!/usr/bin/env python3
"""
智能证件照系统重构自动化脚本
自动完成数据库重建、代码重构、依赖安装等所有工作
"""

import os
import sys
import shutil
import subprocess
import json
from pathlib import Path

class SystemRebuilder:
    def __init__(self):
        self.project_root = Path.cwd()
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        
    def run(self):
        """执行完整的系统重构"""
        print("🚀 开始智能证件照系统重构...")
        
        try:
            self.step1_backup_existing()
            self.step2_clean_old_files()
            self.step3_create_database_files()
            self.step4_create_backend_structure()
            self.step5_create_models()
            self.step6_create_api_routes()
            self.step7_create_config_files()
            self.step8_create_init_scripts()
            self.step9_install_dependencies()
            self.step10_initialize_database()
            self.step11_create_startup_scripts()
            
            print("✅ 系统重构完成!")
            print("🌐 启动命令: python run_system.py")
            
        except Exception as e:
            print(f"❌ 重构失败: {e}")
            sys.exit(1)
    
    def step1_backup_existing(self):
        """备份现有文件"""
        print("📦 备份现有文件...")
        backup_dir = self.project_root / "backup"
        if backup_dir.exists():
            shutil.rmtree(backup_dir)
        backup_dir.mkdir()
        
        # 备份重要配置文件
        important_files = [
            "backend/app/config.py",
            "backend/.env",
            "frontend/.env"
        ]
        
        for file_path in important_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                backup_path = backup_dir / file_path
                backup_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(full_path, backup_path)
        
        print("✅ 备份完成")
    
    def step2_clean_old_files(self):
        """清理旧文件"""
        print("🧹 清理旧文件...")
        
        # 删除旧的数据库文件
        db_files = list(self.backend_dir.glob("*.db"))
        for db_file in db_files:
            db_file.unlink()
        
        # 清理Python缓存
        for cache_dir in self.backend_dir.rglob("__pycache__"):
            shutil.rmtree(cache_dir)
        
        print("✅ 清理完成")
    
    def step3_create_database_files(self):
        """创建数据库文件"""
        print("🗄️ 创建数据库结构...")
        
        db_dir = self.backend_dir / "database"
        db_dir.mkdir(exist_ok=True)
        
        # 创建数据库架构
        schema_sql = """-- 智能证件照系统数据库架构
CREATE DATABASE IF NOT EXISTS intelligent_id_photo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE intelligent_id_photo;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(80) UNIQUE NOT NULL,
    email VARCHAR(120) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_admin BOOLEAN DEFAULT FALSE,
    credits INT DEFAULT 100,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 证件照模板表
CREATE TABLE IF NOT EXISTS photo_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    width INT NOT NULL,
    height INT NOT NULL,
    width_mm DECIMAL(5,2) NOT NULL,
    height_mm DECIMAL(5,2) NOT NULL,
    background_color VARCHAR(7) DEFAULT '#ffffff',
    description TEXT,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 名片模板表
CREATE TABLE IF NOT EXISTS business_card_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'business',
    template_html TEXT NOT NULL,
    template_css TEXT NOT NULL,
    layout_type VARCHAR(20) DEFAULT 'horizontal',
    width_mm DECIMAL(5,2) DEFAULT 90.0,
    height_mm DECIMAL(5,2) DEFAULT 54.0,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户名片表
CREATE TABLE IF NOT EXISTS business_cards (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    template_id INT,
    title VARCHAR(200) NOT NULL,
    card_data JSON,
    status ENUM('draft', 'published') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES business_card_templates(id) ON DELETE SET NULL
);

-- 简历模板表
CREATE TABLE IF NOT EXISTS resume_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    category VARCHAR(50) DEFAULT 'professional',
    template_html TEXT NOT NULL,
    template_css TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户简历表
CREATE TABLE IF NOT EXISTS resumes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    template_id INT,
    title VARCHAR(200) NOT NULL,
    resume_data JSON,
    status ENUM('draft', 'published') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES resume_templates(id) ON DELETE SET NULL
);

-- 文档对比任务表
CREATE TABLE IF NOT EXISTS document_comparisons (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    original_file VARCHAR(500) NOT NULL,
    modified_file VARCHAR(500) NOT NULL,
    result_file VARCHAR(500),
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
"""
        
        with open(db_dir / "schema.sql", "w", encoding="utf-8") as f:
            f.write(schema_sql)
        
        # 创建初始数据
        init_data_sql = """-- 初始化数据
USE intelligent_id_photo;

-- 插入证件照模板
INSERT IGNORE INTO photo_templates (name, category, width, height, width_mm, height_mm, description, sort_order) VALUES
('一寸照', '常用证件照', 295, 413, 25, 35, '标准一寸证件照，适用于简历、学生证等', 1),
('二寸照', '常用证件照', 413, 531, 35, 45, '标准二寸证件照，适用于毕业证、工作证等', 2),
('小二寸照', '常用证件照', 413, 472, 35, 40, '小二寸证件照，适用于护照、签证等', 3),
('护照照片', '出国证件', 354, 472, 30, 40, '中国护照专用照片规格', 4),
('美国签证照', '出国证件', 600, 600, 51, 51, '美国签证申请专用照片', 5),
('驾驶证照片', '证件照', 260, 378, 22, 32, '驾驶证申请照片规格', 6);

-- 插入名片模板
INSERT IGNORE INTO business_card_templates (name, description, category, template_html, template_css, sort_order) VALUES
('经典商务', '传统商务风格名片', 'business', 
'<div class="card"><div class="content"><h1>{{name}}</h1><p>{{title}}</p><p>{{company}}</p><div class="contact"><p>{{phone}}</p><p>{{email}}</p></div></div></div>',
'.card{width:90mm;height:54mm;background:#fff;border:1px solid #ddd;padding:10mm;box-sizing:border-box}.content h1{font-size:18px;margin:0 0 5px}.content p{margin:2px 0;font-size:12px}',
1),
('现代简约', '简约现代风格名片', 'modern',
'<div class="card modern"><div class="header"><h1>{{name}}</h1><p>{{title}}</p></div><div class="info"><p>{{company}}</p><p>{{phone}}</p><p>{{email}}</p></div></div>',
'.card.modern{width:90mm;height:54mm;background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);color:#fff;padding:8mm}.header h1{font-size:16px;margin:0}.info{margin-top:8mm}',
2);

-- 插入简历模板
INSERT IGNORE INTO resume_templates (name, description, category, template_html, template_css, sort_order) VALUES
('专业简历', '适合求职的专业简历模板', 'professional',
'<div class="resume"><header><h1>{{name}}</h1><p>{{title}}</p></header><section class="contact">{{contact_info}}</section><section class="experience">{{work_experience}}</section></div>',
'.resume{max-width:210mm;margin:0 auto;padding:20mm;font-family:Arial}header h1{font-size:24px;margin:0}',
1);
"""
        
        with open(db_dir / "init_data.sql", "w", encoding="utf-8") as f:
            f.write(init_data_sql)
        
        print("✅ 数据库文件创建完成")
    
    def step4_create_backend_structure(self):
        """创建后端目录结构"""
        print("🏗️ 创建后端结构...")
        
        # 创建目录结构
        dirs = [
            "app/api",
            "app/models",
            "app/utils",
            "app/services",
            "uploads/photos",
            "uploads/documents",
            "uploads/business_cards",
            "uploads/resumes",
            "logs"
        ]
        
        for dir_path in dirs:
            (self.backend_dir / dir_path).mkdir(parents=True, exist_ok=True)
        
        # 创建__init__.py文件
        init_files = [
            "app/__init__.py",
            "app/api/__init__.py",
            "app/models/__init__.py",
            "app/utils/__init__.py",
            "app/services/__init__.py"
        ]
        
        for init_file in init_files:
            (self.backend_dir / init_file).touch()
        
        print("✅ 后端结构创建完成")
    
    def step5_create_models(self):
        """创建数据模型"""
        print("📊 创建数据模型...")
        
        # 用户模型
        user_model = '''from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

db = SQLAlchemy()

class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    is_admin = db.Column(db.Boolean, default=False)
    credits = db.Column(db.Integer, default=100)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_admin': self.is_admin,
            'credits': self.credits,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class PhotoTemplate(db.Model):
    __tablename__ = 'photo_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    category = db.Column(db.String(50), nullable=False)
    width = db.Column(db.Integer, nullable=False)
    height = db.Column(db.Integer, nullable=False)
    width_mm = db.Column(db.Numeric(5,2), nullable=False)
    height_mm = db.Column(db.Numeric(5,2), nullable=False)
    background_color = db.Column(db.String(7), default='#ffffff')
    description = db.Column(db.Text)
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'width': self.width,
            'height': self.height,
            'width_mm': float(self.width_mm),
            'height_mm': float(self.height_mm),
            'background_color': self.background_color,
            'description': self.description
        }
'''
        
        with open(self.backend_dir / "app/models/user.py", "w", encoding="utf-8") as f:
            f.write(user_model)
        
        print("✅ 数据模型创建完成")
    
    def step6_create_api_routes(self):
        """创建API路由"""
        print("🛣️ 创建API路由...")
        
        # API蓝图初始化
        api_init = '''from flask import Blueprint

api_bp = Blueprint('api', __name__)

from . import auth, users, photos, business_cards, resumes, documents
'''
        
        with open(self.backend_dir / "app/api/__init__.py", "w", encoding="utf-8") as f:
            f.write(api_init)
        
        # 认证路由
        auth_routes = '''from flask import request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from app.models.user import User, db
from . import api_bp

@api_bp.route('/auth/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    user = User.query.filter_by(username=username).first()
    
    if user and user.check_password(password):
        access_token = create_access_token(identity=user.id)
        return jsonify({
            'success': True,
            'access_token': access_token,
            'user': user.to_dict()
        })
    
    return jsonify({'success': False, 'message': '用户名或密码错误'}), 401

@api_bp.route('/auth/register', methods=['POST'])
def register():
    data = request.get_json()
    username = data.get('username')
    email = data.get('email')
    password = data.get('password')
    
    if User.query.filter_by(username=username).first():
        return jsonify({'success': False, 'message': '用户名已存在'}), 400
    
    if User.query.filter_by(email=email).first():
        return jsonify({'success': False, 'message': '邮箱已存在'}), 400
    
    user = User(username=username, email=email)
    user.set_password(password)
    
    db.session.add(user)
    db.session.commit()
    
    return jsonify({'success': True, 'message': '注册成功'})

@api_bp.route('/auth/profile', methods=['GET'])
@jwt_required()
def profile():
    user_id = get_jwt_identity()
    user = User.query.get(user_id)
    return jsonify({'success': True, 'user': user.to_dict()})
'''
        
        with open(self.backend_dir / "app/api/auth.py", "w", encoding="utf-8") as f:
            f.write(auth_routes)
        
        print("✅ API路由创建完成")
    
    def step7_create_config_files(self):
        """创建配置文件"""
        print("⚙️ 创建配置文件...")
        
        # Flask应用配置
        app_init = '''from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import JWTManager
from flask_cors import CORS
from config import Config

def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)
    
    # 初始化扩展
    from app.models.user import db
    db.init_app(app)
    
    jwt = JWTManager(app)
    CORS(app, origins=["http://localhost:3000"])
    
    # 注册蓝图
    from app.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')
    
    return app
'''
        
        with open(self.backend_dir / "app/__init__.py", "w", encoding="utf-8") as f:
            f.write(app_init)
        
        # 配置文件
        config_py = '''import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # 数据库配置 - 使用SQLite简化部署
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \\
        'sqlite:///' + os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # JWT配置
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or 'jwt-secret-string'
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    
    # 文件上传配置
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'pdf', 'docx', 'doc'}
'''
        
        with open(self.backend_dir / "config.py", "w", encoding="utf-8") as f:
            f.write(config_py)
        
        # requirements.txt
        requirements = '''Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-JWT-Extended==4.5.2
Flask-CORS==4.0.0
opencv-python==********
Pillow==10.0.1
numpy==1.24.4
python-dotenv==1.0.0
requests==2.31.0
qrcode[pil]==7.4.2
reportlab==4.0.4
python-docx==0.8.11
PyMuPDF==1.23.8
'''
        
        with open(self.backend_dir / "requirements.txt", "w", encoding="utf-8") as f:
            f.write(requirements)
        
        print("✅ 配置文件创建完成")
    
    def step8_create_init_scripts(self):
        """创建初始化脚本"""
        print("🔧 创建初始化脚本...")
        
        # 数据库初始化脚本
        init_db_py = '''#!/usr/bin/env python3
import os
from app import create_app
from app.models.user import db, User, PhotoTemplate

def init_database():
    """初始化数据库"""
    app = create_app()
    
    with app.app_context():
        # 创建所有表
        db.create_all()
        print("✅ 数据库表创建完成")
        
        # 创建管理员用户
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                is_admin=True,
                credits=9999
            )
            admin.set_password('admin123')
            db.session.add(admin)
            print("✅ 管理员用户创建完成: admin/admin123")
        
        # 创建证件照模板
        if PhotoTemplate.query.count() == 0:
            templates = [
                {'name': '一寸照', 'category': '常用证件照', 'width': 295, 'height': 413, 'width_mm': 25, 'height_mm': 35, 'description': '标准一寸证件照'},
                {'name': '二寸照', 'category': '常用证件照', 'width': 413, 'height': 531, 'width_mm': 35, 'height_mm': 45, 'description': '标准二寸证件照'},
                {'name': '护照照片', 'category': '出国证件', 'width': 354, 'height': 472, 'width_mm': 30, 'height_mm': 40, 'description': '护照专用照片'},
            ]
            
            for template_data in templates:
                template = PhotoTemplate(**template_data)
                db.session.add(template)
            
            print("✅ 证件照模板创建完成")
        
        db.session.commit()
        print("✅ 数据库初始化完成!")

if __name__ == '__main__':
    init_database()
'''
        
        with open(self.backend_dir / "init_db.py", "w", encoding="utf-8") as f:
            f.write(init_db_py)
        
        # 运行脚本
        run_py = '''#!/usr/bin/env python3
import os
from app import create_app

app = create_app()

if __name__ == '__main__':
    # 确保上传目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    print("🚀 智能证件照系统启动中...")
    print("🌐 访问地址: http://localhost:5000")
    print("👤 管理员账号: admin/admin123")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
'''
        
        with open(self.backend_dir / "run.py", "w", encoding="utf-8") as f:
            f.write(run_py)
        
        print("✅ 初始化脚本创建完成")
    
    def step9_install_dependencies(self):
        """安装依赖"""
        print("📦 安装Python依赖...")
        
        os.chdir(self.backend_dir)
        
        # 创建虚拟环境
        if not (self.backend_dir / "venv").exists():
            subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
        
        # 激活虚拟环境并安装依赖
        if os.name == 'nt':  # Windows
            pip_path = self.backend_dir / "venv/Scripts/pip.exe"
        else:  # Unix/Linux
            pip_path = self.backend_dir / "venv/bin/pip"
        
        subprocess.run([str(pip_path), "install", "-r", "requirements.txt"], check=True)
        
        print("✅ 依赖安装完成")
    
    def step10_initialize_database(self):
        """初始化数据库"""
        print("🗄️ 初始化数据库...")
        
        os.chdir(self.backend_dir)
        
        if os.name == 'nt':  # Windows
            python_path = self.backend_dir / "venv/Scripts/python.exe"
        else:  # Unix/Linux
            python_path = self.backend_dir / "venv/bin/python"
        
        subprocess.run([str(python_path), "init_db.py"], check=True)
        
        print("✅ 数据库初始化完成")
    
    def step11_create_startup_scripts(self):
        """创建启动脚本"""
        print("🚀 创建启动脚本...")
        
        # Windows启动脚本
        start_bat = '''@echo off
chcp 65001 >nul
echo 🚀 启动智能证件照系统...

cd backend
call venv\\Scripts\\activate.bat
python run.py
pause
'''
        
        with open(self.project_root / "start.bat", "w", encoding="utf-8") as f:
            f.write(start_bat)
        
        # Linux/Mac启动脚本
        start_sh = '''#!/bin/bash
echo "🚀 启动智能证件照系统..."

cd backend
source venv/bin/activate
python run.py
'''
        
        with open(self.project_root / "start.sh", "w", encoding="utf-8") as f:
            f.write(start_sh)
        
        # 设置执行权限
        if os.name != 'nt':
            os.chmod(self.project_root / "start.sh", 0o755)
        
        # 统一启动脚本
        run_system_py = '''#!/usr/bin/env python3
"""
智能证件照系统启动脚本
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    project_root = Path(__file__).parent
    backend_dir = project_root / "backend"
    
    print("🚀 启动智能证件照系统...")
    
    # 检查后端目录
    if not backend_dir.exists():
        print("❌ 后端目录不存在，请先运行 python rebuild_system.py")
        sys.exit(1)
    
    # 切换到后端目录
    os.chdir(backend_dir)
    
    # 确定Python路径
    if os.name == 'nt':  # Windows
        python_path = backend_dir / "venv/Scripts/python.exe"
    else:  # Unix/Linux
        python_path = backend_dir / "venv/bin/python"
    
    if not python_path.exists():
        print("❌ 虚拟环境不存在，请先运行 python rebuild_system.py")
        sys.exit(1)
    
    # 启动应用
    try:
        subprocess.run([str(python_path), "run.py"], check=True)
    except KeyboardInterrupt:
        print("\\n👋 系统已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
'''
        
        with open(self.project_root / "run_system.py", "w", encoding="utf-8") as f:
            f.write(run_system_py)
        
        print("✅ 启动脚本创建完成")

if __name__ == '__main__':
    rebuilder = SystemRebuilder()
    rebuilder.run()