from flask import request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
import os
import uuid
import numpy as np
import cv2
import math
import time
from PIL import Image as PILImage, ImageFilter, ImageEnhance, ImageDraw
from datetime import datetime
from scipy import ndimage
import json
import gc

from . import api_bp
from .errors import bad_request, unauthorized, not_found, validation_error
from app import db
from app.models import User, Image, ProcessingTask
from app.utils.memory_monitor import log_memory_usage, monitor_memory, force_memory_cleanup

# 超时优化导入
try:
    from ..utils.ultra_fast_processor import (
        timeout_protected_process, 
        ultra_fast_background_replace_v2,
        process_image_ultra_fast
    )
    ULTRA_FAST_AVAILABLE = True
except ImportError:
    ULTRA_FAST_AVAILABLE = False

# 精准背景替换导入
try:
    from ..utils.precision_rembg_processor import precision_background_replace
    PRECISION_REMBG_AVAILABLE = True
except ImportError:
    PRECISION_REMBG_AVAILABLE = False

# 超精准人体轮廓识别导入
try:
    from ..utils.ultra_precision_segmentation import ultra_precise_background_replace
    ULTRA_PRECISION_AVAILABLE = True
except ImportError:
    ULTRA_PRECISION_AVAILABLE = False

# 增强背景处理器导入
try:
    from ..utils.enhanced_background_processor import enhanced_background_replace
    ENHANCED_PROCESSOR_AVAILABLE = True
except ImportError:
    ENHANCED_PROCESSOR_AVAILABLE = False


@api_bp.route('/processing/tasks', methods=['POST'])
@jwt_required()
def create_processing_task():
    """创建处理任务"""
    current_user_id = int(get_jwt_identity())
    user = User.query.get(current_user_id)
    
    if not user:
        return unauthorized('用户不存在')
    
    data = request.get_json()
    if not data:
        return bad_request('请求数据为空')
    
    image_id = data.get('image_id')
    operations = data.get('operations', [])
    
    if not image_id or not operations:
        return bad_request('请提供图像ID和处理操作')
    
    # 验证图像是否属于当前用户
    image = Image.query.filter_by(
        id=image_id,
        user_id=current_user_id,
        status=1
    ).first()
    
    if not image:
        return not_found('图像不存在')
    
    # 检查用户是否可以处理图像（仅检查用户状态，不检查使用次数）
    can_process, message = user.can_process_image()
    if not can_process:
        return validation_error(message)
    
    try:
        # 创建处理任务
        task = ProcessingTask(
            user_id=current_user_id,
            image_id=image_id,
            task_type=data.get('task_type', 'general'),
            operations={'operations': operations}
        )
        
        # 计算积分消耗
        task.credits_cost = task.get_credits_cost()
        
        # 检查并扣除积分
        if not user.consume_credits(task.credits_cost, '证件照处理'):
            return validation_error('积分不足')
        
        db.session.add(task)
        db.session.flush()  # 获取任务ID
        
        # 简化的同步处理逻辑
        try:
            processed_image_path = process_image_simple(image, operations)
            if processed_image_path:
                task.result_files = [processed_image_path]
                task.result_data = {
                    'processed_image_url': f'http://localhost:5000/static/{processed_image_path}',
                    'original_image_id': image_id,
                    'template_info': operations[0].get('params', {}) if operations else {}
                }
                task.status = 'completed'
                task.completed_at = datetime.utcnow()
                task.progress = 100
            else:
                task.status = 'failed'
                task.error_message = '图片处理失败'
        except Exception as e:
            current_app.logger.error(f"图片处理失败: {e}")
            task.status = 'failed'
            task.error_message = str(e)
        
        db.session.commit()
        
        return jsonify({
            'message': '任务创建成功' if task.status == 'completed' else '任务处理失败',
            'task': task.to_dict(include_details=True)
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建处理任务失败: {e}")
        return validation_error('任务创建失败，请重试')

@api_bp.route('/processing/tasks', methods=['GET'])
@jwt_required()
def get_processing_tasks():
    """获取处理任务列表"""
    current_user_id = int(get_jwt_identity())
    
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 10, type=int), 100)
    status = request.args.get('status')
    
    query = ProcessingTask.query.filter_by(user_id=current_user_id)
    
    if status:
        query = query.filter_by(status=status)
    
    tasks = query.order_by(ProcessingTask.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return jsonify({
        'tasks': [task.to_dict() for task in tasks.items],
        'pagination': {
            'page': page,
            'pages': tasks.pages,
            'per_page': per_page,
            'total': tasks.total
        }
    })

@api_bp.route('/processing/tasks/<task_id>', methods=['GET'])
@jwt_required()
def get_processing_task(task_id):
    """获取处理任务详情"""
    current_user_id = int(get_jwt_identity())
    
    task = ProcessingTask.query.filter_by(
        id=task_id,
        user_id=current_user_id
    ).first()
    
    if not task:
        return not_found('任务不存在')
    
    return jsonify({
        'task': task.to_dict(include_details=True)
    })

@api_bp.route('/processing/tasks/<task_id>/cancel', methods=['POST'])
@jwt_required()
def cancel_processing_task(task_id):
    """取消处理任务"""
    current_user_id = int(get_jwt_identity())
    
    task = ProcessingTask.query.filter_by(
        id=task_id,
        user_id=current_user_id
    ).first()
    
    if not task:
        return not_found('任务不存在')
    
    if task.status not in ['pending', 'processing']:
        return bad_request('任务无法取消')
    
    try:
        task.status = 'cancelled'
        task.completed_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'message': '任务已取消',
            'task': task.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"取消任务失败: {e}")
        return validation_error('取消任务失败，请重试')

@api_bp.route('/processing/image/<int:image_id>/info', methods=['GET'])
@jwt_required()
def get_image_info(image_id):
    """获取图像信息"""
    current_user_id = int(get_jwt_identity())
    
    image = Image.query.filter_by(
        id=image_id,
        user_id=current_user_id,
        status=1
    ).first()
    
    if not image:
        return not_found('图像不存在')
    
    try:
        # 获取图像基本信息
        image_path = image.get_full_path()
        if not os.path.exists(image_path):
            return not_found('图像文件不存在')
        
        with PILImage.open(image_path) as img:
            width, height = img.size
            mode = img.mode
            format_name = img.format
        
        file_size = os.path.getsize(image_path)
        
        return jsonify({
            'image': {
                'id': image.id,
                'filename': image.filename,
                'width': width,
                'height': height,
                'mode': mode,
                'format': format_name,
                'file_size': file_size,
                'created_at': image.created_at.isoformat(),
                'url': f'http://localhost:5000/static/{image.file_path}'
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取图像信息失败: {e}")
        return validation_error('获取图像信息失败')

@monitor_memory("图片处理")
def process_image_simple(image, operations):
    """简化的图片处理逻辑"""
    try:
        # 记录开始处理
        log_memory_usage("图片处理开始")
        
        # 获取原始图片路径
        original_path = image.get_full_path()
        if not os.path.exists(original_path):
            raise Exception("原始图片文件不存在")
        
        # 打开图片
        with PILImage.open(original_path) as img:
            # 转换为RGB模式
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 处理操作
            for operation in operations:
                op_type = operation.get('type')
                params = operation.get('params', {})
                
                if op_type == 'crop':
                    # 简单的裁剪到标准证件照比例
                    img = crop_to_id_photo(img, params)
                elif op_type == 'background_replace':
                    # 简单的背景替换
                    img = replace_background_simple(img, params)
                elif op_type == 'beautify':
                    # 检查是否启用高级美颜
                    if params.get('advanced', False):
                        # 高级美颜处理
                        img = beautify_advanced(img, params)
                    else:
                        # 简单的美颜处理
                        img = beautify_simple(img, params)
            
            # 生成输出文件名
            output_filename = f"processed_{uuid.uuid4().hex}.jpg"
            
            # 使用static文件夹而不是uploads文件夹
            static_folder = os.path.join(os.path.dirname(__file__), '..', '..', 'static')
            processed_folder = os.path.join(static_folder, 'processed', str(image.user_id))
            os.makedirs(processed_folder, exist_ok=True)
            
            output_path = os.path.join(processed_folder, output_filename)
            
            # 保存处理后的图片
            img.save(output_path, 'JPEG', quality=95)
            
            # 返回相对路径
            return f"processed/{image.user_id}/{output_filename}"
            
    except Exception as e:
        current_app.logger.error(f"图片处理失败: {e}")
        raise
    finally:
        # 清理内存
        if 'img' in locals():
            del img
        force_memory_cleanup()
        log_memory_usage("图片处理结束")
        return None

def crop_to_id_photo(img, params):
    """证件照裁剪 - 智能头像比例控制"""
    width, height = img.size
    
    # 获取目标尺寸参数
    target_width = params.get('target_width', 295)
    target_height = params.get('target_height', 413)
    target_ratio = target_width / target_height
    
    # 检查是否有手动裁剪参数
    manual_crop = params.get('manual_crop')
    if manual_crop:
        # 手动裁剪模式
        x = int(manual_crop.get('x', 0))
        y = int(manual_crop.get('y', 0))
        crop_width = int(manual_crop.get('width', width))
        crop_height = int(manual_crop.get('height', height))
        
        # 确保裁剪区域在图片范围内
        x = max(0, min(x, width))
        y = max(0, min(y, height))
        crop_width = min(crop_width, width - x)
        crop_height = min(crop_height, height - y)
        
        # 执行手动裁剪
        img = img.crop((x, y, x + crop_width, y + crop_height))
    else:
        # 智能裁剪模式 - 基于人脸检测和头像比例控制
        img = smart_crop_with_face_proportion(img, target_ratio)
    
    # 调整到目标尺寸
    img = img.resize((target_width, target_height), PILImage.Resampling.LANCZOS)
    
    return img


def smart_crop_with_face_proportion(img_cv, target_ratio):
    """智能裁剪：确保头像比例50%-60%，头部空间5%"""
    import cv2
    import numpy as np
    
    # img_cv已经是OpenCV格式
    height, width = img_cv.shape[:2]
    
    # 人脸检测
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
    
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    
    # 多角度人脸检测
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.05, minNeighbors=3, 
                                         minSize=(30, 30), maxSize=(int(width*0.8), int(height*0.8)))
    
    if len(faces) == 0:
        # 尝试侧面人脸检测
        faces = profile_cascade.detectMultiScale(gray, scaleFactor=1.05, minNeighbors=3,
                                               minSize=(30, 30), maxSize=(int(width*0.8), int(height*0.8)))
    
    if len(faces) > 0:
        # 选择最大的人脸
        face = max(faces, key=lambda f: f[2] * f[3])
        fx, fy, fw, fh = face
        
        current_app.logger.info(f"检测到人脸: ({fx}, {fy}, {fw}, {fh})")
        
        # 计算人脸中心
        face_center_x = fx + fw // 2
        face_center_y = fy + fh // 2
        
        # 根据目标头像比例计算裁剪区域
        head_proportion_target = 0.55  # 目标头像比例55%（50%-60%之间）
        top_space_ratio = 0.15  # 头部空间15%
        
        # 基于人脸尺寸估算头部整体尺寸（包括头发）
        head_width_estimate = fw * 1.3  # 考虑头发的宽度
        head_height_estimate = fh * 1.4  # 考虑头发的高度
        
        # 计算理想的裁剪高度，使头部占比达到目标比例
        ideal_crop_height = head_height_estimate / head_proportion_target
        
        # 计算理想的裁剪宽度，保持目标比例
        ideal_crop_width = ideal_crop_height * target_ratio
        
        # 确保裁剪区域不超出图片边界
        max_crop_width = min(ideal_crop_width, width)
        max_crop_height = min(ideal_crop_height, height)
        
        # 如果理想尺寸超出边界，按比例缩小
        if ideal_crop_width > width or ideal_crop_height > height:
            scale_factor = min(width / ideal_crop_width, height / ideal_crop_height)
            max_crop_width = ideal_crop_width * scale_factor
            max_crop_height = ideal_crop_height * scale_factor
        
        # 计算头部在裁剪框中的位置
        # 头部中心应该在裁剪框上方，预留5%空间给头顶
        head_top_in_crop = max_crop_height * top_space_ratio
        head_center_y_in_crop = head_top_in_crop + head_height_estimate / 2
        
        # 计算裁剪框的起始位置
        crop_y = int(face_center_y - head_center_y_in_crop)
        crop_x = int(face_center_x - max_crop_width / 2)
        
        # 确保裁剪框在图片范围内
        crop_y = max(0, min(crop_y, height - max_crop_height))
        crop_x = max(0, min(crop_x, width - max_crop_width))
        
        # 最终裁剪区域
        crop_x2 = int(crop_x + max_crop_width)
        crop_y2 = int(crop_y + max_crop_height)
        
        current_app.logger.info(f"智能裁剪区域: ({crop_x}, {crop_y}) 到 ({crop_x2}, {crop_y2})")
        current_app.logger.info(f"裁剪尺寸: {max_crop_width}x{max_crop_height}")
        current_app.logger.info(f"头像预估比例: {(head_height_estimate / max_crop_height) * 100:.1f}%")
        
        # 执行裁剪
        cropped_img = img_cv[crop_y:crop_y2, crop_x:crop_x2]
        return cropped_img
        
    else:
        current_app.logger.warning("未检测到人脸，使用传统中心裁剪")
        # 未检测到人脸时的后备方案
        current_ratio = width / height
        
        if current_ratio > target_ratio:
            # 图片太宽，以高度为基准
            new_width = int(height * target_ratio)
            crop_x = (width - new_width) // 2
            cropped_img = img_cv[:, crop_x:crop_x + new_width]
        else:
            # 图片太高，以宽度为基准
            new_height = int(width / target_ratio)
            crop_y = (height - new_height) // 4  # 偏上一些，适合人像
            cropped_img = img_cv[crop_y:crop_y + new_height, :]
        
        return cropped_img
    
    return img_cv

def replace_background_simple(img, params):
    """超精准的背景替换 - 集成多层算法，优先使用增强处理器"""
    start_time = time.time()
    
    # 颜色处理
    color = params.get('color', 'white')
    color_map = {
        'white': (255, 255, 255),
        'blue': (70, 130, 180),
        'red': (220, 20, 60),
        'gray': (128, 128, 128),
        'lightblue': (173, 216, 230),
        'lightgray': (211, 211, 211),
        'pink': (255, 192, 203),
        'yellow': (255, 255, 224),
        'green': (144, 238, 144),
        'darkblue': (44, 62, 80)
    }
    
    if isinstance(color, str):
        if color.startswith('#'):
            try:
                hex_color = color.lstrip('#')
                if len(hex_color) == 6:
                    bg_color = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                else:
                    bg_color = (255, 255, 255)
            except ValueError:
                bg_color = (255, 255, 255)
        else:
            bg_color = color_map.get(color.lower(), (255, 255, 255))
    else:
        bg_color = (255, 255, 255)
    
    # 方法1: 使用增强背景处理器（最新最强算法）
    if ENHANCED_PROCESSOR_AVAILABLE:
        try:
            result = enhanced_background_replace(img, bg_color, method='auto')
            processing_time = time.time() - start_time
            current_app.logger.info(f"增强背景处理器完成，耗时: {processing_time:.2f}秒")
            return result
        except Exception as e:
            current_app.logger.warning(f"增强背景处理器失败: {e}")
    
    # 转换为OpenCV格式（备用方法需要）
    img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
    
    # 方法2: 使用超精准人体轮廓识别（如果可用）
    if ULTRA_PRECISION_AVAILABLE:
        try:
            result = ultra_precise_background_replace(img_cv, bg_color)
            processing_time = time.time() - start_time
            current_app.logger.info(f"超精准人体轮廓识别完成，耗时: {processing_time:.2f}秒")
            return result
        except Exception as e:
            current_app.logger.warning(f"超精准轮廓识别失败: {e}")
    
    # 方法3: 使用精准 rembg 处理器（如果可用）
    if PRECISION_REMBG_AVAILABLE:
        try:
            result = precision_background_replace(img_cv, bg_color)
            processing_time = time.time() - start_time
            current_app.logger.info(f"精准背景替换完成，耗时: {processing_time:.2f}秒")
            return result
        except Exception as e:
            current_app.logger.warning(f"精准背景替换失败，使用备用方法: {e}")
    
    # 方法2: 使用超快速算法（如果可用）
    if ULTRA_FAST_AVAILABLE:
        try:
            color = params.get('color', 'white')
            color_map = {
                'white': (255, 255, 255),
                'blue': (70, 130, 180),
                'red': (220, 20, 60),
                'gray': (128, 128, 128),
                'lightblue': (173, 216, 230),
                'lightgray': (211, 211, 211),
                'pink': (255, 192, 203),
                'yellow': (255, 255, 224),
                'green': (144, 238, 144),
                'darkblue': (44, 62, 80)
            }
            
            if isinstance(color, str):
                if color.startswith('#'):
                    try:
                        hex_color = color.lstrip('#')
                        if len(hex_color) == 6:
                            bg_color = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                        else:
                            bg_color = (255, 255, 255)
                    except ValueError:
                        bg_color = (255, 255, 255)
                else:
                    bg_color = color_map.get(color.lower(), (255, 255, 255))
            else:
                bg_color = (255, 255, 255)
            
            # 转换为OpenCV格式
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            result = ultra_fast_background_replace_v2(img_cv, bg_color)
            
            processing_time = time.time() - start_time
            current_app.logger.info(f"超快速背景替换完成，耗时: {processing_time:.2f}秒")
            return result
            
        except Exception as e:
            current_app.logger.warning(f"超快速算法失败，使用备用方法: {e}")
    
    # 备用方法
    color = params.get('color', 'white')
    
    # 转换颜色名称为RGB值
    color_map = {
        'white': (255, 255, 255),
        'blue': (70, 130, 180),
        'red': (220, 20, 60),
        'gray': (128, 128, 128),
        'lightblue': (173, 216, 230),
        'lightgray': (211, 211, 211),
        'pink': (255, 192, 203),
        'yellow': (255, 255, 224),
        'green': (144, 238, 144),
        'darkblue': (44, 62, 80)
    }
    
    if isinstance(color, str):
        # 支持十六进制颜色值
        if color.startswith('#'):
            try:
                hex_color = color.lstrip('#')
                if len(hex_color) == 6:
                    bg_color = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                elif len(hex_color) == 3:
                    bg_color = tuple(int(hex_color[i]*2, 16) for i in range(3))
                else:
                    bg_color = (255, 255, 255)
            except ValueError:
                bg_color = (255, 255, 255)
        else:
            bg_color = color_map.get(color.lower(), (255, 255, 255))
    elif isinstance(color, (list, tuple)) and len(color) >= 3:
        bg_color = tuple(int(c) for c in color[:3])
    else:
        bg_color = (255, 255, 255)
    
    try:
        # 使用简单的GrabCut算法进行背景替换
        return simple_background_replace(img, bg_color)
    except Exception as e:
        current_app.logger.warning(f"背景替换失败: {e}")
        # 如果失败，返回原图
        return img

def simple_background_replace(img, bg_color):
    """优化的背景替换算法 - 完整人体识别"""
    # 转换PIL图像为OpenCV格式
    img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
    height, width = img_cv.shape[:2]
    
    current_app.logger.info(f"开始优化背景替换，图像尺寸: {width}x{height}")
    
    try:
        # 方法1：尝试人脸检测引导的完整人体分割
        result = face_guided_full_body_segmentation(img_cv, bg_color)
        if result is not None:
            current_app.logger.info("人脸引导的完整人体分割成功")
            return result
    except Exception as e:
        current_app.logger.warning(f"人脸引导分割失败: {e}")
    
    try:
        # 方法2：多层次人体检测
        result = multi_level_body_detection(img_cv, bg_color)
        if result is not None:
            current_app.logger.info("多层次人体检测成功")
            return result
    except Exception as e:
        current_app.logger.warning(f"多层次人体检测失败: {e}")
    
    try:
        # 方法3：增强的GrabCut算法
        result = enhanced_grabcut_full_body(img_cv, bg_color)
        if result is not None:
            current_app.logger.info("增强GrabCut算法成功")
            return result
    except Exception as e:
        current_app.logger.warning(f"增强GrabCut失败: {e}")
    
    # 备用方法：基础算法
    current_app.logger.info("使用基础背景替换算法")
    return basic_background_replace(img_cv, bg_color)

def face_guided_full_body_segmentation(img_cv, bg_color):
    """基于人脸检测的完整人体分割 - 增强衣服识别"""
    height, width = img_cv.shape[:2]
    
    # 多重人脸检测
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
    
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    
    # 正面人脸检测
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.05, minNeighbors=3, minSize=(30, 30))
    
    # 侧面人脸检测
    if len(faces) == 0:
        faces = profile_cascade.detectMultiScale(gray, scaleFactor=1.05, minNeighbors=3, minSize=(30, 30))
    
    if len(faces) > 0:
        # 选择最大的人脸
        face = max(faces, key=lambda f: f[2] * f[3])
        fx, fy, fw, fh = face
        
        current_app.logger.info(f"检测到人脸: ({fx}, {fy}, {fw}, {fh})")
        
        # 步骤1：增强的衣服区域检测
        clothing_mask = enhanced_clothing_detection(img_cv, face)
        
        # 步骤2：创建基于人脸和衣服的完整人体蒙版
        mask = np.full((height, width), cv2.GC_PR_BGD, dtype=np.uint8)
        
        # 人脸核心区域（绝对前景）
        face_core_margin = 0.15  # 稍微减小以更精确
        core_x1 = int(fx + fw * face_core_margin)
        core_y1 = int(fy + fh * face_core_margin)
        core_x2 = int(fx + fw * (1 - face_core_margin))
        core_y2 = int(fy + fh * (1 - face_core_margin))
        mask[core_y1:core_y2, core_x1:core_x2] = cv2.GC_FGD
        
        # 估算完整人体区域（更保守的估算）
        body_height_ratio = 6.0  # 减小比例，更适合证件照
        estimated_body_height = int(fh * body_height_ratio)
        
        # 肩膀和身体宽度
        shoulder_width_ratio = 2.2  # 增大宽度比例
        estimated_body_width = int(fw * shoulder_width_ratio)
        
        # 计算人体中心位置
        face_center_x = fx + fw // 2
        face_center_y = fy + fh // 2
        
        # 人体区域（可能前景）- 更大的区域
        body_x1 = max(0, face_center_x - estimated_body_width // 2)
        body_y1 = max(0, fy - int(fh * 0.4))  # 更多头发区域
        body_x2 = min(width, face_center_x + estimated_body_width // 2)
        body_y2 = min(height, fy + estimated_body_height)
        
        # 如果估算的身体区域超出图片底部，则使用图片底部
        if body_y2 >= height:
            body_y2 = height - 1
        
        mask[body_y1:body_y2, body_x1:body_x2] = cv2.GC_PR_FGD
        
        # 步骤3：融合衣服检测结果
        if clothing_mask is not None:
            # 将检测到的衣服区域标记为可能前景
            clothing_regions = (clothing_mask > 0.3).astype(bool)
            mask[clothing_regions] = cv2.GC_PR_FGD
            current_app.logger.info("已融合衣服检测结果")
        
        # 步骤4：智能肤色和衣服连通性分析
        connected_regions = analyze_skin_clothing_connectivity(img_cv, face, mask)
        if connected_regions is not None:
            mask[connected_regions] = cv2.GC_PR_FGD
            current_app.logger.info("已应用连通性分析结果")
        
        current_app.logger.info(f"最终人体区域: ({body_x1}, {body_y1}) 到 ({body_x2}, {body_y2})")
        
        # 设置边缘为背景（更小的边缘）
        edge_size = min(width, height) // 40  # 进一步减小边缘
        mask[0:edge_size, :] = cv2.GC_BGD
        mask[height-edge_size:height, :] = cv2.GC_BGD
        mask[:, 0:edge_size] = cv2.GC_BGD
        mask[:, width-edge_size:width] = cv2.GC_BGD
        
        # 执行多轮GrabCut（增加迭代次数）
        bgd_model = np.zeros((1, 65), np.float64)
        fgd_model = np.zeros((1, 65), np.float64)
        
        # 第一轮：粗分割（更多迭代）
        cv2.grabCut(img_cv, mask, None, bgd_model, fgd_model, 12, cv2.GC_INIT_WITH_MASK)
        
        # 第二轮：精细化
        cv2.grabCut(img_cv, mask, None, bgd_model, fgd_model, 8, cv2.GC_EVAL)
        
        # 第三轮：最终优化
        cv2.grabCut(img_cv, mask, None, bgd_model, fgd_model, 5, cv2.GC_EVAL)
        
        # 创建最终蒙版
        final_mask = np.where((mask == 2) | (mask == 0), 0, 1).astype('uint8')
        
        # 增强的后处理：特别关注衣服区域
        final_mask = enhanced_post_process_mask(final_mask, img_cv, face)
        
        # 合成图像
        return composite_image(img_cv, final_mask, bg_color)
    
    return None

def multi_level_body_detection(img_cv, bg_color):
    """多层次人体检测算法"""
    height, width = img_cv.shape[:2]
    
    # 创建初始蒙版
    mask = np.full((height, width), cv2.GC_PR_BGD, dtype=np.uint8)
    
    # 层次1：肤色检测
    skin_mask = detect_skin_regions(img_cv)
    
    # 层次2：边缘检测找主要对象
    edge_mask = detect_main_object_by_edges(img_cv)
    
    # 层次3：颜色聚类分析
    cluster_mask = detect_foreground_by_clustering(img_cv)
    
    # 融合多个检测结果
    combined_mask = combine_detection_masks(skin_mask, edge_mask, cluster_mask)
    
    if combined_mask is not None:
        # 将检测结果转换为GrabCut蒙版
        mask[combined_mask > 0.5] = cv2.GC_PR_FGD
        
        # 设置边缘为背景
        edge_size = min(width, height) // 25
        mask[0:edge_size, :] = cv2.GC_BGD
        mask[height-edge_size:height, :] = cv2.GC_BGD
        mask[:, 0:edge_size] = cv2.GC_BGD
        mask[:, width-edge_size:width] = cv2.GC_BGD
        
        # 执行GrabCut
        bgd_model = np.zeros((1, 65), np.float64)
        fgd_model = np.zeros((1, 65), np.float64)
        
        cv2.grabCut(img_cv, mask, None, bgd_model, fgd_model, 6, cv2.GC_INIT_WITH_MASK)
        
        final_mask = np.where((mask == 2) | (mask == 0), 0, 1).astype('uint8')
        final_mask = post_process_mask(final_mask)
        
        return composite_image(img_cv, final_mask, bg_color)
    
    return None

def enhanced_grabcut_full_body(img_cv, bg_color):
    """增强的GrabCut算法 - 针对完整人体"""
    height, width = img_cv.shape[:2]
    
    # 创建更智能的初始蒙版
    mask = np.full((height, width), cv2.GC_PR_BGD, dtype=np.uint8)
    
    # 扩大前景区域 - 适合完整人体
    center_margin_x = 0.15  # 水平边距更小
    center_margin_y = 0.05  # 垂直边距更小，保留更多身体
    
    x1 = int(width * center_margin_x)
    y1 = int(height * center_margin_y)
    x2 = int(width * (1 - center_margin_x))
    y2 = int(height * (1 - center_margin_y))
    
    mask[y1:y2, x1:x2] = cv2.GC_PR_FGD
    
    # 设置更小的边缘为背景
    edge_size = min(width, height) // 30
    mask[0:edge_size, :] = cv2.GC_BGD
    mask[height-edge_size:height, :] = cv2.GC_BGD
    mask[:, 0:edge_size] = cv2.GC_BGD
    mask[:, width-edge_size:width] = cv2.GC_BGD
    
    # 执行多轮GrabCut
    bgd_model = np.zeros((1, 65), np.float64)
    fgd_model = np.zeros((1, 65), np.float64)
    
    # 第一轮：初始分割
    cv2.grabCut(img_cv, mask, None, bgd_model, fgd_model, 10, cv2.GC_INIT_WITH_MASK)
    
    # 第二轮：优化
    cv2.grabCut(img_cv, mask, None, bgd_model, fgd_model, 5, cv2.GC_EVAL)
    
    final_mask = np.where((mask == 2) | (mask == 0), 0, 1).astype('uint8')
    final_mask = post_process_mask(final_mask)
    
    return composite_image(img_cv, final_mask, bg_color)

def detect_skin_regions(img_cv):
    """检测肤色区域"""
    # 转换到HSV色彩空间
    hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
    
    # 肤色范围（HSV）
    lower_skin = np.array([0, 20, 70], dtype=np.uint8)
    upper_skin = np.array([20, 255, 255], dtype=np.uint8)
    
    skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
    
    # 形态学操作去噪
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
    skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
    
    return skin_mask.astype(np.float32) / 255.0

def detect_main_object_by_edges(img_cv):
    """通过边缘检测找到主要对象"""
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    
    # 边缘检测
    edges = cv2.Canny(gray, 50, 150)
    
    # 寻找轮廓
    contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        # 找到最大的轮廓
        largest_contour = max(contours, key=cv2.contourArea)
        
        # 创建蒙版
        mask = np.zeros(gray.shape, dtype=np.uint8)
        cv2.fillPoly(mask, [largest_contour], 255)
        
        return mask.astype(np.float32) / 255.0
    
    return None

def detect_foreground_by_clustering(img_cv):
    """通过颜色聚类检测前景"""
    try:
        from sklearn.cluster import KMeans
        
        height, width = img_cv.shape[:2]
        data = img_cv.reshape(-1, 3)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=4, random_state=42, n_init=10)
        labels = kmeans.fit_predict(data)
        labels = labels.reshape(height, width)
        
        # 假设边缘的聚类是背景
        edge_labels = np.concatenate([
            labels[0, :], labels[-1, :], labels[:, 0], labels[:, -1]
        ])
        
        background_label = np.bincount(edge_labels).argmax()
        
        # 前景蒙版
        foreground_mask = (labels != background_label).astype(np.float32)
        
        return foreground_mask
        
    except ImportError:
        return None

def combine_detection_masks(skin_mask, edge_mask, cluster_mask):
    """融合多个检测蒙版"""
    masks = []
    weights = []
    
    if skin_mask is not None:
        masks.append(skin_mask)
        weights.append(0.3)
    
    if edge_mask is not None:
        masks.append(edge_mask)
        weights.append(0.4)
    
    if cluster_mask is not None:
        masks.append(cluster_mask)
        weights.append(0.3)
    
    if not masks:
        return None
    
    # 加权融合
    combined = np.zeros_like(masks[0])
    total_weight = sum(weights)
    
    for mask, weight in zip(masks, weights):
        combined += mask * (weight / total_weight)
    
    return combined

def post_process_mask(mask):
    """后处理蒙版：填充小洞，平滑边缘"""
    # 填充小洞
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    
    # 去除小噪点
    mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
    
    # 平滑边缘
    mask = cv2.medianBlur(mask, 3)
    
    return mask

def composite_image(img_cv, mask, bg_color):
    """合成图像"""
    height, width = img_cv.shape[:2]
    
    # 创建背景
    background = np.full((height, width, 3), bg_color, dtype=np.uint8)
    
    # 转换为RGB
    img_rgb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)
    
    # 扩展蒙版到3通道
    mask_3d = np.stack([mask] * 3, axis=2)
    
    # 合成
    result = img_rgb * mask_3d + background * (1 - mask_3d)
    result = np.clip(result, 0, 255).astype(np.uint8)
    
    return PILImage.fromarray(result, 'RGB')

def enhanced_clothing_detection(img_cv, face):
    """增强的衣服区域检测"""
    height, width = img_cv.shape[:2]
    fx, fy, fw, fh = face
    
    current_app.logger.info("开始增强衣服检测")
    
    # 方法1：基于颜色一致性的衣服检测
    clothing_mask1 = detect_clothing_by_color_consistency(img_cv, face)
    
    # 方法2：基于纹理特征的衣服检测
    clothing_mask2 = detect_clothing_by_texture(img_cv, face)
    
    # 方法3：基于边缘连续性的衣服检测
    clothing_mask3 = detect_clothing_by_edge_continuity(img_cv, face)
    
    # 方法4：基于区域生长的衣服检测
    clothing_mask4 = detect_clothing_by_region_growing(img_cv, face)
    
    # 融合所有检测结果
    masks = []
    weights = []
    
    if clothing_mask1 is not None:
        masks.append(clothing_mask1)
        weights.append(0.3)
    
    if clothing_mask2 is not None:
        masks.append(clothing_mask2)
        weights.append(0.25)
    
    if clothing_mask3 is not None:
        masks.append(clothing_mask3)
        weights.append(0.25)
    
    if clothing_mask4 is not None:
        masks.append(clothing_mask4)
        weights.append(0.2)
    
    if not masks:
        return None
    
    # 加权融合
    combined_mask = np.zeros((height, width), dtype=np.float32)
    total_weight = sum(weights)
    
    for mask, weight in zip(masks, weights):
        combined_mask += mask * (weight / total_weight)
    
    current_app.logger.info("衣服检测完成")
    return combined_mask

def detect_clothing_by_color_consistency(img_cv, face):
    """基于颜色一致性检测衣服"""
    height, width = img_cv.shape[:2]
    fx, fy, fw, fh = face
    
    # 估算衣服区域（脖子以下）
    neck_y = fy + int(fh * 1.1)  # 脖子位置
    if neck_y >= height:
        return None
    
    clothing_region_y1 = neck_y
    clothing_region_y2 = min(height, fy + int(fh * 4))  # 衣服区域
    clothing_region_x1 = max(0, fx - int(fw * 0.5))
    clothing_region_x2 = min(width, fx + fw + int(fw * 0.5))
    
    if clothing_region_y2 <= clothing_region_y1:
        return None
    
    # 提取衣服区域的颜色特征
    clothing_roi = img_cv[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2]
    
    # 转换到LAB色彩空间进行颜色分析
    lab_roi = cv2.cvtColor(clothing_roi, cv2.COLOR_BGR2LAB)
    
    # 计算主要颜色
    roi_height, roi_width = clothing_roi.shape[:2]
    lab_data = lab_roi.reshape(-1, 3)
    
    try:
        from sklearn.cluster import KMeans
        # 聚类找到主要颜色
        kmeans = KMeans(n_clusters=3, random_state=42, n_init=10)
        labels = kmeans.fit_predict(lab_data)
        centers = kmeans.cluster_centers_
        
        # 找到最大的聚类（可能是衣服颜色）
        unique, counts = np.unique(labels, return_counts=True)
        main_cluster = unique[np.argmax(counts)]
        main_color = centers[main_cluster]
        
        # 在整个图像中找到相似颜色
        lab_img = cv2.cvtColor(img_cv, cv2.COLOR_BGR2LAB)
        
        # 计算颜色距离
        color_diff = np.sqrt(np.sum((lab_img - main_color) ** 2, axis=2))
        
        # 创建衣服蒙版（颜色相似且在合理位置）
        color_threshold = 30  # 颜色相似度阈值
        clothing_mask = (color_diff < color_threshold).astype(np.float32)
        
        # 只保留衣服区域
        full_mask = np.zeros((height, width), dtype=np.float32)
        full_mask[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2] = \
            clothing_mask[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2]
        
        return full_mask
        
    except ImportError:
        return None

def detect_clothing_by_texture(img_cv, face):
    """基于纹理特征检测衣服"""
    height, width = img_cv.shape[:2]
    fx, fy, fw, fh = face
    
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    
    # 估算衣服区域
    neck_y = fy + int(fh * 1.1)
    if neck_y >= height:
        return None
    
    clothing_region_y1 = neck_y
    clothing_region_y2 = min(height, fy + int(fh * 4))
    clothing_region_x1 = max(0, fx - int(fw * 0.5))
    clothing_region_x2 = min(width, fx + fw + int(fw * 0.5))
    
    # 计算局部二值模式（LBP）
    def calculate_lbp_simple(gray_roi):
        """简化的LBP计算"""
        lbp = np.zeros_like(gray_roi, dtype=np.uint8)
        for i in range(1, gray_roi.shape[0] - 1):
            for j in range(1, gray_roi.shape[1] - 1):
                center = gray_roi[i, j]
                code = 0
                code |= (gray_roi[i-1, j-1] >= center) << 7
                code |= (gray_roi[i-1, j] >= center) << 6
                code |= (gray_roi[i-1, j+1] >= center) << 5
                code |= (gray_roi[i, j+1] >= center) << 4
                code |= (gray_roi[i+1, j+1] >= center) << 3
                code |= (gray_roi[i+1, j] >= center) << 2
                code |= (gray_roi[i+1, j-1] >= center) << 1
                code |= (gray_roi[i, j-1] >= center) << 0
                lbp[i, j] = code
        return lbp
    
    # 提取衣服区域的纹理特征
    clothing_roi = gray[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2]
    lbp_roi = calculate_lbp_simple(clothing_roi)
    
    # 计算纹理一致性
    # 衣服通常有相对一致的纹理
    lbp_hist, _ = np.histogram(lbp_roi.flatten(), bins=256, range=(0, 256))
    
    # 找到主要纹理模式
    main_patterns = np.argsort(lbp_hist)[-3:]  # 前3个主要模式
    
    # 在整个图像中找到相似纹理
    lbp_full = calculate_lbp_simple(gray)
    
    texture_mask = np.zeros((height, width), dtype=np.float32)
    for pattern in main_patterns:
        texture_mask += (lbp_full == pattern).astype(np.float32)
    
    # 归一化
    if np.max(texture_mask) > 0:
        texture_mask = texture_mask / np.max(texture_mask)
    
    # 只保留衣服区域
    full_mask = np.zeros((height, width), dtype=np.float32)
    full_mask[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2] = \
        texture_mask[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2]
    
    return full_mask

def detect_clothing_by_edge_continuity(img_cv, face):
    """基于边缘连续性检测衣服"""
    height, width = img_cv.shape[:2]
    fx, fy, fw, fh = face
    
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    
    # 边缘检测
    edges = cv2.Canny(gray, 30, 100)
    
    # 估算衣服区域
    neck_y = fy + int(fh * 1.1)
    if neck_y >= height:
        return None
    
    clothing_region_y1 = neck_y
    clothing_region_y2 = min(height, fy + int(fh * 4))
    clothing_region_x1 = max(0, fx - int(fw * 0.5))
    clothing_region_x2 = min(width, fx + fw + int(fw * 0.5))
    
    # 在衣服区域寻找连续的边缘
    clothing_edges = edges[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2]
    
    # 形态学操作连接边缘
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    connected_edges = cv2.morphologyEx(clothing_edges, cv2.MORPH_CLOSE, kernel)
    
    # 寻找轮廓
    contours, _ = cv2.findContours(connected_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # 创建衣服蒙版
    clothing_mask = np.zeros_like(connected_edges, dtype=np.uint8)
    
    # 填充较大的轮廓
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > 100:  # 过滤小轮廓
            cv2.fillPoly(clothing_mask, [contour], 255)
    
    # 扩展到完整图像
    full_mask = np.zeros((height, width), dtype=np.float32)
    full_mask[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2] = \
        clothing_mask.astype(np.float32) / 255.0
    
    return full_mask

def detect_clothing_by_region_growing(img_cv, face):
    """基于区域生长检测衣服"""
    height, width = img_cv.shape[:2]
    fx, fy, fw, fh = face
    
    # 估算衣服区域的种子点
    neck_y = fy + int(fh * 1.2)
    if neck_y >= height:
        return None
    
    # 多个种子点
    seed_points = [
        (fx + fw // 2, neck_y),  # 中心点
        (fx + fw // 4, neck_y),  # 左侧点
        (fx + 3 * fw // 4, neck_y),  # 右侧点
    ]
    
    # 确保种子点在图像范围内
    valid_seeds = []
    for x, y in seed_points:
        if 0 <= x < width and 0 <= y < height:
            valid_seeds.append((x, y))
    
    if not valid_seeds:
        return None
    
    # 转换到LAB色彩空间
    lab_img = cv2.cvtColor(img_cv, cv2.COLOR_BGR2LAB)
    
    # 区域生长
    grown_mask = np.zeros((height, width), dtype=np.uint8)
    
    for seed_x, seed_y in valid_seeds:
        seed_color = lab_img[seed_y, seed_x]
        
        # 简单的区域生长
        visited = np.zeros((height, width), dtype=bool)
        stack = [(seed_x, seed_y)]
        
        while stack:
            x, y = stack.pop()
            
            if visited[y, x]:
                continue
            
            visited[y, x] = True
            
            # 检查颜色相似性
            current_color = lab_img[y, x]
            color_diff = np.sqrt(np.sum((current_color - seed_color) ** 2))
            
            if color_diff < 25:  # 颜色阈值
                grown_mask[y, x] = 255
                
                # 添加邻居点
                for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1)]:
                    nx, ny = x + dx, y + dy
                    if 0 <= nx < width and 0 <= ny < height and not visited[ny, nx]:
                        stack.append((nx, ny))
    
    return grown_mask.astype(np.float32) / 255.0

def analyze_skin_clothing_connectivity(img_cv, face, current_mask):
    """分析肤色和衣服的连通性"""
    height, width = img_cv.shape[:2]
    fx, fy, fw, fh = face
    
    # 检测肤色区域
    skin_mask = detect_skin_regions(img_cv)
    
    if skin_mask is None:
        return None
    
    # 从人脸区域开始，寻找连通的区域
    face_center_x = fx + fw // 2
    face_center_y = fy + fh // 2
    
    # 创建连通性分析蒙版
    connectivity_mask = np.zeros((height, width), dtype=bool)
    
    # 使用泛洪填充找到与人脸连通的区域
    visited = np.zeros((height, width), dtype=bool)
    stack = [(face_center_x, face_center_y)]
    
    # 转换到HSV进行更好的颜色分析
    hsv_img = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
    face_color = hsv_img[face_center_y, face_center_x]
    
    while stack:
        x, y = stack.pop()
        
        if visited[y, x] or x < 0 or x >= width or y < 0 or y >= height:
            continue
        
        visited[y, x] = True
        
        # 检查是否应该包含这个像素
        current_color = hsv_img[y, x]
        
        # 肤色检测
        is_skin = skin_mask[y, x] > 0.3
        
        # 颜色相似性检测（更宽松的阈值用于衣服）
        color_diff = np.sqrt(np.sum((current_color.astype(float) - face_color.astype(float)) ** 2))
        is_similar_color = color_diff < 60
        
        # 位置合理性检测（在人体估算区域内）
        body_x1 = max(0, fx - fw)
        body_x2 = min(width, fx + 2 * fw)
        body_y1 = max(0, fy - int(fh * 0.3))
        body_y2 = min(height, fy + int(fh * 4))
        
        is_in_body_region = body_x1 <= x <= body_x2 and body_y1 <= y <= body_y2
        
        if (is_skin or is_similar_color) and is_in_body_region:
            connectivity_mask[y, x] = True
            
            # 添加邻居点
            for dx, dy in [(-1, 0), (1, 0), (0, -1), (0, 1), (-1, -1), (-1, 1), (1, -1), (1, 1)]:
                nx, ny = x + dx, y + dy
                if 0 <= nx < width and 0 <= ny < height and not visited[ny, nx]:
                    stack.append((nx, ny))
    
    return connectivity_mask

def enhanced_post_process_mask(mask, img_cv, face):
    """增强的蒙版后处理 - 特别关注衣服区域"""
    height, width = img_cv.shape[:2]
    fx, fy, fw, fh = face
    
    # 基础后处理
    processed_mask = post_process_mask(mask)
    
    # 特殊处理衣服区域
    neck_y = fy + int(fh * 1.1)
    if neck_y < height:
        clothing_region_y1 = neck_y
        clothing_region_y2 = min(height, fy + int(fh * 4))
        clothing_region_x1 = max(0, fx - int(fw * 0.5))
        clothing_region_x2 = min(width, fx + fw + int(fw * 0.5))
        
        # 在衣服区域应用更强的形态学操作
        clothing_roi = processed_mask[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2]
        
        # 更大的核用于衣服区域
        kernel_large = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        clothing_roi = cv2.morphologyEx(clothing_roi, cv2.MORPH_CLOSE, kernel_large)
        clothing_roi = cv2.morphologyEx(clothing_roi, cv2.MORPH_OPEN, kernel_large)
        
        # 更强的平滑
        clothing_roi = cv2.medianBlur(clothing_roi, 5)
        
        # 将处理后的衣服区域放回原蒙版
        processed_mask[clothing_region_y1:clothing_region_y2, clothing_region_x1:clothing_region_x2] = clothing_roi
    
    return processed_mask

def simple_grabcut_background_replace(img_cv, bg_color):
    """增强的GrabCut背景替换 - 高质量边缘处理"""
    try:
        current_app.logger.info(f"开始增强背景替换，背景颜色: {bg_color}")
        
        height, width = img_cv.shape[:2]
        
        # 1. 使用增强的GrabCut算法
        try:
            mask = perform_enhanced_grabcut(img_cv)
            current_app.logger.info("使用增强GrabCut算法")
        except Exception as e:
            current_app.logger.warning(f"增强GrabCut失败，使用基础算法: {e}")
            # 回退到基础GrabCut
            mask = np.zeros((height, width), np.uint8)
            margin_x = max(1, int(width * 0.1))
            margin_y = max(1, int(height * 0.1))
            rect = (margin_x, margin_y, width - 2*margin_x, height - 2*margin_y)
            
            bgdModel = np.zeros((1, 65), np.float64)
            fgdModel = np.zeros((1, 65), np.float64)
            cv2.grabCut(img_cv, mask, rect, bgdModel, fgdModel, 5, cv2.GC_INIT_WITH_RECT)
            mask = np.where((mask == 2) | (mask == 0), 0, 255).astype(np.uint8)
        
        # 2. 边缘细节增强
        try:
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            
            # 头发区域检测
            hair_mask = detect_hair_regions(img_cv, gray)
            
            # 肤色区域检测  
            skin_mask = detect_skin_tone_regions(img_cv)
            
            # 边缘检测
            edge_mask = detect_person_edges(gray)
            
            # 暗色区域检测
            dark_mask = detect_dark_regions(gray)
            
            # 融合所有检测结果
            enhanced_mask = fuse_detection_masks(mask, hair_mask, skin_mask, edge_mask, dark_mask)
            
            # 头发边缘精细处理
            final_mask = post_process_hair_edges(enhanced_mask, img_cv, gray)
            
            # 确保mask是正确的格式
            if final_mask.dtype != np.uint8:
                final_mask = (final_mask * 255).astype(np.uint8)
                
            current_app.logger.info("边缘细节增强完成")
        except Exception as e:
            current_app.logger.warning(f"边缘增强失败，使用基础mask: {e}")
            final_mask = mask
        
        # 3. 应用增强的背景替换（带边缘平滑）
        try:
            result = apply_background_with_smooth_edges(img_cv, final_mask, bg_color)
            current_app.logger.info("增强边缘平滑处理完成")
        except Exception as e:
            current_app.logger.warning(f"增强边缘处理失败，使用基础合成: {e}")
            # 基础背景合成
            mask_norm = final_mask.astype(np.float32) / 255.0
            background = np.full_like(img_cv, bg_color, dtype=np.uint8)
            result = img_cv * mask_norm[:, :, np.newaxis] + background * (1 - mask_norm[:, :, np.newaxis])
            result = result.astype(np.uint8)
        
        current_app.logger.info(f"增强背景替换完成，结果尺寸: {result.shape}")
        return result
        
    except Exception as e:
        current_app.logger.error(f"增强背景替换失败: {e}")
        return img_cv

def basic_background_replace(img_cv, bg_color):
    """基础背景替换算法（备用）"""
    height, width = img_cv.shape[:2]
    
    # 简单的中心区域作为前景
    mask = np.zeros((height, width), dtype=np.uint8)
    
    center_margin = 0.2
    x1 = int(width * center_margin)
    y1 = int(height * center_margin)
    x2 = int(width * (1 - center_margin))
    y2 = int(height * (1 - center_margin))
    
    mask[y1:y2, x1:x2] = 1
    
    return composite_image(img_cv, mask, bg_color)

def beautify_simple(img, params):
    """简单的美颜处理"""
    # 轻微的模糊处理
    blur_level = params.get('blur_level', 1)
    if blur_level > 0:
        img = img.filter(ImageFilter.GaussianBlur(radius=blur_level * 0.5))
    
    # 增强对比度
    contrast_level = params.get('contrast', 1.1)
    if contrast_level != 1.0:
        enhancer = ImageEnhance.Contrast(img)
        img = enhancer.enhance(contrast_level)
    
    # 增强亮度
    brightness_level = params.get('brightness', 1.05)
    if brightness_level != 1.0:
        enhancer = ImageEnhance.Brightness(img)
        img = enhancer.enhance(brightness_level)
    
    return img

def beautify_advanced(img, params):
    """高级美颜处理 - 包含脸部校正、肩膀平衡、去痣等功能"""
    try:
        from app.utils.advanced_beauty_processor import AdvancedBeautyProcessor
        
        # 创建高级美颜处理器
        processor = AdvancedBeautyProcessor()
        
        # 设置默认美颜参数
        beauty_params = {
            'face_alignment': params.get('face_alignment', True),        # 脸部歪斜校正
            'eyebrow_symmetry': params.get('eyebrow_symmetry', True),    # 眉毛对称化
            'mouth_correction': params.get('mouth_correction', True),     # 嘴部校正
            'shoulder_balance': params.get('shoulder_balance', True),     # 肩膀平衡
            'blemish_removal': params.get('blemish_removal', True),       # 去痣功能
            'lighting_enhancement': params.get('lighting_enhancement', True),  # 光线调整
            'clarity_enhancement': params.get('clarity_enhancement', True)     # 清晰度增强
        }
        
        # 执行高级美颜处理
        result_img = processor.process(img, beauty_params)
        
        current_app.logger.info("高级美颜处理完成")
        return result_img
        
    except Exception as e:
        current_app.logger.error(f"高级美颜处理失败: {e}")
        # 如果高级美颜失败，降级到简单美颜
        return beautify_simple(img, params)

@api_bp.route('/beautify-advanced', methods=['POST'])
def advanced_beauty_processing():
    """高级美颜处理API - 脸部校正、肩膀平衡、眉毛对称、去痣、光线调整"""
    try:
        current_app.logger.info("收到高级美颜处理请求")
        
        # 检查文件
        if 'image' not in request.files:
            return jsonify({'error': '没有上传图片'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        # 获取美颜参数
        face_alignment = request.form.get('face_alignment', 'true').lower() == 'true'
        eyebrow_symmetry = request.form.get('eyebrow_symmetry', 'true').lower() == 'true'
        mouth_correction = request.form.get('mouth_correction', 'true').lower() == 'true'
        shoulder_balance = request.form.get('shoulder_balance', 'true').lower() == 'true'
        blemish_removal = request.form.get('blemish_removal', 'true').lower() == 'true'
        lighting_enhancement = request.form.get('lighting_enhancement', 'true').lower() == 'true'
        clarity_enhancement = request.form.get('clarity_enhancement', 'true').lower() == 'true'
        
        # 读取图片
        try:
            image_data = file.read()
            nparr = np.frombuffer(image_data, np.uint8)
            img_cv = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img_cv is None:
                return jsonify({'error': '无法读取图片'}), 400
            
            # 转换为PIL格式
            img_pil = Image.fromarray(cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB))
                
        except Exception as e:
            current_app.logger.error(f"读取图片失败: {e}")
            return jsonify({'error': '图片读取失败'}), 400
        
        # 设置美颜参数
        beauty_params = {
            'face_alignment': face_alignment,
            'eyebrow_symmetry': eyebrow_symmetry,
            'mouth_correction': mouth_correction,
            'shoulder_balance': shoulder_balance,
            'blemish_removal': blemish_removal,
            'lighting_enhancement': lighting_enhancement,
            'clarity_enhancement': clarity_enhancement
        }
        
        current_app.logger.info(f"美颜参数: {beauty_params}")
        
        # 执行高级美颜处理
        try:
            result_img = beautify_advanced(img_pil, beauty_params)
            
            # 转换回OpenCV格式并编码
            result_cv = cv2.cvtColor(np.array(result_img), cv2.COLOR_RGB2BGR)
            _, buffer = cv2.imencode('.jpg', result_cv, [cv2.IMWRITE_JPEG_QUALITY, 95])
            
            # 返回处理后的图片
            from io import BytesIO
            img_io = BytesIO(buffer.tobytes())
            img_io.seek(0)
            
            current_app.logger.info("高级美颜处理完成")
            
            return send_file(
                img_io,
                mimetype='image/jpeg',
                as_attachment=False
            )
            
        except Exception as e:
            current_app.logger.error(f"高级美颜处理失败: {str(e)}")
            return jsonify({'error': f'美颜处理失败: {str(e)}'}), 500
        
    except Exception as e:
        current_app.logger.error(f"高级美颜API处理失败: {str(e)}")
        return jsonify({'error': f'服务器内部错误: {str(e)}'}), 500

@api_bp.route('/process-dark-clothing', methods=['POST'])
def process_dark_clothing():
    """专门处理深色衣物的背景替换API"""
    try:
        current_app.logger.info("收到深色衣物处理请求")
        
        # 检查文件
        if 'image' not in request.files:
            return jsonify({'error': '没有上传图片'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        # 获取背景颜色
        bg_color_str = request.form.get('bg_color', '255,255,255')
        try:
            bg_color = tuple(map(int, bg_color_str.split(',')))
            if len(bg_color) != 3:
                raise ValueError("颜色格式错误")
        except ValueError:
            return jsonify({'error': '背景颜色格式错误，应为 R,G,B 格式'}), 400
        
        # 保存上传的文件
        from werkzeug.utils import secure_filename
        import time
        
        filename = secure_filename(file.filename)
        timestamp = int(time.time())
        unique_filename = f"{timestamp}_{filename}"
        
        # 确保上传目录存在
        upload_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads')
        if not os.path.exists(upload_folder):
            os.makedirs(upload_folder)
        
        input_path = os.path.join(upload_folder, unique_filename)
        file.save(input_path)
        
        current_app.logger.info(f"文件保存至: {input_path}")
        current_app.logger.info(f"背景颜色: {bg_color}")
        
        # 使用优化的深色衣物检测器
        import sys
        backend_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        sys.path.append(backend_root)
        
        from optimized_dark_clothing_detector import OptimizedDarkClothingDetector
        
        # 读取图像
        image = cv2.imread(input_path)
        if image is None:
            return jsonify({'error': '无法读取图像文件'}), 400
        
        # 创建检测器并处理
        detector = OptimizedDarkClothingDetector()
        result_image = detector.replace_background(image, bg_color)
        
        # 保存结果
        output_filename = f"dark_clothing_result_{timestamp}.jpg"
        output_path = os.path.join(upload_folder, output_filename)
        cv2.imwrite(output_path, result_image)
        
        current_app.logger.info(f"深色衣物处理完成: {output_path}")
        
        # 清理输入文件
        try:
            os.remove(input_path)
        except:
            pass
        
        # 检查输出文件是否存在
        if not os.path.exists(output_path):
            return jsonify({'error': '处理结果文件生成失败'}), 500
        
        # 返回处理后的图片
        return send_file(output_path, as_attachment=True, download_name=f"processed_{filename}")
        
    except Exception as e:
        current_app.logger.error(f"深色衣物处理失败: {str(e)}")
        return jsonify({'error': f'处理失败: {str(e)}'}), 500

@api_bp.route('/process', methods=['POST'])
def process_ultra_algorithm():
    """超强深度学习级算法处理端点（无需认证）"""
    try:
        # 检查是否有上传的文件
        if 'image' not in request.files:
            return jsonify({'error': '没有上传图片'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        # 获取背景颜色参数
        bg_color_str = request.form.get('bg_color', '255,255,255')
        try:
            bg_color = [int(x.strip()) for x in bg_color_str.split(',')]
            if len(bg_color) != 3:
                raise ValueError("颜色格式错误")
        except:
            return jsonify({'error': '背景颜色格式错误，应为 R,G,B 格式'}), 400
        
        # 读取图片
        try:
            # 读取上传的图片
            image_data = file.read()
            nparr = np.frombuffer(image_data, np.uint8)
            img_cv = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if img_cv is None:
                return jsonify({'error': '无法读取图片'}), 400
                
        except Exception as e:
            current_app.logger.error(f"读取图片失败: {e}")
            return jsonify({'error': '图片读取失败'}), 400
        
        # 使用超精准多层算法处理
        try:
            if ULTRA_PRECISION_AVAILABLE:
                current_app.logger.info(f"开始超精准轮廓识别，图像尺寸: {img_cv.shape}")
                result_pil = ultra_precise_background_replace(img_cv, bg_color)
                # 转换回OpenCV格式
                result_img = cv2.cvtColor(np.array(result_pil), cv2.COLOR_RGB2BGR)
                current_app.logger.info("超精准轮廓识别处理完成")
            elif PRECISION_REMBG_AVAILABLE:
                current_app.logger.info(f"开始精准rembg处理，图像尺寸: {img_cv.shape}")
                result_pil = precision_background_replace(img_cv, bg_color)
                # 转换回OpenCV格式
                result_img = cv2.cvtColor(np.array(result_pil), cv2.COLOR_RGB2BGR)
                current_app.logger.info("精准rembg处理完成")
            else:
                # 备用处理器
                from app.utils.ultra_fast_processor import ultra_fast_background_replace_v2
                current_app.logger.info(f"开始备用算法处理，图像尺寸: {img_cv.shape}")
                result_pil = ultra_fast_background_replace_v2(img_cv, bg_color)
                result_img = cv2.cvtColor(np.array(result_pil), cv2.COLOR_RGB2BGR)
                current_app.logger.info("备用算法处理完成")
            
            # 将结果编码为JPEG
            _, buffer = cv2.imencode('.jpg', result_img, [cv2.IMWRITE_JPEG_QUALITY, 95])
            
            # 返回处理后的图片
            from io import BytesIO
            img_io = BytesIO(buffer.tobytes())
            img_io.seek(0)
            
            return send_file(
                img_io,
                mimetype='image/jpeg',
                as_attachment=False
            )
            
        except Exception as e:
            current_app.logger.error(f"超强算法处理失败: {e}")
            return jsonify({'error': f'处理失败: {str(e)}'}), 500
        finally:
            # 清理内存
            if 'img_cv' in locals():
                del img_cv
            if 'result_pil' in locals():
                del result_pil
            if 'result_img' in locals():
                del result_img
            if 'buffer' in locals():
                del buffer
            if 'img_io' in locals():
                del img_io
            gc.collect()
            
    except Exception as e:
        current_app.logger.error(f"API处理失败: {e}")
        return jsonify({'error': '服务器内部错误'}), 500 

@api_bp.route('/process-enhanced', methods=['POST'])
def process_enhanced_background():
    """增强背景处理接口 - 使用RemBG和多种AI模型"""
    try:
        # 获取上传的文件
        if 'image' not in request.files:
            return jsonify({'error': '没有上传图片'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        # 获取处理参数
        bg_color_param = request.form.get('bg_color', 'white')
        method = request.form.get('method', 'auto')  # auto, rembg, mediapipe, hybrid
        
        # 颜色映射
        color_map = {
            'white': (255, 255, 255),
            'blue': (70, 130, 180),
            'red': (220, 20, 60),
            'gray': (128, 128, 128),
            'lightblue': (173, 216, 230),
            'lightgray': (211, 211, 211),
            'pink': (255, 192, 203),
            'yellow': (255, 255, 224),
            'green': (144, 238, 144),
            'darkblue': (44, 62, 80)
        }
        
        # 处理背景颜色参数
        if isinstance(bg_color_param, str):
            if bg_color_param.startswith('#'):
                try:
                    hex_color = bg_color_param.lstrip('#')
                    if len(hex_color) == 6:
                        bg_color = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                    elif len(hex_color) == 3:
                        bg_color = tuple(int(hex_color[i]*2, 16) for i in range(3))
                    else:
                        bg_color = (255, 255, 255)
                except ValueError:
                    bg_color = (255, 255, 255)
            elif ',' in bg_color_param:
                # 支持 R,G,B 格式
                try:
                    bg_color = tuple(int(x.strip()) for x in bg_color_param.split(','))
                    if len(bg_color) != 3:
                        bg_color = (255, 255, 255)
                except ValueError:
                    bg_color = (255, 255, 255)
            else:
                bg_color = color_map.get(bg_color_param.lower(), (255, 255, 255))
        else:
            bg_color = (255, 255, 255)
        
        # 读取图片
        try:
            img = PILImage.open(file.stream)
            if img.mode == 'RGBA':
                img = img.convert('RGB')
        except Exception as e:
            return jsonify({'error': f'无法读取图片: {str(e)}'}), 400
        
        start_time = time.time()
        
        # 使用增强背景处理器
        if ENHANCED_PROCESSOR_AVAILABLE:
            try:
                result_img = enhanced_background_replace(img, bg_color, method=method)
                processing_time = time.time() - start_time
                current_app.logger.info(f"增强背景处理成功，方法: {method}，耗时: {processing_time:.2f}秒")
                
                # 将PIL图像转换为字节流返回
                from io import BytesIO
                img_io = BytesIO()
                result_img.save(img_io, 'JPEG', quality=95)
                img_io.seek(0)
                
                return send_file(
                    img_io,
                    mimetype='image/jpeg',
                    as_attachment=False
                )
                
            except Exception as e:
                current_app.logger.error(f"增强背景处理失败: {e}")
                return jsonify({'error': f'增强处理失败: {str(e)}'}), 500
            finally:
                # 清理内存
                if 'img' in locals():
                    del img
                if 'result_img' in locals():
                    del result_img
                if 'img_io' in locals():
                    del img_io
                gc.collect()
        else:
            return jsonify({'error': '增强背景处理器不可用，请检查依赖安装'}), 500
        
    except Exception as e:
        current_app.logger.error(f"增强背景处理接口失败: {e}")
        return jsonify({'error': f'处理失败: {str(e)}'}), 500

def ultra_fast_background_replace(img_cv, bg_color):
    """超快速背景替换 - 3秒内完成"""
    height, width = img_cv.shape[:2]
    
    # 创建简单蒙版 - 保护中心区域
    mask = np.zeros((height, width), dtype=np.float32)
    
    # 保护中心80%区域
    center_x, center_y = width // 2, height // 2
    protect_w, protect_h = int(width * 0.4), int(height * 0.4)
    
    x1 = max(0, center_x - protect_w)
    x2 = min(width, center_x + protect_w)
    y1 = max(0, center_y - protect_h)
    y2 = min(height, center_y + protect_h)
    
    mask[y1:y2, x1:x2] = 1.0
    
    # 边缘羽化
    edge_size = min(width, height) // 20
    if edge_size > 0:
        # 简单边缘处理
        mask[:edge_size, :] *= 0.3
        mask[-edge_size:, :] *= 0.3
        mask[:, :edge_size] *= 0.3
        mask[:, -edge_size:] *= 0.3
    
    # 应用背景
    result = img_cv.copy()
    bg_mask = (mask < 0.5)
    result[bg_mask] = bg_color
    
    # 简单混合
    blend_mask = (mask >= 0.3) & (mask < 0.7)
    if np.any(blend_mask):
        alpha = mask[blend_mask].reshape(-1, 1)
        result[blend_mask] = (result[blend_mask] * alpha + 
                             np.array(bg_color) * (1 - alpha)).astype(np.uint8)
    
    return result


@api_bp.route('/processing/detect-crop-area', methods=['POST'])
def detect_crop_area():
    """检测图片的建议裁剪区域"""
    try:
        data = request.get_json()
        
        if not data or 'image_data' not in data:
            return jsonify({
                'success': False,
                'message': '缺少图片数据'
            }), 400
        
        # 解析图片数据
        image_data = data['image_data']
        if 'base64,' in image_data:
            image_data = image_data.split('base64,')[1]
        
        # 解码图片
        import base64
        import io
        image_bytes = base64.b64decode(image_data)
        img_pil = PILImage.open(io.BytesIO(image_bytes))
        
        # 转换为OpenCV格式
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        if img_cv is None:
            return jsonify({
                'success': False,
                'message': '无效的图片格式'
            }), 400
        
        # 获取模板信息
        template = data.get('template')
        if not template:
            return jsonify({
                'success': False,
                'message': '缺少模板信息'
            }), 400
        
        # 检测人脸并计算建议裁剪区域
        crop_area = detect_face_crop_area(img_cv, template)
        
        if crop_area:
            return jsonify({
                'success': True,
                'crop_area': crop_area,
                'message': '检测到最佳裁剪区域'
            })
        else:
            return jsonify({
                'success': False,
                'message': '无法检测到合适的裁剪区域，建议使用手动调整'
            })
            
    except Exception as e:
        current_app.logger.error(f"检测裁剪区域失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'检测失败: {str(e)}'
        }), 500


@api_bp.route('/processing/id-photo', methods=['POST'])
def process_id_photo():
    """证件照制作专用API - 支持实时处理"""
    try:
        data = request.get_json()
        if not data:
            return bad_request('请求数据为空')
        
        # 获取处理参数
        image_data = data.get('image_data')  # base64图片数据
        template = data.get('template')      # 证件照规格
        background = data.get('background')  # 背景颜色
        beauty = data.get('beauty', {})      # 美颜参数
        size = data.get('size', {})          # 尺寸参数
        transform = data.get('transform', {}) # 图片变换参数
        crop_area = data.get('crop_area') or data.get('crop')  # 裁剪区域参数
        auto_corrections = data.get('auto_corrections', {})  # 自动校正参数
        
        if not image_data:
            return bad_request('缺少图片数据')
        
        # 解码base64图片
        import base64
        import io
        
        # 移除data:image前缀
        if 'base64,' in image_data:
            image_data = image_data.split('base64,')[1]
        
        # 解码图片
        image_bytes = base64.b64decode(image_data)
        img_pil = PILImage.open(io.BytesIO(image_bytes))
        
        # 转换为OpenCV格式
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        # 应用处理操作
        processed_img = apply_id_photo_processing(img_cv, {
            'template': template,
            'background': background,
            'beauty': beauty,
            'size': size,
            'transform': transform,
            'crop_area': crop_area,
            'crop': crop_area,  # 支持两种参数名
            'auto_corrections': auto_corrections
        })
        
        if processed_img is None:
            return validation_error('图片处理失败')
        
        # 将处理后的图片转换为base64
        processed_pil = PILImage.fromarray(cv2.cvtColor(processed_img, cv2.COLOR_BGR2RGB))
        buffer = io.BytesIO()
        processed_pil.save(buffer, format='PNG', quality=95)
        buffer.seek(0)
        
        processed_base64 = base64.b64encode(buffer.getvalue()).decode()
        processed_data_url = f"data:image/png;base64,{processed_base64}"
        
        # 检查是否进行了智能裁剪
        crop_info = None
        auto_corrections = data.get('auto_corrections', {})
        if auto_corrections.get('smart_crop'):
            # 获取智能裁剪信息（这里需要从smart_crop_with_face_proportion函数返回）
            # 暂时使用模拟数据
            h, w = img_cv.shape[:2]
            crop_info = {
                'x': int(w * 0.1),
                'y': int(h * 0.1),
                'width': int(w * 0.8),
                'height': int(h * 0.8)
            }
        
        response_data = {
            'success': True,
            'processed_image': processed_data_url,
            'message': '处理完成'
        }
        
        if crop_info:
            response_data['crop_info'] = crop_info
        
        return jsonify(response_data)
        
    except Exception as e:
        current_app.logger.error(f"证件照处理失败: {e}")
        return validation_error(f'处理失败: {str(e)}')


def apply_id_photo_processing(img_cv, params):
    """应用证件照处理操作"""
    try:
        processed_img = img_cv.copy()
        
        # 记录处理开始
        current_app.logger.info(f"开始ID照片处理，原图尺寸: {processed_img.shape[:2]}")
        
        # 检查是否是手动裁剪模式
        crop = params.get('crop') or params.get('crop_area')  # 支持两种参数名
        auto_corrections = params.get('auto_corrections', {})
        is_manual_crop = (crop and any(crop.values()) and 
                         not auto_corrections.get('smart_crop', False))
        
        current_app.logger.info(f"裁剪参数: {crop}")
        current_app.logger.info(f"自动校正参数: {auto_corrections}")
        current_app.logger.info(f"是否手动裁剪模式: {is_manual_crop}")
        
        if is_manual_crop:
            # 手动裁剪模式：只做裁剪和背景替换，不做其他变换
            current_app.logger.info("使用手动裁剪模式")
            
            # 1. 执行裁剪
            processed_img = apply_crop_transform(processed_img, crop)
            current_app.logger.info(f"裁剪后尺寸: {processed_img.shape[:2]}")
            
            # 2. 背景替换
            background = params.get('background')
            if background and background != '#ffffff':
                processed_img = replace_background_for_id_photo(processed_img, background)
                current_app.logger.info("完成背景替换")
            
            # 3. 美颜处理
            beauty = params.get('beauty', {})
            if any(beauty.values()):
                processed_img = apply_beauty_effects(processed_img, beauty)
                current_app.logger.info("完成美颜处理")
            
            # 手动裁剪模式下不进行尺寸调整，保持裁剪后的尺寸
            
        else:
            # 标准处理模式
            current_app.logger.info("使用标准处理模式")
            
            # 1. 智能自动校正（可选）
            auto_corrections = params.get('auto_corrections', {})
            if auto_corrections.get('auto_level'):
                processed_img = auto_level_correction(processed_img)
            if auto_corrections.get('center_face'):
                processed_img = auto_center_face(processed_img)
            
            # 2. 智能裁剪处理
            if auto_corrections.get('smart_crop'):
                template = params.get('template')
                if template:
                    target_ratio = template['width'] / template['height']
                    processed_img = smart_crop_with_face_proportion(processed_img, target_ratio)
            
            # 3. 图片变换（缩放、旋转、位移）
            transform = params.get('transform', {})
            if any(transform.values()):
                processed_img = apply_image_transform(processed_img, transform)
            
            # 4. 背景替换
            background = params.get('background')
            if background and background != '#ffffff':
                processed_img = replace_background_for_id_photo(processed_img, background)
            
            # 5. 美颜处理
            beauty = params.get('beauty', {})
            if any(beauty.values()):
                processed_img = apply_beauty_effects(processed_img, beauty)
            
            # 6. 尺寸调整（只在非手动裁剪模式下执行）
            template = params.get('template')
            size = params.get('size', {})
            if template or size:
                processed_img = resize_for_id_photo(processed_img, template, size)
        
        current_app.logger.info(f"ID照片处理完成，最终尺寸: {processed_img.shape[:2]}")
        return processed_img
        
    except Exception as e:
        current_app.logger.error(f"应用处理操作失败: {e}")
        return None


def replace_background_for_id_photo(img_cv, bg_color):
    """为证件照替换背景 - 增强头发检测版本"""
    try:
        current_app.logger.info(f"替换背景：收到颜色参数 = {bg_color}, 类型 = {type(bg_color)}")
        
        # 解析颜色
        if isinstance(bg_color, str) and bg_color.startswith('#'):
            hex_color = bg_color[1:]
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            bg_color_bgr = (b, g, r)
            bg_color_rgb = (r, g, b)
            current_app.logger.info(f"解析颜色：RGB = {bg_color_rgb}, BGR = {bg_color_bgr}")
        elif isinstance(bg_color, (tuple, list)) and len(bg_color) == 3:
            # 如果传入的是元组，假设是RGB格式
            r, g, b = bg_color[:3]
            bg_color_bgr = (b, g, r)
            bg_color_rgb = (r, g, b)
            current_app.logger.info(f"元组颜色：RGB = {bg_color_rgb}, BGR = {bg_color_bgr}")
        else:
            bg_color_bgr = (255, 255, 255)  # 默认白色
            bg_color_rgb = (255, 255, 255)
            current_app.logger.info(f"使用默认白色背景")
        
        # 首先尝试使用增强背景处理器
        try:
            if ENHANCED_PROCESSOR_AVAILABLE:
                from ..utils.enhanced_background_processor import enhanced_background_replace
                result_pil = enhanced_background_replace(img_cv, bg_color_rgb, method='auto')
                # 转换回OpenCV格式
                result_cv = cv2.cvtColor(np.array(result_pil), cv2.COLOR_RGB2BGR)
                return result_cv
        except Exception as e:
            current_app.logger.warning(f"增强背景处理器失败，使用改进的标准算法: {e}")
        
        # 使用改进的背景替换算法（加强头发检测）
        try:
            result = enhanced_background_replace_with_hair_detection(img_cv, bg_color_bgr)
            current_app.logger.info(f"增强背景替换完成，结果类型: {type(result)}")
            return result
        except Exception as e:
            current_app.logger.warning(f"增强背景替换失败，使用简单算法: {e}")
            # 使用更可靠的简单背景替换
            return simple_grabcut_background_replace(img_cv, bg_color_bgr)
        
    except Exception as e:
        current_app.logger.error(f"背景替换失败: {e}")
        import traceback
        current_app.logger.error(f"错误详情: {traceback.format_exc()}")
        return img_cv


def enhanced_background_replace_with_hair_detection(img_cv, bg_color):
    """增强的背景替换算法 - 专注头发和细节检测"""
    try:
        h, w = img_cv.shape[:2]
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # 1. 多层次人体分割
        # 基础GrabCut分割
        mask_grabcut = perform_enhanced_grabcut(img_cv)
        
        # 2. 头发专门检测
        hair_mask = detect_hair_regions(img_cv, gray)
        
        # 3. 肤色检测
        skin_mask = detect_skin_tone_regions(img_cv)
        
        # 4. 边缘检测增强
        edge_mask = detect_person_edges(gray)
        
        # 5. 暗色区域检测（头发、深色衣服）
        dark_regions_mask = detect_dark_regions(gray)
        
        # 6. 融合所有检测结果
        combined_mask = fuse_detection_masks(
            mask_grabcut, hair_mask, skin_mask, edge_mask, dark_regions_mask
        )
        
        # 7. 头发边缘后处理
        final_mask = post_process_hair_edges(combined_mask, img_cv, gray)
        
        # 8. 应用背景
        result = apply_background_with_smooth_edges(img_cv, final_mask, bg_color)
        
        return result
        
    except Exception as e:
        current_app.logger.error(f"增强背景替换失败: {e}")
        # 回退到简单算法
        return simple_background_replace_v2(img_cv, bg_color)


def simple_background_replace_v2(img_cv, bg_color):
    """
    简单背景替换算法v2 - 用作备用方案
    
    Args:
        img_cv: OpenCV格式的输入图像
        bg_color: 背景颜色 (B, G, R) 格式
        
    Returns:
        PIL Image格式的处理结果
    """
    try:
        from PIL import Image
        
        # 确保背景颜色格式正确
        if isinstance(bg_color, (list, tuple)) and len(bg_color) == 3:
            bg_color_bgr = tuple(int(c) for c in bg_color)
        else:
            bg_color_bgr = (255, 255, 255)  # 默认白色
        
        height, width = img_cv.shape[:2]
        
        # 使用GrabCut算法进行分割
        mask = np.zeros((height, width), np.uint8)
        
        # 定义前景区域（避免边缘）
        margin_x = int(width * 0.1)
        margin_y = int(height * 0.1)
        rect = (margin_x, margin_y, width - 2*margin_x, height - margin_y)
        
        # 初始化GrabCut模型
        bgdModel = np.zeros((1, 65), np.float64)
        fgdModel = np.zeros((1, 65), np.float64)
        
        # 执行GrabCut分割
        cv2.grabCut(img_cv, mask, rect, bgdModel, fgdModel, 5, cv2.GC_INIT_WITH_RECT)
        
        # 创建最终掩码
        mask2 = np.where((mask == 2) | (mask == 0), 0, 255).astype(np.uint8)
        
        # 形态学处理优化掩码
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask2 = cv2.morphologyEx(mask2, cv2.MORPH_CLOSE, kernel, iterations=2)
        mask2 = cv2.morphologyEx(mask2, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 平滑掩码边缘
        mask2 = cv2.GaussianBlur(mask2, (3, 3), 0)
        _, mask2 = cv2.threshold(mask2, 127, 255, cv2.THRESH_BINARY)
        
        # 创建背景
        background = np.full_like(img_cv, bg_color_bgr, dtype=np.uint8)
        
        # 将掩码转换为3通道
        mask_3ch = cv2.cvtColor(mask2, cv2.COLOR_GRAY2BGR)
        mask_normalized = mask_3ch.astype(np.float32) / 255.0
        
        # 应用掩码进行背景替换
        result = img_cv.astype(np.float32) * mask_normalized + background.astype(np.float32) * (1 - mask_normalized)
        result = result.astype(np.uint8)
        
        # 直接返回OpenCV格式的图像
        current_app.logger.info(f"简单背景替换v2成功，结果尺寸: {result.shape}")
        return result
        
    except Exception as e:
        current_app.logger.error(f"简单背景替换v2失败: {e}")
        # 最后的备用方案：返回原图
        return img_cv


def perform_enhanced_grabcut(img_cv):
    """执行增强的GrabCut分割 - 优化边缘质量"""
    try:
        h, w = img_cv.shape[:2]
        mask = np.zeros((h, w), np.uint8)
        
        # 1. 人脸检测辅助初始化
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4, minSize=(50, 50))
        
        if len(faces) > 0:
            # 有人脸时，基于人脸位置优化前景区域
            face_x, face_y, face_w, face_h = faces[0]  # 使用第一个检测到的人脸
            
            # 扩展人脸区域作为前景估计
            expand_ratio = 2.5  # 扩展倍数
            center_x = face_x + face_w // 2
            center_y = face_y + face_h // 2
            
            # 计算扩展后的区域
            expanded_w = int(face_w * expand_ratio)
            expanded_h = int(face_h * expand_ratio * 1.5)  # 高度多扩展一些，包含身体
            
            rect_x = max(0, center_x - expanded_w // 2)
            rect_y = max(0, center_y - expanded_h // 2)
            rect_w = min(expanded_w, w - rect_x)
            rect_h = min(expanded_h, h - rect_y)
            
            rect = (rect_x, rect_y, rect_w, rect_h)
            current_app.logger.info(f"基于人脸位置的前景区域: {rect}")
        else:
            # 无人脸时使用传统方法
            margin_x = int(w * 0.12)  # 减少边距以包含更多前景
            margin_y = int(h * 0.08)
            rect = (margin_x, margin_y, w - 2*margin_x, h - margin_y)
            current_app.logger.info(f"传统前景区域: {rect}")
        
        # 2. 增强的GrabCut处理
        bgdModel = np.zeros((1, 65), np.float64)
        fgdModel = np.zeros((1, 65), np.float64)
        
        # 第一轮：基础分割
        cv2.grabCut(img_cv, mask, rect, bgdModel, fgdModel, 5, cv2.GC_INIT_WITH_RECT)
        
        # 3. 精细化处理 - 基于边缘信息优化mask
        temp_mask = np.where((mask == 2) | (mask == 0), 0, 1).astype(np.uint8)
        
        # 检测图像边缘
        edges = cv2.Canny(gray, 30, 100)
        
        # 在边缘附近的不确定区域进行手动标记
        edge_dilated = cv2.dilate(edges, np.ones((3,3), np.uint8), iterations=1)
        uncertain_areas = cv2.bitwise_and(edge_dilated, (mask == 2).astype(np.uint8) * 255)
        
        # 将边缘附近的可能背景标记为前景
        mask[uncertain_areas > 0] = cv2.GC_PR_FGD
        
        # 第二轮：精细化分割
        cv2.grabCut(img_cv, mask, None, bgdModel, fgdModel, 3, cv2.GC_INIT_WITH_MASK)
        
        # 4. 后处理优化
        # 将可能前景和确定前景都视为前景
        final_mask = np.where((mask == 2) | (mask == 0), 0, 255).astype(np.uint8)
        
        # 5. 形态学优化
        # 闭运算连接断裂区域
        kernel_close = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_CLOSE, kernel_close, iterations=2)
        
        # 开运算去除小噪声
        kernel_open = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
        final_mask = cv2.morphologyEx(final_mask, cv2.MORPH_OPEN, kernel_open, iterations=1)
        
        # 6. 边缘平滑预处理
        # 对mask边缘进行轻微模糊，为后续的边缘平滑做准备
        final_mask = cv2.GaussianBlur(final_mask, (3, 3), 0.5)
        _, final_mask = cv2.threshold(final_mask, 127, 255, cv2.THRESH_BINARY)
        
        current_app.logger.info("增强GrabCut分割完成")
        return final_mask
        
    except Exception as e:
        current_app.logger.warning(f"增强GrabCut分割失败: {e}")
        return np.ones((img_cv.shape[0], img_cv.shape[1]), dtype=np.uint8) * 255


def detect_hair_regions(img_cv, gray):
    """专门检测头发区域"""
    try:
        h, w = gray.shape
        hair_mask = np.zeros((h, w), dtype=np.uint8)
        
        # 1. 多阈值暗色检测（头发通常较暗）
        _, very_dark = cv2.threshold(gray, 50, 255, cv2.THRESH_BINARY_INV)
        _, dark = cv2.threshold(gray, 80, 255, cv2.THRESH_BINARY_INV)
        _, medium_dark = cv2.threshold(gray, 120, 255, cv2.THRESH_BINARY_INV)
        
        # 2. 头发纹理检测
        # 水平纹理核（检测头发丝）
        kernel_h = np.array([[-1, -1, -1], [2, 2, 2], [-1, -1, -1]], dtype=np.float32)
        # 垂直纹理核
        kernel_v = np.array([[-1, 2, -1], [-1, 2, -1], [-1, 2, -1]], dtype=np.float32)
        
        hair_texture_h = cv2.filter2D(gray, -1, kernel_h)
        hair_texture_v = cv2.filter2D(gray, -1, kernel_v)
        hair_texture = cv2.addWeighted(hair_texture_h, 0.5, hair_texture_v, 0.5, 0)
        
        _, texture_mask = cv2.threshold(hair_texture, 15, 255, cv2.THRESH_BINARY)
        
        # 3. 重点关注头部区域（上半部分）
        head_region_height = int(h * 0.7)
        
        # 4. 边缘检测增强（细化头发边缘）
        edges = cv2.Canny(gray, 30, 100)
        
        # 5. 在头部区域组合检测结果
        hair_candidates = cv2.bitwise_or(very_dark[:head_region_height, :], 
                                       dark[:head_region_height, :])
        hair_candidates = cv2.bitwise_or(hair_candidates, 
                                       texture_mask[:head_region_height, :])
        hair_candidates = cv2.bitwise_or(hair_candidates, 
                                       edges[:head_region_height, :])
        
        # 6. 形态学处理连接头发丝
        hair_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 5))
        hair_candidates = cv2.morphologyEx(hair_candidates, cv2.MORPH_CLOSE, hair_kernel, iterations=2)
        
        # 7. 轻微膨胀确保边缘完整
        dilate_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
        hair_candidates = cv2.dilate(hair_candidates, dilate_kernel, iterations=1)
        
        # 将结果放入完整mask
        hair_mask[:head_region_height, :] = hair_candidates
        
        return hair_mask
        
    except Exception as e:
        current_app.logger.warning(f"头发检测失败: {e}")
        return np.zeros((img_cv.shape[0], img_cv.shape[1]), dtype=np.uint8)


def detect_skin_tone_regions(img_cv):
    """检测肤色区域"""
    try:
        # 转换到多个色彩空间进行肤色检测
        hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
        yuv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2YUV)
        
        # HSV肤色范围
        lower_skin_hsv = np.array([0, 20, 70])
        upper_skin_hsv = np.array([20, 255, 255])
        skin_mask_hsv = cv2.inRange(hsv, lower_skin_hsv, upper_skin_hsv)
        
        # YUV肤色范围
        lower_skin_yuv = np.array([0, 85, 135])
        upper_skin_yuv = np.array([255, 135, 180])
        skin_mask_yuv = cv2.inRange(yuv, lower_skin_yuv, upper_skin_yuv)
        
        # 组合两种检测结果
        skin_mask = cv2.bitwise_or(skin_mask_hsv, skin_mask_yuv)
        
        # 形态学处理，平滑肤色区域
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel, iterations=1)
        skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        # 扩展肤色区域以包含相邻的人体部分
        dilate_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (8, 8))
        skin_mask = cv2.dilate(skin_mask, dilate_kernel, iterations=2)
        
        return skin_mask
        
    except Exception as e:
        current_app.logger.warning(f"肤色检测失败: {e}")
        return np.zeros((img_cv.shape[0], img_cv.shape[1]), dtype=np.uint8)


def detect_person_edges(gray):
    """检测人物边缘"""
    try:
        # 多尺度边缘检测
        edges1 = cv2.Canny(gray, 30, 80)
        edges2 = cv2.Canny(gray, 50, 120)
        edges3 = cv2.Canny(gray, 80, 160)
        
        # 组合不同尺度的边缘
        combined_edges = cv2.bitwise_or(edges1, edges2)
        combined_edges = cv2.bitwise_or(combined_edges, edges3)
        
        # 连接断裂的边缘
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        combined_edges = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel, iterations=1)
        
        return combined_edges
        
    except Exception as e:
        current_app.logger.warning(f"边缘检测失败: {e}")
        return np.zeros(gray.shape, dtype=np.uint8)


def detect_dark_regions(gray):
    """检测暗色区域（衣服、头发等）"""
    try:
        # 自适应阈值检测暗色区域
        _, dark_mask = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
        
        # 去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        dark_mask = cv2.morphologyEx(dark_mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        return dark_mask
        
    except Exception as e:
        current_app.logger.warning(f"暗色区域检测失败: {e}")
        return np.zeros(gray.shape, dtype=np.uint8)


def fuse_detection_masks(mask_grabcut, hair_mask, skin_mask, edge_mask, dark_mask):
    """融合所有检测掩码"""
    try:
        h, w = mask_grabcut.shape
        
        # 归一化所有掩码到0-1范围
        masks = [mask_grabcut, hair_mask, skin_mask, edge_mask, dark_mask]
        normalized_masks = []
        
        for mask in masks:
            if mask.shape != (h, w):
                mask = cv2.resize(mask, (w, h))
            normalized_masks.append(mask.astype(np.float32) / 255.0)
        
        # 加权融合
        weights = [0.4, 0.25, 0.2, 0.1, 0.05]  # GrabCut权重最高，其次是头发检测
        
        fused = np.zeros((h, w), dtype=np.float32)
        for mask, weight in zip(normalized_masks, weights):
            fused += mask * weight
        
        # 转换回二值掩码
        fused = (fused * 255).astype(np.uint8)
        _, binary_mask = cv2.threshold(fused, 127, 255, cv2.THRESH_BINARY)
        
        return binary_mask
        
    except Exception as e:
        current_app.logger.warning(f"掩码融合失败: {e}")
        return mask_grabcut


def post_process_hair_edges(mask, img_cv, gray):
    """头发边缘后处理"""
    try:
        h, w = mask.shape
        
        # 1. 检测mask边缘
        mask_edges = cv2.Canny(mask, 50, 150)
        
        # 2. 在边缘附近寻找额外的头发像素
        # 创建搜索区域（边缘周围5像素）
        dilate_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        search_region = cv2.dilate(mask_edges, dilate_kernel, iterations=1)
        
        # 3. 在搜索区域内检测暗色像素（可能的头发）
        dark_threshold = 80
        _, dark_pixels = cv2.threshold(gray, dark_threshold, 255, cv2.THRESH_BINARY_INV)
        
        # 4. 找到边缘附近的暗色像素
        additional_hair = cv2.bitwise_and(search_region, dark_pixels)
        
        # 5. 检测头发纹理
        # 使用拉普拉斯算子检测头发的精细纹理
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        laplacian_abs = np.absolute(laplacian)
        _, texture_mask = cv2.threshold(laplacian_abs, 15, 255, cv2.THRESH_BINARY)
        texture_mask = texture_mask.astype(np.uint8)
        
        # 6. 在搜索区域内的纹理
        hair_texture = cv2.bitwise_and(search_region, texture_mask)
        
        # 7. 合并额外的头发像素
        extra_hair = cv2.bitwise_or(additional_hair, hair_texture)
        
        # 8. 将额外的头发像素添加到原mask
        enhanced_mask = cv2.bitwise_or(mask, extra_hair)
        
        # 9. 边缘平滑处理
        # 高斯模糊实现边缘羽化
        mask_blurred = cv2.GaussianBlur(enhanced_mask.astype(np.float32), (3, 3), 1)
        
        return mask_blurred
        
    except Exception as e:
        current_app.logger.error(f"头发边缘后处理失败: {e}")
        return mask


def apply_background_with_smooth_edges(img_cv, mask, bg_color):
    """应用背景并处理边缘平滑 - 增强版，消除色阶"""
    try:
        # 确保mask是单通道
        if len(mask.shape) == 3:
            mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)
        
        # 1. 边缘检测和羽化处理
        # 使用形态学操作创建边缘区域
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        mask_eroded = cv2.erode(mask, kernel, iterations=2)
        mask_dilated = cv2.dilate(mask, kernel, iterations=2)
        
        # 计算边缘区域
        edge_region = cv2.subtract(mask_dilated, mask_eroded)
        
        # 2. 多级高斯模糊实现羽化效果
        # 对原始mask进行轻微模糊
        mask_blurred = cv2.GaussianBlur(mask.astype(np.float32), (5, 5), 1.5)
        
        # 对边缘区域进行更强的模糊
        edge_region_float = edge_region.astype(np.float32) / 255.0
        edge_blurred = cv2.GaussianBlur(edge_region_float, (9, 9), 3.0)
        
        # 3. 双边滤波保持重要边缘
        mask_bilateral = cv2.bilateralFilter(mask, 9, 75, 75)
        
        # 4. 组合不同的平滑技术
        # 归一化到0-1范围
        mask_blurred = mask_blurred / 255.0
        mask_bilateral_norm = mask_bilateral.astype(np.float32) / 255.0
        
        # 边缘区域使用更强的平滑
        final_mask = mask_bilateral_norm.copy()
        edge_indices = edge_region > 128
        if np.any(edge_indices):
            final_mask[edge_indices] = mask_blurred[edge_indices] * 0.7 + final_mask[edge_indices] * 0.3
        
        # 5. 抗锯齿处理 - 边缘反走样
        # 对mask边缘进行超采样平滑
        kernel_smooth = np.ones((3, 3), np.float32) / 9
        final_mask = cv2.filter2D(final_mask, -1, kernel_smooth)
        
        # 6. 渐变过渡增强
        # 在边缘区域创建更平滑的渐变
        gradient_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        mask_gradient = cv2.morphologyEx(final_mask, cv2.MORPH_GRADIENT, gradient_kernel)
        
        # 在梯度区域应用额外平滑
        gradient_indices = mask_gradient > 0.1
        if np.any(gradient_indices):
            smooth_patch = cv2.GaussianBlur(final_mask, (7, 7), 2.0)
            final_mask[gradient_indices] = smooth_patch[gradient_indices]
        
        # 7. 确保边界值合理
        final_mask = np.clip(final_mask, 0.0, 1.0)
        
        # 8. 创建三通道mask
        mask_3d = cv2.merge([final_mask, final_mask, final_mask])
        
        # 9. 高质量背景合成
        # 转换为float进行精确计算
        img_float = img_cv.astype(np.float64)
        background = np.full_like(img_cv, bg_color, dtype=np.uint8)
        bg_float = background.astype(np.float64)
        mask_3d_double = mask_3d.astype(np.float64)
        
        # 10. Alpha混合
        result_float = img_float * mask_3d_double + bg_float * (1.0 - mask_3d_double)
        
        # 11. 边缘后处理 - 消除色带
        # 对边缘区域进行轻微的颜色混合
        result = result_float.astype(np.uint8)
        
        # 检测并处理潜在的色阶问题
        edge_mask_binary = (edge_region > 50).astype(np.uint8)
        if np.any(edge_mask_binary):
            # 在边缘区域应用轻微的中值滤波消除色阶
            edge_filtered = cv2.medianBlur(result, 3)
            result = np.where(edge_mask_binary[..., np.newaxis], 
                            (result * 0.7 + edge_filtered * 0.3).astype(np.uint8), 
                            result)
        
        # 12. 最终质量增强
        # 轻微的双边滤波保持细节同时平滑边缘
        result = cv2.bilateralFilter(result, 5, 20, 20)
        
        current_app.logger.info("增强边缘平滑处理完成")
        return result
        
    except Exception as e:
        current_app.logger.error(f"增强背景应用失败: {e}")
        # 回退到基础版本
        try:
            # 基础版本边缘平滑
            if len(mask.shape) == 3:
                mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)
            
            mask_norm = cv2.GaussianBlur(mask.astype(np.float32), (5, 5), 2.0) / 255.0
            mask_3d = cv2.merge([mask_norm, mask_norm, mask_norm])
            
            background = np.full_like(img_cv, bg_color, dtype=np.uint8)
            img_float = img_cv.astype(np.float32)
            bg_float = background.astype(np.float32)
            
            result = img_float * mask_3d + bg_float * (1 - mask_3d)
            return result.astype(np.uint8)
        except:
            return img_cv


def apply_beauty_effects(img_cv, beauty_params):
    """应用美颜效果 - 改进版，减少模糊"""
    try:
        current_app.logger.info(f"应用美颜效果，参数: {beauty_params}")
        
        # 创建副本避免修改原图
        result_img = img_cv.copy()
        
        # 磨皮 - 使用更温和的算法减少模糊
        smoothness = beauty_params.get('smoothness', 0)
        if smoothness > 0:
            # 使用双边滤波替代高斯模糊，保持边缘清晰
            kernel_size = min(int(smoothness / 15) + 3, 9)  # 限制核大小
            if kernel_size % 2 == 0:
                kernel_size += 1  # 确保是奇数
            
            # 双边滤波保持边缘
            result_img = cv2.bilateralFilter(result_img, kernel_size, 
                                           sigmaColor=smoothness * 0.8, 
                                           sigmaSpace=smoothness * 0.8)
            current_app.logger.info(f"磨皮处理: kernel_size={kernel_size}")
        
        # 转换为PIL进行后续处理
        img_pil = PILImage.fromarray(cv2.cvtColor(result_img, cv2.COLOR_BGR2RGB))
        
        # 美白
        whitening = beauty_params.get('whitening', 0)
        if whitening > 0:
            enhancer = ImageEnhance.Brightness(img_pil)
            factor = 1.0 + (whitening / 250)  # 减少美白强度，避免过度曝光
            img_pil = enhancer.enhance(factor)
            current_app.logger.info(f"美白处理: factor={factor}")
        
        # 亮度调节
        brightness = beauty_params.get('brightness', 0)
        if brightness != 0:
            enhancer = ImageEnhance.Brightness(img_pil)
            factor = 1.0 + (brightness / 100)  # -50到50 映射到 0.5-1.5
            img_pil = enhancer.enhance(factor)
            current_app.logger.info(f"亮度调节: factor={factor}")
        
        # 对比度调节
        contrast = beauty_params.get('contrast', 0)
        if contrast != 0:
            enhancer = ImageEnhance.Contrast(img_pil)
            factor = 1.0 + (contrast / 100)  # -50到50 映射到 0.5-1.5
            img_pil = enhancer.enhance(factor)
            current_app.logger.info(f"对比度调节: factor={factor}")
        
        # 轻微锐化以补偿磨皮造成的模糊
        if smoothness > 10:
            sharpness_enhancer = ImageEnhance.Sharpness(img_pil)
            sharpness_factor = 1.0 + (smoothness / 200)  # 轻微锐化
            img_pil = sharpness_enhancer.enhance(sharpness_factor)
            current_app.logger.info(f"锐化补偿: factor={sharpness_factor}")
        
        result = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        current_app.logger.info("美颜处理完成")
        return result
        
    except Exception as e:
        current_app.logger.error(f"美颜处理失败: {e}")
        return img_cv


def apply_image_transform(img_cv, transform_params):
    """应用图片变换（缩放、旋转、位移）"""
    try:
        h, w = img_cv.shape[:2]
        center = (w // 2, h // 2)
        
        # 获取变换参数
        scale = transform_params.get('scale', 1.0)
        rotation = transform_params.get('rotation', 0)
        translate_x = transform_params.get('translateX', 0)
        translate_y = transform_params.get('translateY', 0)
        
        # 创建变换矩阵
        # 1. 旋转和缩放
        M = cv2.getRotationMatrix2D(center, rotation, scale)
        
        # 2. 添加位移
        M[0, 2] += translate_x
        M[1, 2] += translate_y
        
        # 应用变换
        transformed = cv2.warpAffine(
            img_cv, M, (w, h), 
            borderMode=cv2.BORDER_REFLECT,
            flags=cv2.INTER_LANCZOS4
        )
        
        return transformed
        
    except Exception as e:
        current_app.logger.error(f"图片变换失败: {e}")
        return img_cv


def apply_crop_transform(img_cv, crop_params):
    """应用裁剪变换 - 增强版"""
    try:
        if not crop_params:
            current_app.logger.warning("没有裁剪参数，返回原图")
            return img_cv
            
        h, w = img_cv.shape[:2]
        current_app.logger.info(f"=== 开始裁剪变换 ===")
        current_app.logger.info(f"原图尺寸: {w}x{h}")
        current_app.logger.info(f"原始裁剪参数: {crop_params}")
        
        # 安全地获取裁剪参数，支持不同的数据类型
        try:
            x = max(0, int(float(crop_params.get('x', 0))))
            y = max(0, int(float(crop_params.get('y', 0))))
            crop_width = max(1, int(float(crop_params.get('width', w))))
            crop_height = max(1, int(float(crop_params.get('height', h))))
        except (ValueError, TypeError) as e:
            current_app.logger.error(f"裁剪参数格式错误: {e}")
            return img_cv
        
        current_app.logger.info(f"解析后的裁剪参数: x={x}, y={y}, width={crop_width}, height={crop_height}")
        
        # 验证起始位置
        if x >= w or y >= h:
            current_app.logger.error(f"裁剪起始位置超出边界: ({x}, {y}) >= ({w}, {h})")
            return img_cv
        
        # 计算实际可裁剪的区域
        max_crop_width = w - x
        max_crop_height = h - y
        
        actual_width = min(crop_width, max_crop_width)
        actual_height = min(crop_height, max_crop_height)
        
        # 确保最小尺寸
        if actual_width < 1 or actual_height < 1:
            current_app.logger.error(f"裁剪尺寸无效: {actual_width}x{actual_height}")
            return img_cv
        
        current_app.logger.info(f"最终裁剪区域: x={x}, y={y}, width={actual_width}, height={actual_height}")
        
        # 执行裁剪 - 使用copy()确保返回独立的数组
        try:
            # 验证索引范围
            end_x = x + actual_width
            end_y = y + actual_height
            
            if end_x > w or end_y > h:
                current_app.logger.error(f"裁剪结束位置超出边界: ({end_x}, {end_y}) > ({w}, {h})")
                return img_cv
            
            cropped = img_cv[y:end_y, x:end_x].copy()
            
            # 验证裁剪结果
            if cropped.size == 0:
                current_app.logger.error("裁剪结果为空")
                return img_cv
            
            result_h, result_w = cropped.shape[:2]
            current_app.logger.info(f"✅ 裁剪成功: {w}x{h} -> {result_w}x{result_h}")
            current_app.logger.info(f"=== 裁剪变换完成 ===")
            
            return cropped
            
        except Exception as crop_error:
            current_app.logger.error(f"执行裁剪操作失败: {crop_error}")
            import traceback
            current_app.logger.error(f"裁剪错误详情: {traceback.format_exc()}")
            return img_cv
        
    except Exception as e:
        current_app.logger.error(f"裁剪变换总体失败: {e}")
        import traceback
        current_app.logger.error(f"错误详情: {traceback.format_exc()}")
        return img_cv


def auto_level_correction(img_cv):
    """自动水平校正"""
    try:
        # 转换为灰度图
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # 边缘检测
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # 霍夫线变换检测直线
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)
        
        if lines is not None:
            angles = []
            for rho, theta in lines[:20]:  # 只考虑前20条线
                angle = (theta - np.pi/2) * 180 / np.pi
                if abs(angle) < 10:  # 只考虑接近水平的线
                    angles.append(angle)
            
            if angles:
                # 计算平均角度
                mean_angle = np.mean(angles)
                
                # 应用旋转校正
                h, w = img_cv.shape[:2]
                center = (w // 2, h // 2)
                M = cv2.getRotationMatrix2D(center, -mean_angle, 1.0)
                corrected = cv2.warpAffine(img_cv, M, (w, h), borderMode=cv2.BORDER_REFLECT)
                
                current_app.logger.info(f"自动水平校正: 角度 {mean_angle:.2f}°")
                return corrected
        
        return img_cv
        
    except Exception as e:
        current_app.logger.error(f"自动水平校正失败: {e}")
        return img_cv


def auto_center_face(img_cv):
    """自动居中人脸"""
    try:
        # 加载人脸检测器
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # 转换为灰度图进行人脸检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        if len(faces) > 0:
            # 选择最大的人脸
            face = max(faces, key=lambda f: f[2] * f[3])
            fx, fy, fw, fh = face
            
            # 计算人脸中心
            face_center_x = fx + fw // 2
            face_center_y = fy + fh // 2
            
            # 计算图片中心
            h, w = img_cv.shape[:2]
            img_center_x = w // 2
            img_center_y = h // 2
            
            # 计算需要的位移量
            translate_x = img_center_x - face_center_x
            translate_y = img_center_y - face_center_y - int(h * 0.1)  # 略微向上偏移
            
            # 应用位移变换
            M = np.float32([[1, 0, translate_x], [0, 1, translate_y]])
            centered = cv2.warpAffine(img_cv, M, (w, h), borderMode=cv2.BORDER_REFLECT)
            
            current_app.logger.info(f"人脸居中: 位移({translate_x}, {translate_y})")
            return centered
        
        current_app.logger.warning("未检测到人脸，无法自动居中")
        return img_cv
        
    except Exception as e:
        current_app.logger.error(f"自动人脸居中失败: {e}")
        return img_cv


def resize_for_id_photo(img_cv, template, size_params):
    """调整证件照尺寸"""
    try:
        h, w = img_cv.shape[:2]
        
        # 确定目标尺寸
        if template:
            target_width = template.get('width', w)
            target_height = template.get('height', h)
        else:
            target_width = size_params.get('width', w)
            target_height = size_params.get('height', h)
        
        # 计算缩放比例
        scale_x = target_width / w
        scale_y = target_height / h
        scale = min(scale_x, scale_y)
        
        # 缩放图片
        new_width = int(w * scale)
        new_height = int(h * scale)
        resized = cv2.resize(img_cv, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
        
        # 如果需要，创建指定尺寸的画布并居中放置
        if new_width != target_width or new_height != target_height:
            canvas = np.full((target_height, target_width, 3), (255, 255, 255), dtype=np.uint8)
            
            # 计算居中位置
            x_offset = (target_width - new_width) // 2
            y_offset = (target_height - new_height) // 2
            
            # 将缩放后的图片放到画布上
            canvas[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = resized
            return canvas
        
        return resized
        
    except Exception as e:
        current_app.logger.error(f"尺寸调整失败: {e}")
        return img_cv


def detect_face_crop_area(img_cv, template):
    """检测人脸并计算建议的裁剪区域"""
    try:
        # 加载人脸检测器
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # 转换为灰度图进行人脸检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(
            gray, 
            scaleFactor=1.1, 
            minNeighbors=4,
            minSize=(50, 50),
            flags=cv2.CASCADE_SCALE_IMAGE
        )
        
        if len(faces) == 0:
            current_app.logger.warning("未检测到人脸")
            return None
        
        # 选择最大的人脸
        face = max(faces, key=lambda f: f[2] * f[3])
        fx, fy, fw, fh = face
        
        # 获取图片和模板尺寸
        h, w = img_cv.shape[:2]
        template_ratio = template['width'] / template['height']
        
        # 计算理想的人脸在证件照中的比例
        # 对于证件照，人脸应该占照片高度的60-75%
        face_ratio = 0.65
        ideal_face_height = int(h * face_ratio)
        
        # 根据检测到的人脸大小计算缩放比例
        scale_factor = ideal_face_height / fh
        
        # 计算人脸中心点
        face_center_x = fx + fw // 2
        face_center_y = fy + fh // 2
        
        # 根据模板比例计算裁剪区域
        if template_ratio > 1:  # 横向照片
            crop_height = h
            crop_width = int(crop_height * template_ratio)
        else:  # 纵向照片（大多数证件照）
            crop_width = w
            crop_height = int(crop_width / template_ratio)
        
        # 确保裁剪区域不超出图片边界
        crop_width = min(crop_width, w)
        crop_height = min(crop_height, h)
        
        # 计算裁剪区域的起始位置
        # 人脸应该在裁剪区域的上半部分（约1/3处）
        crop_x = max(0, face_center_x - crop_width // 2)
        crop_y = max(0, face_center_y - int(crop_height * 0.35))
        
        # 调整位置确保不超出边界
        if crop_x + crop_width > w:
            crop_x = w - crop_width
        if crop_y + crop_height > h:
            crop_y = h - crop_height
        
        crop_x = max(0, crop_x)
        crop_y = max(0, crop_y)
        
        # 返回裁剪区域信息
        crop_area = {
            'x': int(crop_x),
            'y': int(crop_y),
            'width': int(crop_width),
            'height': int(crop_height),
            'face_info': {
                'x': int(fx),
                'y': int(fy),
                'width': int(fw),
                'height': int(fh),
                'center_x': int(face_center_x),
                'center_y': int(face_center_y)
            },
            'scale_factor': float(scale_factor),
            'confidence': 'high' if fw * fh > 10000 else 'medium' if fw * fh > 5000 else 'low'
        }
        
        current_app.logger.info(f"检测到人脸裁剪区域: {crop_area}")
        return crop_area
        
    except Exception as e:
        current_app.logger.error(f"人脸裁剪区域检测失败: {e}")
        return None


def detect_multiple_faces_crop_area(img_cv, template):
    """检测多个人脸并计算最佳裁剪区域（预留功能）"""
    try:
        # 加载人脸检测器
        face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
        
        # 转换为灰度图进行人脸检测
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        
        if len(faces) == 0:
            return None
        
        # 如果只有一个人脸，使用标准检测
        if len(faces) == 1:
            return detect_face_crop_area(img_cv, template)
        
        # 多人脸情况：选择最中心且最大的人脸
        h, w = img_cv.shape[:2]
        img_center_x, img_center_y = w // 2, h // 2
        
        best_face = None
        best_score = 0
        
        for face in faces:
            fx, fy, fw, fh = face
            face_center_x = fx + fw // 2
            face_center_y = fy + fh // 2
            
            # 计算得分：距离中心越近，面积越大，得分越高
            distance_from_center = np.sqrt((face_center_x - img_center_x)**2 + (face_center_y - img_center_y)**2)
            max_distance = np.sqrt(w**2 + h**2) / 2
            distance_score = 1 - (distance_from_center / max_distance)
            
            area_score = (fw * fh) / (w * h)
            total_score = distance_score * 0.6 + area_score * 0.4
            
            if total_score > best_score:
                best_score = total_score
                best_face = face
        
        if best_face is not None:
            # 使用最佳人脸计算裁剪区域
            fx, fy, fw, fh = best_face
            return detect_face_crop_area(img_cv, template)
        
        return None
        
    except Exception as e:
        current_app.logger.error(f"多人脸裁剪区域检测失败: {e}")
        return None

@api_bp.route('/processing/test-crop', methods=['POST'])
def test_crop_function():
    """测试裁剪功能的专用端点 - 简化版"""
    try:
        # 检查是否有上传的文件
        if 'image' not in request.files:
            return jsonify({'error': '没有上传图片'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        # 获取裁剪参数
        crop_data = request.form.get('crop_data')
        if not crop_data:
            return jsonify({'error': '没有提供裁剪参数'}), 400
        
        try:
            crop_params = json.loads(crop_data)
        except json.JSONDecodeError:
            return jsonify({'error': '裁剪参数格式错误'}), 400
        
        # 读取图片
        image_data = file.read()
        nparr = np.frombuffer(image_data, np.uint8)
        img_cv = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img_cv is None:
            return jsonify({'error': '无法读取图片'}), 400
        
        original_shape = img_cv.shape[:2]
        current_app.logger.info(f"=== 开始测试裁剪 ===")
        current_app.logger.info(f"原图尺寸: {original_shape}")
        current_app.logger.info(f"裁剪参数: {crop_params}")
        
        # 验证裁剪参数
        x = int(crop_params.get('x', 0))
        y = int(crop_params.get('y', 0))
        w = int(crop_params.get('width', 100))
        h = int(crop_params.get('height', 100))
        
        # 边界检查
        img_h, img_w = img_cv.shape[:2]
        x = max(0, min(x, img_w - 1))
        y = max(0, min(y, img_h - 1))
        w = min(w, img_w - x)
        h = min(h, img_h - y)
        
        current_app.logger.info(f"调整后裁剪参数: x={x}, y={y}, w={w}, h={h}")
        
        # 直接使用NumPy切片进行裁剪
        cropped_img = img_cv[y:y+h, x:x+w].copy()
        cropped_shape = cropped_img.shape[:2]
        
        current_app.logger.info(f"裁剪成功，结果尺寸: {cropped_shape}")
        current_app.logger.info(f"=== 测试裁剪完成 ===")
        
        # 将结果转换为JPEG
        _, buffer = cv2.imencode('.jpg', cropped_img, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        # 返回处理后的图片
        from io import BytesIO
        img_io = BytesIO(buffer.tobytes())
        img_io.seek(0)
        
        return send_file(
            img_io,
            mimetype='image/jpeg',
            as_attachment=False
        )
        
    except Exception as e:
        current_app.logger.error(f"测试裁剪失败: {e}")
        import traceback
        current_app.logger.error(f"错误详情: {traceback.format_exc()}")
        return jsonify({'error': f'测试裁剪失败: {str(e)}'}), 500


@api_bp.route('/processing/pure-crop', methods=['POST'])
def pure_crop_api():
    """纯裁剪API - 使用JSON数据"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        image_data = data.get('image_data')
        crop_params = data.get('crop_params') or data.get('crop')  # 支持两种参数名
        
        if not image_data or not crop_params:
            return jsonify({'error': '缺少图片数据或裁剪参数'}), 400
        
        # 解码base64图片
        import base64
        import io
        
        try:
            if 'base64,' in image_data:
                image_data = image_data.split('base64,')[1]
            
            # 清理base64字符串
            image_data = image_data.strip()
            
            # 检查base64字符串长度
            if len(image_data) < 100:
                raise ValueError("图片数据过小")
            
            image_bytes = base64.b64decode(image_data)
            current_app.logger.info(f"成功解码base64图片，大小: {len(image_bytes)} 字节")
            
            img_pil = PILImage.open(io.BytesIO(image_bytes))
            img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
            
            current_app.logger.info(f"成功加载图片，尺寸: {img_cv.shape[1]}x{img_cv.shape[0]}")
            
        except Exception as e:
            current_app.logger.error(f"图片解码失败: {e}")
            raise ValueError(f"图片解码失败: {str(e)}")
        
        current_app.logger.info(f"=== 纯裁剪API ===")
        current_app.logger.info(f"原图尺寸: {img_cv.shape[:2]}")
        current_app.logger.info(f"裁剪参数: {crop_params}")
        
        # 执行裁剪
        x = int(crop_params.get('x', 0))
        y = int(crop_params.get('y', 0))
        w = int(crop_params.get('width', 100))
        h = int(crop_params.get('height', 100))
        
        # 边界检查
        img_h, img_w = img_cv.shape[:2]
        x = max(0, min(x, img_w - 1))
        y = max(0, min(y, img_h - 1))
        w = min(w, img_w - x)
        h = min(h, img_h - y)
        
        # 执行裁剪
        cropped_img = img_cv[y:y+h, x:x+w].copy()
        
        current_app.logger.info(f"裁剪结果尺寸: {cropped_img.shape[:2]}")
        
        # 如果提供了模板信息，调整到证件照标准尺寸
        template = data.get('template')
        if template:
            # 调整到证件照标准尺寸
            final_img = resize_for_id_photo(cropped_img, template, {})
            current_app.logger.info(f"调整到证件照尺寸: {final_img.shape[:2]}")
        else:
            final_img = cropped_img
            current_app.logger.info("未提供模板，保持裁剪尺寸")
        
        # 转换回PIL并编码为base64
        cropped_pil = PILImage.fromarray(cv2.cvtColor(final_img, cv2.COLOR_BGR2RGB))
        buffer = io.BytesIO()
        cropped_pil.save(buffer, format='PNG')
        buffer.seek(0)
        
        result_base64 = base64.b64encode(buffer.getvalue()).decode()
        result_data_url = f"data:image/png;base64,{result_base64}"
        
        return jsonify({
            'success': True,
            'processed_image': result_data_url,
            'crop_info': {
                'original_size': {'width': img_w, 'height': img_h},
                'cropped_size': {'width': w, 'height': h},
                'crop_area': {'x': x, 'y': y, 'width': w, 'height': h}
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"纯裁剪API失败: {e}")
        import traceback
        current_app.logger.error(f"错误详情: {traceback.format_exc()}")
        return jsonify({'error': f'裁剪失败: {str(e)}'}), 500


# 统一裁剪API - 整合手动裁剪和自动裁剪功能

@api_bp.route('/processing/unified-crop', methods=['POST'])
def unified_crop_api():
    """统一裁剪API - 整合手动裁剪和自动裁剪功能
    
    参数:
    - image_data: base64编码的图片数据
    - crop_mode: 'auto' 或 'manual'
    - crop_params: 手动裁剪参数 {x, y, width, height}
    - template: 证件照模板信息
    - auto_settings: 自动裁剪设置
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        image_data = data.get('image_data')
        crop_mode = data.get('crop_mode', 'auto')
        crop_params = data.get('crop_params', {})
        template = data.get('template')
        auto_settings = data.get('auto_settings', {})
        
        if not image_data:
            return jsonify({'error': '缺少图片数据'}), 400
        
        # 解码base64图片
        import base64
        import io
        
        if 'base64,' in image_data:
            image_data = image_data.split('base64,')[1]
        
        image_bytes = base64.b64decode(image_data)
        img_pil = PILImage.open(io.BytesIO(image_bytes))
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        current_app.logger.info(f"=== 统一裁剪API ===")
        current_app.logger.info(f"模式: {crop_mode}")
        current_app.logger.info(f"原图尺寸: {img_cv.shape[:2]}")
        
        processed_img = None
        crop_info = None
        
        if crop_mode == 'manual':
            # 手动裁剪模式
            if not crop_params or not all(k in crop_params for k in ['x', 'y', 'width', 'height']):
                return jsonify({'error': '手动裁剪模式需要完整的裁剪参数'}), 400
            
            # 边界检查
            x = int(crop_params.get('x', 0))
            y = int(crop_params.get('y', 0))
            w = int(crop_params.get('width', 100))
            h = int(crop_params.get('height', 100))
            
            img_h, img_w = img_cv.shape[:2]
            x = max(0, min(x, img_w - 1))
            y = max(0, min(y, img_h - 1))
            w = min(w, img_w - x)
            h = min(h, img_h - y)
            
            # 执行裁剪
            processed_img = img_cv[y:y+h, x:x+w].copy()
            crop_info = {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)}
            
            current_app.logger.info(f"手动裁剪完成: {crop_info}")
            
        elif crop_mode == 'auto':
            # 自动裁剪模式
            if not template:
                return jsonify({'error': '自动裁剪模式需要模板参数'}), 400
            
            # 计算目标比例
            target_ratio = template['width'] / template['height']
            
            # 应用智能自动裁剪
            processed_img, crop_info = smart_crop_with_face_proportion_v2(
                img_cv, target_ratio, auto_settings
            )
            
            # 如果智能裁剪失败，使用中心裁剪作为后备
            if processed_img is None:
                current_app.logger.warning("智能裁剪失败，使用中心裁剪后备方案")
                img_height, img_width = img_cv.shape[:2]
                current_ratio = img_width / img_height
                
                # 计算中心裁剪区域
                if target_ratio > current_ratio:
                    # 目标更宽，以高度为准
                    new_height = img_height
                    new_width = int(new_height * target_ratio)
                    if new_width > img_width:
                        new_width = img_width
                        new_height = int(new_width / target_ratio)
                else:
                    # 目标更高，以宽度为准
                    new_width = img_width
                    new_height = int(new_width / target_ratio)
                    if new_height > img_height:
                        new_height = img_height
                        new_width = int(new_height * target_ratio)
                
                crop_x = max(0, (img_width - new_width) // 2)
                crop_y = max(0, (img_height - new_height) // 2)
                crop_w = min(new_width, img_width - crop_x)
                crop_h = min(new_height, img_height - crop_y)
                
                # 执行中心裁剪
                processed_img = img_cv[crop_y:crop_y+crop_h, crop_x:crop_x+crop_w].copy()
                crop_info = {
                    'x': crop_x,
                    'y': crop_y,
                    'width': crop_w,
                    'height': crop_h,
                    'face_detected': False,
                    'face_count': 0,
                    'fallback_mode': 'center_crop'
                }
            
            current_app.logger.info(f"自动裁剪完成: {crop_info}")
            
        else:
            return jsonify({'error': '不支持的裁剪模式，请使用 auto 或 manual'}), 400
        
        if processed_img is None:
            return jsonify({'error': '裁剪失败'}), 500
        
        # 如果有模板，调整到证件照标准尺寸
        final_img = processed_img
        if template:
            final_img = resize_for_id_photo(processed_img, template, {})
            current_app.logger.info(f"调整到证件照尺寸: {final_img.shape[:2]}")
        
        # 转换回PIL并编码为base64
        final_pil = PILImage.fromarray(cv2.cvtColor(final_img, cv2.COLOR_BGR2RGB))
        buffer = io.BytesIO()
        final_pil.save(buffer, format='PNG', quality=95)
        buffer.seek(0)
        
        processed_base64 = base64.b64encode(buffer.getvalue()).decode()
        processed_data_url = f"data:image/png;base64,{processed_base64}"
        
        return jsonify({
            'success': True,
            'processed_image': processed_data_url,
            'crop_info': crop_info,
            'crop_mode': crop_mode,
            'message': f'{"智能" if crop_mode == "auto" else "手动"}裁剪完成',
            'original_size': {
                'width': int(img_cv.shape[1]),
                'height': int(img_cv.shape[0])
            },
            'cropped_size': {
                'width': int(final_img.shape[1]),
                'height': int(final_img.shape[0])
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"统一裁剪失败: {e}")
        import traceback
        current_app.logger.error(f"错误详情: {traceback.format_exc()}")
        
        # 添加更友好的错误提示
        error_msg = str(e)
        if "face_cascade" in error_msg or "CascadeClassifier" in error_msg:
            error_msg = "人脸检测模块加载失败，请检查OpenCV配置"
        elif "base64" in error_msg:
            error_msg = "图片数据格式错误，请重新上传图片"
        elif "memory" in error_msg.lower():
            error_msg = "内存不足，请尝试使用更小尺寸的图片"
        else:
            error_msg = "智能裁剪处理失败，您可以尝试手动裁剪功能"
            
        return jsonify({'error': error_msg}), 500


def smart_crop_with_face_proportion_v2(img_cv, target_ratio, auto_settings=None):
    """增强版智能自动裁剪 - 带详细裁剪信息返回"""
    try:
        if auto_settings is None:
            auto_settings = {}
        
        # 获取图片尺寸
        img_height, img_width = img_cv.shape[:2]
        current_ratio = img_width / img_height
        
        current_app.logger.info(f"智能裁剪 - 原图尺寸: {img_width}x{img_height}, 目标比例: {target_ratio:.3f}")
        
        # 检查图片尺寸是否有效
        if img_width <= 0 or img_height <= 0:
            raise ValueError("图片尺寸无效")
        
        # 人脸检测
        try:
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            if face_cascade.empty():
                raise ValueError("人脸检测模型加载失败")
                
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            current_app.logger.info(f"人脸检测结果: 检测到 {len(faces)} 个人脸")
            
        except Exception as e:
            current_app.logger.error(f"人脸检测失败: {e}")
            faces = []  # 人脸检测失败时继续处理
        
        if len(faces) == 0:
            # 未检测到人脸，使用中心裁剪
            center_x, center_y = img_width // 2, img_height // 2
            
            if target_ratio > current_ratio:
                # 目标更宽，以高度为准
                new_height = img_height
                new_width = int(new_height * target_ratio)
                if new_width > img_width:
                    new_width = img_width
                    new_height = int(new_width / target_ratio)
            else:
                # 目标更高，以宽度为准
                new_width = img_width
                new_height = int(new_width / target_ratio)
                if new_height > img_height:
                    new_height = img_height
                    new_width = int(new_height * target_ratio)
            
            crop_x = max(0, center_x - new_width // 2)
            crop_y = max(0, center_y - new_height // 2)
            crop_width = min(new_width, img_width - crop_x)
            crop_height = min(new_height, img_height - crop_y)
            
        else:
            # 检测到人脸，使用智能裁剪
            # 找到最大的人脸区域
            main_face = max(faces, key=lambda x: x[2] * x[3])
            face_x, face_y, face_w, face_h = main_face
            
            # 计算人脸中心
            face_center_x = face_x + face_w // 2
            face_center_y = face_y + face_h // 2
            
            # 计算人脸占图片的比例（基于高度）
            face_height_ratio = face_h / img_height
            
            # 根据人脸比例调整裁剪区域
            target_face_ratio = auto_settings.get('face_ratio', 0.55)  # 默认55%
            scale_factor = target_face_ratio / face_height_ratio
            
            # 计算裁剪区域尺寸
            crop_height = int(img_height / scale_factor)
            crop_width = int(crop_height * target_ratio)
            
            # 确保裁剪区域在图片范围内
            crop_width = min(crop_width, img_width)
            crop_height = min(crop_height, img_height)
            
            # 计算裁剪起始点，确保人脸在合适位置
            crop_x = max(0, min(face_center_x - crop_width // 2, img_width - crop_width))
            crop_y = max(0, min(face_center_y - int(crop_height * 0.4), img_height - crop_height))
        
        # 执行裁剪
        cropped_img = img_cv[crop_y:crop_y+crop_height, crop_x:crop_x+crop_width].copy()
        
        # 返回裁剪信息和处理后的图片
        # 确保所有值都是Python原生类型，避免NumPy序列化问题
        crop_info = {
            'x': int(crop_x),
            'y': int(crop_y),
            'width': int(crop_width),
            'height': int(crop_height),
            'face_detected': bool(len(faces) > 0),
            'face_count': int(len(faces))
        }
        
        return cropped_img, crop_info
        
    except Exception as e:
        current_app.logger.error(f"智能自动裁剪失败: {e}")
        import traceback
        current_app.logger.error(f"错误详情: {traceback.format_exc()}")
        
        # 发生异常时使用中心裁剪作为后备
        img_height, img_width = img_cv.shape[:2]
        current_ratio = img_width / img_height
        
        if target_ratio > current_ratio:
            new_height = img_height
            new_width = int(new_height * target_ratio)
            if new_width > img_width:
                new_width = img_width
                new_height = int(new_width / target_ratio)
        else:
            new_width = img_width
            new_height = int(new_width / target_ratio)
            if new_height > img_height:
                new_height = img_height
                new_width = int(new_height * target_ratio)
        
        crop_x = max(0, (img_width - new_width) // 2)
        crop_y = max(0, (img_height - new_height) // 2)
        crop_w = min(new_width, img_width - crop_x)
        crop_h = min(new_height, img_height - crop_y)
        
        cropped_img = img_cv[crop_y:crop_y+crop_h, crop_x:crop_x+crop_w].copy()
        
        crop_info = {
            'x': int(crop_x),
            'y': int(crop_y),
            'width': int(crop_w),
            'height': int(crop_h),
            'face_detected': False,
            'face_count': 0,
            'fallback_mode': 'center_crop',
            'error': str(e)
        }
        
        return cropped_img, crop_info


# 工作流分步处理API端点

@api_bp.route('/processing/beauty', methods=['POST'])
@jwt_required()
def process_beauty_step():
    """美颜处理步骤"""
    current_user_id = int(get_jwt_identity())
    user = User.query.get(current_user_id)
    
    if not user:
        return unauthorized('用户不存在')
    
    try:
        data = request.get_json()
        if not data:
            return bad_request('请求数据为空')
        
        image_data = data.get('image_data')
        beauty_params = data.get('beauty', {})
        
        if not image_data:
            return bad_request('请提供图片数据')
        
        # 解码base64图片
        import base64
        import io
        if 'data:image' in image_data:
            image_data = image_data.split(',')[1]
        
        img_bytes = base64.b64decode(image_data)
        img_pil = PILImage.open(io.BytesIO(img_bytes))
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        # 应用美颜效果
        processed_img = apply_beauty_effects(img_cv, beauty_params)
        
        # 转换为base64返回 - 使用高质量PNG避免压缩损失
        processed_pil = PILImage.fromarray(cv2.cvtColor(processed_img, cv2.COLOR_BGR2RGB))
        buffer = io.BytesIO()
        processed_pil.save(buffer, format='PNG', quality=100)
        buffer.seek(0)
        
        img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return jsonify({
            'success': True,
            'processed_image': f'data:image/png;base64,{img_base64}',
            'message': '美颜处理完成'
        })
        
    except Exception as e:
        current_app.logger.error(f"美颜处理失败: {e}")
        return jsonify({
            'success': False,
            'message': f'美颜处理失败: {str(e)}'
        }), 500

@api_bp.route('/processing/background', methods=['POST'])
@jwt_required()
def process_background_step():
    """背景处理步骤"""
    current_user_id = int(get_jwt_identity())
    user = User.query.get(current_user_id)
    
    if not user:
        return unauthorized('用户不存在')
    
    try:
        data = request.get_json()
        if not data:
            return bad_request('请求数据为空')
        
        image_data = data.get('image_data')
        background_color = data.get('background', '#ffffff')
        
        if not image_data:
            return bad_request('请提供图片数据')
        
        # 解码base64图片
        import base64
        import io
        if 'data:image' in image_data:
            image_data = image_data.split(',')[1]
        
        img_bytes = base64.b64decode(image_data)
        img_pil = PILImage.open(io.BytesIO(img_bytes))
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        # 直接传递背景颜色字符串给处理函数
        # replace_background_for_id_photo函数会自己处理颜色格式转换
        current_app.logger.info(f"背景处理：颜色参数 = {background_color}")
        
        # 应用背景替换
        processed_img = replace_background_for_id_photo(img_cv, background_color)
        
        # 转换为base64返回 - 使用高质量PNG避免压缩损失
        processed_pil = PILImage.fromarray(cv2.cvtColor(processed_img, cv2.COLOR_BGR2RGB))
        buffer = io.BytesIO()
        processed_pil.save(buffer, format='PNG', quality=100)
        buffer.seek(0)
        
        img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return jsonify({
            'success': True,
            'processed_image': f'data:image/png;base64,{img_base64}',
            'message': '背景处理完成'
        })
        
    except Exception as e:
        current_app.logger.error(f"背景处理失败: {e}")
        return jsonify({
            'success': False,
            'message': f'背景处理失败: {str(e)}'
        }), 500

@api_bp.route('/processing/final-size', methods=['POST'])
@jwt_required()
def process_final_size_step():
    """最终尺寸处理步骤"""
    current_user_id = int(get_jwt_identity())
    user = User.query.get(current_user_id)
    
    if not user:
        return unauthorized('用户不存在')
    
    try:
        data = request.get_json()
        if not data:
            return bad_request('请求数据为空')
        
        image_data = data.get('image_data')
        template = data.get('template')
        
        if not image_data:
            return bad_request('请提供图片数据')
        
        if not template:
            return bad_request('请提供证件照模板')
        
        # 解码base64图片
        import base64
        import io
        if 'data:image' in image_data:
            image_data = image_data.split(',')[1]
        
        img_bytes = base64.b64decode(image_data)
        img_pil = PILImage.open(io.BytesIO(img_bytes))
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        
        # 在规格选择步骤，暂时不调整尺寸，保持原始尺寸
        # 只记录模板信息，真正的尺寸调整在最后裁剪时进行
        processed_img = img_cv
        current_app.logger.info(f"规格选择完成，保持原始尺寸: {processed_img.shape[:2]}")
        
        # 转换为base64返回 - 使用高质量PNG避免压缩损失
        processed_pil = PILImage.fromarray(cv2.cvtColor(processed_img, cv2.COLOR_BGR2RGB))
        buffer = io.BytesIO()
        processed_pil.save(buffer, format='PNG', quality=100)
        buffer.seek(0)
        
        img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        
        return jsonify({
            'success': True,
            'processed_image': f'data:image/png;base64,{img_base64}',
            'message': '尺寸处理完成'
        })
        
    except Exception as e:
        current_app.logger.error(f"尺寸处理失败: {e}")
        return jsonify({
            'success': False,
            'message': f'尺寸处理失败: {str(e)}'
        }), 500

@api_bp.route('/memory/status', methods=['GET'])
def get_memory_status():
    """获取内存使用状态"""
    try:
        from app.utils.memory_monitor import get_memory_summary, check_memory_health
        
        summary = get_memory_summary()
        health = check_memory_health()
        
        return jsonify({
            'success': True,
            'memory_summary': summary,
            'memory_health': health
        })
        
    except Exception as e:
        current_app.logger.error(f"获取内存状态失败: {e}")
        return jsonify({'error': f'获取失败: {str(e)}'}), 500

@api_bp.route('/memory/cleanup', methods=['POST'])
def force_memory_cleanup():
    """强制内存清理"""
    try:
        from app.utils.memory_monitor import force_memory_cleanup
        
        # 执行清理
        force_memory_cleanup()
        
        # 获取清理后的状态
        from app.utils.memory_monitor import get_memory_summary
        summary = get_memory_summary()
        
        return jsonify({
            'success': True,
            'message': '内存清理完成',
            'memory_summary': summary
        })
        
    except Exception as e:
        current_app.logger.error(f"内存清理失败: {e}")
        return jsonify({'error': f'清理失败: {str(e)}'}), 500
