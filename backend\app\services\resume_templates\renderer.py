"""简历模板渲染器

此模块负责将简历数据按照指定模板渲染成HTML、PDF或DOCX格式。
"""

import os
from typing import Dict, Any, Optional
from jinja2 import Environment, FileSystemLoader
from docxtpl import DocxTemplate
from . import basic

class ResumeRenderer:
    """Resume template renderer class."""
    
    def __init__(self):
        """Initialize the renderer."""
        self.templates_dir = os.path.join(os.path.dirname(__file__), 'templates')
        self.env = Environment(
            loader=FileSystemLoader(self.templates_dir),
            autoescape=True
        )
        
        # 只保留两个模板
        self.available_templates = {
            'basic': basic.get_basic_template(),
            'modern_two_column': basic.get_modern_two_column_template()
        }

    def get_template_info(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板信息"""
        return self.available_templates.get(template_id)

    def list_templates(self) -> Dict[str, Dict[str, Any]]:
        """列出所有可用模板"""
        return {
            template_id: {
                'id': template['id'],
                'name': template['name'],
                'description': template['description'],
                'preview_image': template['preview_image'],
                'category': template.get('category', 'general')
            }
            for template_id, template in self.available_templates.items()
        }

    def render_html(self, resume_data: Dict[str, Any], template_id: str = 'basic') -> str:
        """渲染简历HTML"""
        if template_id not in self.available_templates:
            raise ValueError(f"Template {template_id} not found")
            
        template_info = self.available_templates[template_id]
        template = self.env.get_template(f"{template_id}/resume.html")
        
        return template.render(resume=resume_data)

    def render_pdf(self, resume_data: Dict[str, Any], template_id: str = 'basic') -> bytes:
        """渲染简历PDF（暂未实现）"""
        raise NotImplementedError("PDF rendering is not implemented yet")

    def render_docx(self, resume_data: Dict[str, Any], template_id: str = 'basic') -> bytes:
        """渲染简历DOCX"""
        template_info = self.get_template_info(template_id)
        template_path = template_info['export_settings']['docx']['template_path']
        
        if not os.path.exists(template_path):
            raise FileNotFoundError(f"DOCX template not found: {template_path}")
        
        doc = DocxTemplate(template_path)
        
        # 准备渲染上下文
        context = {
            'resume': resume_data,
            'template': template_info
        }
        
        # 应用样式设置
        doc.render(context)
        
        # 返回渲染后的文档
        output = bytes()
        doc.save(output)
        return output

    def preview_template(self, template_id: str) -> Dict[str, Any]:
        """获取模板预览信息"""
        template_info = self.get_template_info(template_id)
        if not template_info:
            raise ValueError(f"Template {template_id} not found")
            
        return {
            'template': template_info,
            'sample_html': self.render_html(self._get_sample_resume(), template_id)
        }
        
    def _get_sample_resume(self) -> Dict[str, Any]:
        """获取示例简历数据用于预览"""
        return {
            'title': '高级软件工程师简历',
            'full_name': '张三',
            'email': '<EMAIL>',
            'phone': '13800138000',
            'summary': '具有8年软件开发经验，专注于后端系统架构设计和团队管理。精通Python、Java等编程语言，具有丰富的分布式系统开发经验。',
            'educations': [
                {
                    'school_name': '清华大学',
                    'major': '计算机科学与技术',
                    'degree': '硕士',
                    'start_date': '2012-09',
                    'end_date': '2015-07'
                }
            ],
            'work_experiences': [
                {
                    'company_name': '某科技有限公司',
                    'position': '高级后端工程师',
                    'start_date': '2015-08',
                    'end_date': '至今',
                    'description': '负责公司核心业务系统的架构设计和开发',
                    'achievements': [
                        '设计并实现了新一代微服务架构',
                        '带领团队完成系统重构，性能提升300%',
                        '获得年度最佳员工奖'
                    ]
                }
            ],
            'skills': [
                {'name': 'Python', 'level': 90},
                {'name': 'Java', 'level': 85},
                {'name': 'Docker', 'level': 80},
                {'name': 'Kubernetes', 'level': 75}
            ]
        } 