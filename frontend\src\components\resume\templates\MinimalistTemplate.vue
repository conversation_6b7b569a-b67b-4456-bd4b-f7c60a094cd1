<template>
  <div class="resume-content-wrapper minimalist-template">
    <!-- 头部信息 -->
    <header class="resume-header">
      <div class="header-main">
        <div class="personal-info">
          <h1 class="full-name">{{ formattedResumeData.full_name }}</h1>
          <div class="contact-info">
            <span v-if="formattedResumeData.phone" class="contact-item">{{ formattedResumeData.phone }}</span>
            <span v-if="formattedResumeData.email" class="contact-item">{{ formattedResumeData.email }}</span>
            <span v-if="formattedResumeData.address" class="contact-item">{{ formattedResumeData.address }}</span>
          </div>
        </div>
        <div v-if="displayPhotoUrl" class="photo-container">
          <img :src="displayPhotoUrl" alt="个人照片" class="photo">
        </div>
      </div>
    </header>

    <!-- 求职意向 -->
    <section v-if="formattedResumeData.objective" class="resume-section">
      <h3 class="section-title">求职意向</h3>
      <div class="section-content">
        <p class="objective-text">{{ formattedResumeData.objective }}</p>
      </div>
    </section>

    <!-- 个人总结 -->
    <section v-if="formattedResumeData.summary" class="resume-section">
      <h3 class="section-title">个人总结</h3>
      <div class="section-content">
        <p class="summary-text">{{ formattedResumeData.summary }}</p>
      </div>
    </section>

    <!-- 工作经历 -->
    <section v-if="formattedResumeData.work_experiences?.length" class="resume-section">
      <h3 class="section-title">工作经历</h3>
      <div class="section-content">
        <div v-for="exp in formattedResumeData.work_experiences" :key="exp.company_name" class="experience-item">
          <div class="experience-header">
            <h4 class="company-name">{{ exp.company_name }}</h4>
            <span class="period">{{ exp.period }}</span>
          </div>
          <div class="position">{{ exp.position }}</div>
          <p v-if="exp.description" class="description">{{ exp.description }}</p>
          <ul v-if="exp.achievements?.length" class="achievements">
            <li v-for="(achievement, index) in exp.achievements" :key="index">
              {{ achievement }}
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 教育背景 -->
    <section v-if="formattedResumeData.educations?.length" class="resume-section">
      <h3 class="section-title">教育背景</h3>
      <div class="section-content">
        <div v-for="edu in formattedResumeData.educations" :key="edu.school_name" class="education-item">
          <div class="education-header">
            <h4 class="school-name">{{ edu.school_name }}</h4>
            <span class="period">{{ edu.period }}</span>
          </div>
          <div class="major">{{ edu.major }} · {{ edu.degree }}</div>
          <p v-if="edu.description" class="description">{{ edu.description }}</p>
        </div>
      </div>
    </section>

    <!-- 项目经验 -->
    <section v-if="formattedResumeData.projects?.length" class="resume-section">
      <h3 class="section-title">项目经验</h3>
      <div class="section-content">
        <div v-for="project in formattedResumeData.projects" :key="project.name" class="project-item">
          <div class="project-header">
            <h4 class="project-name">{{ project.name }}</h4>
            <span class="period">{{ project.period }}</span>
          </div>
          <p v-if="project.description" class="description">{{ project.description }}</p>
          <div v-if="project.technologies?.length" class="technologies">
            <span v-for="tech in project.technologies" :key="tech" class="tech-tag">
              {{ tech }}
            </span>
          </div>
        </div>
      </div>
    </section>

    <!-- 技能特长 -->
    <section v-if="formattedResumeData.skills?.length" class="resume-section">
      <h3 class="section-title">技能特长</h3>
      <div class="section-content">
        <div class="skills-list">
          <div v-for="skill in formattedResumeData.skills" :key="skill.name" class="skill-item">
            <span class="skill-name">{{ skill.name }}</span>
            <span class="skill-level">{{ skill.level }}%</span>
          </div>
        </div>
      </div>
    </section>

    <!-- 证书资质 -->
    <section v-if="formattedResumeData.certifications?.length" class="resume-section">
      <h3 class="section-title">证书资质</h3>
      <div class="section-content">
        <div class="certifications-list">
          <div v-for="cert in formattedResumeData.certifications" :key="cert.name" class="certification-item">
            <span class="cert-name">{{ cert.name }}</span>
            <span v-if="cert.date" class="cert-date">{{ cert.date }}</span>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { computed, watch } from 'vue'

export default {
  name: 'MinimalistTemplate',
  
  props: {
    resumeData: {
      type: Object,
      required: true,
      validator(value) {
        const requiredFields = ['id', 'title', 'full_name']
        const hasRequiredFields = requiredFields.every(field => 
          value[field] !== undefined && value[field] !== null
        )
        
        const arrayFields = ['work_experiences', 'educations', 'projects', 'skills', 'certifications']
        const hasValidArrays = arrayFields.every(field => 
          !value[field] || Array.isArray(value[field])
        )
        
        return hasRequiredFields && hasValidArrays
      }
    }
  },

  emits: ['error'],

  setup(props, { emit }) {
    const formattedResumeData = computed(() => {
      try {
        const data = props.resumeData

        const work_experiences = (data.work_experiences || []).map(exp => {
          return {
            ...exp,
            period: exp.start_date && exp.end_date ? `${exp.start_date} - ${exp.end_date}` : '',
            achievements: Array.isArray(exp.achievements) ? exp.achievements : []
          }
        })

        const educations = (data.educations || []).map(edu => {
          return {
            ...edu,
            period: edu.start_date && edu.end_date ? `${edu.start_date} - ${edu.end_date}` : ''
          }
        })

        const projects = (data.projects || []).map(proj => {
          return {
            ...proj,
            period: proj.start_date && proj.end_date ? `${proj.start_date} - ${proj.end_date}` : '',
            technologies: Array.isArray(proj.technologies) ? proj.technologies : [],
            achievements: Array.isArray(proj.achievements) ? proj.achievements : []
          }
        })

        const skills = (data.skills || []).map(skill => {
          return {
            ...skill,
            level: typeof skill.level === 'number' ? skill.level : 
                   skill.proficiency === '专家' ? 100 :
                   skill.proficiency === '高级' ? 80 :
                   skill.proficiency === '中级' ? 60 :
                   skill.proficiency === '初级' ? 40 : 20
          }
        })

        const certifications = (data.certifications || []).map(cert => {
          return {
            ...cert,
            date: cert.issue_date || cert.date || ''
          }
        })

        return {
          ...data,
          work_experiences,
          educations,
          projects,
          skills,
          certifications
        }
      } catch (err) {
        console.error('简历数据格式化失败:', err)
        emit('error', '简历数据格式化失败')
        return props.resumeData
      }
    })

    const displayPhotoUrl = computed(() => {
      const url = props.resumeData.thumbnail_url || props.resumeData.photo_url
      if (!url) return ''
      if (url.startsWith('http')) return url
      // 兼容相对路径
      return (window.API_BASE_URL || 'http://localhost:5000') + url
    })

    watch(() => props.resumeData, (newData) => {
      if (!newData) {
        console.warn('模板接收到空数据')
        return
      }
    }, { immediate: true })

    return {
      formattedResumeData,
      displayPhotoUrl
    }
  }
}
</script>

<style scoped>
.minimalist-template {
  font-family: 'Helvetica Neue', 'Arial', sans-serif;
  color: #333;
  font-size: 14px;
  line-height: 1.25;
  padding: 3cm 2cm;
  background: white;
  max-width: 21cm;
  margin: 0 auto;
}

/* 头部样式 */
.resume-header {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #ddd;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 3rem;
}

.personal-info {
  flex: 1;
}

.full-name {
  font-size: 29px;
  font-weight: 300;
  margin: 4rem 0 0.5rem 0;
  color: #333;
  letter-spacing: 1px;
}

.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  font-size: 14px;
  color: #666;
}

.contact-item {
  display: inline-block;
  font-size: 14px;
}

.photo-container {
  width: 100px;
  height: 130px;
  overflow: hidden;
  border-radius: 2px;
  flex-shrink: 0;
}

.photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 章节样式 */
.resume-section {
  margin-bottom: 2rem;
  break-inside: avoid;
}

.section-title {
  font-size: 17px;
  font-weight: 400;
  color: #333;
  margin: 0 0 0.75rem 0;
  text-transform: uppercase;
  letter-spacing: 2px;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.25rem;
}

.section-content {
  padding: 0;
  font-size: 14px;
}

/* 经验项目通用样式 */
.experience-item,
.education-item,
.project-item {
  margin-bottom: 1.5rem;
  break-inside: avoid;
}

.experience-header,
.education-header,
.project-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.company-name,
.school-name,
.project-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.period {
  color: #999;
  font-size: 12px;
  font-weight: 300;
}

.position,
.major {
  font-weight: 400;
  color: #666;
  margin-bottom: 0.8rem;
  font-size: 15px;
}

.description {
  color: #666;
  margin: 0.8rem 0;
  line-height: 0.8;
  white-space: pre-line;
  font-size: 14px;
}

/* 技能样式 */
.skills-list {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.2rem;
}

.skill-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f5f5f5;
  font-size: 14px;
  background: none;
}

.skill-name {
  font-size: 13px;
  color: #333;
  font-weight: 400;
}

.skill-level {
  font-size: 12px;
  color: #999;
  font-weight: 300;
}

/* 证书列表样式 */
.certifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.certification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f5f5f5;
  font-size: 13px;
}

.cert-name {
  font-weight: 400;
  color: #333;
}

.cert-date {
  color: #999;
  font-weight: 300;
}

/* 成就列表样式 */
.achievements {
  list-style: none;
  padding: 0;
  margin: 0.8rem 0 0;
}

.achievements li {
  position: relative;
  padding-left: 1rem;
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 13px;
  line-height: 0.8;
}

.achievements li::before {
  content: "—";
  position: absolute;
  left: 0;
  color: #ccc;
}

/* 技术标签样式 */
.technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.8rem;
}

.tech-tag {
  background: #f8f9fa;
  color: #666;
  padding: 0.2rem 0.6rem;
  border: 1px solid #e9ecef;
  font-size: 11px;
  font-weight: 300;
}

.summary-text,
.objective-text {
  color: #666;
  line-height: 0.8;
  margin: 0;
  white-space: pre-line;
  font-size: 14px;
}

/* 打印样式优化 */
@media print {
  .minimalist-template {
    box-shadow: none;
    padding: 0;
  }

  .resume-section {
    page-break-inside: avoid;
  }

  .experience-item,
  .education-item,
  .project-item,
  .skill-item,
  .certification-item {
    background: none;
    padding: 0;
  }

  .section-title {
    border-bottom: 1px solid #eee;
  }
}
</style> 