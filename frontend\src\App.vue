<template>
  <div id="app">
    <!-- 顶部导航栏 -->
    <NavBar v-if="showNavBar" />
    
    <!-- 主内容区域 -->
    <main class="main-content" :class="{ 'with-nav': showNavBar }">
      <router-view v-slot="{ Component, route }">
        <transition name="fade" mode="out-in">
          <component :is="Component" :key="route.path" />
        </transition>
      </router-view>
    </main>
    
    <!-- 全局加载指示器 -->
    <div v-if="globalLoading" class="global-loading">
      <el-loading-directive
        :loading="true"
        element-loading-text="处理中..."
        element-loading-background="rgba(0, 0, 0, 0.8)"
      />
    </div>
    
    <!-- 全局消息提示 -->
    <MessageBox />
  </div>
</template>

<script>
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from './stores/auth'
import { useAppStore } from './stores/app'
import NavBar from './components/layout/NavBar.vue'
import MessageBox from './components/common/MessageBox.vue'

export default {
  name: 'App',
  components: {
    NavBar,
    MessageBox
  },
  setup() {
    const route = useRoute()
    const authStore = useAuthStore()
    const appStore = useAppStore()
    
    // 计算是否显示导航栏
    const showNavBar = computed(() => {
      const hideNavRoutes = ['/login', '/register', '/404', '/500']
      return !hideNavRoutes.includes(route.path)
    })
    
    // 全局加载状态
    const globalLoading = computed(() => appStore.loading)
    
    // 应用初始化
    onMounted(async () => {
      try {
        console.log('开始应用初始化...')
        
        // 加载应用配置
        await appStore.loadConfig()
        
        // 初始化认证状态
        await authStore.initAuth()
        
        // 调试认证状态
        authStore.debugAuthState()
        
        console.log('应用初始化完成')
      } catch (error) {
        console.error('应用初始化失败:', error)
      }
    })
    
    return {
      showNavBar,
      globalLoading
    }
  }
}
</script>

<style scoped>
#app {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.main-content {
  min-height: 100vh;
  transition: padding-top 0.3s ease;
}

.main-content.with-nav {
  padding-top: 60px; /* 导航栏高度 */
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content.with-nav {
    padding-top: 50px;
  }
}
</style> 