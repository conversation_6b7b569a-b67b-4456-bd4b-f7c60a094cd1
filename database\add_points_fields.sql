-- 添加积分字段到用户表
-- 执行时间: 2024-01-01
-- 兼容SQLite语法

-- 检查并添加points字段
PRAGMA foreign_keys=off;

-- 创建临时表
CREATE TABLE users_temp AS SELECT * FROM users;

-- 删除原表
DROP TABLE users;

-- 重新创建表结构（包含新字段）
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    nickname VARCHAR(100),
    gender INTEGER,
    birthday DATE,
    status INTEGER DEFAULT 1,
    vip_level INTEGER DEFAULT 0,
    vip_expire_time DATETIME,
    total_credits INTEGER DEFAULT 100,
    used_credits INTEGER DEFAULT 0,
    points INTEGER DEFAULT 0,
    points_used INTEGER DEFAULT 0,
    last_login_time DATETIME,
    last_login_ip VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 复制数据
INSERT INTO users SELECT 
    id, username, email, phone, password_hash, avatar_url, nickname, 
    gender, birthday, status, vip_level, vip_expire_time, 
    total_credits, used_credits, 
    COALESCE(total_credits, 0) as points, 
    COALESCE(used_credits, 0) as points_used,
    last_login_time, last_login_ip, created_at, updated_at
FROM users_temp;

-- 删除临时表
DROP TABLE users_temp;

-- 重新创建索引
CREATE INDEX idx_username ON users (username);
CREATE INDEX idx_email ON users (email);
CREATE INDEX idx_phone ON users (phone);
CREATE INDEX idx_users_points ON users (points);
CREATE INDEX idx_users_points_used ON users (points_used);

PRAGMA foreign_keys=on;

-- 创建积分日志表
CREATE TABLE points_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    type VARCHAR(50) NOT NULL,
    change INTEGER NOT NULL,
    balance INTEGER NOT NULL,
    remark VARCHAR(255),
    related_id INTEGER,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建积分配置表
CREATE TABLE points_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认积分配置
INSERT OR IGNORE INTO points_configs (config_key, config_value, description) VALUES
('register_reward', '100', '注册奖励积分'),
('invite_reward', '50', '邀请奖励积分'),
('ad_reward', '10', '广告任务奖励积分'),
('photo_process_cost', '1', '证件照处理消耗积分'),
('document_compare_cost', '2', '文档对比消耗积分'),
('resume_generate_cost', '5', '简历生成消耗积分'),
('recharge_rate', '1', '充值比例（1元=1积分）'),
('min_recharge', '10', '最小充值金额'),
('max_recharge', '1000', '最大充值金额');

-- 创建支付订单表
CREATE TABLE payment_orders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_no VARCHAR(50) NOT NULL UNIQUE,
    user_id INTEGER NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    points INTEGER NOT NULL,
    payment_method VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    transaction_id VARCHAR(100),
    callback_data TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    paid_at DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建邀请记录表
CREATE TABLE invitations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    inviter_id INTEGER NOT NULL,
    invitee_id INTEGER NOT NULL,
    invite_code VARCHAR(20) NOT NULL,
    reward_paid INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (inviter_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invitee_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建广告任务记录表
CREATE TABLE ad_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    task_id VARCHAR(100) NOT NULL,
    reward INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    completed_at DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX idx_points_logs_user_id ON points_logs (user_id);
CREATE INDEX idx_points_logs_type ON points_logs (type);
CREATE INDEX idx_points_logs_created_at ON points_logs (created_at);
CREATE INDEX idx_points_logs_user_type ON points_logs (user_id, type);

CREATE INDEX idx_payment_orders_order_no ON payment_orders (order_no);
CREATE INDEX idx_payment_orders_user_id ON payment_orders (user_id);
CREATE INDEX idx_payment_orders_status ON payment_orders (status);
CREATE INDEX idx_payment_orders_created_at ON payment_orders (created_at);
CREATE INDEX idx_payment_orders_user_status ON payment_orders (user_id, status);

CREATE INDEX idx_invitations_inviter_id ON invitations (inviter_id);
CREATE INDEX idx_invitations_invitee_id ON invitations (invitee_id);
CREATE INDEX idx_invitations_invite_code ON invitations (invite_code);

CREATE INDEX idx_ad_tasks_user_id ON ad_tasks (user_id);
CREATE INDEX idx_ad_tasks_task_type ON ad_tasks (task_type);
CREATE INDEX idx_ad_tasks_status ON ad_tasks (status);
CREATE INDEX idx_ad_tasks_created_at ON ad_tasks (created_at);
CREATE INDEX idx_ad_tasks_user_type ON ad_tasks (user_id, task_type); 