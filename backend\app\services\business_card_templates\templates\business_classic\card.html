<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="商务经典风格名片">
    <title>{{ card.name|default('名片', true) }}</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ style.font_family|default('Arial, sans-serif', true) }};
            background-color: {{ style.background_color|default('#ffffff', true) }};
            line-height: 1.4;
            color: {{ style.text_color|default('#333333', true) }};
        }
        
        /* 名片容器 */
        .business-card {
            width: 90mm;
            height: 54mm;
            padding: 8mm;
            background: {{ style.background_color|default('#ffffff', true) }};
            border: 1px solid {{ style.border_color|default('#e0e0e0', true) }};
            font-family: {{ style.font_family|default('Arial, sans-serif', true) }};
            position: relative;
            overflow: hidden;
        }
        
        /* 头部区域 */
        .business-classic .header {
            margin-bottom: 4mm;
        }
        
        .business-classic .name {
            font-size: {{ style.name_size|default('18px', true) }};
            font-weight: {{ style.name_weight|default('bold', true) }};
            color: {{ style.primary_color|default('#2c3e50', true) }};
            margin: 0 0 2mm 0;
            line-height: 1.2;
        }
        
        .business-classic .title {
            font-size: {{ style.title_size|default('12px', true) }};
            color: {{ style.text_color|default('#333333', true) }};
            margin: 0;
            font-weight: normal;
        }
        
        /* 公司信息 */
        .business-classic .company {
            margin-bottom: 4mm;
        }
        
        .business-classic .company-name {
            font-size: 14px;
            color: {{ style.secondary_color|default('#7f8c8d', true) }};
            font-weight: 500;
            margin: 0;
        }
        
        /* 联系信息 */
        .business-classic .contact {
            font-size: 11px;
            line-height: 1.4;
        }
        
        .business-classic .contact-item {
            margin-bottom: 1mm;
            color: {{ style.light_color|default('#666666', true) }};
            display: flex;
            align-items: center;
            min-height: 3mm;
        }
        
        .business-classic .contact-item i {
            margin-right: 2mm;
            color: {{ style.secondary_color|default('#7f8c8d', true) }};
            font-size: 10px;
            width: 4mm;
            text-align: center;
            flex-shrink: 0;
        }
        
        .business-classic .contact-item span {
            word-break: break-all;
        }
        
        /* 二维码 */
        .business-classic .qr-code {
            position: absolute;
            right: 8mm;
            bottom: 8mm;
            width: 12mm;
            height: 12mm;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .business-classic .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
        }
        
        /* 图标样式 */
        .icon-phone::before { content: "📞"; }
        .icon-email::before { content: "📧"; }
        .icon-location::before { content: "📍"; }
        .icon-website::before { content: "🌐"; }
        
        /* 响应式设计 */
        @media print {
            body {
                background: white;
            }
            .business-card {
                border: none;
                box-shadow: none;
            }
        }
        
        /* 可访问性增强 */
        .business-classic .name:focus,
        .business-classic .title:focus,
        .business-classic .company-name:focus {
            outline: 2px solid {{ style.primary_color|default('#2c3e50', true) }};
            outline-offset: 1px;
        }
    </style>
</head>
<body>
    <div class="business-card business-classic" role="article" aria-label="商务名片">
        <header class="header">
            <h1 class="name" id="card-name">{{ card.name|default('姓名', true) }}</h1>
            <p class="title" id="card-title">{{ card.title|default('职位', true) }}</p>
        </header>
        
        <section class="company" aria-labelledby="card-company">
            <p class="company-name" id="card-company">{{ card.company|default('公司名称', true) }}</p>
        </section>
        
        <section class="contact" aria-label="联系信息">
            {% if card.phone %}
            <div class="contact-item" role="group" aria-labelledby="phone-label">
                <i class="icon-phone" aria-hidden="true"></i>
                <span id="phone-label">{{ card.phone }}</span>
            </div>
            {% endif %}
            
            {% if card.email %}
            <div class="contact-item" role="group" aria-labelledby="email-label">
                <i class="icon-email" aria-hidden="true"></i>
                <span id="email-label">{{ card.email }}</span>
            </div>
            {% endif %}
            
            {% if card.address %}
            <div class="contact-item" role="group" aria-labelledby="address-label">
                <i class="icon-location" aria-hidden="true"></i>
                <span id="address-label">{{ card.address }}</span>
            </div>
            {% endif %}
            
            {% if card.website %}
            <div class="contact-item" role="group" aria-labelledby="website-label">
                <i class="icon-website" aria-hidden="true"></i>
                <span id="website-label">{{ card.website }}</span>
            </div>
            {% endif %}
        </section>
        
        {% if qr_code_url %}
        <div class="qr-code" role="img" aria-label="名片二维码">
            <img src="{{ qr_code_url }}" alt="二维码" loading="lazy">
        </div>
        {% endif %}
    </div>
</body>
</html> 