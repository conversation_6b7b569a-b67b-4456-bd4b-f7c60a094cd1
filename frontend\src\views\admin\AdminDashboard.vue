<template>
  <div class="admin-dashboard">
    <!-- 侧边栏 -->
    <div class="admin-sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <span class="logo-icon">⚙️</span>
          <span v-if="!sidebarCollapsed" class="logo-text">管理后台</span>
        </div>
        <button class="collapse-btn" @click="toggleSidebar">
          <span class="collapse-icon">{{ sidebarCollapsed ? '→' : '←' }}</span>
        </button>
      </div>
      
      <nav class="sidebar-nav">
        <div 
          v-for="menu in menuItems" 
          :key="menu.key"
          class="menu-group"
        >
          <div class="menu-title" v-if="!sidebarCollapsed">{{ menu.title }}</div>
          <div class="menu-items">
            <router-link
              v-for="item in menu.items"
              :key="item.key"
              :to="item.path"
              class="menu-item"
              :class="{ active: currentRoute === item.path }"
              :title="sidebarCollapsed ? item.label : ''"
            >
              <span class="menu-icon">{{ item.icon }}</span>
              <span v-if="!sidebarCollapsed" class="menu-label">{{ item.label }}</span>
            </router-link>
          </div>
        </div>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="admin-main" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- 顶部导航栏 -->
      <div class="admin-header">
        <div class="header-left">
          <h1 class="page-title">{{ currentPageTitle }}</h1>
        </div>
        <div class="header-right">
          <div class="admin-info">
            <span class="admin-name">{{ authStore.user?.username }}</span>
            <el-dropdown @command="handleAdminAction">
              <el-avatar :size="32" :src="authStore.user?.avatar_url">
                {{ authStore.user?.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人设置</el-dropdown-item>
                  <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="admin-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const sidebarCollapsed = ref(false)

// 菜单配置
const menuItems = [
  {
    title: '用户管理',
    items: [
      { key: 'users', label: '用户列表', icon: '👥', path: '/admin/users' },
      { key: 'user-points', label: '积分管理', icon: '💰', path: '/admin/points' },
      { key: 'user-logs', label: '用户日志', icon: '📊', path: '/admin/user-logs' }
    ]
  },
  {
    title: '内容管理',
    items: [
      { key: 'content', label: '内容管理', icon: '📁', path: '/admin/content' },
    ]
  },
  {
    title: '系统配置',
    items: [
      { key: 'system-config', label: '系统设置', icon: '⚙️', path: '/admin/system' },
      { key: 'templates', label: '模板管理', icon: '🎨', path: '/admin/templates' }
    ]
  },
  {
    title: '数据统计',
    items: [
      { key: 'statistics', label: '数据概览', icon: '📈', path: '/admin/statistics' },
      { key: 'reports', label: '报表中心', icon: '📋', path: '/admin/reports' }
    ]
  }
]

// 计算属性
const currentRoute = computed(() => route.path)

const currentPageTitle = computed(() => {
  const allItems = menuItems.flatMap(menu => menu.items)
  const currentItem = allItems.find(item => item.path === route.path)
  return currentItem ? currentItem.label : '管理后台'
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const handleAdminAction = (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      authStore.logout()
      router.push('/login')
      ElMessage.success('已退出登录')
      break
  }
}

// 检查管理员权限
const checkAdminPermission = () => {
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }
  
  // 检查是否为管理员（这里可以根据实际需求调整判断逻辑）
  if (authStore.user?.username !== 'admin') {
    ElMessage.error('无权限访问管理后台')
    router.push('/')
    return
  }
}

onMounted(() => {
  checkAdminPermission()
})
</script>

<style scoped>
.admin-dashboard {
  display: flex;
  min-height: 100vh;
  background: #f5f7fa;
}

/* 侧边栏样式 */
.admin-sidebar {
  width: 260px;
  background: #2c3e50;
  color: #ecf0f1;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: 1000;
}

.admin-sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #34495e;
  min-height: 70px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  font-size: 24px;
}

.logo-text {
  font-size: 18px;
  font-weight: bold;
}

.collapse-btn {
  background: none;
  border: none;
  color: #ecf0f1;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.collapse-btn:hover {
  background: #34495e;
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.menu-group {
  margin-bottom: 30px;
}

.menu-title {
  padding: 0 20px 10px;
  font-size: 12px;
  font-weight: bold;
  color: #95a5a6;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.menu-items {
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #bdc3c7;
  text-decoration: none;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.menu-item:hover {
  background: #34495e;
  color: #ecf0f1;
}

.menu-item.active {
  background: #3498db;
  color: #fff;
  border-left-color: #2980b9;
}

.menu-icon {
  font-size: 18px;
  min-width: 20px;
  text-align: center;
}

.menu-label {
  font-size: 14px;
  white-space: nowrap;
}

/* 主内容区域样式 */
.admin-main {
  flex: 1;
  margin-left: 260px;
  transition: margin-left 0.3s ease;
  display: flex;
  flex-direction: column;
}

.admin-main.sidebar-collapsed {
  margin-left: 60px;
}

.admin-header {
  background: #fff;
  border-bottom: 1px solid #e1e8ed;
  padding: 0 30px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.admin-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.admin-name {
  font-size: 14px;
  color: #7f8c8d;
}

.admin-content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(-100%);
  }
  
  .admin-sidebar.collapsed {
    transform: translateX(0);
    width: 260px;
  }
  
  .admin-main {
    margin-left: 0;
  }
  
  .admin-main.sidebar-collapsed {
    margin-left: 0;
  }
}
</style> 