<template>
  <div class="card-wrapper business-classic" :style="styleVariables">
    <div class="left-bar"></div>
    <div class="main-content">
      <div class="identity">
        <h2 class="name" :style="nameStyle">{{ cardData.name || '姓名' }}</h2>
        <p class="title" v-if="cardData.title" :style="titleStyle">{{ cardData.title }}</p>
      </div>
      <div class="divider"></div>
      <div class="contact-info">
        <div class="contact-item" v-if="cardData.phone" :style="contactStyle">
          <svg class="icon" viewBox="0 0 1024 1024"><path d="M896 64H128C92.672 64 64 92.672 64 128v768c0 35.328 28.672 64 64 64h768c35.328 0 64-28.672 64-64V128c0-35.328-28.672-64-64-64zM256 192h512v128H256V192z m640 640H128V448h768v384z" fill="currentColor"></path></svg>
          <span>{{ cardData.phone }}</span>
        </div>
        <div class="contact-item" v-if="cardData.email" :style="contactStyle">
          <svg class="icon" viewBox="0 0 1024 1024"><path d="M512 562.176L64 278.912V128c0-35.328 28.672-64 64-64h768c35.328 0 64 28.672 64 64v150.912L512 562.176zM64 364.8v499.2c0 35.328 28.672 64 64 64h768c35.328 0 64-28.672 64-64V364.8L512 640 64 364.8z" fill="currentColor"></path></svg>
          <span>{{ cardData.email }}</span>
        </div>
        <div class="contact-item" v-if="cardData.address" :style="contactStyle">
          <svg class="icon" viewBox="0 0 1024 1024"><path d="M512 64C337.92 64 192 210.048 192 384c0 133.248 83.2 250.88 204.8 307.2L512 960l115.2-268.8C748.8 634.88 832 517.248 832 384 832 210.048 686.08 64 512 64z m0 512c-70.656 0-128-57.344-128-128s57.344-128 128-128 128 57.344 128 128-57.344 128-128 128z" fill="currentColor"></path></svg>
          <span>{{ cardData.address }}</span>
        </div>
        <div class="contact-item" v-if="cardData.website" :style="contactStyle">
          <svg class="icon" viewBox="0 0 1024 1024"><path d="M512 64C264.576 64 64 264.576 64 512s200.*********** ***********.576 448-448S759.424 64 512 64z m281.6 281.6L684.8 454.4c-19.2-19.2-51.2-19.2-70.4 0l-38.4 38.4-140.8-140.8 38.4-38.4c19.2-19.2 19.2-51.2 0-70.4L364.8 134.4c-19.2-19.2-51.2-19.2-70.4 0L224 204.8c-19.2 19.2-19.2 51.2 0 70.4l320 320c19.2 19.2 51.2 19.2 70.4 0l70.4-70.4c19.2-19.2 19.2-51.2 0-70.4z" fill="currentColor"></path></svg>
          <span>{{ cardData.website }}</span>
        </div>
      </div>
      <div class="company-bar" v-if="cardData.company">
        <span class="company-name" :style="companyStyle">{{ cardData.company }}</span>
      </div>
    </div>
    <div class="qr-code" v-if="qrCodeUrl">
      <img :src="qrCodeUrl" alt="二维码" class="qr-img" />
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';

export default {
  name: 'BusinessClassic',
  props: {
    cardData: {
      type: Object,
      required: true,
    },
    styleConfig: {
      type: Object,
      default: () => ({}),
    },
    qrCodeUrl: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const styleVariables = computed(() => {
      const config = props.styleConfig || {};
      const multiplier = (config.fontSize || 100) / 100;
      return {
        '--primary-color': config.primary || '#003366',
        '--secondary-color': config.secondary || '#FFFFFF',
        '--text-color': config.text || '#333333',
        '--light-text-color': config.light || '#666666',
        '--font-size-name': `${80 * multiplier}px`,
        '--font-size-title': `${45 * multiplier}px`,
        '--font-size-contact': `${40 * multiplier}px`,
        '--font-size-company': `${55 * multiplier}px`,
      };
    });

    const nameStyle = computed(() => ({
      color: props.styleConfig.nameColor || 'var(--primary-color)',
      fontWeight: props.styleConfig.nameFontStyle === 'bold' ? 'bold' : 'normal',
      fontStyle: props.styleConfig.nameFontStyle === 'italic' ? 'italic' : 'normal',
    }));

    const companyStyle = computed(() => ({
      color: props.styleConfig.companyColor || 'var(--primary-color)',
      fontWeight: props.styleConfig.companyFontStyle === 'bold' ? 'bold' : 'normal',
      fontStyle: props.styleConfig.companyFontStyle === 'italic' ? 'italic' : 'normal',
    }));

    const titleStyle = computed(() => ({
      color: props.styleConfig.text || 'var(--text-color)',
      fontWeight: props.styleConfig.textFontStyle === 'bold' ? 'bold' : 'normal',
      fontStyle: props.styleConfig.textFontStyle === 'italic' ? 'italic' : 'normal',
    }));

    const contactStyle = computed(() => ({
      color: props.styleConfig.light || 'var(--light-text-color)',
      fontWeight: props.styleConfig.lightFontStyle === 'bold' ? 'bold' : 'normal',
      fontStyle: props.styleConfig.lightFontStyle === 'italic' ? 'italic' : 'normal',
    }));

    return {
      styleVariables,
      nameStyle,
      companyStyle,
      titleStyle,
      contactStyle,
    };
  },
};
</script>

<style scoped>
.card-wrapper {
  width: 100%;
  height: 100%;
  background: #fff;
  font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  box-sizing: border-box;
  display: flex;
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  position: relative;
}
.left-bar {
  width: 6px;
  background: var(--primary-color);
  border-radius: 6px;
  margin-right: 24px;
  height: 80%;
  align-self: center;
}
.main-content {
  flex-grow: 1;
  padding: 32px 32px 16px 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-width: 0;
}
.identity .name {
  font-size: var(--font-size-name);
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0 4px 0;
  letter-spacing: 1px;
}
.identity .title {
  font-size: var(--font-size-title);
  color: var(--text-color);
  margin: 0 0 8px 0;
  opacity: 0.9;
}
.divider {
  width: 100%;
  height: 1px;
  background-color: var(--primary-color);
  margin: 12px 0 16px 0;
  opacity: 0.12;
}
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.contact-item {
  display: flex;
  align-items: center;
  font-size: var(--font-size-contact);
  color: var(--light-text-color);
  padding: 2px 0;
}
.contact-item .icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  color: var(--primary-color);
  flex-shrink: 0;
}
.company-bar {
  margin-top: auto;
  text-align: left;
  padding-top: 18px;
}
.company-name {
  font-size: var(--font-size-company);
  font-weight: 500;
  color: var(--primary-color);
  letter-spacing: 1px;
  opacity: 0.85;
}
.qr-code {
  position: absolute;
  right: 18px;
  bottom: 18px;
  width: 48px;
  height: 48px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px #0001;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qr-img {
  width: 80%;
  height: 80%;
  object-fit: contain;
  display: block;
}
</style> 