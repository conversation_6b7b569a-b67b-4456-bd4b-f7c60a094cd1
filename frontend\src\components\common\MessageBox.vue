<template>
  <div>
    <!-- 全局消息提示容器 -->
  </div>
</template>

<script>
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'

export default {
  name: 'MessageBox',
  setup(props, { expose }) {
    // 通用显示方法
    const show = (message, type = 'info', duration = 3000) => {
      ElMessage({
        message,
        type,
        duration
      })
    }
    
    // 成功消息
    const success = (message, duration = 3000) => {
      ElMessage({
        message,
        type: 'success',
        duration
      })
    }
    
    // 错误消息
    const error = (message, duration = 3000) => {
      ElMessage({
        message,
        type: 'error',
        duration
      })
    }
    
    // 警告消息
    const warning = (message, duration = 3000) => {
      ElMessage({
        message,
        type: 'warning',
        duration
      })
    }
    
    // 信息消息
    const info = (message, duration = 3000) => {
      ElMessage({
        message,
        type: 'info',
        duration
      })
    }
    
    // 确认对话框
    const confirm = (message, title = '提示', options = {}) => {
      return ElMessageBox.confirm(
        message,
        title,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          ...options
        }
      )
    }
    
    // 提示对话框
    const alert = (message, title = '提示', options = {}) => {
      return ElMessageBox.alert(
        message,
        title,
        {
          confirmButtonText: '确定',
          ...options
        }
      )
    }
    
    // 输入对话框
    const prompt = (message, title = '提示', options = {}) => {
      return ElMessageBox.prompt(
        message,
        title,
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          ...options
        }
      )
    }
    
    // 通知
    const notify = (options) => {
      if (typeof options === 'string') {
        options = { message: options }
      }
      
      ElNotification({
        title: '通知',
        duration: 4500,
        ...options
      })
    }

    // 暴露方法给父组件
    expose({
      show,
      success,
      error,
      warning,
      info,
      confirm,
      alert,
      prompt,
      notify
    })

    return {
      show,
      success,
      error,
      warning,
      info,
      confirm,
      alert,
      prompt,
      notify
    }
  }
}
</script>

<style scoped>
/* 组件样式 */
</style> 