-- 名片功能数据库初始化脚本

-- 名片模板表
CREATE TABLE `business_card_templates` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `description` TEXT DEFAULT NULL COMMENT '模板描述',
    `category` VARCHAR(50) DEFAULT 'business' COMMENT '分类(business/creative/minimal/elegant)',
    `industry` VARCHAR(50) DEFAULT 'other' COMMENT '适用行业',
    `preview_image` VARCHAR(500) DEFAULT NULL COMMENT '预览图URL',
    `layout_type` VARCHAR(30) DEFAULT 'standard' COMMENT '布局类型(standard/vertical/horizontal)',
    `width_mm` DECIMAL(5,2) DEFAULT 90.00 COMMENT '宽度(毫米)',
    `height_mm` DECIMAL(5,2) DEFAULT 54.00 COMMENT '高度(毫米)',
    `template_html` TEXT NOT NULL COMMENT '模板HTML结构',
    `template_css` TEXT NOT NULL COMMENT '模板CSS样式',
    `default_colors` JSON DEFAULT NULL COMMENT '默认颜色方案',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `usage_count` INT(11) DEFAULT 0 COMMENT '使用次数',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_industry` (`industry`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='名片模板表';

-- 用户名片表
CREATE TABLE `business_cards` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `template_id` INT(11) NOT NULL COMMENT '模板ID',
    `title` VARCHAR(200) NOT NULL COMMENT '名片标题',
    `card_data` JSON NOT NULL COMMENT '名片数据(姓名、职位、公司、联系方式等)',
    `style_config` JSON DEFAULT NULL COMMENT '样式配置(颜色、字体等)',
    `qr_code_data` TEXT DEFAULT NULL COMMENT '二维码数据',
    `qr_code_image` VARCHAR(500) DEFAULT NULL COMMENT '二维码图片路径',
    `status` VARCHAR(20) DEFAULT 'draft' COMMENT '状态(draft/published/archived)',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_template_id` (`template_id`),
    KEY `idx_status` (`status`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`template_id`) REFERENCES `business_card_templates`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户名片表';

-- 插入默认名片模板
INSERT INTO `business_card_templates` (`name`, `description`, `category`, `industry`, `layout_type`, `template_html`, `template_css`, `default_colors`, `sort_order`) VALUES

-- 1. 商务经典模板
('商务经典', '简洁大方的商务名片模板，适合各行各业', 'business', 'other', 'standard',
'<div class="business-card business-classic">
  <div class="header">
    <h1 class="name">{{name}}</h1>
    <p class="title">{{title}}</p>
  </div>
  <div class="company">
    <p class="company-name">{{company}}</p>
  </div>
  <div class="contact">
    <div class="contact-item" v-if="phone">
      <i class="icon-phone"></i>
      <span>{{phone}}</span>
    </div>
    <div class="contact-item" v-if="email">
      <i class="icon-email"></i>
      <span>{{email}}</span>
    </div>
    <div class="contact-item" v-if="address">
      <i class="icon-location"></i>
      <span>{{address}}</span>
    </div>
  </div>
  <div class="qr-code" v-if="qr_image">
    <img :src="qr_image" alt="二维码">
  </div>
</div>',
'.business-card { width: 90mm; height: 54mm; padding: 8mm; background: white; border: 1px solid #ddd; box-sizing: border-box; font-family: "Microsoft YaHei", Arial, sans-serif; position: relative; }
.business-classic .header { margin-bottom: 4mm; }
.business-classic .name { font-size: 18px; font-weight: bold; color: #2c3e50; margin: 0 0 2mm 0; }
.business-classic .title { font-size: 14px; color: #34495e; margin: 0; }
.business-classic .company { margin-bottom: 4mm; }
.business-classic .company-name { font-size: 14px; color: #3498db; font-weight: 500; margin: 0; }
.business-classic .contact { font-size: 11px; line-height: 1.4; }
.business-classic .contact-item { margin-bottom: 1mm; color: #7f8c8d; }
.business-classic .qr-code { position: absolute; right: 8mm; bottom: 8mm; width: 12mm; height: 12mm; }
.business-classic .qr-code img { width: 100%; height: 100%; }',
'{"primary": "#3498db", "secondary": "#2c3e50", "text": "#34495e", "light": "#7f8c8d"}', 1),

-- 2. 现代简约模板
('现代简约', '极简风格，注重留白和层次感', 'minimal', 'design', 'standard',
'<div class="business-card modern-minimal">
  <div class="content">
    <div class="name-section">
      <h1 class="name">{{name}}</h1>
      <div class="divider"></div>
      <p class="title">{{title}}</p>
    </div>
    <div class="company-section">
      <p class="company">{{company}}</p>
    </div>
    <div class="contact-section">
      <p class="contact-line">{{phone}}</p>
      <p class="contact-line">{{email}}</p>
    </div>
  </div>
  <div class="accent-bar"></div>
</div>',
'.modern-minimal { background: #fafafa; border: none; padding: 6mm 8mm; position: relative; }
.modern-minimal .content { height: 100%; display: flex; flex-direction: column; justify-content: space-between; }
.modern-minimal .name { font-size: 20px; font-weight: 300; color: #2c3e50; margin: 0; letter-spacing: 1px; }
.modern-minimal .divider { width: 30mm; height: 1px; background: #e74c3c; margin: 3mm 0; }
.modern-minimal .title { font-size: 12px; color: #7f8c8d; margin: 0; text-transform: uppercase; letter-spacing: 0.5px; }
.modern-minimal .company { font-size: 14px; color: #34495e; margin: 0; font-weight: 500; }
.modern-minimal .contact-line { font-size: 11px; color: #95a5a6; margin: 0 0 1mm 0; }
.modern-minimal .accent-bar { position: absolute; right: 0; top: 0; width: 3mm; height: 100%; background: linear-gradient(180deg, #e74c3c 0%, #c0392b 100%); }',
'{"primary": "#e74c3c", "secondary": "#2c3e50", "text": "#34495e", "light": "#95a5a6"}', 2),

-- 3. 创意设计模板
('创意设计', '富有创意的设计，适合设计师和创意工作者', 'creative', 'design', 'standard',
'<div class="business-card creative-design">
  <div class="background-pattern"></div>
  <div class="content">
    <div class="header">
      <div class="name-block">
        <h1 class="name">{{name}}</h1>
        <span class="name-bg">{{name}}</span>
      </div>
      <p class="title">{{title}}</p>
    </div>
    <div class="company">
      <p>{{company}}</p>
    </div>
    <div class="contact">
      <div class="contact-grid">
        <div class="contact-item">
          <span class="label">Phone</span>
          <span class="value">{{phone}}</span>
        </div>
        <div class="contact-item">
          <span class="label">Email</span>
          <span class="value">{{email}}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="qr-section" v-if="qr_image">
    <img :src="qr_image" alt="QR Code">
  </div>
</div>',
'.creative-design { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 6mm; position: relative; overflow: hidden; }
.creative-design .background-pattern { position: absolute; top: -50%; right: -50%; width: 200%; height: 200%; background: url("data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="20" cy="20" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); opacity: 0.3; animation: float 20s ease-in-out infinite; }
.creative-design .name-block { position: relative; margin-bottom: 2mm; }
.creative-design .name { font-size: 18px; font-weight: bold; margin: 0; position: relative; z-index: 2; }
.creative-design .name-bg { position: absolute; top: 0; left: 0; font-size: 24px; font-weight: 900; color: rgba(255,255,255,0.1); z-index: 1; }
.creative-design .title { font-size: 12px; margin: 0; color: rgba(255,255,255,0.9); text-transform: uppercase; letter-spacing: 1px; }
.creative-design .company p { font-size: 14px; margin: 4mm 0; color: rgba(255,255,255,0.9); }
.creative-design .contact-grid { display: grid; grid-template-columns: 1fr; gap: 2mm; }
.creative-design .contact-item { display: flex; flex-direction: column; }
.creative-design .label { font-size: 9px; color: rgba(255,255,255,0.7); text-transform: uppercase; margin-bottom: 0.5mm; }
.creative-design .value { font-size: 11px; color: white; }
.creative-design .qr-section { position: absolute; bottom: 6mm; right: 6mm; width: 12mm; height: 12mm; background: white; padding: 1mm; border-radius: 2mm; }
.creative-design .qr-section img { width: 100%; height: 100%; }',
'{"primary": "#667eea", "secondary": "#764ba2", "text": "#ffffff", "light": "rgba(255,255,255,0.7)"}', 3),

-- 4. 优雅商务模板
('优雅商务', '高端商务风格，适合高级管理人员', 'business', 'finance', 'standard',
'<div class="business-card elegant-business">
  <div class="gold-line"></div>
  <div class="content">
    <div class="header">
      <h1 class="name">{{name}}</h1>
      <p class="title">{{title}}</p>
    </div>
    <div class="company">
      <p class="company-name">{{company}}</p>
    </div>
    <div class="contact">
      <div class="contact-row">
        <span class="icon">📞</span>
        <span>{{phone}}</span>
      </div>
      <div class="contact-row">
        <span class="icon">✉</span>
        <span>{{email}}</span>
      </div>
      <div class="contact-row">
        <span class="icon">📍</span>
        <span>{{address}}</span>
      </div>
    </div>
  </div>
  <div class="corner-decoration"></div>
</div>',
'.elegant-business { background: #1a1a1a; color: #ffffff; padding: 8mm; position: relative; border: 1px solid #444; }
.elegant-business .gold-line { position: absolute; top: 0; left: 8mm; right: 8mm; height: 2px; background: linear-gradient(90deg, transparent, #d4af37, transparent); }
.elegant-business .name { font-size: 18px; font-weight: 700; color: #d4af37; margin: 0 0 2mm 0; letter-spacing: 0.5px; }
.elegant-business .title { font-size: 13px; color: #cccccc; margin: 0 0 4mm 0; text-transform: uppercase; letter-spacing: 1px; }
.elegant-business .company-name { font-size: 14px; color: #ffffff; margin: 0 0 4mm 0; font-weight: 500; }
.elegant-business .contact-row { display: flex; align-items: center; margin-bottom: 2mm; font-size: 11px; color: #cccccc; }
.elegant-business .icon { margin-right: 3mm; font-size: 10px; color: #d4af37; width: 4mm; }
.elegant-business .corner-decoration { position: absolute; bottom: 6mm; right: 6mm; width: 8mm; height: 8mm; border: 1px solid #d4af37; border-radius: 50%; }
.elegant-business .corner-decoration::after { content: ""; position: absolute; top: 2mm; left: 2mm; width: 4mm; height: 4mm; background: radial-gradient(circle, #d4af37 30%, transparent 30%); border-radius: 50%; }',
'{"primary": "#d4af37", "secondary": "#1a1a1a", "text": "#ffffff", "light": "#cccccc"}', 4),

-- 5. 头像圆形模板
INSERT INTO `business_card_templates` (`name`, `description`, `category`, `industry`, `layout_type`, `template_html`, `template_css`, `default_colors`, `sort_order`) VALUES
('头像圆形', '极简清新风格，头像为圆形居左，适合个人品牌与现代商务', 'minimal', 'other', 'standard',
'<div class="business-card avatar-circle">
  <div class="avatar-section">
    <img v-if="avatar" :src="avatar" class="avatar-img" alt="头像" />
    <div v-else class="avatar-placeholder">头像</div>
  </div>
  <div class="main-content">
    <div class="identity">
      <h2 class="name">{{name}}</h2>
      <p class="title">{{title}}</p>
      <p class="company">{{company}}</p>
    </div>
    <div class="contact-row">
      <span class="contact-item">{{phone}}</span>
      <span class="contact-item">{{email}}</span>
      <span class="contact-item">{{website}}</span>
    </div>
  </div>
  <div class="qr-code" v-if="qr_image">
    <img :src="qr_image" alt="二维码" class="qr-img" />
  </div>
</div>',
'.avatar-circle { width: 90mm; height: 54mm; background: #fff; border-radius: 18px; box-shadow: 0 2px 8px #0001; display: flex; align-items: center; padding: 0 32px; position: relative; font-family: "Inter", "PingFang SC", "Microsoft YaHei", Arial, sans-serif; }
.avatar-section { flex-shrink: 0; display: flex; align-items: center; justify-content: center; margin-right: 32px; }
.avatar-img { width: 28mm; height: 28mm; border-radius: 50%; object-fit: cover; border: 2px solid #409EFF; background: #fff; }
.avatar-placeholder { width: 28mm; height: 28mm; border-radius: 50%; background: #f0f0f0; color: #bbb; display: flex; align-items: center; justify-content: center; font-size: 12px; border: 2px dashed #ddd; }
.main-content { flex: 1; display: flex; flex-direction: column; align-items: center; justify-content: center; min-width: 0; }
.identity { text-align: center; }
.name { font-size: 18px; margin: 0 0 2px 0; letter-spacing: 1px; color: #409EFF; font-weight: bold; }
.title { font-size: 12px; margin: 0 0 2px 0; color: #222; opacity: 0.9; }
.company { font-size: 11px; margin: 0 0 6px 0; color: #222; opacity: 0.8; }
.contact-row { display: flex; gap: 10px; margin-top: 6px; justify-content: center; }
.contact-item { font-size: 10px; color: #888; }
.qr-code { position: absolute; right: 8mm; bottom: 8mm; width: 12mm; height: 12mm; background: #fff; border-radius: 4px; box-shadow: 0 1px 4px #0001; display: flex; align-items: center; justify-content: center; }
.qr-img { width: 80%; height: 80%; object-fit: contain; display: block; }',
'{"primary": "#409EFF", "secondary": "#FFFFFF", "text": "#222", "light": "#888"}', 5);

-- 创建索引
CREATE INDEX `idx_business_cards_user_status` ON `business_cards` (`user_id`, `status`);
CREATE INDEX `idx_business_card_templates_category_active` ON `business_card_templates` (`category`, `is_active`); 