<template>
  <nav class="toolbar">
    <!-- 文件/添加分组 -->
    <div class="toolbar-group">
      <button class="toolbar-btn" @click="$emit('file-menu')" title="文件">
        <span class="icon"><svg viewBox="0 0 24 24" width="20" height="20"><rect x="4" y="4" width="16" height="16" rx="2" fill="none" stroke="currentColor" stroke-width="2"/></svg></span>
      </button>
      <button class="toolbar-btn" @click="$emit('add-file')" title="添加PDF">
        <span class="icon"><svg viewBox="0 0 24 24" width="20" height="20"><path d="M12 5v14M5 12h14" stroke="currentColor" stroke-width="2" fill="none"/></svg></span>
      </button>
    </div>
    <!-- 视图分组 -->
    <div class="toolbar-group">
      <button class="toolbar-btn" @click="$emit('zoom-out')" title="缩小"><span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><rect x="5" y="11" width="14" height="2" rx="1" fill="currentColor"/></svg></span></button>
      <span class="toolbar-label">{{ zoom }}%</span>
      <button class="toolbar-btn" @click="$emit('zoom-in')" title="放大"><span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><rect x="5" y="11" width="14" height="2" rx="1" fill="currentColor"/><rect x="11" y="5" width="2" height="14" rx="1" fill="currentColor"/></svg></span></button>
    </div>
    <!-- 页面操作分组 -->
    <div class="toolbar-group">
      <button class="toolbar-btn" :disabled="!canDeletePage" @click="$emit('delete-pages')" title="删除页面">
        <span class="icon"><svg viewBox="0 0 24 24" width="20" height="20"><path d="M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z" fill="currentColor"/></svg></span>
      </button>
      <button class="toolbar-btn" :disabled="!canReorder" @click="$emit('reorder')" title="页面排序">
        <span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><path d="M4 10h16M4 14h16" stroke="currentColor" stroke-width="2"/></svg></span>
      </button>
    </div>
    <!-- 批注/编辑分组 -->
    <div class="toolbar-group">
      <button class="toolbar-btn" :disabled="!canAnnotate" :class="{active: annotateMode === 'pen'}" @click="$emit('annotate', 'pen')" title="画笔">
        <span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><path d="M3 17.25V21h3.75l11.06-11.06-3.75-3.75L3 17.25z" fill="currentColor"/></svg></span>
      </button>
      <button class="toolbar-btn" :disabled="!canAnnotate" :class="{active: annotateMode === 'highlight'}" @click="$emit('annotate', 'highlight')" title="高亮">
        <span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><rect x="4" y="17" width="16" height="2" rx="1" fill="currentColor"/><rect x="7" y="7" width="10" height="6" rx="2" fill="currentColor" opacity=".3"/></svg></span>
      </button>
      <button class="toolbar-btn" :disabled="!canAnnotate" :class="{active: annotateMode === 'text'}" @click="$emit('annotate', 'text')" title="文本">
        <span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><text x="4" y="18" font-size="16" fill="currentColor">T</text></svg></span>
      </button>
      <button class="toolbar-btn" @click="$emit('ocr')" title="OCR文字识别">
        <span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/><text x="7" y="16" font-size="8" fill="currentColor">OCR</text></svg></span>
      </button>
    </div>
    <!-- 导出/保存分组 -->
    <div class="toolbar-group toolbar-group-right">
      <button class="toolbar-btn" :disabled="!canMerge" @click="$emit('merge')" title="合并PDF">
        <span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><path d="M6 6h12v12H6z" fill="currentColor"/></svg></span>
      </button>
      <button class="toolbar-btn" :disabled="!canLongImage" @click="$emit('long-image')" title="导出长图">
        <span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><rect x="8" y="4" width="8" height="16" rx="2" fill="currentColor"/></svg></span>
      </button>
      <button class="toolbar-btn" @click="$emit('save')" title="导出/保存">
        <span class="icon"><svg width="20" height="20" viewBox="0 0 24 24"><path d="M5 20h14v-2H5v2zm7-18C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 14.5h-2v-6h2v6zm0-8h-2V7h2v1.5z" fill="currentColor"/></svg></span>
      </button>
    </div>
  </nav>
</template>
<script setup>
defineProps({
  canMerge: Boolean,
  canLongImage: Boolean,
  canAnnotate: Boolean,
  canDeletePage: Boolean,
  canReorder: Boolean,
  viewMode: String,
  zoom: Number,
  annotateMode: String
})
</script>
<style scoped>
.toolbar {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background: #fff;
  border-bottom: 1px solid #e5e6eb;
  padding: 0 1rem;
  min-height: 56px;
  gap: 0.5rem;
  font-size: 15px;
  z-index: 10;
}
.toolbar-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-right: 1.5rem;
}
.toolbar-group-right {
  margin-left: auto;
}
.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 0.25em;
  background: none;
  border: none;
  border-radius: 6px;
  padding: 0.5em 1em;
  color: #222;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  font-size: 15px;
  position: relative;
}
.toolbar-btn .icon {
  width: 1.1em;
  height: 1.1em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.toolbar-btn.active, .toolbar-btn:active {
  background: #e3eaff;
  color: #2563eb;
}
.toolbar-btn:disabled {
  color: #bbb;
  background: #f5f5f5;
  cursor: not-allowed;
}
.toolbar-btn:hover:not(:disabled) {
  background: #f0f6ff;
  color: #2563eb;
}
.toolbar-label {
  margin: 0 0.5em;
  color: #888;
  font-size: 14px;
}
@media (max-width: 900px) {
  .toolbar {
    flex-wrap: nowrap;
    overflow-x: auto;
    font-size: 13px;
    min-height: 48px;
  }
  .toolbar-group {
    margin-right: 0.5rem;
  }
  .toolbar-btn {
    padding: 0.4em 0.7em;
    font-size: 13px;
  }
}
</style> 