import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models.ad_position import AdPosition

app = create_app()
with app.app_context():
    # 默认广告位配置
    default_positions = [
        {
            'name': '首页顶部横幅',
            'code': 'home_top_banner',
            'description': '首页顶部横幅广告位',
            'position_type': 'banner',
            'width': 728,
            'height': 90,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'home',
            'css_class': 'ad-home-top-banner',
            'sort_order': 1
        },
        {
            'name': '首页侧边栏',
            'code': 'home_sidebar',
            'description': '首页侧边栏广告位',
            'position_type': 'sidebar',
            'width': 300,
            'height': 250,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'home',
            'css_class': 'ad-home-sidebar',
            'sort_order': 2
        },
        {
            'name': '用户中心顶部',
            'code': 'profile_top',
            'description': '用户中心页面顶部广告位',
            'position_type': 'banner',
            'width': 728,
            'height': 90,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'profile',
            'css_class': 'ad-profile-top',
            'sort_order': 3
        },
        {
            'name': '证件照编辑页侧边栏',
            'code': 'photo_editor_sidebar',
            'description': '证件照编辑页面侧边栏广告位',
            'position_type': 'sidebar',
            'width': 300,
            'height': 250,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'photo_editor',
            'css_class': 'ad-photo-editor-sidebar',
            'sort_order': 4
        },
        {
            'name': '简历编辑页底部',
            'code': 'resume_editor_bottom',
            'description': '简历编辑页面底部广告位',
            'position_type': 'banner',
            'width': 728,
            'height': 90,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'resume_editor',
            'css_class': 'ad-resume-editor-bottom',
            'sort_order': 5
        },
        {
            'name': '名片编辑页侧边栏',
            'code': 'business_card_sidebar',
            'description': '名片编辑页面侧边栏广告位',
            'position_type': 'sidebar',
            'width': 300,
            'height': 250,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'business_card',
            'css_class': 'ad-business-card-sidebar',
            'sort_order': 6
        },
        {
            'name': '文档管理页顶部',
            'code': 'document_manager_top',
            'description': '文档管理页面顶部广告位',
            'position_type': 'banner',
            'width': 728,
            'height': 90,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'document_manager',
            'css_class': 'ad-document-manager-top',
            'sort_order': 7
        },
        {
            'name': '积分中心侧边栏',
            'code': 'credits_sidebar',
            'description': '积分中心页面侧边栏广告位',
            'position_type': 'sidebar',
            'width': 300,
            'height': 250,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'credits',
            'css_class': 'ad-credits-sidebar',
            'sort_order': 8
        },
        {
            'name': '帮助页面底部',
            'code': 'help_bottom',
            'description': '帮助页面底部广告位',
            'position_type': 'banner',
            'width': 728,
            'height': 90,
            'is_enabled': True,
            'is_visible': True,
            'page_location': 'help',
            'css_class': 'ad-help-bottom',
            'sort_order': 9
        }
    ]
    
    for position_data in default_positions:
        existing = AdPosition.query.filter_by(code=position_data['code']).first()
        if not existing:
            position = AdPosition(**position_data)
            db.session.add(position)
            print(f"添加广告位: {position_data['name']}")
        else:
            print(f"广告位已存在: {position_data['name']}")
    
    db.session.commit()
    print("广告位初始化完成！") 