#!/usr/bin/env python3
"""
相片制作工具集 - Flask应用启动文件
"""

import os
import sys
from flask import Flask
from flask_migrate import upgrade

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, Image, ProcessingTask, PhotoTemplate, PaperSize, UserLog, SystemConfig

def create_app_instance():
    """创建Flask应用实例"""
    # 获取配置环境
    config_name = os.environ.get('FLASK_ENV', 'development')
    
    # 创建应用
    app = create_app(config_name)
    
    return app

def init_database(app):
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            db.create_all()
            
            # 检查是否需要初始化数据
            if PhotoTemplate.query.count() == 0:
                print("正在初始化数据库数据...")
                init_default_data()
                
            print("数据库初始化完成")
            
        except Exception as e:
            print(f"数据库初始化失败: {e}")
            sys.exit(1)

def init_default_data():
    """初始化默认数据"""
    # 初始化证件照模板
    templates_data = [
        {'name': '一寸照', 'category': '常用证件照', 'width': 295, 'height': 413, 'width_mm': 25, 'height_mm': 35, 'description': '标准一寸证件照，适用于简历、学生证等', 'sort_order': 1},
        {'name': '二寸照', 'category': '常用证件照', 'width': 413, 'height': 531, 'width_mm': 35, 'height_mm': 45, 'description': '标准二寸证件照，适用于毕业证、工作证等', 'sort_order': 2},
        {'name': '小二寸照', 'category': '常用证件照', 'width': 413, 'height': 472, 'width_mm': 35, 'height_mm': 40, 'description': '小二寸证件照，适用于护照、签证等', 'sort_order': 3},
        {'name': '护照照片', 'category': '出国证件', 'width': 354, 'height': 472, 'width_mm': 30, 'height_mm': 40, 'description': '中国护照专用照片规格', 'sort_order': 4},
        {'name': '美国签证照', 'category': '出国证件', 'width': 600, 'height': 600, 'width_mm': 51, 'height_mm': 51, 'description': '美国签证申请专用照片', 'sort_order': 5}
    ]
    
    for template_data in templates_data:
        template = PhotoTemplate(**template_data)
        db.session.add(template)
    
    # 初始化纸张规格
    paper_sizes_data = [
        {'name': 'A4', 'width_mm': 210.00, 'height_mm': 297.00, 'width_px': 2480, 'height_px': 3508, 'is_common': True, 'sort_order': 1},
        {'name': 'A5', 'width_mm': 148.00, 'height_mm': 210.00, 'width_px': 1748, 'height_px': 2480, 'is_common': False, 'sort_order': 2},
        {'name': '5寸照片', 'width_mm': 127.00, 'height_mm': 89.00, 'width_px': 1500, 'height_px': 1050, 'is_common': True, 'sort_order': 3},
        {'name': '6寸照片', 'width_mm': 152.00, 'height_mm': 102.00, 'width_px': 1800, 'height_px': 1200, 'is_common': True, 'sort_order': 4}
    ]
    
    for paper_data in paper_sizes_data:
        paper_size = PaperSize(**paper_data)
        db.session.add(paper_size)
    
    # 初始化系统配置
    configs_data = [
        {'config_key': 'max_upload_size', 'config_value': '16777216', 'config_type': 'integer', 'description': '最大上传文件大小(字节)', 'is_system': True},
        {'config_key': 'allowed_image_types', 'config_value': '["jpg", "jpeg", "png", "bmp", "gif"]', 'config_type': 'json', 'description': '允许的图片格式', 'is_system': True},
        {'config_key': 'default_background_colors', 'config_value': '["#ffffff", "#ff0000", "#0000ff", "#e6f3ff"]', 'config_type': 'json', 'description': '默认背景颜色选项', 'is_system': True},
        {'config_key': 'free_user_daily_limit', 'config_value': '10', 'config_type': 'integer', 'description': '免费用户每日处理限制', 'is_system': True},
        {'config_key': 'vip_user_daily_limit', 'config_value': '100', 'config_type': 'integer', 'description': 'VIP用户每日处理限制', 'is_system': True},
        {'config_key': 'enable_face_beautify', 'config_value': 'true', 'config_type': 'boolean', 'description': '是否启用美颜功能', 'is_system': True},
        {'config_key': 'enable_background_removal', 'config_value': 'true', 'config_type': 'boolean', 'description': '是否启用背景移除功能', 'is_system': True}
    ]
    
    for config_data in configs_data:
        config = SystemConfig(**config_data)
        db.session.add(config)
    
    # 提交所有更改
    db.session.commit()
    print("默认数据初始化完成")

def create_admin_user():
    """创建管理员用户"""
    admin_username = os.environ.get('ADMIN_USERNAME', 'admin')
    admin_email = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
    admin_password = os.environ.get('ADMIN_PASSWORD', 'admin123456')
    
    # 检查管理员是否已存在
    admin = User.query.filter_by(username=admin_username).first()
    if not admin:
        admin = User(
            username=admin_username,
            email=admin_email,
            nickname='系统管理员',
            vip_level=99  # 超级管理员
        )
        admin.set_password(admin_password)
        db.session.add(admin)
        db.session.commit()
        print(f"管理员用户创建成功: {admin_username}")
    else:
        print(f"管理员用户已存在: {admin_username}")

# 创建应用实例
app = create_app_instance()

# 添加CORS支持
try:
    from flask_cors import CORS
    CORS(app, supports_credentials=True)
    print('CORS已启用')
except ImportError:
    print('未安装flask-cors，跨域请求可能失败。请运行 pip install flask-cors')

# Shell上下文处理器
@app.shell_context_processor
def make_shell_context():
    """为Flask shell提供上下文"""
    return {
        'db': db,
        'User': User,
        'Image': Image,
        'ProcessingTask': ProcessingTask,
        'PhotoTemplate': PhotoTemplate,
        'PaperSize': PaperSize,
        'UserLog': UserLog,
        'SystemConfig': SystemConfig
    }

if __name__ == '__main__':
    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'init-db':
            # 初始化数据库
            init_database(app)
            create_admin_user()
            sys.exit(0)
        elif command == 'create-admin':
            # 创建管理员用户
            with app.app_context():
                create_admin_user()
            sys.exit(0)
    
    # 默认启动开发服务器
    with app.app_context():
        # 确保数据库已初始化
        init_database(app)
        
        # 如果是开发环境，创建管理员用户
        if app.config.get('DEBUG'):
            create_admin_user()
    
    # 启动Flask开发服务器
    print("🚀 启动相片制作工具集后端服务...")
    print(f"📍 环境: {app.config.get('ENV', 'unknown')}")
    print(f"🔧 调试模式: {app.config.get('DEBUG', False)}")
    print(f"🌐 访问地址: http://localhost:5000")
    print("🔗 API文档: http://localhost:5000/api/common/health")
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=app.config.get('DEBUG', False),
        threaded=True
    ) 