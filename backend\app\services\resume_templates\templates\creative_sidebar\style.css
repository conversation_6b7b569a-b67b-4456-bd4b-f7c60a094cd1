/* 创意左侧栏简历模板样式 */
:root {
    --font-family: 'Source Han Sans CN', 'Microsoft YaHei', sans-serif;
    --base-font-size: 14px;
    --primary-color: #2c3e50;
    --secondary-color: #7f8c8d;
    --accent-color: #e74c3c;
    --heading-color: #2c3e50;
    --link-color: #3498db;
    --sidebar-bg: #2c3e50;
    --sidebar-text: #ecf0f1;
    --main-bg: #ffffff;
    --section-spacing: 2rem;
    --line-height: 1.6;
    --skill-level: 80%;  /* Default skill level */
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--base-font-size);
    line-height: var(--line-height);
    color: var(--primary-color);
    background-color: #fff;
}

/* 容器布局 */
.resume-container {
    display: flex;
    min-height: 100vh;
    max-width: 210mm;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

/* 左侧栏样式 */
.sidebar {
    width: 35%;
    background: var(--sidebar-bg);
    padding: 2rem 1.5rem;
    color: var(--sidebar-text);
}


/* 个人信息 */
.personal-info {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 2px solid var(--accent-color);
}

.full-name {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: var(--sidebar-text);
}

.job-title {
    font-size: 1.1rem;
    color: var(--accent-color);
    font-weight: 500;
}

/* 侧边栏标题 */
.sidebar-title {
    font-size: 1.2rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(231, 76, 60, 0.3);
    position: relative;
}

.sidebar-title::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--accent-color);
}

/* 联系方式 */
.contact-section {
    margin-bottom: 2rem;
}

.contact-list {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.contact-item:last-child {
    border-bottom: none;
}

.contact-item i {
    font-size: 1.2rem;
    color: var(--accent-color);
    width: 20px;
}

/* 技能部分 */
.skills-section {
    margin-bottom: 2rem;
}

.skills-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.skill-item {
    margin-bottom: 0.8rem;
}

.skill-name {
    display: block;
    margin-bottom: 0.3rem;
    font-weight: 500;
}

.skill-progress {
    background: rgba(255, 255, 255, 0.2);
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
}

.skill-bar {
    background: var(--accent-color);
    height: 100%;
    width: var(--skill-level);
    transition: width 0.5s ease;
}

/* 证书资质 */
.certifications-section {
    margin-bottom: 2rem;
}

.cert-list {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.cert-item {
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
    border-left: 3px solid var(--accent-color);
}

.cert-name {
    font-weight: 500;
    margin-bottom: 0.3rem;
}

.cert-date {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

/* 主内容区 */
.main-content {
    width: 65%;
    padding: 2rem;
    background: #fff;
}

/* 章节样式 */
.section {
    margin-bottom: var(--section-spacing);
}

.section-title {
    font-size: 1.5rem;
    color: var(--heading-color);
    margin-bottom: 1.2rem;
    padding-bottom: 0.8rem;
    border-bottom: 2px solid var(--accent-color);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--accent-color);
}

.section-content {
    padding-left: 0.5rem;
}

/* 个人总结 */
.summary-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--primary-color);
    text-align: justify;
}

/* 时间轴样式 */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--accent-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    background: var(--accent-color);
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.timeline-content {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid var(--accent-color);
}

.work-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.8rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.company-name {
    font-size: 1.3rem;
    color: var(--heading-color);
    font-weight: bold;
}

.work-period {
    color: var(--secondary-color);
    font-weight: 500;
    background: var(--accent-color);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

.position {
    font-size: 1.1rem;
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 0.8rem;
}

.work-description {
    margin-bottom: 1rem;
    line-height: 1.7;
}

.achievements {
    list-style: none;
    padding-left: 0;
}

.achievements li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.achievements li::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

/* 教育背景 */
.education-item {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--accent-color);
}

.education-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.school-name {
    font-size: 1.2rem;
    color: var(--heading-color);
    font-weight: bold;
}

.education-period {
    color: var(--secondary-color);
    font-weight: 500;
    background: var(--accent-color);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

.major {
    font-size: 1.1rem;
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.degree {
    color: var(--secondary-color);
    margin-bottom: 0.8rem;
}

.education-description {
    line-height: 1.6;
}

/* 项目经验 */
.project-item {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--accent-color);
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.project-name {
    font-size: 1.2rem;
    color: var(--heading-color);
    font-weight: bold;
}

.project-period {
    color: var(--secondary-color);
    font-weight: 500;
    background: var(--accent-color);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

.project-role {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 0.8rem;
}

.project-description {
    margin-bottom: 1rem;
    line-height: 1.7;
}

.tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.tech-tag {
    background: var(--accent-color);
    color: #fff;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .resume-container {
        flex-direction: column;
    }
    
    .sidebar,
    .main-content {
        width: 100%;
    }
    
    .sidebar {
        order: 2;
    }
    
    .main-content {
        order: 1;
    }
    
    .work-header,
    .education-header,
    .project-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .timeline {
        padding-left: 1rem;
    }
    
    .timeline-marker {
        left: -1rem;
    }
}

/* 打印样式 */
@media print {
    .resume-container {
        box-shadow: none;
        max-width: none;
    }
    
    .timeline::before {
        background: #000;
    }
    
    .timeline-marker {
        background: #000;
        box-shadow: 0 0 0 2px #000;
    }
}