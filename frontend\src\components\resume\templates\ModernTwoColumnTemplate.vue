<template>
  <div class="resume-content-wrapper modern-two-column-template">
    <!-- 头部信息 -->
    <header class="resume-header">
      <div class="header-main">
        <div class="personal-info">
          <h1 class="full-name">{{ formattedResumeData.full_name }}</h1>
          <div class="title-info">
            <span v-if="formattedResumeData.objective" class="title-text">{{ formattedResumeData.objective }}</span>
          </div>
        </div>
       
      </div>
    </header>

    <div class="resume-body">
      <!-- 左侧栏 -->
      <aside class="sidebar">

        <!-- 个人照片 -->
        <section v-if="formattedResumeData.photo_url" class="sidebar-section">
          <div class="photo-container">
            <img :src="displayPhotoUrl" alt="个人照片" class="photo">
          </div>
        </section>

        <!-- 基本信息 -->
        <section v-if="formattedResumeData.gender || formattedResumeData.age" class="sidebar-section">
          <h3 class="sidebar-title">基本信息</h3>
          <div class="basic-info-list">
            <div v-if="formattedResumeData.gender" class="info-item">
              <span class="info-label">性别：</span>
              <span class="info-value">{{ formattedResumeData.gender }}</span>
            </div>
            <div v-if="formattedResumeData.age" class="info-item">
              <span class="info-label">年龄：</span>
              <span class="info-value">{{ formattedResumeData.age }}岁</span>
            </div>
          </div>
        </section>


        <!-- 联系信息 -->
        <section class="sidebar-section">
          
          <h3 class="sidebar-title">联系方式</h3>
          <div class="contact-list">
            <div v-if="formattedResumeData.phone" class="contact-item">
              <span class="contact-icon">📞</span>
              <span class="contact-text">{{ formattedResumeData.phone }}</span>
            </div>
            <div v-if="formattedResumeData.email" class="contact-item">
              <span class="contact-icon">✉️</span>
              <span class="contact-text">{{ formattedResumeData.email }}</span>
            </div>
            <div v-if="formattedResumeData.address" class="contact-item">
              <span class="contact-icon">📍</span>
              <span class="contact-text">{{ formattedResumeData.address }}</span>
            </div>
          </div>
        </section>

        

        <!-- 技能特长 -->
        <section v-if="formattedResumeData.skills?.length" class="sidebar-section">
          <h3 class="sidebar-title">技能特长</h3>
          <div class="skills-list">
            <div v-for="skill in formattedResumeData.skills" :key="skill.name" class="skill-item">
              <div class="skill-header">
                <span class="skill-name">{{ skill.name }}</span>
                <span class="skill-level-text">{{ skill.level }}%</span>
              </div>
              <div class="skill-bar-container">
                <div class="skill-bar" :style="{ width: skill.level + '%' }"></div>
              </div>
            </div>
          </div>
        </section>

        <!-- 证书资质 -->
        <section v-if="formattedResumeData.certifications?.length" class="sidebar-section">
          <h3 class="sidebar-title">证书资质</h3>
          <div class="certifications-list">
            <div v-for="cert in formattedResumeData.certifications" :key="cert.name" class="certification-item">
              <div class="cert-name">{{ cert.name }}</div>
              <div v-if="cert.date" class="cert-date">{{ cert.date }}</div>
            </div>
          </div>
        </section>
      </aside>
      <div class="vertical-divider"></div>
      <!-- 右侧主栏 -->
      <main class="main-content">
        <!-- 个人总结 -->
        <section v-if="formattedResumeData.summary" class="main-section">
          <h3 class="main-section-title">个人总结</h3>
          <div class="section-content">
            <p class="summary-text">{{ formattedResumeData.summary }}</p>
          </div>
        </section>

        <!-- 工作经历 -->
        <section v-if="formattedResumeData.work_experiences?.length" class="main-section">
          <h3 class="main-section-title">工作经历</h3>
          <div class="section-content">
            <div v-for="exp in formattedResumeData.work_experiences" :key="exp.company_name" class="experience-item">
              <div class="experience-header">
                <h4 class="company-name">{{ exp.company_name }}</h4>
                <span class="period">{{ exp.period }}</span>
              </div>
              <div class="position">{{ exp.position }}</div>
              <p v-if="exp.description" class="description">{{ exp.description }}</p>
              <ul v-if="exp.achievements?.length" class="achievements">
                <li v-for="(achievement, index) in exp.achievements" :key="index">
                  {{ achievement }}
                </li>
              </ul>
            </div>
          </div>
        </section>

        <!-- 教育背景 -->
        <section v-if="formattedResumeData.educations?.length" class="main-section">
          <h3 class="main-section-title">教育背景</h3>
          <div class="section-content">
            <div v-for="edu in formattedResumeData.educations" :key="edu.school_name" class="education-item">
              <div class="education-header">
                <h4 class="school-name">{{ edu.school_name }}</h4>
                <span class="period">{{ edu.period }}</span>
              </div>
              <div class="major">{{ edu.major }} · {{ edu.degree }}</div>
              <p v-if="edu.description" class="description">{{ edu.description }}</p>
            </div>
          </div>
        </section>

        <!-- 项目经验 -->
        <section v-if="formattedResumeData.projects?.length" class="main-section">
          <h3 class="main-section-title">项目经验</h3>
          <div class="section-content">
            <div v-for="project in formattedResumeData.projects" :key="project.name" class="project-item">
              <div class="project-header">
                <h4 class="project-name">{{ project.name }}</h4>
                <span class="period">{{ project.period }}</span>
              </div>
              <p v-if="project.description" class="description">{{ project.description }}</p>
              <div v-if="project.technologies?.length" class="technologies">
                <span v-for="tech in project.technologies" :key="tech" class="tech-tag">
                  {{ tech }}
                </span>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  </div>
</template>

<script>
import { getImageUrl } from '@/utils/api'
import { computed, watch } from 'vue'

export default {
  name: 'ModernTwoColumnTemplate',
  
  props: {
    resumeData: {
      type: Object,
      required: true,
      validator(value) {
        const requiredFields = ['id', 'title', 'full_name']
        const hasRequiredFields = requiredFields.every(field => 
          value[field] !== undefined && value[field] !== null
        )
        
        const arrayFields = ['work_experiences', 'educations', 'projects', 'skills', 'certifications']
        const hasValidArrays = arrayFields.every(field => 
          !value[field] || Array.isArray(value[field])
        )
        
        return hasRequiredFields && hasValidArrays
      }
    }
  },

  emits: ['error'],

  setup(props, { emit }) {
    const formattedResumeData = computed(() => {
      try {
        const data = props.resumeData

        const work_experiences = (data.work_experiences || []).map(exp => {
          return {
            ...exp,
            period: exp.start_date && exp.end_date ? `${exp.start_date} - ${exp.end_date}` : '',
            achievements: Array.isArray(exp.achievements) ? exp.achievements : []
          }
        })

        const educations = (data.educations || []).map(edu => {
          return {
            ...edu,
            period: edu.start_date && edu.end_date ? `${edu.start_date} - ${edu.end_date}` : ''
          }
        })

        const projects = (data.projects || []).map(proj => {
          return {
            ...proj,
            period: proj.start_date && proj.end_date ? `${proj.start_date} - ${proj.end_date}` : '',
            technologies: Array.isArray(proj.technologies) ? proj.technologies : [],
            achievements: Array.isArray(proj.achievements) ? proj.achievements : []
          }
        })

        const skills = (data.skills || []).map(skill => {
          return {
            ...skill,
            level: typeof skill.level === 'number' ? skill.level : 
                   skill.proficiency === '专家' ? 100 :
                   skill.proficiency === '高级' ? 80 :
                   skill.proficiency === '中级' ? 60 :
                   skill.proficiency === '初级' ? 40 : 20
          }
        })

        const certifications = (data.certifications || []).map(cert => {
          return {
            ...cert,
            date: cert.issue_date || cert.date || ''
          }
        })

        return {
          ...data,
          work_experiences,
          educations,
          projects,
          skills,
          certifications
        }
      } catch (err) {
        console.error('简历数据格式化失败:', err)
        emit('error', '简历数据格式化失败')
        return props.resumeData
      }
    })

    const displayPhotoUrl = computed(() => {
      const url = props.resumeData.thumbnail_url || props.resumeData.photo_url
      if (!url) return ''
      if (url.startsWith('http')) return url
      // 兼容相对路径
      return getImageUrl(url)
    })

    watch(() => props.resumeData, (newData) => {
      if (!newData) {
        console.warn('模板接收到空数据')
        return
      }
    }, { immediate: true })

    return {
      formattedResumeData,
      displayPhotoUrl
    }
  }
}
</script>

<style scoped>
.modern-two-column-template {
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
  color: #2c3e50;
  font-size: 14px;
  line-height: 1.5;
  width: 100%;
  background: white;
  box-sizing: border-box;
  padding: 32px 24px;
}

/* 头部样式 */
.resume-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 3px solid #3498db;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.personal-info {
  flex: 1;
}

.full-name {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
  letter-spacing: 0.5px;
}

.title-info {
  margin-bottom: 0.5rem;
}

.title-text {
  font-size: 16px;
  color: #3498db;
  font-weight: 500;
}

.photo-container {
  width: 120px;
  height: 160px;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  flex-shrink: 0;
  margin: 0 auto 1rem auto;
  display: block;
}

.photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 主体布局 */
.resume-body {
  display: grid;
  grid-template-columns: 250px 0.5rem 1.2fr;
  gap: 0.8rem;
  align-items: start;
  width: 100%;
  margin: 0;
  box-sizing: border-box;
  position: relative;
}

.vertical-divider {
  width: 1px;
  background: #d0d7de;
  border-radius: 1px;
  height: 100%;
  min-height: 300px;
  margin: 0 auto;
  align-self: stretch;
}

/* 侧边栏样式 */
.sidebar {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  /* border-left: 4px solid #3498db; */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.sidebar-section {
  margin-bottom: 1.2rem;
  width: 100%;
  text-align: center;
}

.sidebar-section:last-child {
  margin-bottom: 0;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 联系信息 */
.contact-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 13px;
}

.contact-icon {
  font-size: 14px;
  width: 20px;
  text-align: center;
}

.contact-text {
  color: #2c3e50;
}

/* 基本信息 */
.basic-info-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}

.info-label {
  color: #7f8c8d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: 600;
}

/* 技能样式 */
.skills-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.skill-item {
  margin-bottom: 0.5rem;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.3rem;
}

.skill-name {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
}

.skill-level-text {
  font-size: 11px;
  color: #7f8c8d;
  font-weight: 500;
}

.skill-bar-container {
  height: 6px;
  background: #ecf0f1;
  border-radius: 3px;
  overflow: hidden;
}

.skill-bar {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 证书样式 */
.certifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.certification-item {
  padding: 0.5rem;
  background: white;
  border-radius: 6px;
  border-left: none;
}

.cert-name {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.cert-date {
  font-size: 11px;
  color: #7f8c8d;
}

/* 主内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.main-section {
  break-inside: avoid;
}

.main-section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #3498db;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.section-content {
  padding: 0;
  line-height: 1.4;
}

/* 经验项目通用样式 */
.experience-item,
.education-item,
.project-item {
  margin-bottom: 0.7rem;
  padding: 0.7rem;
  background: #f8f9fa;
  border-radius: 8px;
  /* border-left: 4px solid #3498db; */
  break-inside: avoid;
}

.experience-header,
.education-header,
.project-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.company-name,
.school-name,
.project-name {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.period {
  color: #7f8c8d;
  font-size: 13px;
  font-weight: 500;
}

.position,
.major {
  font-weight: 600;
  color: #3498db;
  margin-bottom: 0.5rem;
  font-size: 14px;
}

.description {
  color: #2c3e50;
  margin: 0.3rem 0;
  line-height: 1.4;
  white-space: pre-line;
  font-size: 13px;
}

/* 成就列表样式 */
.achievements {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0;
}

.achievements li {
  position: relative;
  padding-left: 1.2rem;
  margin-bottom: 0.4rem;
  color: #2c3e50;
  font-size: 13px;
}

.achievements li::before {
  content: "▶";
  position: absolute;
  left: 0;
  color: #3498db;
  font-size: 10px;
}

/* 技术标签样式 */
.technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.tech-tag {
  background: #3498db;
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 500;
}

.summary-text {
  color: #2c3e50;
  line-height: 1.4;
  margin: 0.3rem 0;
  white-space: pre-line;
  font-size: 13px;
}

/* 打印样式优化 */
@media print {
  .modern-two-column-template {
    box-shadow: none;
    padding: 0 !important;
    margin: 0 !important;
    width: 100vw !important;
    max-width: none !important;
  }
  .resume-body {
    grid-template-columns: 250px 1fr;
    gap: 1rem;
    width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  .sidebar {
    background: #f8f9fa;
    padding: 1rem;
    border-left: none !important;
    align-items: center;
  }
  .experience-item,
  .education-item,
  .project-item,
  .certification-item {
    background: #f8f9fa;
    padding: 0.5rem;
    border-left: none !important;
  }
  .main-section {
    page-break-inside: avoid;
  }
  .vertical-divider {
    background: #b0b0b0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .resume-body {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    order: 2;
  }

  .main-content {
    order: 1;
  }
  .vertical-divider {
    display: none;
  }
}
</style>