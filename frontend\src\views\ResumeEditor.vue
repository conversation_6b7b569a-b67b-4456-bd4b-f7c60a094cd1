<template>
  <div class="resume-editor">
    <div class="editor-header">
      <div class="header-left">
        <el-button @click="$router.back()" :icon="ArrowLeft" type="text">返回</el-button>
        <h2 class="page-title">{{ resumeData.title || '新建简历' }}</h2>
      </div>
      <div class="header-right">
        <el-button @click="previewResume" :icon="View">预览</el-button>
        <el-dropdown @command="handleExport" :disabled="!resumeData.id">
          <el-button type="primary" :icon="Download">
            导出<el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="pdf">导出PDF</el-dropdown-item>
              <el-dropdown-item command="docx">导出DOCX</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button type="primary" @click="saveResume" :loading="saving">保存</el-button>
      </div>
    </div>

    <div class="editor-content">
      <el-row :gutter="20">
        <el-col :span="10">
          <div class="edit-section">
            <!-- 基本信息 -->
            <el-card class="section-card">
              <template #header>
                <div class="card-header" style="cursor: pointer" @click="isBasicInfoExpanded = !isBasicInfoExpanded">
                  <span>基本信息</span>
                  <el-icon class="expand-icon" :class="{ 'is-expanded': isBasicInfoExpanded }">
                    <ArrowDown />
                  </el-icon>
                </div>
              </template>
              <div v-show="isBasicInfoExpanded">
                <el-form label-position="top">
                  <el-form-item label="简历标题">
                    <el-input v-model="resumeData.title" placeholder="请输入简历标题" />
                  </el-form-item>
                  
                  <!-- 照片上传 -->
                  <el-form-item label="个人照片">
                    <el-upload
                      class="avatar-uploader"
                      :action="uploadUrl"
                      :headers="uploadHeaders"
                      :show-file-list="false"
                      :on-success="handlePhotoUploadSuccess"
                      :on-error="handlePhotoUploadError"
                      :before-upload="beforePhotoUpload"
                      :data="{ type: 'resume_photo' }"
                      accept="image/jpeg,image/png"
                    >
                      <img v-if="displayPhotoUrl" :src="displayPhotoUrl" class="avatar" />
                      <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                    </el-upload>
                    <div class="upload-tips">支持 JPG、PNG 格式，文件大小不超过 2MB</div>
                  </el-form-item>
                  
              <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="姓名">
                        <el-input v-model="resumeData.full_name" placeholder="请输入姓名" />
                  </el-form-item>
                </el-col>
                    <el-col :span="8">
                      <el-form-item label="性别">
                        <el-select v-model="resumeData.gender" placeholder="请选择性别">
                          <el-option label="男" value="男" />
                          <el-option label="女" value="女" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="年龄">
                        <el-input-number v-model="resumeData.age" :min="16" :max="65" placeholder="请输入年龄" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="电话">
                        <el-input v-model="resumeData.phone" placeholder="请输入联系电话" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="邮箱">
                        <el-input v-model="resumeData.email" placeholder="请输入邮箱地址" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="地址">
                    <el-input v-model="resumeData.address" placeholder="请输入居住地址" />
              </el-form-item>
              <el-form-item label="求职意向">
                    <el-input v-model="resumeData.objective" placeholder="请输入求职意向" />
              </el-form-item>
              <el-form-item label="个人总结">
                    <el-input type="textarea" v-model="resumeData.summary" :rows="4" placeholder="请输入个人总结" />
              </el-form-item>
                  <div class="form-actions">
                    <el-button type="primary" @click="saveBasicInfo" :loading="savingBasicInfo">保存基本信息</el-button>
            </div>
                </el-form>
              </div>
            </el-card>

            <!-- 教育背景 -->
            <el-card class="section-card">
              <template #header>
                <div class="card-header">
                  <span>教育背景</span>
                  <el-button type="primary" :icon="Plus" circle @click="addEducation" />
                  </div>
              </template>
              <el-collapse v-model="expandedEducation">
                <el-collapse-item v-for="(edu, index) in resumeData.educations" :key="index" :name="index">
                  <template #title>
                    <span class="collapse-title">{{ edu.school_name || '新教育经历' }} {{ edu.major ? `- ${edu.major}` : '' }}</span>
                  </template>
                  <el-form label-position="top">
                    <el-form-item label="学校名称" required>
                      <el-input v-model="edu.school_name" placeholder="请输入学校名称" />
                    </el-form-item>
                  <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="专业" required>
                          <el-input v-model="edu.major" placeholder="请输入专业" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="学位">
                          <el-select v-model="edu.degree" placeholder="请选择学位">
                            <el-option
                              v-for="option in degreeOptions"
                              :key="option.value"
                              :label="option.label"
                              :value="option.value"
                            />
                          </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="开始时间" required>
                          <el-date-picker
                            v-model="edu.start_date"
                            type="date"
                            placeholder="选择开始时间"
                            value-format="YYYY-MM-DD"
                          />
                      </el-form-item>
                    </el-col>
                      <el-col :span="12">
                        <el-form-item label="结束时间" required>
                          <el-date-picker
                            v-model="edu.end_date"
                            type="date"
                            placeholder="选择结束时间"
                            value-format="YYYY-MM-DD"
                          />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="描述">
                      <el-input type="textarea" v-model="edu.description" :rows="3" placeholder="请输入教育经历描述" />
                  </el-form-item>
                    <div class="item-actions">
                      <el-button type="danger" @click.stop="removeEducation(index)">删除</el-button>
                      <el-button type="primary" @click.stop="confirmEducation(index)">确认</el-button>
              </div>
                  </el-form>
                </el-collapse-item>
              </el-collapse>
            </el-card>

            <!-- 工作经历 -->
            <el-card class="section-card">
              <template #header>
                <div class="card-header">
                  <span>工作经历</span>
                  <el-button type="primary" :icon="Plus" circle @click="addWorkExperience" />
                  </div>
              </template>
              <el-collapse v-model="expandedWorkExperience">
                <el-collapse-item v-for="(exp, index) in resumeData.work_experiences" :key="index" :name="index">
                  <template #title>
                    <span class="collapse-title">{{ exp.company_name || '新工作经历' }} {{ exp.position ? `- ${exp.position}` : '' }}</span>
                  </template>
                  <el-form label-position="top">
                    <el-form-item label="公司名称" required>
                      <el-input v-model="exp.company_name" placeholder="请输入公司名称" />
                    </el-form-item>
                    <el-form-item label="职位" required>
                      <el-input v-model="exp.position" placeholder="请输入职位" />
                    </el-form-item>
                  <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="开始时间" required>
                          <el-date-picker 
                            v-model="exp.start_date" 
                            type="date" 
                            placeholder="选择开始时间"
                            value-format="YYYY-MM-DD"
                          />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="结束时间" required>
                          <el-date-picker 
                            v-model="exp.end_date" 
                            type="date" 
                            placeholder="选择结束时间"
                            value-format="YYYY-MM-DD"
                          />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="工作描述">
                      <el-input type="textarea" v-model="exp.description" :rows="3" placeholder="请输入工作描述" />
                  </el-form-item>
                    <div class="item-actions">
                      <el-button type="danger" @click.stop="removeWorkExperience(index)">删除</el-button>
                      <el-button type="primary" @click.stop="confirmWorkExperience(index)">确认</el-button>
                    </div>
                  </el-form>
                </el-collapse-item>
              </el-collapse>
                </el-card>

            <!-- 项目经验 -->
            <el-card class="section-card">
              <template #header>
                <div class="card-header">
                  <span>项目经验</span>
                  <el-button type="primary" :icon="Plus" circle @click="addProject" />
                  </div>
              </template>
              <el-collapse v-model="expandedProject">
                <el-collapse-item v-for="(proj, index) in resumeData.projects" :key="index" :name="index">
                  <template #title>
                    <span class="collapse-title">{{ proj.name || '新项目经验' }} {{ proj.role ? `- ${proj.role}` : '' }}</span>
                  </template>
                  <el-form label-position="top">
                    <el-form-item label="项目名称" required>
                      <el-input v-model="proj.name" placeholder="请输入项目名称" />
                      </el-form-item>
                    <el-form-item label="担任角色" required>
                      <el-input v-model="proj.role" placeholder="请输入担任角色" />
                      </el-form-item>
                  <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="开始时间" required>
                          <el-date-picker 
                            v-model="proj.start_date" 
                            type="date" 
                            placeholder="选择开始时间"
                            value-format="YYYY-MM-DD"
                          />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="结束时间" required>
                          <el-date-picker 
                            v-model="proj.end_date" 
                            type="date" 
                            placeholder="选择结束时间"
                            value-format="YYYY-MM-DD"
                          />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="项目描述">
                      <el-input type="textarea" v-model="proj.description" :rows="3" placeholder="请输入项目描述" />
                  </el-form-item>
                    <div class="item-actions">
                      <el-button type="danger" @click.stop="removeProject(index)">删除</el-button>
                      <el-button type="primary" @click.stop="confirmProject(index)">确认</el-button>
              </div>
                  </el-form>
                </el-collapse-item>
              </el-collapse>
            </el-card>

            <!-- 技能特长 -->
            <el-card class="section-card">
              <template #header>
                <div class="card-header">
                  <span>技能特长</span>
                  <el-button type="primary" :icon="Plus" circle @click="addSkill" />
                  </div>
              </template>
              <el-collapse v-model="expandedSkill">
                <el-collapse-item v-for="(skill, index) in resumeData.skills" :key="index" :name="index">
                  <template #title>
                    <span class="collapse-title">{{ skill.name || '新技能特长' }}</span>
                  </template>
                  <el-form label-position="top">
                    <el-form-item label="技能名称" required>
                      <el-input v-model="skill.name" placeholder="请输入技能名称" />
                      </el-form-item>
                      <el-form-item label="熟练度">
                      <el-select v-model="skill.proficiency" placeholder="请选择熟练度">
                        <el-option label="初级" value="初级" />
                        <el-option label="中级" value="中级" />
                        <el-option label="高级" value="高级" />
                        <el-option label="专家" value="专家" />
                        </el-select>
                      </el-form-item>
                    <el-form-item label="使用年限">
                      <el-input-number v-model="skill.years_experience" :min="0" :max="50" :step="0.5" placeholder="请输入使用年限" />
                    </el-form-item>
                    <div class="item-actions">
                      <el-button type="danger" @click.stop="removeSkill(index)">删除</el-button>
                      <el-button type="primary" @click.stop="confirmSkill(index)">确认</el-button>
              </div>
                  </el-form>
                </el-collapse-item>
              </el-collapse>
            </el-card>

            <!-- 证书荣誉 -->
            <el-card class="section-card">
              <template #header>
                <div class="card-header">
                  <span>证书荣誉</span>
                  <el-button type="primary" :icon="Plus" circle @click="addCertification" />
                  </div>
              </template>
              <el-collapse v-model="expandedCertification">
                <el-collapse-item v-for="(cert, index) in resumeData.certifications" :key="index" :name="index">
                  <template #title>
                    <span class="collapse-title">{{ cert.name || '新证书' }}</span>
                  </template>
                  <el-form label-position="top">
                    <el-form-item label="证书名称" required>
                      <el-input v-model="cert.name" placeholder="请输入证书名称" />
                      </el-form-item>
                      <el-form-item label="颁发机构">
                      <el-input v-model="cert.issuer" placeholder="请输入颁发机构" />
                      </el-form-item>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="获得时间">
                          <el-date-picker
                            v-model="cert.issue_date"
                            type="date"
                            placeholder="选择获得时间"
                            value-format="YYYY-MM-DD"
                          />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="过期时间">
                          <el-date-picker
                            v-model="cert.expiry_date"
                            type="date"
                            placeholder="选择过期时间"
                            value-format="YYYY-MM-DD"
                          />
                      </el-form-item>
                    </el-col>
                  </el-row>
                    <el-form-item label="证书编号">
                      <el-input v-model="cert.credential_id" placeholder="请输入证书编号" />
                    </el-form-item>
                    <div class="item-actions">
                      <el-button type="danger" @click.stop="removeCertification(index)">删除</el-button>
                      <el-button type="primary" @click.stop="confirmCertification(index)">确认</el-button>
                    </div>
                  </el-form>
                </el-collapse-item>
              </el-collapse>
                </el-card>
          </div>
        </el-col>

        <el-col :span="14">
          <div class="preview-section">
            <div class="preview-header">
              <h3>实时预览</h3>
            </div>
            <div class="preview-container" ref="previewContainer">
              <div class="resume-pages" :style="{ transform: `scale(${resumeScale})`, transformOrigin: 'top center' }">
                <!-- 第一页 -->
                <div class="resume-page">
                  <div class="resume-content">
                    <!-- 基本信息 -->
                    <div class="resume-header">
                      <div class="resume-header-content">
                        <div v-if="displayPhotoUrl" class="resume-photo">
                          <img :src="displayPhotoUrl" alt="个人照片" />
                        </div>
                        <div class="resume-info">
                          <h1 class="resume-name">{{ resumeData.full_name || '姓名' }}</h1>
                          <div class="basic-info">
                            <span v-if="resumeData.gender" class="info-item">{{ resumeData.gender }}</span>
                            <span v-if="resumeData.age" class="info-item">{{ resumeData.age }}岁</span>
                          </div>
                          <div class="contact-info">
                            <span v-if="resumeData.phone" class="contact-item">
                              <el-icon><Phone /></el-icon>
                              {{ resumeData.phone }}
                            </span>
                            <span v-if="resumeData.email" class="contact-item">
                              <el-icon><Message /></el-icon>
                              {{ resumeData.email }}
                            </span>
                            <span v-if="resumeData.address" class="contact-item">
                              <el-icon><Location /></el-icon>
                              {{ resumeData.address }}
                            </span>
                          </div>
                        </div>
              </div>
            </div>

                    <!-- 求职意向 -->
                    <div v-if="resumeData.objective" class="resume-section">
                      <h2 class="section-title">求职意向</h2>
                      <div class="section-content">
                        <p>{{ resumeData.objective }}</p>
                      </div>
      </div>

                    <!-- 个人总结 -->
                    <div v-if="resumeData.summary" class="resume-section">
                      <h2 class="section-title">个人总结</h2>
                      <div class="section-content">
                        <p>{{ resumeData.summary }}</p>
        </div>
        </div>

                    <!-- 教育背景 -->
                    <div v-if="resumeData.educations?.length" class="resume-section">
                      <h2 class="section-title">教育背景</h2>
                      <div class="section-content">
                        <div v-for="edu in firstPageEducations" :key="edu.school_name" class="experience-item">
                          <div class="experience-header">
                            <h3 class="experience-title">{{ edu.school_name }}</h3>
                            <span class="experience-date">{{ edu.start_date }} - {{ edu.end_date }}</span>
                          </div>
                          <div class="experience-subtitle">{{ edu.major }} · {{ edu.degree }}</div>
                          <p v-if="edu.description" class="experience-description">{{ edu.description }}</p>
                        </div>
      </div>
    </div>

                    <!-- 工作经历 -->
                    <div v-if="resumeData.work_experiences?.length" class="resume-section">
                      <h2 class="section-title">工作经历</h2>
                      <div class="section-content">
                        <div v-for="exp in firstPageWorkExperiences" :key="exp.company_name" class="experience-item">
                          <div class="experience-header">
                            <h3 class="experience-title">{{ exp.company_name }}</h3>
                            <span class="experience-date">{{ exp.start_date }} - {{ exp.end_date }}</span>
          </div>
                          <div class="experience-subtitle">{{ exp.position }}</div>
                          <p v-if="exp.description" class="experience-description">{{ exp.description }}</p>
          </div>
        </div>
      </div>

                    <!-- 项目经验（仅当工作经历不需要延续到第二页时显示） -->
                    <div v-if="resumeData.projects?.length && !hasRemainingWorkExperiences" class="resume-section">
                      <h2 class="section-title">项目经验</h2>
                      <div class="section-content">
                        <div v-for="proj in firstPageProjects" :key="proj.name" class="experience-item">
                          <div class="experience-header">
                            <h3 class="experience-title">{{ proj.name }}</h3>
                            <span class="experience-date">{{ proj.start_date }} - {{ proj.end_date }}</span>
      </div>
                          <div class="experience-subtitle">{{ proj.role }}</div>
                          <p v-if="proj.description" class="experience-description">{{ proj.description }}</p>
          </div>
        </div>
        </div>
      </div>
                </div>

                <!-- 第二页 -->
                <div class="resume-page" v-if="needSecondPage">
                  <div class="resume-content">
                    <!-- 工作经历（续）- 如果有剩余工作经历，优先显示 -->
                    <div v-if="remainingWorkExperiences.length" class="resume-section">
                      <h2 class="section-title">工作经历（续）</h2>
                      <div class="section-content">
                        <div v-for="exp in remainingWorkExperiences" :key="exp.company_name" class="experience-item">
                          <div class="experience-header">
                            <h3 class="experience-title">{{ exp.company_name }}</h3>
                            <span class="experience-date">{{ exp.start_date }} - {{ exp.end_date }}</span>
                          </div>
                          <div class="experience-subtitle">{{ exp.position }}</div>
                          <p v-if="exp.description" class="experience-description">{{ exp.description }}</p>
                        </div>
                      </div>
                    </div>

                    <!-- 项目经验（当工作经历有延续时，在第二页工作经历后显示；否则显示剩余项目） -->
                    <div v-if="shouldShowProjectsOnSecondPage" class="resume-section">
                      <h2 class="section-title">{{ hasRemainingWorkExperiences ? '项目经验' : '项目经验（续）' }}</h2>
                      <div class="section-content">
                        <div v-for="proj in (hasRemainingWorkExperiences ? resumeData.projects : remainingProjects)" 
                             :key="proj.name" 
                             class="experience-item">
                          <div class="experience-header">
                            <h3 class="experience-title">{{ proj.name }}</h3>
                            <span class="experience-date">{{ proj.start_date }} - {{ proj.end_date }}</span>
                          </div>
                          <div class="experience-subtitle">{{ proj.role }}</div>
                          <p v-if="proj.description" class="experience-description">{{ proj.description }}</p>
                        </div>
                      </div>
                    </div>

                    <!-- 技能特长 -->
                    <div v-if="resumeData.skills?.length" class="resume-section">
                      <h2 class="section-title">技能特长</h2>
                      <div class="section-content skills-grid">
                        <div v-for="skill in resumeData.skills" :key="skill.name" class="skill-item">
                          <span class="skill-name">{{ skill.name }}</span>
                          <span v-if="skill.proficiency" class="skill-proficiency">{{ skill.proficiency }}</span>
                          <span v-if="skill.years_experience" class="skill-years">{{ skill.years_experience }}年</span>
                        </div>
                      </div>
                    </div>

                    <!-- 证书荣誉 -->
                    <div v-if="resumeData.certifications?.length" class="resume-section">
                      <h2 class="section-title">证书荣誉</h2>
                      <div class="section-content">
                        <div v-for="cert in resumeData.certifications" :key="cert.name" class="cert-item">
                          <div class="cert-header">
                            <h3 class="cert-title">{{ cert.name }}</h3>
                            <span v-if="cert.issue_date" class="cert-date">{{ cert.issue_date }}</span>
                          </div>
                          <div v-if="cert.issuer" class="cert-issuer">{{ cert.issuer }}</div>
                          <div v-if="cert.credential_id" class="cert-id">证书编号：{{ cert.credential_id }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Plus, View, Phone, Message, Location, Download, ArrowDown } from '@element-plus/icons-vue'
import api from '@/api'
import { API_BASE_URL } from '@/api'

const router = useRouter()
const route = useRoute()

// 响应式数据
const resumeData = reactive({
  id: null,
  title: '',
  full_name: '',
  gender: '',
  age: null,
  phone: '',
  email: '',
  address: '',
  photo_url: '',
  thumbnail_url: '', // 新增缩略图URL
  objective: '',
  summary: '',
  template_id: null,
  educations: [],
  work_experiences: [],
  projects: [],
  skills: [],
  certifications: []
})

const saving = ref(false)
const expandedEducation = ref(-1)
const expandedWorkExperience = ref(-1)
const expandedProject = ref(-1)
const savingBasicInfo = ref(false)
const expandedSkill = ref(-1)
const expandedCertification = ref(-1)
const isBasicInfoExpanded = ref(true)

// 获取简历ID
const resumeId = computed(() => route.params.id)

// 预览容器引用
const previewContainer = ref(null)
const resumeScale = ref(1)

// 计算是否需要第二页
const needSecondPage = computed(() => {
  return hasRemainingWorkExperiences.value || 
         remainingProjects.value.length > 0 || 
         resumeData.skills?.length > 0 || 
         resumeData.certifications?.length > 0
})

// 学位选项
const degreeOptions = [
  { label: '高中', value: '高中' },
  { label: '实中', value: '实中' },
  { label: '中专', value: '中专' },
  { label: '大专', value: '大专' },
  { label: '学士', value: '学士' },
  { label: '硕士', value: '硕士' },
  { label: '博士', value: '博士' }
]

// 照片上传相关
const uploadUrl = ref('http://localhost:5000/api/images/resume-photo')
const uploadHeaders = ref({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
})

// 计算缩放比例
function calculateScale() {
  nextTick(() => {
    if (previewContainer.value) {
      const containerWidth = previewContainer.value.clientWidth
      const containerHeight = previewContainer.value.clientHeight
      const a4Width = 595 // A4纸宽度（像素）
      const a4Height = 842 // A4纸高度（像素）
      
      // 根据容器宽度计算缩放比例
      const widthScale = containerWidth / a4Width
      const heightScale = (containerHeight - 100) / a4Height // 减去一些空间用于边距
      
      // 使用较小的缩放比例以确保完整显示
      resumeScale.value = Math.min(widthScale, heightScale, 1)
    }
  })
}

// 监听窗口大小变化
let resizeObserver = null

// 获取简历数据
async function fetchResumeData() {
  try {
    console.log('Fetching resume data for ID:', resumeId.value)
    const response = await api.resumes.getDetail(resumeId.value)
    console.log('API Response:', response)
    
    if (response.data.success) {
      const data = response.data.data
      console.log('Resume data received:', data)
      
      // 更新基本信息
      resumeData.id = data.id
      resumeData.title = data.title || ''
      resumeData.full_name = data.full_name || ''
      resumeData.gender = data.gender || ''
      resumeData.age = data.age || null
      resumeData.phone = data.phone || ''
      resumeData.email = data.email || ''
      resumeData.address = data.address || ''
      resumeData.objective = data.objective || ''
      resumeData.summary = data.summary || ''
      resumeData.template_id = data.template_id || 1
      resumeData.photo_url = data.photo_url || ''
      resumeData.thumbnail_url = data.thumbnail_url || '' // 更新缩略图URL
      
      // 更新关联数据
      resumeData.educations = data.educations || []
      resumeData.work_experiences = data.work_experiences || []
      resumeData.projects = data.projects || []
      resumeData.skills = data.skills || []
      resumeData.certifications = data.certifications || []
      
      console.log('Resume data updated successfully')
    } else {
      throw new Error(response.data.message || '获取简历数据失败')
    }
  } catch (error) {
    console.error('获取简历数据失败:', error)
    ElMessage.error(error.message || '未知错误')
    
    // 如果获取失败，跳转到简历管理页面
    router.push('/resumes')
  }
}

// 组件挂载时获取数据
onMounted(async () => {
  console.log('ResumeEditor mounted, resumeId:', resumeId.value)
  
  if (resumeId.value && resumeId.value !== 'new') {
    console.log('Fetching resume data...')
    await fetchResumeData()
  } else {
    console.log('Creating new resume')
    // 如果是新建简历，设置默认值
    resumeData.title = '新建简历'
    resumeData.template_id = 1
  }
  
  // 计算初始缩放比例
  calculateScale()
  
  // 使用ResizeObserver监听容器大小变化
  if (window.ResizeObserver && previewContainer.value) {
    resizeObserver = new ResizeObserver(calculateScale)
    resizeObserver.observe(previewContainer.value)
  } else {
    // 降级方案：使用窗口resize事件
    window.addEventListener('resize', calculateScale)
  }
})

// 组件卸载时清理
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  } else {
    window.removeEventListener('resize', calculateScale)
  }
})

// 保存简历
async function saveResume() {
  saving.value = true
  try {
    let response
    if (resumeData.id) {
      response = await api.resumes.update(resumeData.id, resumeData)
    } else {
      response = await api.resumes.create(resumeData)
    }

    if (response.data.success) {
      ElMessage.success('保存成功')
      if (!resumeData.id) {
        resumeData.id = response.data.data.id
        router.replace(`/resume/edit/${response.data.data.id}`)
      }
    }
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('Save resume error:', error)
  } finally {
    saving.value = false
  }
}

// 预览简历
function previewResume() {
  if (resumeId.value) {
    router.push(`/resume/preview/${resumeId.value}`)
  }
}

// 添加教育经历
function addEducation() {
  resumeData.educations.push({
    school_name: '',
    degree: '', // 默认为空，让用户选择
    major: '',
    start_date: '',
    end_date: '',
    description: ''
  })
  expandedEducation.value = resumeData.educations.length - 1
}

// 删除教育经历
async function removeEducation(index) {
  try {
    resumeData.educations.splice(index, 1)
    // 保存更改
    if (resumeData.id) {
      const response = await api.resumes.update(resumeData.id, resumeData)
    if (response.data.success) {
        ElMessage.success('教育经历删除成功')
      } else {
        throw new Error(response.data.message || '删除失败')
      }
    } else {
      ElMessage.success('教育经历删除成功')
    }
  } catch (error) {
    console.error('Remove education error:', error)
    ElMessage.error(error.message || '删除失败')
  }
}

// 确认教育经历
async function confirmEducation(index) {
  const edu = resumeData.educations[index]
  
  // 验证必填字段
  if (!edu.school_name?.trim()) {
    ElMessage.error('请输入学校名称')
    return
  }
  if (!edu.major?.trim()) {
    ElMessage.error('请输入专业')
    return
  }
  if (!edu.start_date) {
    ElMessage.error('请选择开始时间')
    return
  }
  if (!edu.end_date) {
    ElMessage.error('请选择结束时间')
    return
  }

  // 验证日期顺序
  if (new Date(edu.start_date) > new Date(edu.end_date)) {
    ElMessage.error('开始时间不能晚于结束时间')
    return
  }

  // 格式化数据
  edu.school_name = edu.school_name.trim()
  edu.major = edu.major.trim()
  edu.degree = edu.degree?.trim() || ''
  edu.description = edu.description?.trim() || ''

  try {
    // 保存简历
    let response
    if (resumeData.id) {
      response = await api.resumes.update(resumeData.id, resumeData)
    } else {
      response = await api.resumes.create(resumeData)
    }
    
    if (response.data.success) {
      ElMessage.success('教育经历保存成功')
      // 如果是新建简历，更新路由
      if (!resumeData.id && response.data.data?.id) {
        resumeData.id = response.data.data.id
        router.replace(`/resume/edit/${response.data.data.id}`)
      }
      // 关闭当前展开的面板
      expandedEducation.value = -1
    } else {
      throw new Error(response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('Save education error:', error)
    ElMessage.error(error.message || '保存失败')
  }
}

// 添加工作经历
function addWorkExperience() {
  resumeData.work_experiences.push({
    company_name: '',
    position: '',
    start_date: '',
    end_date: '',
    is_current: false,
    description: '',
    achievements: []
  })
  expandedWorkExperience.value = resumeData.work_experiences.length - 1
}

// 删除工作经历
function removeWorkExperience(index) {
  resumeData.work_experiences.splice(index, 1)
}

// 确认工作经历
async function confirmWorkExperience(index) {
  const exp = resumeData.work_experiences[index]
  
  // 验证必填字段
  if (!exp.company_name?.trim()) {
    ElMessage.error('请输入公司名称')
    return
  }
  if (!exp.position?.trim()) {
    ElMessage.error('请输入职位')
    return
  }
  if (!exp.start_date) {
    ElMessage.error('请选择开始时间')
    return
  }
  if (!exp.end_date) {
    ElMessage.error('请选择结束时间')
    return
  }

  // 验证日期顺序
  if (new Date(exp.start_date) > new Date(exp.end_date)) {
    ElMessage.error('开始时间不能晚于结束时间')
    return
  }

  // 格式化数据
  exp.company_name = exp.company_name.trim()
  exp.position = exp.position.trim()
  exp.description = exp.description?.trim() || ''

  try {
    // 保存简历
    let response
    if (resumeData.id) {
      response = await api.resumes.update(resumeData.id, resumeData)
    } else {
      response = await api.resumes.create(resumeData)
    }
    
    if (response.data.success) {
      ElMessage.success('工作经历保存成功')
      // 如果是新建简历，更新路由
      if (!resumeData.id && response.data.data?.id) {
        resumeData.id = response.data.data.id
        router.replace(`/resume/edit/${response.data.data.id}`)
      }
      // 关闭当前展开的面板
      expandedWorkExperience.value = -1
    } else {
      throw new Error(response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('Save work experience error:', error)
    ElMessage.error(error.message || '保存工作经历失败')
  }
}

// 添加项目经验
function addProject() {
  resumeData.projects.push({
    name: '',
    role: '',
    start_date: '',
    end_date: '',
    description: ''
  })
  // 展开新添加的项目
  expandedProject.value = resumeData.projects.length - 1
}

// 删除项目经验
function removeProject(index) {
  resumeData.projects.splice(index, 1)
}

// 确认项目经验
async function confirmProject(index) {
  const proj = resumeData.projects[index]
  
  // 验证必填字段
  if (!proj.name?.trim()) {
    ElMessage.error('请输入项目名称')
    return
  }
  if (!proj.role?.trim()) {
    ElMessage.error('请输入担任角色')
    return
  }
  if (!proj.start_date) {
    ElMessage.error('请选择开始时间')
    return
  }
  if (!proj.end_date) {
    ElMessage.error('请选择结束时间')
    return
  }

  // 验证日期顺序
  if (new Date(proj.start_date) > new Date(proj.end_date)) {
    ElMessage.error('开始时间不能晚于结束时间')
    return
  }

  // 格式化数据
  proj.name = proj.name.trim()
  proj.role = proj.role.trim()
  proj.description = proj.description?.trim() || ''

  try {
    // 保存简历
    let response
    if (resumeData.id) {
      response = await api.resumes.update(resumeData.id, resumeData)
    } else {
      response = await api.resumes.create(resumeData)
    }
    
    if (response.data.success) {
      ElMessage.success('项目经验保存成功')
      // 如果是新建简历，更新路由
      if (!resumeData.id && response.data.data?.id) {
        resumeData.id = response.data.data.id
        router.replace(`/resume/edit/${response.data.data.id}`)
      }
      // 关闭当前展开的面板
      expandedProject.value = -1
    } else {
      throw new Error(response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('Save project error:', error)
    ElMessage.error(error.message || '保存项目经验失败')
  }
}

// 添加技能
function addSkill() {
  resumeData.skills.push({
    name: '',
    proficiency: '',
    years_experience: null
  })
  // 展开新添加的技能
  expandedSkill.value = resumeData.skills.length - 1
}

// 删除技能
function removeSkill(index) {
  resumeData.skills.splice(index, 1)
}

// 确认技能
async function confirmSkill(index) {
  const skill = resumeData.skills[index]
  
  // 验证必填字段
  if (!skill.name?.trim()) {
    ElMessage.error('请输入技能名称')
    return
  }

  // 格式化数据
  skill.name = skill.name.trim()
  
  try {
    // 保存简历
    let response
    if (resumeData.id) {
      response = await api.resumes.update(resumeData.id, resumeData)
    } else {
      response = await api.resumes.create(resumeData)
    }
    
    if (response.data.success) {
      ElMessage.success('技能保存成功')
      // 如果是新建简历，更新路由
      if (!resumeData.id && response.data.data?.id) {
        resumeData.id = response.data.data.id
        router.replace(`/resume/edit/${response.data.data.id}`)
      }
      // 关闭当前展开的面板
      expandedSkill.value = -1
    } else {
      throw new Error(response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('Save skill error:', error)
    ElMessage.error(error.message || '保存技能失败')
  }
}

// 添加证书
function addCertification() {
  resumeData.certifications.push({
    name: '',
    issuer: '',
    issue_date: '',
    expiry_date: '',
    credential_id: ''
  })
  // 展开新添加的证书
  expandedCertification.value = resumeData.certifications.length - 1
}

// 删除证书
function removeCertification(index) {
  resumeData.certifications.splice(index, 1)
}

// 确认证书
async function confirmCertification(index) {
  const cert = resumeData.certifications[index]
  
  // 验证必填字段
  if (!cert.name?.trim()) {
    ElMessage.error('请输入证书名称')
    return
  }

  // 验证日期顺序
  if (cert.issue_date && cert.expiry_date && new Date(cert.issue_date) > new Date(cert.expiry_date)) {
    ElMessage.error('获得时间不能晚于过期时间')
    return
  }

  // 格式化数据
  cert.name = cert.name.trim()
  cert.issuer = cert.issuer?.trim() || ''
  cert.credential_id = cert.credential_id?.trim() || ''
  
  try {
    // 保存简历
    let response
    if (resumeData.id) {
      response = await api.resumes.update(resumeData.id, resumeData)
    } else {
      response = await api.resumes.create(resumeData)
    }

    if (response.data.success) {
      ElMessage.success('证书保存成功')
      // 如果是新建简历，更新路由
      if (!resumeData.id && response.data.data?.id) {
        resumeData.id = response.data.data.id
        router.replace(`/resume/edit/${response.data.data.id}`)
      }
      // 关闭当前展开的面板
      expandedCertification.value = -1
    } else {
      throw new Error(response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('Save certification error:', error)
    ElMessage.error(error.message || '保存证书失败')
  }
}

// 保存基本信息
async function saveBasicInfo() {
  // 验证必填字段
  if (!resumeData.title?.trim()) {
    ElMessage.error('请输入简历标题')
    return
  }
  if (!resumeData.full_name?.trim()) {
    ElMessage.error('请输入姓名')
    return
  }
  if (!resumeData.phone?.trim()) {
    ElMessage.error('请输入联系电话')
    return
  }
  if (!resumeData.email?.trim()) {
    ElMessage.error('请输入邮箱地址')
    return
  }

  savingBasicInfo.value = true
  try {
    // 格式化数据
    resumeData.title = resumeData.title.trim()
    resumeData.full_name = resumeData.full_name.trim()
    resumeData.phone = resumeData.phone.trim()
    resumeData.email = resumeData.email.trim()
    resumeData.address = resumeData.address?.trim() || ''
    resumeData.objective = resumeData.objective?.trim() || ''
    resumeData.summary = resumeData.summary?.trim() || ''

    let response
    if (resumeData.id) {
      response = await api.resumes.update(resumeData.id, resumeData)
    } else {
      response = await api.resumes.create(resumeData)
    }

    if (response.data.success) {
      ElMessage.success('基本信息保存成功')
      // 如果是新建简历，更新路由
      if (!resumeData.id && response.data.data?.id) {
        resumeData.id = response.data.data.id
        router.replace(`/resume/edit/${response.data.data.id}`)
      }
      // 收起基本信息表单
      isBasicInfoExpanded.value = false
    } else {
      throw new Error(response.data.message || '保存失败')
    }
  } catch (error) {
    console.error('Save basic info error:', error)
    ElMessage.error(error.message || '保存基本信息失败')
  } finally {
    savingBasicInfo.value = false
  }
}

// 导出简历
async function handleExport(format) {
  if (!resumeData.id) {
    ElMessage.warning('请先保存简历')
    return
  }

  try {
    const response = await api.resumes.export(resumeData.id, format)
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    
    if (format === 'pdf') {
      // PDF功能暂时不可用，下载HTML版本供打印
      link.download = `${resumeData.full_name || '简历'}_${resumeData.title || '未命名'}.html`
      ElMessage.info('PDF功能暂时不可用，已下载HTML版本，您可以在浏览器中打开并打印为PDF')
    } else {
      link.download = `${resumeData.full_name || '简历'}_${resumeData.title || '未命名'}.${format}`
      ElMessage.success('导出成功')
    }
    
    link.click()
    window.URL.revokeObjectURL(url)
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  }
}

// 在script setup部分添加以下计算属性
const firstPageEducations = computed(() => {
  return resumeData.educations?.slice(0, 2) || []
})

const firstPageWorkExperiences = computed(() => {
  return resumeData.work_experiences?.slice(0, 2) || []
})

const firstPageProjects = computed(() => {
  // 只在工作经历不需要延续到第二页时才显示项目
  if (hasRemainingWorkExperiences.value) {
    return []
  }
  return resumeData.projects?.slice(0, 2) || []
})

const remainingWorkExperiences = computed(() => {
  return resumeData.work_experiences?.slice(2) || []
})

const remainingProjects = computed(() => {
  // 如果工作经历需要延续，则所有项目都在第二页显示
  if (hasRemainingWorkExperiences.value) {
    return []
  }
  return resumeData.projects?.slice(2) || []
})

const hasRemainingWorkExperiences = computed(() => {
  return (resumeData.work_experiences?.length || 0) > 2
})

const shouldShowProjectsOnSecondPage = computed(() => {
  return hasRemainingWorkExperiences.value || remainingProjects.value.length > 0
})

// 照片上传处理
function beforePhotoUpload(file) {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传头像图片只能是 JPG/PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传头像图片大小不能超过 2MB!')
    return false
  }
  return true
}

const displayPhotoUrl = computed(() => {
  // 优先用 thumbnail_url，其次 photo_url
  let url = resumeData.thumbnail_url || resumeData.photo_url
  if (!url) return ''
  if (url.startsWith('http')) return url
  return API_BASE_URL + url
})

function handlePhotoUploadSuccess(response) {
  console.log('Photo upload response:', response)
  try {
    if (response.success) {
      // 兼容后端返回结构
      if (response.data) {
        resumeData.photo_url = response.data.url
        resumeData.thumbnail_url = response.data.thumbnail_url
      } else {
        resumeData.photo_url = response.url
        resumeData.thumbnail_url = response.thumbnail_url
      }
      ElMessage.success('照片上传成功')
    } else {
      console.error('Upload failed:', response)
      ElMessage.error(response.message || '照片上传失败')
    }
  } catch (error) {
    console.error('Error handling upload response:', error)
    ElMessage.error('处理上传响应时出错')
  }
}

function handlePhotoUploadError(error, file) {
  console.error('Photo upload error:', error)
  console.error('File details:', {
    name: file.name,
    size: file.size,
    type: file.type
  })
  ElMessage.error(`照片上传失败: ${error.message || '未知错误'}`)
}
</script>

<style scoped>
.resume-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.editor-header {
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 12px;
}

.editor-content {
  flex: 1;
  padding: 20px;
  overflow: hidden;
}

.edit-section {
  height: calc(100vh - 73px - 40px); /* 减去header高度和padding */
  overflow-y: auto;
  padding-right: 10px;
}

.preview-section {
  height: calc(100vh - 73px - 40px); /* 减去header高度和padding */
  overflow: auto;
  background: #f5f7fa;
}

.preview-header {
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.preview-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

/* 美化编辑区域滚动条 */
.edit-section::-webkit-scrollbar {
  width: 6px;
}

.edit-section::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.edit-section::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.edit-section::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.section-card {
  margin-bottom: 16px;
}

.section-card:first-child {
  margin-top: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.card-header .expand-icon {
  transition: transform 0.3s;
}

.card-header .expand-icon.is-expanded {
  transform: rotate(180deg);
}

.collapse-title {
  font-weight: 500;
  color: #303133;
}

.item-actions {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.tech-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-input {
  width: 100px;
  margin-right: 8px;
  vertical-align: bottom;
}

.skill-item, .cert-item {
  margin-bottom: 12px;
}

.preview-container {
  width: 100%;
  margin: 0 auto;
  background: white;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.resume-pages {
  display: flex;
  flex-direction: column;
  gap: 30px;
  width: 595px; /* A4宽度 */
  margin: 0 auto;
  transition: transform 0.3s ease;
}

.resume-page {
  width: 595px; /* A4宽度 */
  height: 842px; /* A4高度 */
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.resume-content {
  padding: 72px 54px; /* 上下2.54cm, 左右1.91cm */
  height: 698px; /* A4纸高度842px - 上下边距144px */
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.resume-header {
  margin-bottom: 20px; /* 减小头部信息的下边距 */
}

.resume-header-content {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.resume-photo {
  flex-shrink: 0;
}

.resume-photo img {
  width: 120px;
  height: 150px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.resume-info {
  flex: 1;
}

.resume-name {
  font-size: 24px;
  margin: 0 0 8px;
  font-weight: 600;
  color: #303133;
}

.basic-info {
  margin-bottom: 12px;
}

.info-item {
  color: #606266;
  margin-right: 16px;
  font-size: 14px;
}

.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px; /* 减小联系方式之间的间距 */
  font-size: 14px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
}

.contact-item .el-icon {
  font-size: 1.1em;
}

.resume-section {
  margin-bottom: 16px; /* 减小段落间距 */
  page-break-inside: avoid;
}

.section-title {
  font-size: 16px;
  color: #303133;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 6px; /* 减小标题下边距 */
  margin-bottom: 12px; /* 减小标题下边距 */
  font-weight: 600;
}

.experience-item {
  margin-bottom: 12px; /* 减小经历项目间距 */
  page-break-inside: avoid;
}

.experience-item:last-child {
  margin-bottom: 0;
}

.experience-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 4px;
}

.experience-title {
  font-size: 15px;
  font-weight: 600;
  margin: 0;
  color: #303133;
}

.experience-date {
  color: #666;
  font-size: 14px;
}

.experience-subtitle {
  color: #666;
  margin-bottom: 4px;
  font-size: 14px;
}

.experience-description {
  margin: 4px 0 0; /* 减小描述文字的上下边距 */
  color: #606266;
  white-space: pre-line;
  font-size: 14px;
  line-height: 1.4; /* 减小行高 */
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.skill-item {
  padding: 8px 12px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  font-size: 14px;
  background: #f8f9fa;
}

.skill-name {
  font-weight: 500;
}

.skill-proficiency {
  color: #409EFF;
  margin-left: 8px;
  font-size: 13px;
}

.skill-years {
  color: #666;
  margin-left: 8px;
  font-size: 13px;
}

.cert-item {
  margin-bottom: 12px;
  padding: 8px 12px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  background: #f8f9fa;
}

.cert-item:last-child {
  margin-bottom: 0;
}

.cert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cert-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0;
  color: #303133;
}

.cert-date {
  color: #666;
  font-size: 13px;
}

.cert-issuer {
  color: #666;
  margin-top: 4px;
  font-size: 13px;
}

.cert-id {
  color: #909399;
  margin-top: 4px;
  font-size: 13px;
}

/* 照片上传样式 */
.avatar-uploader {
  display: block;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: .3s;
  width: 120px;
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.avatar {
  width: 120px;
  height: 150px;
  object-fit: cover;
  display: block;
}

.upload-tips {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
  line-height: 1.4;
}

@media screen {
  .preview-container {
    width: 100% !important; /* 强制宽度与容器一致 */
    max-width: none !important;
  }
  
  .resume-page {
    transform-origin: top center;
    margin: 0 auto;
  }
}

@media print {
  .preview-section {
  padding: 0;
    height: auto;
    overflow: visible;
  }
  
  .preview-container {
    box-shadow: none;
  }
  
  .resume-page {
    box-shadow: none;
    margin: 0;
    page-break-after: always;
  }
  
  .resume-page:last-child {
    page-break-after: avoid;
  }
}
</style> 