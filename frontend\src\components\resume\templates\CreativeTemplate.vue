<template>
  <div class="resume-content-wrapper creative-template">
    <!-- 头部信息 -->
    <header class="resume-header">
      <div class="header-main">
        <div class="personal-info">
          <h1 class="full-name">{{ formattedResumeData.full_name }}</h1>
          <div class="title-info">
            <span v-if="formattedResumeData.objective" class="title-text">{{ formattedResumeData.objective }}</span>
          </div>
          <div class="contact-info">
            <div v-if="formattedResumeData.phone" class="contact-item">
              <span class="contact-icon">📞</span>
              <span class="contact-text">{{ formattedResumeData.phone }}</span>
            </div>
            <div v-if="formattedResumeData.email" class="contact-item">
              <span class="contact-icon">✉️</span>
              <span class="contact-text">{{ formattedResumeData.email }}</span>
            </div>
            <div v-if="formattedResumeData.address" class="contact-item">
              <span class="contact-icon">📍</span>
              <span class="contact-text">{{ formattedResumeData.address }}</span>
            </div>
          </div>
        </div>
        <div v-if="displayPhotoUrl" class="photo-container">
          <img :src="displayPhotoUrl" alt="个人照片" class="photo">
        </div>
      </div>
    </header>

    <!-- 个人总结 -->
    <section v-if="formattedResumeData.summary" class="resume-section">
      <div class="section-header">
        <span class="section-icon">💡</span>
        <h3 class="section-title">个人总结</h3>
      </div>
      <div class="section-content">
        <p class="summary-text">{{ formattedResumeData.summary }}</p>
      </div>
    </section>

    <!-- 工作经历 -->
    <section v-if="formattedResumeData.work_experiences?.length" class="resume-section">
      <div class="section-header">
        <span class="section-icon">💼</span>
        <h3 class="section-title">工作经历</h3>
      </div>
      <div class="section-content">
        <div v-for="exp in formattedResumeData.work_experiences" :key="exp.company_name" class="experience-item">
          <div class="experience-header">
            <h4 class="company-name">{{ exp.company_name }}</h4>
            <span class="period">{{ exp.period }}</span>
          </div>
          <div class="position">{{ exp.position }}</div>
          <p v-if="exp.description" class="description">{{ exp.description }}</p>
          <ul v-if="exp.achievements?.length" class="achievements">
            <li v-for="(achievement, index) in exp.achievements" :key="index">
              {{ achievement }}
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 教育背景 -->
    <section v-if="formattedResumeData.educations?.length" class="resume-section">
      <div class="section-header">
        <span class="section-icon">🎓</span>
        <h3 class="section-title">教育背景</h3>
      </div>
      <div class="section-content">
        <div v-for="edu in formattedResumeData.educations" :key="edu.school_name" class="education-item">
          <div class="education-header">
            <h4 class="school-name">{{ edu.school_name }}</h4>
            <span class="period">{{ edu.period }}</span>
          </div>
          <div class="major">{{ edu.major }} · {{ edu.degree }}</div>
          <p v-if="edu.description" class="description">{{ edu.description }}</p>
        </div>
      </div>
    </section>

    <!-- 项目经验 -->
    <section v-if="formattedResumeData.projects?.length" class="resume-section">
      <div class="section-header">
        <span class="section-icon">🚀</span>
        <h3 class="section-title">项目经验</h3>
      </div>
      <div class="section-content">
        <div v-for="project in formattedResumeData.projects" :key="project.name" class="project-item">
          <div class="project-header">
            <h4 class="project-name">{{ project.name }}</h4>
            <span class="period">{{ project.period }}</span>
          </div>
          <p v-if="project.description" class="description">{{ project.description }}</p>
          <div v-if="project.technologies?.length" class="technologies">
            <span v-for="tech in project.technologies" :key="tech" class="tech-tag">
              {{ tech }}
            </span>
          </div>
        </div>
      </div>
    </section>

    <!-- 技能特长 -->
    <section v-if="formattedResumeData.skills?.length" class="resume-section">
      <div class="section-header">
        <span class="section-icon">⚡</span>
        <h3 class="section-title">技能特长</h3>
      </div>
      <div class="section-content">
        <div class="skills-grid">
          <div v-for="skill in formattedResumeData.skills" :key="skill.name" class="skill-item">
            <div class="skill-header">
              <span class="skill-name">{{ skill.name }}</span>
              <span class="skill-level-text">{{ skill.level }}%</span>
            </div>
            <div class="skill-bar-container">
              <div class="skill-bar" :style="{ width: skill.level + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 证书资质 -->
    <section v-if="formattedResumeData.certifications?.length" class="resume-section">
      <div class="section-header">
        <span class="section-icon">🏆</span>
        <h3 class="section-title">证书资质</h3>
      </div>
      <div class="section-content">
        <div class="certifications-grid">
          <div v-for="cert in formattedResumeData.certifications" :key="cert.name" class="certification-item">
            <div class="cert-icon">📜</div>
            <div class="cert-info">
              <div class="cert-name">{{ cert.name }}</div>
              <div v-if="cert.date" class="cert-date">{{ cert.date }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { computed, watch } from 'vue'
import { getImageUrl } from '@/utils/api'

export default {
  name: 'CreativeTemplate',
  
  props: {
    resumeData: {
      type: Object,
      required: true,
      validator(value) {
        const requiredFields = ['id', 'title', 'full_name']
        const hasRequiredFields = requiredFields.every(field => 
          value[field] !== undefined && value[field] !== null
        )
        
        const arrayFields = ['work_experiences', 'educations', 'projects', 'skills', 'certifications']
        const hasValidArrays = arrayFields.every(field => 
          !value[field] || Array.isArray(value[field])
        )
        
        return hasRequiredFields && hasValidArrays
      }
    }
  },

  emits: ['error'],

  setup(props, { emit }) {
    const formattedResumeData = computed(() => {
      try {
        const data = props.resumeData

        const work_experiences = (data.work_experiences || []).map(exp => {
          return {
            ...exp,
            period: exp.start_date && exp.end_date ? `${exp.start_date} - ${exp.end_date}` : '',
            achievements: Array.isArray(exp.achievements) ? exp.achievements : []
          }
        })

        const educations = (data.educations || []).map(edu => {
          return {
            ...edu,
            period: edu.start_date && edu.end_date ? `${edu.start_date} - ${edu.end_date}` : ''
          }
        })

        const projects = (data.projects || []).map(proj => {
          return {
            ...proj,
            period: proj.start_date && proj.end_date ? `${proj.start_date} - ${proj.end_date}` : '',
            technologies: Array.isArray(proj.technologies) ? proj.technologies : [],
            achievements: Array.isArray(proj.achievements) ? proj.achievements : []
          }
        })

        const skills = (data.skills || []).map(skill => {
          return {
            ...skill,
            level: typeof skill.level === 'number' ? skill.level : 
                   skill.proficiency === '专家' ? 100 :
                   skill.proficiency === '高级' ? 80 :
                   skill.proficiency === '中级' ? 60 :
                   skill.proficiency === '初级' ? 40 : 20
          }
        })

        const certifications = (data.certifications || []).map(cert => {
          return {
            ...cert,
            date: cert.issue_date || cert.date || ''
          }
        })

        return {
          ...data,
          work_experiences,
          educations,
          projects,
          skills,
          certifications
        }
      } catch (err) {
        console.error('简历数据格式化失败:', err)
        emit('error', '简历数据格式化失败')
        return props.resumeData
      }
    })

    const displayPhotoUrl = computed(() => {
      const url = props.resumeData.thumbnail_url || props.resumeData.photo_url
      if (!url) return ''
      if (url.startsWith('http')) return url
      // 兼容相对路径
      return getImageUrl(url)
    })

    watch(() => props.resumeData, (newData) => {
      if (!newData) {
        console.warn('模板接收到空数据')
        return
      }
    }, { immediate: true })

    return {
      formattedResumeData,
      displayPhotoUrl
    }
  }
}
</script>

<style scoped>
.creative-template {
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
  color: #2c3e50;
  font-size: 13px;
  line-height: 1.4;
  padding: 2.54cm 1.97cm;
  background: white;
  max-width: 21cm;
  margin: 0 auto;
}

/* 头部样式 */
.resume-header {
  margin-bottom: 2rem;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  position: relative;
  overflow: hidden;
}

.resume-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
  opacity: 0.3;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
  position: relative;
  z-index: 1;
}

.personal-info {
  flex: 1;
}

.full-name {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.title-info {
  margin-bottom: 1rem;
}

.title-text {
  font-size: 15px;
  color: rgba(255,255,255,0.9);
  font-weight: 500;
}

.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 14px;
  color: rgba(255,255,255,0.9);
}

.contact-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.contact-text {
  color: rgba(255,255,255,0.9);
}

.photo-container {
  width: 120px;
  height: 160px;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
  border: 3px solid rgba(255,255,255,0.3);
}

.photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 章节样式 */
.resume-section {
  margin-bottom: 1rem;
  break-inside: avoid;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.7rem;
  padding: 0.7rem 1rem;
  background: linear-gradient(135deg, #fbefff 0%, #e0eaff 100%);
  border-radius: 8px;
  color: #2c3e50;
  font-size: 15px;
  line-height: 1.2;
}

.section-header .section-title,
.section-header .section-icon {
  color: #2c3e50 !important;
}

.section-title {
  color: #2c3e50 !important;
}

.section-icon {
  color: #2c3e50 !important;
  margin-right: 0.5em;
}

.section-content {
  padding: 0 0.5rem;
}

/* 经验项目通用样式 */
.experience-item,
.education-item,
.project-item {
  margin-bottom: 0.15rem;
  padding: 0.4rem 0.5rem;
}

.experience-item:hover,
.education-item:hover,
.project-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.experience-header,
.education-header,
.project-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 0.3rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.company-name,
.school-name,
.project-name {
  font-size: 15px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.period {
  color: #667eea;
  padding: 0.1rem 0.5rem;
  font-size: 12px;
  font-weight: 500;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 20px;
}

.position,
.major {
  font-weight: 600;
  color: #667eea;
  margin-bottom: 0.2rem;
  font-size: 12.5px;
}

.description {
  color: #2c3e50;
  margin: 0.2rem 0 0.1rem 0;
  line-height: 1.3;
  white-space: pre-line;
  font-size: 11.5px;
}

/* 技能样式 */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.skill-item {
  padding: 0.5rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.skill-name {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
}

.skill-level-text {
  font-size: 11px;
  color: #667eea;
  font-weight: 600;
  background: rgba(102, 126, 234, 0.1);
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
}

.skill-bar-container {
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.skill-bar {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* 证书样式 */
.certifications-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.5rem;
}

.certification-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
}

.cert-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  color: white;
}

.cert-info {
  flex: 1;
}

.cert-name {
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.2rem;
}

.cert-date {
  font-size: 11px;
  color: #667eea;
}

/* 成就列表样式 */
.achievements {
  list-style: none;
  padding: 0;
  margin: 0.8rem 0 0;
}

.achievements li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-size: 12px;
  line-height: 1.3;
}

.achievements li::before {
  content: "✨";
  position: absolute;
  left: 0;
  color: #667eea;
  font-size: 12px;
}

/* 技术标签样式 */
.technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.8rem;
}

.tech-tag {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.summary-text {
  color: #2c3e50;
  line-height: 1.3;
  margin: 0;
  white-space: pre-line;
  font-size: 13px;
}

/* 打印样式优化 */
@media print {
  .creative-template {
    box-shadow: none;
    padding: 0;
  }

  .resume-header {
    background: #667eea;
    padding: 1.5rem;
  }

  .section-header {
    background: #f093fb;
    padding: 0.7rem 1rem;
    font-size: 15px;
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.7rem;
    box-sizing: border-box;
    border-radius: 8px;
    color: #2c3e50;
    display: flex;
    align-items: center;
  }

  .experience-item,
  .education-item,
  .project-item {
    background: #f8f9fa;
    padding: 1rem;
    box-shadow: none;
    border: 1px solid #e9ecef;
  }

  .skill-item,
  .certification-item {
    background: white;
    padding: 0.8rem;
    box-shadow: none;
    border: 1px solid #e9ecef;
  }

  .resume-section {
    page-break-inside: avoid;
  }
}
</style>