from datetime import datetime
from app import db

class AdPosition(db.Model):
    """广告位模型"""
    __tablename__ = 'ad_positions'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='广告位名称')
    code = db.Column(db.String(50), unique=True, nullable=False, comment='广告位代码')
    description = db.Column(db.String(255), comment='广告位描述')
    position_type = db.Column(db.String(20), default='banner', comment='广告位类型')
    width = db.Column(db.Integer, comment='广告位宽度(px)')
    height = db.Column(db.Integer, comment='广告位高度(px)')
    is_enabled = db.Column(db.Boolean, default=True, comment='是否启用')
    is_visible = db.Column(db.<PERSON>, default=True, comment='是否显示')
    page_location = db.Column(db.String(50), comment='页面位置')
    css_class = db.Column(db.String(100), comment='CSS类名')
    sort_order = db.Column(db.Integer, default=0, comment='排序')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(AdPosition, self).__init__(**kwargs)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'position_type': self.position_type,
            'width': self.width,
            'height': self.height,
            'is_enabled': self.is_enabled,
            'is_visible': self.is_visible,
            'page_location': self.page_location,
            'css_class': self.css_class,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<AdPosition {self.name}: {self.code}>' 