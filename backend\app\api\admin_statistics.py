from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required
from datetime import datetime
from app import db
from app.models.user import User
from app.models.business_card import BusinessCard
from app.models.resume import Resume
from app.models.document import Document
from app.models.image import Image
from app.models.points_log import PointsLog

admin_statistics_bp = Blueprint('admin_statistics', __name__, url_prefix='/api/admin')

@admin_statistics_bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_statistics():
    today = datetime.utcnow().date()
    # 用户
    total_users = User.query.count()
    today_users = User.query.filter(User.created_at >= today).count()
    # 名片
    total_business_cards = BusinessCard.query.count()
    today_business_cards = BusinessCard.query.filter(BusinessCard.created_at >= today).count()
    # 简历
    total_resumes = Resume.query.count()
    today_resumes = Resume.query.filter(Resume.created_at >= today).count()
    # 文档
    total_documents = Document.query.count()
    today_documents = Document.query.filter(Document.upload_time >= today).count()
    # 图片
    total_images = Image.query.count()
    today_images = Image.query.filter(Image.created_at >= today).count()
    # 积分
    total_points = db.session.query(db.func.sum(User.points)).scalar() or 0
    today_points = db.session.query(db.func.sum(PointsLog.change)).filter(PointsLog.created_at >= today).scalar() or 0
    # 存储
    total_file_size = (db.session.query(db.func.sum(Image.file_size)).scalar() or 0) + (db.session.query(db.func.sum(Document.file_size)).scalar() or 0)
    # 活跃用户
    active_users = db.session.query(db.func.count(db.func.distinct(PointsLog.user_id))).filter(PointsLog.created_at >= today).scalar() or 0

    return jsonify({
        'users': {'total': total_users, 'today': today_users, 'active': active_users},
        'business_cards': {'total': total_business_cards, 'today': today_business_cards},
        'resumes': {'total': total_resumes, 'today': today_resumes},
        'documents': {'total': total_documents, 'today': today_documents},
        'images': {'total': total_images, 'today': today_images},
        'points': {'total': total_points, 'today': today_points},
        'storage': {'total_size_bytes': total_file_size, 'total_size_mb': round(total_file_size / (1024 * 1024), 2)}
    }) 