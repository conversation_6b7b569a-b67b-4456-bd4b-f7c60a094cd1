from flask import Blueprint, jsonify, request, send_file
from flask_jwt_extended import jwt_required
from datetime import datetime, timedelta
from sqlalchemy import func, extract
from app import db
from app.models.user import User
from app.models.business_card import BusinessCard
from app.models.resume import Resume
from app.models.document import Document
from app.models.image import Image

import pandas as pd
from io import BytesIO

admin_statistics_bp = Blueprint('admin_statistics', __name__, url_prefix='/api/admin')

@admin_statistics_bp.route('/statistics', methods=['GET'])
@jwt_required()
def get_statistics():
    today = datetime.utcnow().date()
    # 用户
    total_users = User.query.count()
    today_users = User.query.filter(User.created_at >= today).count()
    # 名片
    total_business_cards = BusinessCard.query.count()
    today_business_cards = BusinessCard.query.filter(BusinessCard.created_at >= today).count()
    # 简历
    total_resumes = Resume.query.count()
    today_resumes = Resume.query.filter(Resume.created_at >= today).count()
    # 文档
    total_documents = Document.query.count()
    today_documents = Document.query.filter(Document.upload_time >= today).count()
    # 图片
    total_images = Image.query.count()
    today_images = Image.query.filter(Image.created_at >= today).count()
    # 存储
    total_file_size = (db.session.query(func.sum(Image.file_size)).scalar() or 0) + (db.session.query(func.sum(Document.file_size)).scalar() or 0)
    # 活跃用户
    active_users = db.session.query(func.count(func.distinct(User.id))).filter(User.last_login_time >= today).scalar() or 0

    return jsonify({
        'users': {'total': total_users, 'today': today_users, 'active': active_users},
        'business_cards': {'total': total_business_cards, 'today': today_business_cards},
        'resumes': {'total': total_resumes, 'today': today_resumes},
        'documents': {'total': total_documents, 'today': today_documents},
        'images': {'total': total_images, 'today': today_images},

        'storage': {'total_size_bytes': total_file_size, 'total_size_mb': round(total_file_size / (1024 * 1024), 2)}
    })

@admin_statistics_bp.route('/statistics/user-chart', methods=['GET'])
@jwt_required()
def get_user_chart():
    """获取用户统计图表数据"""
    period = request.args.get('period', '7d')
    
    # 根据时间段确定查询范围
    end_date = datetime.utcnow().date()
    if period == '7d':
        start_date = end_date - timedelta(days=7)
    elif period == '30d':
        start_date = end_date - timedelta(days=30)
    elif period == '90d':
        start_date = end_date - timedelta(days=90)
    else:
        start_date = end_date - timedelta(days=7)
    
    # 获取每日用户注册数据
    daily_users = db.session.query(
        func.date(User.created_at).label('date'),
        func.count(User.id).label('count')
    ).filter(
        User.created_at >= start_date,
        User.created_at < end_date + timedelta(days=1)
    ).group_by(func.date(User.created_at)).order_by(func.date(User.created_at)).all()
    
    # 获取用户活跃度数据
    active_users = db.session.query(
        func.date(User.last_login_time).label('date'),
        func.count(func.distinct(User.id)).label('count')
    ).filter(
        User.last_login_time >= start_date,
        User.last_login_time < end_date + timedelta(days=1)
    ).group_by(func.date(User.last_login_time)).order_by(func.date(User.last_login_time)).all()
    
    return jsonify({
        'period': period,
        'daily_registrations': [{'date': str(date), 'count': count} for date, count in daily_users],
        'daily_active_users': [{'date': str(date), 'count': count} for date, count in active_users]
    })

@admin_statistics_bp.route('/statistics/business-chart', methods=['GET'])
@jwt_required()
def get_business_chart():
    """获取业务统计图表数据"""
    period = request.args.get('period', '7d')
    
    # 根据时间段确定查询范围
    end_date = datetime.utcnow().date()
    if period == '7d':
        start_date = end_date - timedelta(days=7)
    elif period == '30d':
        start_date = end_date - timedelta(days=30)
    elif period == '90d':
        start_date = end_date - timedelta(days=90)
    else:
        start_date = end_date - timedelta(days=7)
    
    # 获取每日业务数据
    daily_business_cards = db.session.query(
        func.date(BusinessCard.created_at).label('date'),
        func.count(BusinessCard.id).label('count')
    ).filter(
        BusinessCard.created_at >= start_date,
        BusinessCard.created_at < end_date + timedelta(days=1)
    ).group_by(func.date(BusinessCard.created_at)).order_by(func.date(BusinessCard.created_at)).all()
    
    daily_resumes = db.session.query(
        func.date(Resume.created_at).label('date'),
        func.count(Resume.id).label('count')
    ).filter(
        Resume.created_at >= start_date,
        Resume.created_at < end_date + timedelta(days=1)
    ).group_by(func.date(Resume.created_at)).order_by(func.date(Resume.created_at)).all()
    
    daily_documents = db.session.query(
        func.date(Document.upload_time).label('date'),
        func.count(Document.id).label('count')
    ).filter(
        Document.upload_time >= start_date,
        Document.upload_time < end_date + timedelta(days=1)
    ).group_by(func.date(Document.upload_time)).order_by(func.date(Document.upload_time)).all()
    
    daily_images = db.session.query(
        func.date(Image.created_at).label('date'),
        func.count(Image.id).label('count')
    ).filter(
        Image.created_at >= start_date,
        Image.created_at < end_date + timedelta(days=1)
    ).group_by(func.date(Image.created_at)).order_by(func.date(Image.created_at)).all()
    
    return jsonify({
        'period': period,
        'daily_business_cards': [{'date': str(date), 'count': count} for date, count in daily_business_cards],
        'daily_resumes': [{'date': str(date), 'count': count} for date, count in daily_resumes],
        'daily_documents': [{'date': str(date), 'count': count} for date, count in daily_documents],
        'daily_images': [{'date': str(date), 'count': count} for date, count in daily_images]
    })

@admin_statistics_bp.route('/statistics/user-stats', methods=['GET'])
@jwt_required()
def get_user_stats():
    """获取用户详细统计数据"""
    period = request.args.get('period', '7d')
    
    # 根据时间段确定查询范围
    end_date = datetime.utcnow().date()
    if period == '7d':
        start_date = end_date - timedelta(days=7)
    elif period == '30d':
        start_date = end_date - timedelta(days=30)
    elif period == '90d':
        start_date = end_date - timedelta(days=90)
    else:
        start_date = end_date - timedelta(days=7)
    
    # 用户等级分布
    level_distribution = db.session.query(
        User.vip_level,
        func.count(User.id).label('count')
    ).group_by(User.vip_level).all()
    
    # 用户状态分布
    status_distribution = db.session.query(
        User.status,
        func.count(User.id).label('count')
    ).group_by(User.status).all()
    
    # 用户注册来源（根据邮箱格式推断）
    gmail_count = db.session.query(func.count(User.id)).filter(User.email.like('%@gmail.com')).scalar() or 0
    qq_count = db.session.query(func.count(User.id)).filter(User.email.like('%@qq.com')).scalar() or 0
    count_163 = db.session.query(func.count(User.id)).filter(User.email.like('%@163.com')).scalar() or 0
    count_126 = db.session.query(func.count(User.id)).filter(User.email.like('%@126.com')).scalar() or 0
    other_count = db.session.query(func.count(User.id)).filter(
        ~User.email.like('%@gmail.com'),
        ~User.email.like('%@qq.com'),
        ~User.email.like('%@163.com'),
        ~User.email.like('%@126.com')
    ).scalar() or 0
    
    source_distribution = [
        ('Gmail', gmail_count),
        ('QQ', qq_count),
        ('163', count_163),
        ('126', count_126),
        ('Other', other_count)
    ]
    
    # 用户活跃度统计（基于时间段）
    active_users_period = db.session.query(
        func.count(func.distinct(User.id))
    ).filter(
        User.last_login_time >= start_date,
        User.last_login_time < end_date + timedelta(days=1)
    ).scalar() or 0
    
    # 每日注册数据（基于时间段）
    daily_registrations = db.session.query(
        func.date(User.created_at).label('date'),
        func.count(User.id).label('count')
    ).filter(
        User.created_at >= start_date,
        User.created_at < end_date + timedelta(days=1)
    ).group_by(func.date(User.created_at)).order_by(func.date(User.created_at)).all()
    
    return jsonify({
        'period': period,
        'level_distribution': [{'level': level, 'count': count} for level, count in level_distribution],
        'status_distribution': [{'status': status, 'count': count} for status, count in status_distribution],
        'source_distribution': [{'source': source, 'count': count} for source, count in source_distribution],
        'active_users': active_users_period,
        'daily_registrations': [{'date': str(date), 'count': count} for date, count in daily_registrations]
    })

@admin_statistics_bp.route('/statistics/system-metrics', methods=['GET'])
@jwt_required()
def get_system_metrics():
    """获取系统指标数据"""
    # 数据库大小（近似）
    db_size = 0
    try:
        # SQLite数据库大小
        result = db.session.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
        db_size = result.scalar() or 0
    except:
        pass
    
    # 表记录数统计
    table_counts = {
        'users': User.query.count(),
        'business_cards': BusinessCard.query.count(),
        'resumes': Resume.query.count(),
        'documents': Document.query.count(),
        'images': Image.query.count(),

    }
    
    # 存储使用情况
    total_image_size = db.session.query(func.sum(Image.file_size)).scalar() or 0
    total_document_size = db.session.query(func.sum(Document.file_size)).scalar() or 0
    total_storage = total_image_size + total_document_size
    
    # 最近24小时的操作统计
    last_24h = datetime.utcnow() - timedelta(hours=24)
    recent_operations = db.session.query(
        func.count(UserLog.id)
    ).filter(
        UserLog.created_at >= last_24h
    ).scalar() or 0
    
    return jsonify({
        'database_size': db_size,
        'table_counts': table_counts,
        'storage_usage': {
            'total_bytes': total_storage,
            'total_mb': round(total_storage / (1024 * 1024), 2),
            'images_bytes': total_image_size,
            'documents_bytes': total_document_size
        },
        'recent_operations': recent_operations
    })

@admin_statistics_bp.route('/statistics/export', methods=['GET'])
@jwt_required()
def export_statistics():
    """导出统计报表"""
    try:
        period = request.args.get('period', '7d')
        report_type = request.args.get('type', 'all')
        
        # 根据时间段确定查询范围
        end_date = datetime.utcnow().date()
        if period == '7d':
            start_date = end_date - timedelta(days=7)
        elif period == '30d':
            start_date = end_date - timedelta(days=30)
        elif period == '90d':
            start_date = end_date - timedelta(days=90)
        else:
            start_date = end_date - timedelta(days=7)
        
        # 创建Excel工作簿
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            
            # 1. 数据概览
            overview_data = []
            today = datetime.utcnow().date()
            
            # 用户统计
            total_users = User.query.count()
            today_users = User.query.filter(User.created_at >= today).count()
            active_users = db.session.query(func.count(func.distinct(User.id))).filter(User.last_login_time >= today).scalar() or 0
            
            # 业务统计
            total_business_cards = BusinessCard.query.count()
            today_business_cards = BusinessCard.query.filter(BusinessCard.created_at >= today).count()
            total_resumes = Resume.query.count()
            today_resumes = Resume.query.filter(Resume.created_at >= today).count()
            total_documents = Document.query.count()
            today_documents = Document.query.filter(Document.upload_time >= today).count()
            total_images = Image.query.count()
            today_images = Image.query.filter(Image.created_at >= today).count()
            

            
            # 存储统计
            total_file_size = (db.session.query(func.sum(Image.file_size)).scalar() or 0) + (db.session.query(func.sum(Document.file_size)).scalar() or 0)
            
            overview_data = [
                {'指标': '总用户数', '数值': total_users},
                {'指标': '今日新增用户', '数值': today_users},
                {'指标': '今日活跃用户', '数值': active_users},
                {'指标': '总名片数', '数值': total_business_cards},
                {'指标': '今日新增名片', '数值': today_business_cards},
                {'指标': '总简历数', '数值': total_resumes},
                {'指标': '今日新增简历', '数值': today_resumes},
                {'指标': '总文档数', '数值': total_documents},
                {'指标': '今日新增文档', '数值': today_documents},
                {'指标': '总图片数', '数值': total_images},
                {'指标': '今日新增图片', '数值': today_images},

                {'指标': '总存储(MB)', '数值': round(total_file_size / (1024 * 1024), 2)}
            ]
            
            df_overview = pd.DataFrame(overview_data)
            df_overview.to_excel(writer, sheet_name='数据概览', index=False)
            
            # 2. 用户详细数据
            if report_type in ['all', 'users']:
                # 用户等级分布
                level_distribution = db.session.query(
                    User.vip_level,
                    func.count(User.id).label('count')
                ).group_by(User.vip_level).all()
                
                level_data = [{'等级': level, '用户数': count} for level, count in level_distribution]
                df_level = pd.DataFrame(level_data)
                df_level.to_excel(writer, sheet_name='用户等级分布', index=False)
                
                # 用户状态分布
                status_distribution = db.session.query(
                    User.status,
                    func.count(User.id).label('count')
                ).group_by(User.status).all()
                
                status_map = {0: '正常', 1: '禁用', 2: '未验证'}
                status_data = [{'状态': status_map.get(status, '未知'), '用户数': count} for status, count in status_distribution]
                df_status = pd.DataFrame(status_data)
                df_status.to_excel(writer, sheet_name='用户状态分布', index=False)
                
                # 每日注册数据
                daily_registrations = db.session.query(
                    func.date(User.created_at).label('date'),
                    func.count(User.id).label('count')
                ).filter(
                    User.created_at >= start_date,
                    User.created_at < end_date + timedelta(days=1)
                ).group_by(func.date(User.created_at)).order_by(func.date(User.created_at)).all()
                
                reg_data = [{'日期': str(date), '注册数': count} for date, count in daily_registrations]
                df_reg = pd.DataFrame(reg_data)
                df_reg.to_excel(writer, sheet_name='每日注册统计', index=False)
                
                # 用户活跃度数据
                daily_active = db.session.query(
                    func.date(User.last_login_time).label('date'),
                    func.count(func.distinct(User.id)).label('count')
                ).filter(
                    User.last_login_time >= start_date,
                    User.last_login_time < end_date + timedelta(days=1)
                ).group_by(func.date(User.last_login_time)).order_by(func.date(User.last_login_time)).all()
                
                active_data = [{'日期': str(date), '活跃数': count} for date, count in daily_active]
                df_active = pd.DataFrame(active_data)
                df_active.to_excel(writer, sheet_name='每日活跃用户', index=False)
            
            # 3. 业务详细数据
            if report_type in ['all', 'business']:
                # 每日业务数据
                daily_business_cards = db.session.query(
                    func.date(BusinessCard.created_at).label('date'),
                    func.count(BusinessCard.id).label('count')
                ).filter(
                    BusinessCard.created_at >= start_date,
                    BusinessCard.created_at < end_date + timedelta(days=1)
                ).group_by(func.date(BusinessCard.created_at)).order_by(func.date(BusinessCard.created_at)).all()
                
                daily_resumes = db.session.query(
                    func.date(Resume.created_at).label('date'),
                    func.count(Resume.id).label('count')
                ).filter(
                    Resume.created_at >= start_date,
                    Resume.created_at < end_date + timedelta(days=1)
                ).group_by(func.date(Resume.created_at)).order_by(func.date(Resume.created_at)).all()
                
                daily_documents = db.session.query(
                    func.date(Document.upload_time).label('date'),
                    func.count(Document.id).label('count')
                ).filter(
                    Document.upload_time >= start_date,
                    Document.upload_time < end_date + timedelta(days=1)
                ).group_by(func.date(Document.upload_time)).order_by(func.date(Document.upload_time)).all()
                
                daily_images = db.session.query(
                    func.date(Image.created_at).label('date'),
                    func.count(Image.id).label('count')
                ).filter(
                    Image.created_at >= start_date,
                    Image.created_at < end_date + timedelta(days=1)
                ).group_by(func.date(Image.created_at)).order_by(func.date(Image.created_at)).all()
                
                # 合并业务数据
                all_dates = set([str(date) for date, _ in daily_business_cards + daily_resumes + daily_documents + daily_images])
                business_data = []
                for date in sorted(all_dates):
                    bc_count = next((count for d, count in daily_business_cards if str(d) == date), 0)
                    resume_count = next((count for d, count in daily_resumes if str(d) == date), 0)
                    doc_count = next((count for d, count in daily_documents if str(d) == date), 0)
                    img_count = next((count for d, count in daily_images if str(d) == date), 0)
                    
                    business_data.append({
                        '日期': date,
                        '名片数': bc_count,
                        '简历数': resume_count,
                        '文档数': doc_count,
                        '图片数': img_count,
                        '总数': bc_count + resume_count + doc_count + img_count
                    })
                
                df_business = pd.DataFrame(business_data)
                df_business.to_excel(writer, sheet_name='业务数据统计', index=False)
            
            # 4. 系统指标
            if report_type in ['all', 'system']:
                # 数据库大小
                db_size = 0
                try:
                    result = db.session.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
                    db_size = result.scalar() or 0
                except:
                    pass
                
                # 表记录数
                table_counts = {
                    'users': User.query.count(),
                    'business_cards': BusinessCard.query.count(),
                    'resumes': Resume.query.count(),
                    'documents': Document.query.count(),
                    'images': Image.query.count(),
            
                }
                
                system_data = [
                    {'指标': '数据库大小(MB)', '数值': round(db_size / (1024 * 1024), 2)},
                    {'指标': '用户表记录数', '数值': table_counts['users']},
                    {'指标': '名片表记录数', '数值': table_counts['business_cards']},
                    {'指标': '简历表记录数', '数值': table_counts['resumes']},
                    {'指标': '文档表记录数', '数值': table_counts['documents']},
                    {'指标': '图片表记录数', '数值': table_counts['images']},

                ]
                
                df_system = pd.DataFrame(system_data)
                df_system.to_excel(writer, sheet_name='系统指标', index=False)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'统计报表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )
        
    except Exception as e:
        import traceback
        from flask import current_app
        current_app.logger.error(f"导出统计报表失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({'error': '导出失败，请稍后重试'}), 500