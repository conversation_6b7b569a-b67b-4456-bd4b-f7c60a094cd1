import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '../api'

export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const config = ref({})
  const isInitialized = ref(false)
  
  // 计算属性
  const appInfo = computed(() => config.value.app_info || {})
  const systemConfig = computed(() => config.value.config || {})
  const maxUploadSize = computed(() => systemConfig.value.max_upload_size || 16777216)
  const allowedImageTypes = computed(() => systemConfig.value.allowed_image_types || [])
  
  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
  
  // 方法
  const setLoading = (status) => {
    loading.value = status
  }
  
  const loadConfig = async () => {
    try {
      const response = await api.common.getConfig()
      config.value = response.data
      console.log('应用配置加载完成:', config.value)
      return config.value
    } catch (error) {
      console.error('加载应用配置失败:', error)
      throw error
    }
  }
  
  const healthCheck = async () => {
    try {
      const response = await api.common.healthCheck()
      return response.data
    } catch (error) {
      console.error('健康检查失败:', error)
      throw error
    }
  }
  
  const validateImageFile = (file) => {
    const errors = []
    
    // 检查文件类型
    if (allowedImageTypes.value.length > 0) {
      const fileType = file.type.split('/')[1]
      if (!allowedImageTypes.value.includes(fileType)) {
        errors.push(`不支持的文件类型，支持的格式：${allowedImageTypes.value.join(', ')}`)
      }
    }
    
    // 检查文件大小
    if (file.size > maxUploadSize.value) {
      errors.push(`文件大小超过限制，最大允许 ${formatFileSize(maxUploadSize.value)}`)
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }
  
  const init = async () => {
    if (isInitialized.value) {
      return
    }
    
    try {
      setLoading(true)
      
      // 加载基础配置
      await loadConfig()
      
      isInitialized.value = true
      console.log('应用初始化完成')
    } catch (error) {
      console.error('应用初始化失败:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }
  
  // 格式化字节数
  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const dm = decimals < 0 ? 0 : decimals
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
    
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
  }
  
  return {
    // 状态
    loading,
    config,
    isInitialized,
    
    // 计算属性
    appInfo,
    systemConfig,
    maxUploadSize,
    allowedImageTypes,
    
    // 方法
    setLoading,
    loadConfig,
    healthCheck,
    validateImageFile,
    formatBytes,
    init
  }
}) 