// 打印工具函数
export const createPrintWindow = (resumeContent, resumeTitle, currentStyles) => {
  // 创建打印窗口
  const printWindow = window.open('', '_blank', 'width=800,height=600')
  if (!printWindow) {
    throw new Error('无法打开打印窗口，请检查浏览器弹窗设置。')
  }

  // 构建打印页面HTML
  const htmlParts = [
    '<!DOCTYPE html>',
    '<html lang="zh-CN">',
    '<head>',
    '<meta charset="UTF-8">',
    '<meta name="viewport" content="width=device-width, initial-scale=1.0">',
    '<title>' + (resumeTitle || '简历') + '</title>',
    '<style>',
    '/* 重置样式 */',
    '* { margin: 0; padding: 0; box-sizing: border-box; }',
    'body { font-family: "Microsoft YaHei", Arial, sans-serif; line-height: 1.4; color: #333; background: white; padding: 20px; }',
    '/* 打印样式 */',
    '@media print {',
    'body { padding: 0; margin: 0; }',
    '.resume-content-wrapper { width: 100% !important; max-width: none !important; margin: 0 !important; padding: 0 !important; }',
    '@page { size: A4; margin: 15mm; }',
    '.no-print { display: none !important; }',
    '}',
    '/* 简历内容样式 */',
    '.resume-content-wrapper { width: 210mm; min-height: 297mm; margin: 0 auto; background: white; box-shadow: none; }',
    '.resume-body { display: block !important; }',
    '.sidebar, .main-content { display: block !important; }',
    '.sidebar { float: left; width: 30%; margin-right: 2%; }',
    '.main-content { float: left; width: 68%; }',
    '.resume-body::after { content: ""; display: table; clear: both; }',
    'table { width: 100%; border-collapse: collapse; }',
    'img { max-width: 100%; height: auto; }',
    currentStyles,
    '</style>',
    '</head>',
    '<body>',
    '<div class="resume-content-wrapper">',
    resumeContent,
    '</div>',
    '<script>',
    'window.onload = function() {',
    'setTimeout(function() {',
    'window.print();',
    'window.onafterprint = function() { window.close(); };',
    '}, 500);',
    '};',
    '</script>',
    '</body>',
    '</html>'
  ]
  
  const printHtml = htmlParts.join('')

  // 写入打印窗口
  printWindow.document.write(printHtml)
  printWindow.document.close()
  
  return printWindow
}

// 获取当前页面样式
export const getCurrentStyles = () => {
  return Array.from(document.styleSheets)
    .filter(sheet => !sheet.href || sheet.href.startsWith(window.location.origin))
    .map(sheet => {
      try {
        return Array.from(sheet.cssRules || sheet.rules || [])
          .map(rule => rule.cssText)
          .join('\n')
      } catch (e) {
        return ''
      }
    })
    .join('\n')
} 