<template>
  <div class="statistics">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">数据统计</h2>
        <p class="page-description">系统运营数据概览，用户增长，业务统计等</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="exportReport">
          <el-icon><Download /></el-icon>
          导出报表
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon users">👥</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalUsers }}</div>
                <div class="stat-label">总用户数</div>
                <div class="stat-change" :class="stats.userGrowth >= 0 ? 'positive' : 'negative'">
                  {{ stats.userGrowth >= 0 ? '+' : '' }}{{ stats.userGrowth }}% 较上月
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon photos">📸</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalPhotos }}</div>
                <div class="stat-label">处理照片</div>
                <div class="stat-change" :class="stats.photoGrowth >= 0 ? 'positive' : 'negative'">
                  {{ stats.photoGrowth >= 0 ? '+' : '' }}{{ stats.photoGrowth }}% 较上月
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revenue">💰</div>
              <div class="stat-info">
                <div class="stat-value">¥{{ stats.totalRevenue }}</div>
                <div class="stat-label">总收入</div>
                <div class="stat-change" :class="stats.revenueGrowth >= 0 ? 'positive' : 'negative'">
                  {{ stats.revenueGrowth >= 0 ? '+' : '' }}{{ stats.revenueGrowth }}% 较上月
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon active">⚡</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.activeUsers }}</div>
                <div class="stat-label">活跃用户</div>
                <div class="stat-change" :class="stats.activeGrowth >= 0 ? 'positive' : 'negative'">
                  {{ stats.activeGrowth >= 0 ? '+' : '' }}{{ stats.activeGrowth }}% 较上月
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 用户增长趋势 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>用户增长趋势</span>
                <el-select v-model="userChartPeriod" size="small" @change="loadUserChart">
                  <el-option label="最近7天" value="7" />
                  <el-option label="最近30天" value="30" />
                  <el-option label="最近90天" value="90" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <div ref="userChartRef" class="chart"></div>
            </div>
          </el-card>
        </el-col>

        <!-- 业务统计 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>业务统计</span>
                <el-select v-model="businessChartType" size="small" @change="loadBusinessChart">
                  <el-option label="按功能" value="function" />
                  <el-option label="按时间" value="time" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <div ref="businessChartRef" class="chart"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计表格 -->
    <div class="detailed-stats">
      <el-card class="stats-table-card">
        <template #header>
          <div class="card-header">
            <span>详细统计数据</span>
            <el-tabs v-model="activeTab" size="small">
              <el-tab-pane label="用户统计" name="users" />
              <el-tab-pane label="业务统计" name="business" />
              <el-tab-pane label="收入统计" name="revenue" />
            </el-tabs>
          </div>
        </template>

        <!-- 用户统计表格 -->
        <div v-if="activeTab === 'users'" class="tab-content">
          <el-table :data="userStats" style="width: 100%">
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="new_users" label="新增用户" width="100" />
            <el-table-column prop="active_users" label="活跃用户" width="100" />
            <el-table-column prop="total_users" label="总用户数" width="100" />
            <el-table-column prop="retention_rate" label="留存率" width="100">
              <template #default="{ row }">
                {{ row.retention_rate }}%
              </template>
            </el-table-column>
            <el-table-column prop="avg_session_time" label="平均会话时长" width="120">
              <template #default="{ row }">
                {{ row.avg_session_time }}分钟
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 业务统计表格 -->
        <div v-if="activeTab === 'business'" class="tab-content">
          <el-table :data="businessStats" style="width: 100%">
            <el-table-column prop="function" label="功能" width="120" />
            <el-table-column prop="usage_count" label="使用次数" width="100" />
            <el-table-column prop="success_rate" label="成功率" width="100">
              <template #default="{ row }">
                {{ row.success_rate }}%
              </template>
            </el-table-column>
            <el-table-column prop="avg_process_time" label="平均处理时间" width="120">
              <template #default="{ row }">
                {{ row.avg_process_time }}秒
              </template>
            </el-table-column>
            <el-table-column prop="user_satisfaction" label="用户满意度" width="100">
              <template #default="{ row }">
                <el-rate v-model="row.user_satisfaction" disabled show-score />
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 收入统计表格 -->
        <div v-if="activeTab === 'revenue'" class="tab-content">
          <el-table :data="revenueStats" style="width: 100%">
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="recharge_amount" label="充值金额" width="120">
              <template #default="{ row }">
                ¥{{ row.recharge_amount }}
              </template>
            </el-table-column>
            <el-table-column prop="consumption_amount" label="消费金额" width="120">
              <template #default="{ row }">
                ¥{{ row.consumption_amount }}
              </template>
            </el-table-column>
            <el-table-column prop="net_revenue" label="净收入" width="120">
              <template #default="{ row }">
                ¥{{ row.net_revenue }}
              </template>
            </el-table-column>
            <el-table-column prop="payment_method" label="支付方式" width="100" />
            <el-table-column prop="conversion_rate" label="转化率" width="100">
              <template #default="{ row }">
                {{ row.conversion_rate }}%
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>

    <!-- 实时监控 -->
    <div class="realtime-monitor">
      <el-card class="monitor-card">
        <template #header>
          <div class="card-header">
            <span>实时监控</span>
            <el-tag :type="systemStatus === 'normal' ? 'success' : 'danger'">
              {{ systemStatus === 'normal' ? '系统正常' : '系统异常' }}
            </el-tag>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="6">
            <div class="monitor-item">
              <div class="monitor-label">CPU使用率</div>
              <div class="monitor-value">{{ systemMetrics.cpu }}%</div>
              <el-progress :percentage="systemMetrics.cpu" :color="getProgressColor(systemMetrics.cpu)" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="monitor-item">
              <div class="monitor-label">内存使用率</div>
              <div class="monitor-value">{{ systemMetrics.memory }}%</div>
              <el-progress :percentage="systemMetrics.memory" :color="getProgressColor(systemMetrics.memory)" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="monitor-item">
              <div class="monitor-label">磁盘使用率</div>
              <div class="monitor-value">{{ systemMetrics.disk }}%</div>
              <el-progress :percentage="systemMetrics.disk" :color="getProgressColor(systemMetrics.disk)" />
            </div>
          </el-col>
          <el-col :span="6">
            <div class="monitor-item">
              <div class="monitor-label">在线用户</div>
              <div class="monitor-value">{{ systemMetrics.onlineUsers }}</div>
              <div class="monitor-trend">实时更新</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import api from '@/api'

// 响应式数据
const activeTab = ref('users')
const userChartPeriod = ref('30')
const businessChartType = ref('function')
const systemStatus = ref('normal')

const userChartRef = ref(null)
const businessChartRef = ref(null)
let userChart = null
let businessChart = null

// 统计数据
const stats = reactive({
  totalUsers: 0,
  totalPhotos: 0,
  totalRevenue: 0,
  activeUsers: 0,
  // 可扩展更多字段
})

const systemMetrics = reactive({
  cpu: 0,
  memory: 0,
  disk: 0,
  onlineUsers: 0
})

const userStats = ref([])
const businessStats = ref([])
const revenueStats = ref([])

// 方法
const loadStats = async () => {
  try {
    const response = await api.admin.getStatistics()
    // 兼容后端返回结构
    const data = response.data
    stats.totalUsers = data.users?.total || 0
    stats.activeUsers = data.users?.active || 0
    stats.totalPhotos = data.images?.total || 0
    // stats.totalRevenue = data.revenue?.total || 0 // 如有收入统计
    // 可继续补充名片、简历、文档等统计
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadUserChart = async () => {
  try {
    const response = await api.admin.getUserChart({ days: userChartPeriod.value })
    const data = response.data
    
    const option = {
      title: {
        text: '用户增长趋势',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新增用户', '活跃用户', '总用户数'],
        bottom: 10
      },
      xAxis: {
        type: 'category',
        data: data.dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新增用户',
          type: 'line',
          data: data.newUsers,
          smooth: true
        },
        {
          name: '活跃用户',
          type: 'line',
          data: data.activeUsers,
          smooth: true
        },
        {
          name: '总用户数',
          type: 'line',
          data: data.totalUsers,
          smooth: true
        }
      ]
    }
    
    if (userChart) {
      userChart.setOption(option)
    }
  } catch (error) {
    console.error('加载用户图表失败:', error)
  }
}

const loadBusinessChart = async () => {
  try {
    const response = await api.admin.getBusinessChart({ type: businessChartType.value })
    const data = response.data
    
    let option = {}
    
    if (businessChartType.value === 'function') {
      option = {
        title: {
          text: '业务功能使用统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            name: '使用次数',
            type: 'pie',
            radius: '50%',
            data: data.map(item => ({
              name: item.function,
              value: item.usage_count
            }))
          }
        ]
      }
    } else {
      option = {
        title: {
          text: '业务时间分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: data.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '处理数量',
            type: 'bar',
            data: data.counts
          }
        ]
      }
    }
    
    if (businessChart) {
      businessChart.setOption(option)
    }
  } catch (error) {
    console.error('加载业务图表失败:', error)
  }
}

const loadDetailedStats = async () => {
  try {
    const [userResponse, businessResponse, revenueResponse] = await Promise.all([
      api.admin.getUserStats(),
      api.admin.getBusinessStats(),
      api.admin.getRevenueStats()
    ])
    
    userStats.value = userResponse.data
    businessStats.value = businessResponse.data
    revenueStats.value = revenueResponse.data
  } catch (error) {
    console.error('加载详细统计失败:', error)
  }
}

const loadSystemMetrics = async () => {
  try {
    const response = await api.admin.getSystemMetrics()
    Object.assign(systemMetrics, response.data)
    
    // 判断系统状态
    if (systemMetrics.cpu > 90 || systemMetrics.memory > 90 || systemMetrics.disk > 90) {
      systemStatus.value = 'warning'
    } else {
      systemStatus.value = 'normal'
    }
  } catch (error) {
    console.error('加载系统指标失败:', error)
  }
}

const refreshData = async () => {
  await Promise.all([
    loadStats(),
    loadUserChart(),
    loadBusinessChart(),
    loadDetailedStats(),
    loadSystemMetrics()
  ])
  ElMessage.success('数据刷新成功')
}

const exportReport = async () => {
  try {
    const response = await api.admin.exportStatisticsReport()
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `统计报表_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('报表导出成功')
  } catch (error) {
    ElMessage.error('报表导出失败')
  }
}

const getProgressColor = (percentage) => {
  if (percentage < 60) return '#67C23A'
  if (percentage < 80) return '#E6A23C'
  return '#F56C6C'
}

// 初始化图表
const initCharts = () => {
  if (userChartRef.value) {
    userChart = echarts.init(userChartRef.value)
  }
  if (businessChartRef.value) {
    businessChart = echarts.init(businessChartRef.value)
  }
}

// 定时刷新系统指标
let metricsTimer = null

onMounted(() => {
  initCharts()
  refreshData()
  
  // 每30秒刷新一次系统指标
  metricsTimer = setInterval(loadSystemMetrics, 30000)
})

onUnmounted(() => {
  if (userChart) {
    userChart.dispose()
  }
  if (businessChart) {
    businessChart.dispose()
  }
  if (metricsTimer) {
    clearInterval(metricsTimer)
  }
})
</script>

<style scoped>
.statistics {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  margin-bottom: 0;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
}

.stat-icon.users {
  background: #e3f2fd;
}

.stat-icon.photos {
  background: #f3e5f5;
}

.stat-icon.revenue {
  background: #e8f5e8;
}

.stat-icon.active {
  background: #fff3e0;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #27ae60;
}

.stat-change.negative {
  color: #e74c3c;
}

.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
}

.chart {
  width: 100%;
  height: 100%;
}

.detailed-stats {
  margin-bottom: 24px;
}

.stats-table-card {
  margin-bottom: 0;
}

.tab-content {
  margin-top: 16px;
}

.realtime-monitor {
  margin-bottom: 24px;
}

.monitor-card {
  margin-bottom: 0;
}

.monitor-item {
  text-align: center;
  padding: 16px;
}

.monitor-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 8px;
}

.monitor-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.monitor-trend {
  font-size: 12px;
  color: #7f8c8d;
}

:deep(.el-progress) {
  margin-top: 8px;
}
</style> 