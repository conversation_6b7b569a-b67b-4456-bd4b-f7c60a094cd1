<template>
  <div class="user-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">用户管理</h2>
        <p class="page-description">管理系统用户，查看用户信息，调整用户积分和状态</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="exportUsers">
          <el-icon><Download /></el-icon>
          导出用户
        </el-button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filters-section">
      <el-card class="filter-card">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="filters.keyword"
              placeholder="搜索用户名/邮箱/手机号"
              clearable
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.status" placeholder="用户状态" clearable @change="handleSearch">
              <el-option label="正常" value="1" />
              <el-option label="禁用" value="2" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.vip_level" placeholder="VIP等级" clearable @change="handleSearch">
              <el-option label="普通用户" value="0" />
              <el-option label="VIP用户" value="1" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="注册开始日期"
              end-placeholder="注册结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 用户列表 -->
    <el-card class="user-list-card">
      <template #header>
        <div class="card-header">
          <span>用户列表 (共 {{ pagination.total }} 人)</span>
          <div class="header-actions">
            <el-button 
              type="danger" 
              :disabled="selectedUsers.length === 0"
              @click="batchDisableUsers"
            >
              批量禁用
            </el-button>
            <el-button 
              type="success" 
              :disabled="selectedUsers.length === 0"
              @click="batchEnableUsers"
            >
              批量启用
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="users"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="40" :src="row.avatar_url ? getImageUrl(row.avatar_url) : null">
                {{ row.username?.charAt(0).toUpperCase() }}
              </el-avatar>
              <div class="user-details">
                <div class="username">{{ row.username }}</div>
                <div class="email">{{ row.email }}</div>
                <div class="phone" v-if="row.phone">{{ row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="积分信息" width="150">
          <template #default="{ row }">
            <div class="points-info">
              <div class="points">积分: {{ row.points || 0 }}</div>
              <div class="points-used">已用: {{ row.points_used || 0 }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="VIP等级" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.vip_level > 0" type="warning">
              VIP{{ row.vip_level }}
            </el-tag>
            <span v-else>普通用户</span>
          </template>
        </el-table-column>
        
        <el-table-column label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="最后登录" width="180">
          <template #default="{ row }">
            <div v-if="row.last_login_time">
              {{ formatDate(row.last_login_time) }}
              <div class="login-ip">{{ row.last_login_ip }}</div>
            </div>
            <span v-else>从未登录</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewUserDetail(row)">查看</el-button>
            <el-button size="small" type="primary" @click="adjustPoints(row)">调积分</el-button>
            <el-button 
              size="small" 
              :type="row.status === 1 ? 'danger' : 'success'"
              @click="toggleUserStatus(row)"
            >
              {{ row.status === 1 ? '禁用' : '启用' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.per_page"
          :page-sizes="[20, 50, 100, 200]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      v-model="showUserDetail"
      title="用户详情"
      width="600px"
    >
      <div v-if="selectedUserDetail" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ selectedUserDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedUserDetail.username }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">{{ selectedUserDetail.email }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ selectedUserDetail.phone || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="昵称">{{ selectedUserDetail.nickname || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ selectedUserDetail.gender === 1 ? '男' : selectedUserDetail.gender === 2 ? '女' : '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="生日">{{ selectedUserDetail.birthday || '未设置' }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="selectedUserDetail.status === 1 ? 'success' : 'danger'">
              {{ selectedUserDetail.status === 1 ? '正常' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="VIP等级">
            <el-tag v-if="selectedUserDetail.vip_level > 0" type="warning">
              VIP{{ selectedUserDetail.vip_level }}
            </el-tag>
            <span v-else>普通用户</span>
          </el-descriptions-item>
          <el-descriptions-item label="当前积分">{{ selectedUserDetail.points || 0 }}</el-descriptions-item>
          <el-descriptions-item label="已用积分">{{ selectedUserDetail.points_used || 0 }}</el-descriptions-item>
          <el-descriptions-item label="注册时间">{{ formatDate(selectedUserDetail.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="最后登录">{{ formatDate(selectedUserDetail.last_login_time) || '从未登录' }}</el-descriptions-item>
          <el-descriptions-item label="最后登录IP" :span="2">{{ selectedUserDetail.last_login_ip || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 积分调整对话框 -->
    <el-dialog
      v-model="showPointsDialog"
      title="积分调整"
      width="400px"
    >
      <el-form :model="pointsForm" label-width="100px">
        <el-form-item label="用户">
          <span>{{ pointsForm.username }}</span>
        </el-form-item>
        <el-form-item label="当前积分">
          <span>{{ pointsForm.currentPoints }}</span>
        </el-form-item>
        <el-form-item label="调整积分">
          <el-input-number 
            v-model="pointsForm.change" 
            :min="-10000" 
            :max="10000"
            placeholder="正数为增加，负数为减少"
          />
        </el-form-item>
        <el-form-item label="调整原因">
          <el-input 
            v-model="pointsForm.remark" 
            type="textarea" 
            :rows="3"
            placeholder="请输入调整原因"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showPointsDialog = false">取消</el-button>
        <el-button type="primary" @click="submitPointsAdjust" :loading="adjustingPoints">
          确认调整
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Download } from '@element-plus/icons-vue'
import api from '@/api'
import { getImageUrl } from '@/utils/imageUtils'

// 响应式数据
const loading = ref(false)
const users = ref([])
const selectedUsers = ref([])
const showUserDetail = ref(false)
const showPointsDialog = ref(false)
const selectedUserDetail = ref(null)
const adjustingPoints = ref(false)

const filters = reactive({
  keyword: '',
  status: '',
  vip_level: '',
  dateRange: []
})

const pagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

const pointsForm = reactive({
  userId: null,
  username: '',
  currentPoints: 0,
  change: 0,
  remark: ''
})

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 方法
const loadUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      ...filters
    }
    
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0]
      params.end_date = filters.dateRange[1]
    }
    
    const response = await api.admin.users.getUsers(params)
    users.value = response.data.users
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载用户列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadUsers()
}

const resetFilters = () => {
  Object.assign(filters, {
    keyword: '',
    status: '',
    vip_level: '',
    dateRange: []
  })
  handleSearch()
}

const handleSelectionChange = (selection) => {
  selectedUsers.value = selection
}

const handleSizeChange = (size) => {
  pagination.per_page = size
  loadUsers()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadUsers()
}

const viewUserDetail = async (user) => {
  try {
    const response = await api.admin.users.getUserDetail(user.id)
    selectedUserDetail.value = response.data
    showUserDetail.value = true
  } catch (error) {
    ElMessage.error('获取用户详情失败')
  }
}

const adjustPoints = (user) => {
  pointsForm.userId = user.id
  pointsForm.username = user.username
  pointsForm.currentPoints = user.points || 0
  pointsForm.change = 0
  pointsForm.remark = ''
  showPointsDialog.value = true
}

const submitPointsAdjust = async () => {
  if (!pointsForm.change) {
    ElMessage.warning('请输入调整积分数量')
    return
  }
  
  adjustingPoints.value = true
  try {
    await api.admin.users.adjustPoints(pointsForm.userId, {
      change: pointsForm.change,
      remark: pointsForm.remark
    })
    ElMessage.success('积分调整成功')
    showPointsDialog.value = false
    loadUsers()
  } catch (error) {
    ElMessage.error('积分调整失败')
  } finally {
    adjustingPoints.value = false
  }
}

const toggleUserStatus = async (user) => {
  const action = user.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}用户 "${user.username}" 吗？`, '确认操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.admin.users.toggleUserStatus(user.id)
    ElMessage.success(`用户${action}成功`)
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`用户${action}失败`)
    }
  }
}

const batchDisableUsers = async () => {
  try {
    await ElMessageBox.confirm(`确定要禁用选中的 ${selectedUsers.value.length} 个用户吗？`, '批量操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const userIds = selectedUsers.value.map(user => user.id)
    await api.admin.users.batchToggleStatus(userIds, 'disable')
    ElMessage.success('批量禁用成功')
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量禁用失败')
    }
  }
}

const batchEnableUsers = async () => {
  try {
    await ElMessageBox.confirm(`确定要启用选中的 ${selectedUsers.value.length} 个用户吗？`, '批量操作', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const userIds = selectedUsers.value.map(user => user.id)
    await api.admin.users.batchToggleStatus(userIds, 'enable')
    ElMessage.success('批量启用成功')
    loadUsers()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量启用失败')
    }
  }
}

const exportUsers = async () => {
  try {
    const response = await api.admin.users.exportUsers(filters)
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `用户列表_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

onMounted(() => {
  loadUsers()
})
</script>

<style scoped>
.user-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
}

.filters-section {
  margin-bottom: 24px;
}

.filter-card {
  margin-bottom: 0;
}

.user-list-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.username {
  font-weight: 500;
  color: #2c3e50;
}

.email {
  font-size: 12px;
  color: #7f8c8d;
}

.phone {
  font-size: 12px;
  color: #7f8c8d;
}

.points-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.points {
  font-weight: 500;
  color: #27ae60;
}

.points-used {
  font-size: 12px;
  color: #e74c3c;
}

.login-ip {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 4px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.user-detail {
  max-height: 500px;
  overflow-y: auto;
}
</style>