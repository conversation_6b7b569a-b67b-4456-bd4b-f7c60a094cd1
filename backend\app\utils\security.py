"""
安全性工具
"""
import time
import logging
from functools import wraps
from typing import Optional, Dict, List, Any
from flask import request, current_app, g
from .error_handler import APIError

logger = logging.getLogger(__name__)

class RateLimiter:
    """请求速率限制器"""
    def __init__(self, max_requests=60, time_window=60):
        self.max_requests = max_requests  # 最大请求数
        self.time_window = time_window    # 时间窗口（秒）
        self.requests = {}  # 记录请求历史

    def is_allowed(self, key: Optional[str]) -> bool:
        """
        检查请求是否允许
        :param key: 请求标识（如IP地址）
        :return: 是否允许请求
        """
        if key is None:
            return False
            
        current_time = time.time()
        if key not in self.requests:
            self.requests[key] = []

        # 清理过期的请求记录
        self.requests[key] = [req_time for req_time in self.requests[key]
                            if current_time - req_time < self.time_window]

        # 检查是否超过限制
        if len(self.requests[key]) >= self.max_requests:
            return False

        # 记录新请求
        self.requests[key].append(current_time)
        return True

    def clean_old_records(self):
        """清理过期记录"""
        current_time = time.time()
        for key in list(self.requests.keys()):
            self.requests[key] = [req_time for req_time in self.requests[key]
                                if current_time - req_time < self.time_window]
            if not self.requests[key]:
                del self.requests[key]

def rate_limit(max_requests=60, time_window=60):
    """
    速率限制装饰器
    :param max_requests: 时间窗口内的最大请求数
    :param time_window: 时间窗口大小（秒）
    """
    limiter = RateLimiter(max_requests, time_window)

    def decorator(f):
        @wraps(f)
        def wrapped(*args, **kwargs):
            if not limiter.is_allowed(request.remote_addr):
                raise APIError('请求过于频繁，请稍后再试', status_code=429)
            return f(*args, **kwargs)
        return wrapped
    return decorator

def require_auth(f):
    """要求用户认证的装饰器"""
    @wraps(f)
    def wrapped(*args, **kwargs):
        if not g.get('user'):
            raise APIError('需要登录', status_code=401)
        return f(*args, **kwargs)
    return wrapped

def require_admin(f):
    """要求管理员权限的装饰器"""
    @wraps(f)
    def wrapped(*args, **kwargs):
        if not g.get('user') or not g.user.is_admin:
            raise APIError('需要管理员权限', status_code=403)
        return f(*args, **kwargs)
    return wrapped

def validate_input(*required_fields):
    """
    输入验证装饰器
    :param required_fields: 必需的字段列表
    """
    def decorator(f):
        @wraps(f)
        def wrapped(*args, **kwargs):
            json_data = request.get_json()
            if not json_data:
                raise APIError('无效的JSON数据')
            
            missing_fields = [field for field in required_fields 
                            if field not in json_data]
            if missing_fields:
                raise APIError(f'缺少必需字段: {", ".join(missing_fields)}')
            
            return f(*args, **kwargs)
        return wrapped
    return decorator

def log_activity(activity_type):
    """
    活动日志装饰器
    :param activity_type: 活动类型
    """
    def decorator(f):
        @wraps(f)
        def wrapped(*args, **kwargs):
            try:
                result = f(*args, **kwargs)
                # 记录成功的活动
                logger.info(f"活动成功 - 类型: {activity_type}, 用户: {g.get('user', 'anonymous')}")
                return result
            except Exception as e:
                # 记录失败的活动
                logger.error(f"活动失败 - 类型: {activity_type}, 用户: {g.get('user', 'anonymous')}, 错误: {str(e)}")
                raise
        return wrapped
    return decorator
