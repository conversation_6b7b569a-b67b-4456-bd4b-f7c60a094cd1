from datetime import datetime
from enum import Enum
from app import db
import uuid
import os

class DocumentType(Enum):
    """文档类型枚举"""
    STANDARD = "standard"  # 标准版原文
    MODIFIED = "modified"  # 修改后原文
    REPORT = "report"      # 差异报告

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class Document(db.Model):
    """文档模型"""
    __tablename__ = 'documents'
    
    id = db.Column(db.Integer, primary_key=True)
    file_id = db.Column(db.String(32), unique=True, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer, nullable=False)
    file_type = db.Column(db.String(10), nullable=False)  # docx, pdf, wps
    mime_type = db.Column(db.String(100))
    document_type = db.Column(db.Enum(DocumentType), nullable=False)
    upload_time = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.Integer, default=1)  # 1=正常, 0=删除
    
    # 关联关系
    user = db.relationship('User', backref=db.backref('documents', lazy=True))
    standard_tasks = db.relationship('ComparisonTask', foreign_keys='ComparisonTask.standard_doc_id', backref='standard_document', lazy=True)
    modified_tasks = db.relationship('ComparisonTask', foreign_keys='ComparisonTask.modified_doc_id', backref='modified_document', lazy=True)
    
    def get_full_path(self):
        """获取完整文件路径"""
        from flask import current_app
        base_upload_dir = os.path.join(os.path.dirname(current_app.config['UPLOAD_FOLDER']), 'backend', 'uploads')
        return os.path.join(base_upload_dir, self.file_path)

    def delete_files(self):
        """删除相关文件"""
        files_to_delete = []
        
        # 添加文档文件
        full_path = self.get_full_path()
        if os.path.exists(full_path):
            files_to_delete.append(full_path)
        
        # 删除文件
        for file_path in files_to_delete:
            try:
                os.remove(file_path)
            except Exception as e:
                print(f"删除文件失败 {file_path}: {e}")

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'file_id': self.file_id,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_type': self.file_type,
            'document_type': self.document_type.value,
            'upload_time': self.upload_time.isoformat() if self.upload_time else None,
            'status': self.status
        }
    
    def get_file_url(self):
        """获取文件访问URL"""
        return f"/static/documents/{self.user_id}/{self.filename}"

class ComparisonTask(db.Model):
    """文档对比任务模型"""
    __tablename__ = 'comparison_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.String(32), unique=True, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    task_name = db.Column(db.String(255))
    standard_doc_id = db.Column(db.Integer, db.ForeignKey('documents.id'), nullable=False)
    modified_doc_id = db.Column(db.Integer, db.ForeignKey('documents.id'), nullable=False)
    report_doc_id = db.Column(db.Integer, db.ForeignKey('documents.id'), nullable=True)
    
    # 对比配置 - 扩展新字段
    ignore_whitespace = db.Column(db.Boolean, default=True)
    detailed_report = db.Column(db.Boolean, default=True)
    extract_tables = db.Column(db.Boolean, default=True)  # 新增：是否提取表格
    use_ocr = db.Column(db.Boolean, default=False)  # 新增：是否使用OCR
    visual_comparison = db.Column(db.Boolean, default=False)  # 新增：是否视觉对比
    
    # 任务状态
    status = db.Column(db.Enum(TaskStatus), default=TaskStatus.PENDING)
    progress = db.Column(db.Integer, default=0)  # 进度百分比
    status_message = db.Column(db.String(500))
    error_message = db.Column(db.Text)
    
    # 结果信息
    diff_count = db.Column(db.Integer, default=0)  # 差异数量
    processing_time = db.Column(db.Float)  # 处理时间（秒）
    report_path = db.Column(db.String(500))  # 报告文件路径
    similarity = db.Column(db.Float)  # 相似度
    
    # 时间戳
    created_time = db.Column(db.DateTime, default=datetime.utcnow)
    started_time = db.Column(db.DateTime)
    completed_time = db.Column(db.DateTime)
    
    # 关联关系
    user = db.relationship('User', backref=db.backref('comparison_tasks', lazy=True))
    report_document = db.relationship('Document', foreign_keys=[report_doc_id], backref='report_tasks')
    
    def __init__(self, **kwargs):
        super(ComparisonTask, self).__init__(**kwargs)
        if not self.task_id:
            self.task_id = self.generate_task_id()
    
    @staticmethod
    def generate_task_id():
        """生成唯一任务ID"""
        return str(uuid.uuid4()).replace('-', '')
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'task_id': self.task_id,
            'task_name': self.task_name,
            'standard_doc': self.standard_document.to_dict() if self.standard_document else None,
            'modified_doc': self.modified_document.to_dict() if self.modified_document else None,
            'report_doc': self.report_document.to_dict() if self.report_document else None,
            'ignore_whitespace': self.ignore_whitespace,
            'detailed_report': self.detailed_report,
            'extract_tables': self.extract_tables,
            'use_ocr': self.use_ocr,
            'visual_comparison': self.visual_comparison,
            'status': self.status.value,
            'progress': self.progress,
            'status_message': self.status_message,
            'error_message': self.error_message,
            'diff_count': self.diff_count,
            'processing_time': self.processing_time,
            'similarity': self.similarity,
            'report_path': self.report_path,
            'created_time': self.created_time.isoformat() if self.created_time else None,
            'started_time': self.started_time.isoformat() if self.started_time else None,
            'completed_time': self.completed_time.isoformat() if self.completed_time else None
        }