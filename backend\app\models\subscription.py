"""
订阅计划相关模型
"""
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Float
from sqlalchemy.orm import relationship
from .base import BaseModel

class SubscriptionPlan(BaseModel):
    """订阅计划模型"""
    __tablename__ = 'subscription_plans'
    
    # 基本信息
    name = Column(String(100), nullable=False)
    code = Column(String(50), unique=True, nullable=False)  # 计划代码
    description = Column(Text)
    features = Column(Text)  # JSON格式存储功能列表
    
    # 价格信息
    price = Column(Integer, nullable=False)  # 以分为单位
    original_price = Column(Integer)  # 原价
    duration_days = Column(Integer, nullable=False)  # 订阅时长（天）
    trial_days = Column(Integer, default=0)  # 试用期天数
    
    # 限制信息
    max_users = Column(Integer)  # 最大用户数（企业版）
    max_storage = Column(Integer)  # 最大存储空间（MB）
    max_templates = Column(Integer)  # 最大模板数
    max_exports = Column(Integer)  # 最大导出次数
    
    # 控制信息
    is_active = Column(Boolean, default=True)
    is_public = Column(Boolean, default=True)  # 是否公开
    sort_order = Column(Integer, default=0)
    version = Column(String(20), default='1.0.0')
    
    # 统计信息
    subscriber_count = Column(Integer, default=0)
    total_revenue = Column(Integer, default=0)  # 总收入（分）
    
    def __init__(self, **kwargs):
        """初始化订阅计划"""
        super().__init__(**kwargs)
        if self.features is None:
            self.features = {
                'basic': ['unlimited_templates', 'basic_export'],
                'premium': ['advanced_templates', 'priority_support'],
                'enterprise': ['custom_branding', 'api_access']
            }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        created_at = getattr(self, 'created_at', None)
        updated_at = getattr(self, 'updated_at', None)
        
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'features': self.features,
            'price': self.price,
            'original_price': self.original_price,
            'duration_days': self.duration_days,
            'trial_days': self.trial_days,
            'max_users': self.max_users,
            'max_storage': self.max_storage,
            'max_templates': self.max_templates,
            'max_exports': self.max_exports,
            'is_active': self.is_active,
            'is_public': self.is_public,
            'sort_order': self.sort_order,
            'version': self.version,
            'subscriber_count': self.subscriber_count,
            'created_at': created_at.isoformat() if created_at else None,
            'updated_at': updated_at.isoformat() if updated_at else None
        }
    
    def increment_subscriber_count(self) -> None:
        """增加订阅者数量"""
        self.subscriber_count = (self.subscriber_count or 0) + 1
        self.save()
    
    def add_revenue(self, amount: int) -> None:
        """
        增加收入统计
        :param amount: 收入金额（分）
        """
        self.total_revenue = (self.total_revenue or 0) + amount
        self.save()

class UserSubscription(BaseModel):
    """用户订阅记录模型"""
    __tablename__ = 'user_subscriptions'
    
    # 关联关系
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    plan_id = Column(Integer, ForeignKey('subscription_plans.id', ondelete='CASCADE'), nullable=False)
    
    user = relationship('User', backref='subscriptions')
    plan = relationship('SubscriptionPlan')
    
    # 订阅信息
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    is_trial = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    auto_renew = Column(Boolean, default=False)
    
    # 支付信息
    payment_id = Column(String(100))
    price_paid = Column(Integer)  # 实际支付金额（分）
    payment_method = Column(String(50))
    payment_status = Column(String(20), default='pending')
    
    # 取消信息
    canceled_at = Column(DateTime)
    cancel_reason = Column(String(500))
    
    def __init__(self, **kwargs):
        """初始化订阅记录"""
        super().__init__(**kwargs)
        start_date = getattr(self, 'start_date', None)
        if start_date is None:
            start_date = datetime.utcnow()
            setattr(self, 'start_date', start_date)
        
        end_date = getattr(self, 'end_date', None)
        if end_date is None and 'duration_days' in kwargs:
            end_date = start_date + timedelta(days=kwargs['duration_days'])
            setattr(self, 'end_date', end_date)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        # 使用getattr安全地获取属性值
        start_date = getattr(self, 'start_date', None)
        end_date = getattr(self, 'end_date', None)
        canceled_at = getattr(self, 'canceled_at', None)
        created_at = getattr(self, 'created_at', None)
        updated_at = getattr(self, 'updated_at', None)
        
        return {
            'id': self.id,
            'user_id': self.user_id,
            'plan_id': self.plan_id,
            'start_date': start_date.isoformat() if start_date else None,
            'end_date': end_date.isoformat() if end_date else None,
            'is_trial': self.is_trial,
            'is_active': self.is_active,
            'auto_renew': self.auto_renew,
            'payment_id': self.payment_id,
            'price_paid': self.price_paid,
            'payment_method': self.payment_method,
            'payment_status': self.payment_status,
            'canceled_at': canceled_at.isoformat() if canceled_at else None,
            'cancel_reason': self.cancel_reason,
            'created_at': created_at.isoformat() if created_at else None,
            'updated_at': updated_at.isoformat() if updated_at else None
        }
    
    def is_expired(self) -> bool:
        """检查订阅是否已过期"""
        end_date = getattr(self, 'end_date', None)
        if end_date is None:
            return False
        return end_date < datetime.utcnow()
    
    def cancel(self, reason: Optional[str] = None) -> None:
        """
        取消订阅
        :param reason: 取消原因
        """
        self.is_active = False
        self.auto_renew = False
        self.canceled_at = datetime.utcnow()
        self.cancel_reason = reason
        self.save()
    
    def extend(self, days: int) -> None:
        """
        延长订阅时间
        :param days: 延长天数
        """
        end_date = getattr(self, 'end_date', None)
        if end_date is None:
            end_date = datetime.utcnow()
        new_end_date = end_date + timedelta(days=days)
        setattr(self, 'end_date', new_end_date)
        self.save()
