# 名片模板系统实现报告

## 概述

根据简历模板的实现方式，成功为名片功能实现了完整的模板系统。该实现完全参考了简历模板的架构设计，包括模板定义、渲染器、生成器等核心组件。

## 实现架构对比

### 简历模板系统架构
```
backend/app/services/resume_templates/
├── __init__.py
├── basic.py                    # 模板定义
├── renderer.py                 # 模板渲染器
└── templates/                  # HTML模板文件
    ├── basic/
    │   ├── resume.html
    │   └── style.css
    ├── modern_two_column/
    │   ├── resume.html
    │   └── style.css
    └── ...
```

### 名片模板系统架构（新实现）
```
backend/app/services/business_card_templates/
├── __init__.py
├── basic.py                    # 模板定义
├── renderer.py                 # 模板渲染器
└── templates/                  # HTML模板文件
    ├── business_classic/
    │   └── card.html
    ├── modern_minimal/
    │   └── card.html
    ├── creative_design/
    │   └── card.html
    └── elegant_business/
        └── card.html
```

## 核心组件实现

### 1. 模板定义 (basic.py)

**简历模板定义方式：**
```python
def get_basic_template() -> Dict[str, Any]:
    return {
        "id": "basic",
        "name": "基础简历模板",
        "description": "清晰简洁的基础简历模板，适合大多数求职场景",
        "preview_image": "templates/basic/preview.png",
        "category": "general",
        "styles": {
            "font_family": "Microsoft YaHei, sans-serif",
            "primary_color": "#2c3e50",
            # ...
        },
        "sections": {
            "header": {"order": 1, "fields": ["full_name", "title", "contact_info"]},
            "summary": {"order": 2, "title": "个人总结"},
            # ...
        }
    }
```

**名片模板定义方式（参考实现）：**
```python
def get_business_classic_template() -> Dict[str, Any]:
    return {
        "id": "business_classic",
        "name": "商务经典",
        "description": "简洁大方的商务名片模板，适合各行各业",
        "preview_image": "templates/business_classic/preview.png",
        "category": "business",
        "industry": "other",
        "layout_type": "standard",
        "width_mm": 90.0,
        "height_mm": 54.0,
        "styles": {
            "font_family": "Microsoft YaHei, Arial, sans-serif",
            "primary_color": "#2c3e50",
            "secondary_color": "#3498db",
            # ...
        },
        "sections": {
            "header": {"order": 1, "fields": ["name", "title"]},
            "company": {"order": 2, "fields": ["company"]},
            "contact": {"order": 3, "fields": ["phone", "email", "address"]}
        }
    }
```

### 2. 模板渲染器 (renderer.py)

**简历渲染器核心功能：**
- `render_html()` - 渲染HTML内容
- `render_pdf()` - 渲染PDF（暂未实现）
- `render_docx()` - 渲染DOCX文档
- `preview_template()` - 获取模板预览
- `list_templates()` - 列出所有模板

**名片渲染器核心功能（参考实现）：**
- `render_html()` - 渲染HTML内容
- `render_png()` - 渲染PNG图片
- `render_jpg()` - 渲染JPG图片
- `render_pdf()` - 渲染PDF（暂未实现）
- `preview_template()` - 获取模板预览
- `list_templates()` - 列出所有模板

### 3. 生成器服务

**简历生成器 (resume_generator.py)：**
```python
class ResumeGenerator:
    def __init__(self):
        self.renderer = ResumeRenderer()
    
    def generate_html(self, resume: Resume) -> str:
        # 使用模板渲染器生成HTML
        return self.renderer.render_html(resume_data, template_id)
    
    def preview_from_data(self, data: Dict[str, Any]) -> str:
        # 从数据直接生成预览
        return self.renderer.render_html(data, template_id)
```

**名片生成器 (business_card_generator.py) - 参考实现：**
```python
class BusinessCardGenerator:
    def __init__(self):
        self.renderer = BusinessCardRenderer()
    
    def generate_html(self, business_card: BusinessCard) -> str:
        # 使用模板渲染器生成HTML
        return self.renderer.render_html(card_data, template_id, style_config)
    
    def generate_png(self, business_card: BusinessCard) -> bytes:
        # 使用模板渲染器生成PNG
        return self.renderer.render_png(card_data, template_id, style_config)
    
    def generate_jpg(self, business_card: BusinessCard, quality: int = 95) -> bytes:
        # 使用模板渲染器生成JPG
        return self.renderer.render_jpg(card_data, template_id, style_config, quality)
    
    def preview_from_data(self, data: Dict[str, Any], template_id: str) -> str:
        # 从数据直接生成预览
        return self.renderer.render_html(card_data, template_id, style_config)
```

## 模板文件结构

### 简历模板HTML结构
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{{ resume.full_name }} - 简历</title>
    <style>
        /* 样式定义 */
    </style>
</head>
<body>
    <div class="resume-container">
        <header class="resume-header">
            <h1 class="full-name">{{ resume.full_name }}</h1>
            <h2 class="title">{{ resume.title }}</h2>
        </header>
        
        <main class="resume-body">
            {% if resume.summary %}
            <section class="resume-section summary-section">
                <h3 class="section-title">个人总结</h3>
                <p class="summary-text">{{ resume.summary }}</p>
            </section>
            {% endif %}
            
            {% if resume.work_experiences %}
            <section class="resume-section work-section">
                <h3 class="section-title">工作经历</h3>
                {% for work in resume.work_experiences %}
                <div class="work-item">
                    <!-- 工作经历内容 -->
                </div>
                {% endfor %}
            </section>
            {% endif %}
        </main>
    </div>
</body>
</html>
```

### 名片模板HTML结构（参考实现）
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{{ card.name or '名片' }}</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: {{ style.font_family }};
            background-color: {{ style.background_color }};
        }
        
        .business-card {
            width: 90mm;
            height: 54mm;
            padding: 8mm;
            background: {{ style.background_color }};
            border: 1px solid {{ style.border_color }};
            box-sizing: border-box;
            position: relative;
        }
        
        .business-classic .name {
            font-size: {{ style.name_size }};
            font-weight: {{ style.name_weight }};
            color: {{ style.primary_color }};
            margin: 0 0 2mm 0;
        }
        
        /* 更多样式... */
    </style>
</head>
<body>
    <div class="business-card business-classic">
        <div class="header">
            <h1 class="name">{{ card.name or '姓名' }}</h1>
            <p class="title">{{ card.title or '职位' }}</p>
        </div>
        <div class="company">
            <p class="company-name">{{ card.company or '公司名称' }}</p>
        </div>
        <div class="contact">
            {% if card.phone %}
            <div class="contact-item">
                <i class="icon-phone"></i>
                <span>{{ card.phone }}</span>
            </div>
            {% endif %}
            {% if card.email %}
            <div class="contact-item">
                <i class="icon-email"></i>
                <span>{{ card.email }}</span>
            </div>
            {% endif %}
        </div>
        {% if qr_code_url %}
        <div class="qr-code">
            <img src="{{ qr_code_url }}" alt="二维码">
        </div>
        {% endif %}
    </div>
</body>
</html>
```

## API集成

### 简历API集成
```python
# 简历预览API
@api_bp.route('/resumes/render-preview', methods=['POST'])
def render_resume_preview():
    generator = ResumeGenerator()
    html_content = generator.preview_from_data(data)
    return success_response(data={'html_content': html_content})

# 简历下载API
@api_bp.route('/resumes/<int:resume_id>/download', methods=['GET'])
def download_resume(resume_id):
    generator = ResumeGenerator()
    html_content = generator.generate_html(resume)
    # 转换为PDF或其他格式
```

### 名片API集成（参考实现）
```python
# 名片预览API
@api_bp.route('/business-cards/render-preview', methods=['POST'])
def render_business_card_preview():
    generator = BusinessCardGenerator()
    html_content = generator.preview_from_data(preview_data, backend_template_id)
    return success_response(data={'html_content': html_content})

# 名片下载API
@api_bp.route('/business-cards/<int:card_id>/download', methods=['GET'])
def download_business_card(card_id):
    generator = BusinessCardGenerator()
    if format_type == 'jpg':
        img_data = generator.generate_jpg(business_card, quality=95)
    else:
        img_data = generator.generate_png(business_card)
    # 返回图片数据
```

## 模板映射机制

### 简历模板映射
```python
# 数据库模板ID到后端模板ID的映射
template_mapping = {
    1: 'basic',
    2: 'modern', 
    3: 'creative_sidebar',
    4: 'elegant_business',
    5: 'basic',  # 极简主义暂时映射到基础模板
    6: 'creative_icon',
    7: 'professional_timeline',
    8: 'simple_ats',
    9: 'modern_two_column',
    10: 'classic_one_column'
}
```

### 名片模板映射（参考实现）
```python
# 数据库模板ID到后端模板ID的映射
template_mapping = {
    1: 'business_classic',
    2: 'modern_minimal', 
    3: 'creative_design',
    4: 'elegant_business'
}
```

## 样式配置系统

### 简历样式配置
```python
# 默认样式配置
self.default_style_config = {
    'fontSize': 100,  # 字体大小百分比
    'primary': '#2c3e50',  # 主色调
    'secondary': '#7f8c8d',  # 次要色调
    'text': '#34495e',  # 文本颜色
    # ...
}
```

### 名片样式配置（参考实现）
```python
# 默认样式配置
self.default_style_config = {
    'fontSize': 100,  # 字体大小百分比
    'primary': '#2c3e50',  # 主色调
    'secondary': '#3498db',  # 次要色调
    'text': '#34495e',  # 文本颜色
    'light': '#7f8c8d',  # 浅色文本
    'backgroundColor': '#ffffff',  # 背景色
    'borderColor': '#ddd',  # 边框色
    'nameColor': '#2c3e50',  # 姓名颜色
    'companyColor': '#3498db',  # 公司颜色
    'nameFontStyle': 'normal',  # 姓名字体样式
    'companyFontStyle': 'normal',  # 公司字体样式
    'textFontStyle': 'normal'  # 文本字体样式
}
```

## 实现的模板类型

### 简历模板类型
1. **基础简历模板** - 简洁大方的经典模板
2. **现代专业模板** - 富有现代感的专业格式
3. **创意左侧栏模板** - 创意设计，左侧栏布局
4. **创意图标模板** - 使用精美图标突出信息
5. **专业时间轴模板** - 时间轴布局展示经历
6. **现代两栏模板** - 现代感的两栏布局

### 名片模板类型（参考实现）
1. **商务经典** - 简洁大方的商务名片模板
2. **现代简约** - 极简风格，注重留白和层次感
3. **创意设计** - 富有创意的设计，适合设计师
4. **优雅商务** - 高端商务风格，适合金融、法律等专业领域

## 技术特点

### 共同特点
1. **模板化设计** - 使用Jinja2模板引擎
2. **样式可配置** - 支持自定义颜色、字体等样式
3. **多格式输出** - 支持HTML、PNG、JPG等格式
4. **实时预览** - 支持数据实时预览
5. **模板映射** - 数据库模板ID到后端模板ID的映射
6. **错误处理** - 完善的异常处理和回退机制

### 名片特有特点
1. **标准尺寸** - 90mm × 54mm 标准名片尺寸
2. **二维码支持** - 集成二维码生成功能
3. **打印排版** - 支持批量打印排版
4. **高分辨率** - 300 DPI 高分辨率输出

## 总结

通过参考简历模板的实现方式，成功为名片功能实现了完整的模板系统。该实现保持了与简历模板系统相同的架构设计，包括：

1. **相同的目录结构** - 模板定义、渲染器、HTML模板文件
2. **相同的核心组件** - 模板定义、渲染器、生成器
3. **相同的API集成方式** - 预览、下载、模板管理
4. **相同的样式配置系统** - 颜色、字体、布局配置
5. **相同的模板映射机制** - 数据库ID到后端模板ID的映射

这种实现方式确保了代码的一致性和可维护性，同时为名片功能提供了与简历功能相同级别的模板化支持。 