"""
基础模型类
提供了基本的模型功能，包括ID字段、时间戳、字典转换和数据库操作。
"""
from datetime import datetime
from typing import Any, Dict, Type, TypeVar, Optional, Union
from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, JSON, Float, ForeignKey, Date
from sqlalchemy.orm import declarative_base, declared_attr
from sqlalchemy.sql.schema import Column as SQLColumn
from ..database import db

T = TypeVar('T', bound='BaseModel')

class BaseModel(db.Model):
    """
    基础模型类
    
    提供了以下功能：
    1. ID字段
    2. 创建和更新时间戳
    3. 字典转换
    4. 基本的数据库操作
    5. 支持直接使用SQLAlchemy表定义
    """
    __abstract__ = True
    __table_args__ = {'extend_existing': True}
    
    # 这些字段将在使用__table__定义表时被忽略
    id = Column(Integer, primary_key=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 自动生成表名
    def __init_subclass__(cls, **kwargs):
        """初始化子类"""
        super().__init_subclass__(**kwargs)
        if not hasattr(cls, '__table__'):
            if not hasattr(cls, '__tablename__'):
                cls.__tablename__ = cls.__name__.lower() + 's'
    
    @classmethod
    def get_tablename(cls: Type[T]) -> str:
        """获取表名"""
        return cls.__name__.lower() + 's'
    
    def get_safe_value(self, field_name: str, default: Any = None) -> Any:
        """
        安全地获取字段值，处理SQLAlchemy Column类型
        
        :param field_name: 字段名
        :param default: 默认值
        :return: 字段值或默认值
        """
        value = getattr(self, field_name, default)
        if isinstance(value, SQLColumn):
            return default
        return value
    
    def get_datetime_str(self, field_name: str, fmt: str = '%Y-%m-%d %H:%M:%S') -> Optional[str]:
        """
        获取日期时间的格式化字符串
        
        :param field_name: 字段名
        :param fmt: 日期时间格式
        :return: 格式化的日期时间字符串
        """
        value = self.get_safe_value(field_name)
        if isinstance(value, datetime):
            return value.strftime(fmt)
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典，自动处理时间格式化
        
        :return: 字典形式的模型数据
        """
        result = {}
        for col in self.__table__.columns:
            value = self.get_safe_value(col.name)
            if isinstance(value, datetime):
                value = value.strftime('%Y-%m-%d %H:%M:%S')
            result[col.name] = value
        return result
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """
        从字典更新属性
        
        :param data: 要更新的数据字典
        """
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        # 更新修改时间
        self.updated_at = datetime.utcnow()
    
    def save(self) -> None:
        """
        保存到数据库
        同时更新修改时间
        """
        self.updated_at = datetime.utcnow()
        db.session.add(self)
        db.session.commit()
    
    def delete(self) -> None:
        """从数据库删除"""
        db.session.delete(self)
        db.session.commit()
    
    @classmethod
    def create(cls: Type[T], **kwargs) -> T:
        """
        创建并保存新实例
        
        :param kwargs: 实例属性
        :return: 新创建的实例
        """
        instance = cls(**kwargs)
        instance.save()
        return instance