import os
import uuid
import traceback
from datetime import datetime
from flask import request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
import sqlalchemy.exc

from app import db
from app.models.resume import (
    Resume, ResumeTemplate, Education, WorkExperience, 
    Project, Skill, Certification, ResumeStatus, 
    TemplateCategory, IndustryType
)
from app.models.user import User
from app.models.image import Image
from app.api.errors import validation_error, not_found, bad_request, internal_server_error
from app.services.resume_parser import ResumeParser
from app.services.ai_assistant import AIAssistant
from app.services.resume_generator import ResumeGenerator

# 允许的文件类型
ALLOWED_EXTENSIONS = {'docx'}
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

def allowed_file(filename):
    """检查文件是否允许上传"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@jwt_required()
def get_resumes():
    """获取用户的简历列表"""
    try:
        user_id = get_jwt_identity()
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        status = request.args.get('status', 'all')
        
        query = Resume.query.filter_by(user_id=user_id)
        
        if status != 'all':
            query = query.filter_by(status=status)
        
        resumes = query.order_by(Resume.updated_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'success': True,
            'data': {
                'resumes': [resume.to_dict() for resume in resumes.items],
                'total': resumes.total,
                'pages': resumes.pages,
                'current_page': page,
                'per_page': per_page
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取简历列表失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('获取简历列表失败')

@jwt_required()
def create_resume():
    """创建新简历"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # 验证必需字段
        if not data.get('title'):
            return bad_request('简历标题不能为空')
        
        if not data.get('full_name'):
            return bad_request('姓名不能为空')
        
        # 创建简历
        resume = Resume(
            user_id=user_id,
            title=data['title'],
            full_name=data['full_name'],
            gender=data.get('gender', ''),
            age=data.get('age'),
            phone=data.get('phone', ''),
            email=data.get('email', ''),
            address=data.get('address', ''),
            objective=data.get('objective', ''),
            summary=data.get('summary', ''),
            template_id=data.get('template_id'),
            photo_url=data.get('photo_url', ''),
            photo_image_id=data.get('photo_image_id'),
            status=ResumeStatus.DRAFT.value
        )
        
        db.session.add(resume)
        db.session.flush()  # 获取ID
        
        # 添加教育背景
        for edu_data in data.get('educations', []):
            education = Education(
                resume_id=resume.id,
                school_name=edu_data.get('school_name', ''),
                degree=edu_data.get('degree', ''),
                major=edu_data.get('major', ''),
                start_date=edu_data.get('start_date', ''),
                end_date=edu_data.get('end_date', ''),
                gpa=edu_data.get('gpa'),
                description=edu_data.get('description', ''),
                sort_order=edu_data.get('sort_order', 0)
            )
            db.session.add(education)
        
        # 添加工作经历
        for exp_data in data.get('work_experiences', []):
            work_exp = WorkExperience(
                resume_id=resume.id,
                company_name=exp_data.get('company_name', ''),
                position=exp_data.get('position', ''),
                start_date=exp_data.get('start_date', ''),
                end_date=exp_data.get('end_date', ''),
                is_current=exp_data.get('is_current', False),
                description=exp_data.get('description', ''),
                achievements=exp_data.get('achievements', []),
                sort_order=exp_data.get('sort_order', 0)
            )
            db.session.add(work_exp)
        
        # 添加项目经验
        for proj_data in data.get('projects', []):
            project = Project(
                resume_id=resume.id,
                name=proj_data.get('name', ''),
                role=proj_data.get('role', ''),
                start_date=proj_data.get('start_date', ''),
                end_date=proj_data.get('end_date', ''),
                description=proj_data.get('description', ''),
                technologies=proj_data.get('technologies', []),
                achievements=proj_data.get('achievements', []),
                project_url=proj_data.get('project_url', ''),
                sort_order=proj_data.get('sort_order', 0)
            )
            db.session.add(project)
        
        # 添加技能
        for skill_data in data.get('skills', []):
            skill = Skill(
                resume_id=resume.id,
                category=skill_data.get('category', ''),
                name=skill_data.get('name', ''),
                proficiency=skill_data.get('proficiency', ''),
                years_experience=skill_data.get('years_experience'),
                sort_order=skill_data.get('sort_order', 0)
            )
            db.session.add(skill)
        
        # 添加证书
        for cert_data in data.get('certifications', []):
            certification = Certification(
                resume_id=resume.id,
                name=cert_data.get('name', ''),
                issuer=cert_data.get('issuer', ''),
                issue_date=cert_data.get('issue_date', ''),
                expiry_date=cert_data.get('expiry_date', ''),
                credential_id=cert_data.get('credential_id', ''),
                credential_url=cert_data.get('credential_url', ''),
                sort_order=cert_data.get('sort_order', 0)
            )
            db.session.add(certification)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '简历创建成功',
            'data': resume.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建简历失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('创建简历失败')

@jwt_required()
def get_resume(resume_id):
    """获取简历详情"""
    try:
        user_id = get_jwt_identity()
        resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
        
        if not resume:
            return not_found('简历不存在')
        
        return jsonify({
            'success': True,
            'data': resume.to_dict(include_relations=True)
        })
        
    except Exception as e:
        current_app.logger.error(f"获取简历详情失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('获取简历详情失败')

@jwt_required()
def update_resume(resume_id):
    """更新简历"""
    try:
        user_id = get_jwt_identity()
        current_app.logger.info(f"开始更新简历: resume_id={resume_id}, user_id={user_id}")
        
        resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
        if not resume:
            current_app.logger.warning(f"简历不存在: resume_id={resume_id}, user_id={user_id}")
            return not_found('简历不存在')
        
        data = request.get_json()
        if not data:
            current_app.logger.error("请求数据为空")
            return bad_request('请求数据不能为空')
        
        try:
            # 更新基本信息
            current_app.logger.info("更新基本信息")
            resume.title = data.get('title', resume.title)
            resume.full_name = data.get('full_name', resume.full_name)
            resume.gender = data.get('gender', resume.gender)
            resume.age = data.get('age', resume.age)
            resume.phone = data.get('phone', resume.phone)
            resume.email = data.get('email', resume.email)
            resume.address = data.get('address', resume.address)
            resume.objective = data.get('objective', resume.objective)
            resume.summary = data.get('summary', resume.summary)
            resume.template_id = data.get('template_id', resume.template_id)
            resume.photo_url = data.get('photo_url', resume.photo_url)
            resume.photo_image_id = data.get('photo_image_id', resume.photo_image_id)
            resume.updated_at = datetime.utcnow()
            
            # 更新教育背景
            if 'educations' in data:
                current_app.logger.info("更新教育背景")
                # 删除原有记录
                Education.query.filter_by(resume_id=resume.id).delete()
                
                # 添加新记录
                for edu_data in data['educations']:
                    education = Education(
                        resume_id=resume.id,
                        school_name=edu_data.get('school_name', ''),
                        degree=edu_data.get('degree', ''),
                        major=edu_data.get('major', ''),
                        start_date=edu_data.get('start_date', ''),
                        end_date=edu_data.get('end_date', ''),
                        gpa=edu_data.get('gpa'),
                        description=edu_data.get('description', ''),
                        sort_order=edu_data.get('sort_order', 0)
                    )
                    db.session.add(education)
            
            # 更新工作经历
            if 'work_experiences' in data:
                current_app.logger.info("更新工作经历")
                WorkExperience.query.filter_by(resume_id=resume.id).delete()
                
                for exp_data in data['work_experiences']:
                    work_exp = WorkExperience(
                        resume_id=resume.id,
                        company_name=exp_data.get('company_name', ''),
                        position=exp_data.get('position', ''),
                        start_date=exp_data.get('start_date', ''),
                        end_date=exp_data.get('end_date', ''),
                        is_current=exp_data.get('is_current', False),
                        description=exp_data.get('description', ''),
                        achievements=exp_data.get('achievements', []),
                        sort_order=exp_data.get('sort_order', 0)
                    )
                    db.session.add(work_exp)
            
            # 更新项目经验
            if 'projects' in data:
                current_app.logger.info("更新项目经验")
                Project.query.filter_by(resume_id=resume.id).delete()
                
                for proj_data in data['projects']:
                    project = Project(
                        resume_id=resume.id,
                        name=proj_data.get('name', ''),
                        role=proj_data.get('role', ''),
                        start_date=proj_data.get('start_date', ''),
                        end_date=proj_data.get('end_date', ''),
                        description=proj_data.get('description', ''),
                        technologies=proj_data.get('technologies', []),
                        achievements=proj_data.get('achievements', []),
                        project_url=proj_data.get('project_url', ''),
                        sort_order=proj_data.get('sort_order', 0)
                    )
                    db.session.add(project)
            
            # 更新技能
            if 'skills' in data:
                current_app.logger.info("更新技能")
                Skill.query.filter_by(resume_id=resume.id).delete()
                
                for skill_data in data['skills']:
                    skill = Skill(
                        resume_id=resume.id,
                        category=skill_data.get('category', ''),
                        name=skill_data.get('name', ''),
                        proficiency=skill_data.get('proficiency', ''),
                        years_experience=skill_data.get('years_experience'),
                        sort_order=skill_data.get('sort_order', 0)
                    )
                    db.session.add(skill)
            
            # 更新证书
            if 'certifications' in data:
                current_app.logger.info("更新证书")
                Certification.query.filter_by(resume_id=resume.id).delete()
                
                for cert_data in data['certifications']:
                    certification = Certification(
                        resume_id=resume.id,
                        name=cert_data.get('name', ''),
                        issuer=cert_data.get('issuer', ''),
                        issue_date=cert_data.get('issue_date', ''),
                        expiry_date=cert_data.get('expiry_date', ''),
                        credential_id=cert_data.get('credential_id', ''),
                        credential_url=cert_data.get('credential_url', ''),
                        sort_order=cert_data.get('sort_order', 0)
                    )
                    db.session.add(certification)
            
            current_app.logger.info("提交数据库事务")
            db.session.commit()
            
            current_app.logger.info("简历更新成功")
            return jsonify({
                'success': True,
                'message': '简历更新成功',
                'data': resume.to_dict()
            })
            
        except sqlalchemy.exc.IntegrityError as e:
            db.session.rollback()
            current_app.logger.error(f"数据完整性错误: {str(e)}\n{traceback.format_exc()}")
            return bad_request('数据验证失败，请检查输入')
            
        except sqlalchemy.exc.SQLAlchemyError as e:
            db.session.rollback()
            current_app.logger.error(f"数据库错误: {str(e)}\n{traceback.format_exc()}")
            return internal_server_error('数据库操作失败')
            
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新简历失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('更新简历失败，请稍后重试')

@jwt_required()
def delete_resume(resume_id):
    """删除简历"""
    try:
        user_id = get_jwt_identity()
        resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
        
        if not resume:
            return not_found('简历不存在')
        
        db.session.delete(resume)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '简历删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除简历失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('删除简历失败')

@jwt_required()
def copy_resume(resume_id):
    """复制简历"""
    try:
        user_id = get_jwt_identity()
        original_resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
        
        if not original_resume:
            return not_found('简历不存在')
        
        # 创建新简历
        new_resume = Resume(
            user_id=user_id,
            title=f"{original_resume.title} - 副本",
            full_name=original_resume.full_name,
            gender=original_resume.gender,
            age=original_resume.age,
            phone=original_resume.phone,
            email=original_resume.email,
            address=original_resume.address,
            objective=original_resume.objective,
            summary=original_resume.summary,
            template_id=original_resume.template_id,
            photo_url=original_resume.photo_url,
            photo_image_id=original_resume.photo_image_id,
            status=ResumeStatus.DRAFT.value
        )
        
        db.session.add(new_resume)
        db.session.flush()
        
        # 复制教育背景
        for edu in original_resume.educations:
            new_edu = Education(
                resume_id=new_resume.id,
                school_name=edu.school_name,
                degree=edu.degree,
                major=edu.major,
                start_date=edu.start_date,
                end_date=edu.end_date,
                gpa=edu.gpa,
                description=edu.description,
                sort_order=edu.sort_order
            )
            db.session.add(new_edu)
        
        # 复制工作经历
        for exp in original_resume.work_experiences:
            new_exp = WorkExperience(
                resume_id=new_resume.id,
                company_name=exp.company_name,
                position=exp.position,
                start_date=exp.start_date,
                end_date=exp.end_date,
                is_current=exp.is_current,
                description=exp.description,
                achievements=exp.achievements,
                sort_order=exp.sort_order
            )
            db.session.add(new_exp)
        
        # 复制项目经验
        for proj in original_resume.projects:
            new_proj = Project(
                resume_id=new_resume.id,
                name=proj.name,
                role=proj.role,
                start_date=proj.start_date,
                end_date=proj.end_date,
                description=proj.description,
                technologies=proj.technologies,
                achievements=proj.achievements,
                project_url=proj.project_url,
                sort_order=proj.sort_order
            )
            db.session.add(new_proj)
        
        # 复制技能
        for skill in original_resume.skills:
            new_skill = Skill(
                resume_id=new_resume.id,
                category=skill.category,
                name=skill.name,
                proficiency=skill.proficiency,
                years_experience=skill.years_experience,
                sort_order=skill.sort_order
            )
            db.session.add(new_skill)
        
        # 复制证书
        for cert in original_resume.certifications:
            new_cert = Certification(
                resume_id=new_resume.id,
                name=cert.name,
                issuer=cert.issuer,
                issue_date=cert.issue_date,
                expiry_date=cert.expiry_date,
                credential_id=cert.credential_id,
                credential_url=cert.credential_url,
                sort_order=cert.sort_order
            )
            db.session.add(new_cert)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '简历复制成功',
            'data': new_resume.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"复制简历失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('复制简历失败')

@jwt_required()
def parse_resume():
    """解析上传的简历文件"""
    try:
        user_id = get_jwt_identity()
        
        if 'file' not in request.files:
            return bad_request('请选择要上传的文件')
        
        file = request.files['file']
        if file.filename == '':
            return bad_request('请选择要上传的文件')
        
        if not allowed_file(file.filename):
            return bad_request('只支持DOCX格式的文件')
        
        # 检查文件大小
        file.seek(0, os.SEEK_END)
        size = file.tell()
        file.seek(0)
        
        if size > MAX_FILE_SIZE:
            return bad_request('文件大小不能超过10MB')
        
        # 保存文件
        filename = secure_filename(file.filename)
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'resumes', str(user_id))
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = os.path.join(upload_dir, f"{uuid.uuid4().hex}_{filename}")
        file.save(file_path)
        
        try:
            # 解析简历
            parser = ResumeParser()
            parsed_data = parser.parse_resume(file_path)
            
            # 清理临时文件
            os.remove(file_path)
            
            return jsonify({
                'success': True,
                'message': '简历解析成功',
                'data': parsed_data
            })
            
        except Exception as e:
            # 清理临时文件
            if os.path.exists(file_path):
                os.remove(file_path)
            raise e
        
    except Exception as e:
        current_app.logger.error(f"简历解析失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('简历解析失败')

@jwt_required()
def get_resume_templates():
    """获取简历模板列表"""
    try:
        category = request.args.get('category', 'all')
        industry = request.args.get('industry', 'all')
        
        query = ResumeTemplate.query.filter_by(is_active=True)
        
        if category != 'all':
            query = query.filter_by(category=category)
        
        if industry != 'all':
            query = query.filter_by(industry=industry)
        
        templates = query.order_by(ResumeTemplate.sort_order.asc()).all()
        
        return jsonify({
            'success': True,
            'data': [template.to_dict() for template in templates]
        })
        
    except Exception as e:
        current_app.logger.error(f"获取模板列表失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('获取模板列表失败')

@jwt_required()
def preview_resume(resume_id):
    """预览简历"""
    try:
        user_id = get_jwt_identity()
        current_app.logger.info(f"用户 {user_id} 请求预览简历 {resume_id}")
        
        resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
        if not resume:
            current_app.logger.error(f"简历不存在: resume_id={resume_id}, user_id={user_id}")
            return not_found('简历不存在')
        
        current_app.logger.info(f"找到简历: {resume.title}")
        current_app.logger.info(f"简历模板: {resume.template.name if resume.template else 'None'}")
        
        generator = ResumeGenerator()
        html_content = generator.preview_html(resume_id)
        
        return jsonify({
            'success': True,
            'data': {
                'html': html_content
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"预览简历失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('预览简历失败: ' + str(e))

@jwt_required()
def generate_preview():
    """生成临时预览（用于实时预览）"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        if not data.get('full_name'):
            return bad_request('姓名不能为空')
        
        generator = ResumeGenerator()
        html_content = generator.preview_from_data(data)
        
        return jsonify({
            'success': True,
            'data': {
                'html': html_content
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"生成临时预览失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('生成临时预览失败')

@jwt_required()
def export_resume(resume_id):
    """导出简历"""
    try:
        user_id = get_jwt_identity()
        resume = Resume.query.filter_by(id=resume_id, user_id=user_id).first()
        
        if not resume:
            return not_found('简历不存在')
        
        # 从JSON数据中获取格式
        data = request.get_json()
        format_type = data.get('format', 'pdf').lower() if data else 'pdf'
        
        if format_type not in ['pdf', 'docx']:
            return bad_request('不支持的导出格式')
        
        generator = ResumeGenerator()
        
        try:
            if format_type == 'pdf':
                # 暂时返回HTML文件供用户手动打印为PDF
                file_content = generator.generate_pdf(resume_id)
                mimetype = 'text/html'
                filename = f"{resume.full_name}_{resume.title}.html"
            else:
                file_content = generator.generate_docx(resume_id)
                mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            filename = f"{resume.full_name}_{resume.title}.{format_type}"
            
            # 如果返回的是文件路径
            if isinstance(file_content, str) and os.path.exists(file_content):
                return send_file(
                    file_content,
                    as_attachment=True,
                    download_name=filename,
                    mimetype=mimetype
                )
            # 如果返回的是字节内容
            elif isinstance(file_content, bytes):
                from io import BytesIO
                return send_file(
                    BytesIO(file_content),
                    as_attachment=True,
                    download_name=filename,
                    mimetype=mimetype
                )
            else:
                raise ValueError("生成器返回了无效的文件内容")
                
        except Exception as gen_error:
            current_app.logger.error(f"生成{format_type}文件失败: {str(gen_error)}")
            # 如果生成失败，返回HTML预览作为备选
            html_content = generator.preview_html(resume_id)
            return jsonify({
                'success': False,
                'message': f'暂不支持{format_type}格式导出，请使用预览功能',
                'data': {'html': html_content}
            })
        
    except Exception as e:
        current_app.logger.error(f"导出简历失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('导出简历失败')

@jwt_required()
def ai_generate_work_description():
    """AI生成工作描述"""
    try:
        data = request.get_json()
        
        position = data.get('position', '')
        company = data.get('company', '')
        industry = data.get('industry', '')
        keywords = data.get('keywords', '')
        
        if not position:
            return bad_request('职位名称不能为空')
        
        ai_assistant = AIAssistant()
        descriptions = ai_assistant.generate_work_description(
            position=position,
            company=company,
            industry=industry,
            keywords=keywords
        )
        
        return jsonify({
            'success': True,
            'data': {
                'descriptions': descriptions
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"AI生成工作描述失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('AI生成工作描述失败')

@jwt_required()
def ai_generate_project_description():
    """AI生成项目描述"""
    try:
        data = request.get_json()
        
        project_name = data.get('project_name', '')
        technologies = data.get('technologies', '')
        role = data.get('role', '')
        scale = data.get('scale', '')
        keywords = data.get('keywords', '')
        
        if not project_name:
            return bad_request('项目名称不能为空')
        
        ai_assistant = AIAssistant()
        descriptions = ai_assistant.generate_project_description(
            project_name=project_name,
            technologies=technologies,
            role=role,
            scale=scale,
            keywords=keywords
        )
        
        return jsonify({
            'success': True,
            'data': {
                'descriptions': descriptions
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"AI生成项目描述失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('AI生成项目描述失败')

@jwt_required()
def ai_optimize_summary():
    """AI优化个人总结"""
    try:
        data = request.get_json()
        
        current_summary = data.get('current_summary', '')
        years_experience = data.get('years_experience', '')
        key_skills = data.get('key_skills', '')
        target_position = data.get('target_position', '')
        
        ai_assistant = AIAssistant()
        optimized_summary = ai_assistant.generate_resume_summary(
            current_summary=current_summary,
            years_experience=years_experience,
            key_skills=key_skills,
            target_position=target_position
        )
        
        return jsonify({
            'success': True,
            'data': {
                'summary': optimized_summary
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"AI优化个人总结失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('AI优化个人总结失败')

@jwt_required()
def get_resume_images():
    """获取用户上传的图片列表（用于简历照片选择）"""
    try:
        user_id = get_jwt_identity()
        images = Image.query.filter_by(user_id=user_id).order_by(Image.upload_time.desc()).all()
        
        return jsonify({
            'success': True,
            'data': [image.to_dict() for image in images]
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户图片列表失败: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('获取用户图片列表失败')

def register_resume_routes(bp):
    """注册简历相关路由"""
    bp.add_url_rule('/resumes', 'get_resumes', get_resumes, methods=['GET'])
    bp.add_url_rule('/resumes', 'create_resume', create_resume, methods=['POST'])
    bp.add_url_rule('/resumes/<int:resume_id>', 'get_resume', get_resume, methods=['GET'])
    bp.add_url_rule('/resumes/<int:resume_id>', 'update_resume', update_resume, methods=['PUT'])
    bp.add_url_rule('/resumes/<int:resume_id>', 'delete_resume', delete_resume, methods=['DELETE'])
    bp.add_url_rule('/resumes/<int:resume_id>/copy', 'copy_resume', copy_resume, methods=['POST'])
    bp.add_url_rule('/resumes/parse', 'parse_resume', parse_resume, methods=['POST'])
    bp.add_url_rule('/resumes/templates', 'get_resume_templates', get_resume_templates, methods=['GET'])
    bp.add_url_rule('/resumes/<int:resume_id>/preview', 'preview_resume', preview_resume, methods=['POST'])
    bp.add_url_rule('/resumes/preview', 'generate_preview', generate_preview, methods=['POST'])
    bp.add_url_rule('/resumes/<int:resume_id>/export', 'export_resume', export_resume, methods=['POST'])
    bp.add_url_rule('/resumes/ai/work-description', 'ai_generate_work_description', ai_generate_work_description, methods=['POST'])
    bp.add_url_rule('/resumes/ai/project-description', 'ai_generate_project_description', ai_generate_project_description, methods=['POST'])
    bp.add_url_rule('/resumes/ai/optimize-summary', 'ai_optimize_summary', ai_optimize_summary, methods=['POST'])
    bp.add_url_rule('/resumes/images', 'get_resume_images', get_resume_images, methods=['GET']) 