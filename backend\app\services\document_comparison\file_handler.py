import os
import hashlib
import tempfile
import mimetypes
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class FileHandler:
    """文件处理工具类"""
    
    # 支持的文件类型和对应的MIME类型
    SUPPORTED_FORMATS = {
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.wps': 'application/vnd.kingsoft.wps',
        '.pdf': 'application/pdf',
        '.txt': 'text/plain'
    }
    
    @staticmethod
    def get_file_extension(filename: str) -> str:
        """获取文件扩展名"""
        return os.path.splitext(filename.lower())[1]
    
    @staticmethod
    def is_supported_format(filename: str) -> bool:
        """检查文件格式是否支持"""
        ext = FileHandler.get_file_extension(filename)
        return ext in FileHandler.SUPPORTED_FORMATS
    
    @staticmethod
    def get_file_mime_type(filename: str) -> str:
        """获取文件MIME类型"""
        ext = FileHandler.get_file_extension(filename)
        return FileHandler.SUPPORTED_FORMATS.get(ext, 'application/octet-stream')
    
    @staticmethod
    def validate_file_type(file_path: str, allowed_extensions: list = None) -> bool:
        """
        验证文件类型
        
        Args:
            file_path: 文件路径
            allowed_extensions: 允许的扩展名列表
            
        Returns:
            是否为有效文件类型
        """
        if not os.path.exists(file_path):
            return False
        
        ext = FileHandler.get_file_extension(file_path)
        
        if allowed_extensions:
            return ext in allowed_extensions
        
        return ext in FileHandler.SUPPORTED_FORMATS
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict[str, Any]:
        """
        获取文件详细信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        if not os.path.exists(file_path):
            return {}
        
        stat = os.stat(file_path)
        ext = FileHandler.get_file_extension(file_path)
        
        return {
            "filename": os.path.basename(file_path),
            "extension": ext,
            "size_bytes": stat.st_size,
            "size_mb": round(stat.st_size / (1024 * 1024), 2),
            "mime_type": FileHandler.get_file_mime_type(file_path),
            "modified_time": stat.st_mtime,
            "is_supported": ext in FileHandler.SUPPORTED_FORMATS
        }
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = 'md5') -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 (md5, sha1, sha256)
            
        Returns:
            文件哈希值
        """
        if not os.path.exists(file_path):
            return ""
        
        hash_func = getattr(hashlib, algorithm.lower(), None)
        if not hash_func:
            hash_func = hashlib.md5
        
        with open(file_path, 'rb') as f:
            hasher = hash_func()
            for chunk in iter(lambda: f.read(4096), b""):
                hasher.update(chunk)
        
        return hasher.hexdigest()
    
    @staticmethod
    def create_temp_file(suffix: str = None, prefix: str = 'doc_compare_') -> str:
        """
        创建临时文件
        
        Args:
            suffix: 文件后缀
            prefix: 文件前缀
            
        Returns:
            临时文件路径
        """
        with tempfile.NamedTemporaryFile(
            suffix=suffix,
            prefix=prefix,
            delete=False
        ) as tmp_file:
            return tmp_file.name
    
    @staticmethod
    def safe_delete_file(file_path: str) -> bool:
        """
        安全删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否删除成功
        """
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
                return True
            return True
        except Exception as e:
            logger.warning(f"删除文件失败 {file_path}: {str(e)}")
            return False
    
    @staticmethod
    def ensure_directory(directory: str) -> bool:
        """
        确保目录存在
        
        Args:
            directory: 目录路径
            
        Returns:
            是否创建成功
        """
        try:
            os.makedirs(directory, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"创建目录失败 {directory}: {str(e)}")
            return False
    
    @staticmethod
    def get_safe_filename(filename: str) -> str:
        """
        获取安全的文件名（移除特殊字符）
        
        Args:
            filename: 原始文件名
            
        Returns:
            安全的文件名
        """
        # 移除或替换不安全的字符
        unsafe_chars = '<>:"/\\|?*'
        safe_name = filename
        
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
        
        # 限制文件名长度
        if len(safe_name) > 255:
            name, ext = os.path.splitext(safe_name)
            safe_name = name[:255-len(ext)] + ext
        
        return safe_name 