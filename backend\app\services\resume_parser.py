import os
import re
import logging
from typing import Dict, List, Optional, Any
from docx import Document
from docx.opc.exceptions import PackageNotFoundError
import unicodedata

logger = logging.getLogger(__name__)

class ResumeParser:
    """
    简历解析器 - 专门用于解析DOCX格式的简历文件
    按照me.md文档要求实现智能信息提取功能
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 定义关键词模式用于识别不同部分
        self.section_patterns = {
            'personal_info': [
                r'个人信息', r'基本信息', r'联系方式', r'个人资料',
                r'personal.*info', r'contact.*info', r'basic.*info'
            ],
            'education': [
                r'教育背景', r'教育经历', r'学历', r'毕业院校',
                r'education', r'academic', r'university', r'college'
            ],
            'work_experience': [
                r'工作经历', r'工作经验', r'职业经历', r'任职经历',
                r'work.*experience', r'employment', r'career', r'professional.*experience'
            ],
            'project_experience': [
                r'项目经验', r'项目经历', r'项目案例', r'参与项目',
                r'project.*experience', r'projects', r'project.*portfolio'
            ],
            'skills': [
                r'技能', r'专业技能', r'核心技能', r'技术技能',
                r'skills', r'technical.*skills', r'core.*competencies'
            ],
            'certifications': [
                r'证书', r'资格证书', r'认证', r'资质',
                r'certificates', r'certifications', r'qualifications'
            ],
            'summary': [
                r'个人总结', r'自我评价', r'个人简介', r'职业摘要',
                r'summary', r'profile', r'objective', r'career.*summary'
            ]
        }
        
        # 常见的联系信息模式
        self.contact_patterns = {
            'phone': re.compile(r'(\+?86)?[-\s]?1[3-9]\d{9}'),
            'email': re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'),
            'name': re.compile(r'^[^\d\W]+(?:\s+[^\d\W]+)*$', re.UNICODE)
        }
        
        # 时间日期模式
        self.date_patterns = [
            re.compile(r'(\d{4})[年.-](\d{1,2})[月.-]?'),  # 2020年6月, 2020-06, 2020.06
            re.compile(r'(\d{4})[年.-](\d{1,2})[月.-](\d{1,2})[日]?'),  # 2020年6月15日
            re.compile(r'(\d{1,2})/(\d{4})'),  # 06/2020
            re.compile(r'(\d{4})/(\d{1,2})'),  # 2020/06
        ]
    
    def parse_resume(self, file_path: str) -> Dict[str, Any]:
        """
        解析简历主入口方法
        
        Args:
            file_path: DOCX文件路径
            
        Returns:
            Dict: 包含解析出的简历信息的字典
        """
        try:
            # 验证文件存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 解析DOCX文档
            doc = Document(file_path)
            
            # 提取所有文本内容
            full_text = self._extract_full_text(doc)
            
            # 初始化结果字典
            resume_data = {
                'personal_info': {},
                'education': [],
                'work_experience': [],
                'project_experience': [],
                'skills': [],
                'certifications': [],
                'summary': '',
                'raw_text': full_text
            }
            
            # 分段解析
            sections = self._split_into_sections(full_text)
            
            # 解析各个部分
            resume_data['personal_info'] = self._parse_personal_info(sections)
            resume_data['education'] = self._parse_education(sections)
            resume_data['work_experience'] = self._parse_work_experience(sections)
            resume_data['project_experience'] = self._parse_project_experience(sections)
            resume_data['skills'] = self._parse_skills(sections)
            resume_data['certifications'] = self._parse_certifications(sections)
            resume_data['summary'] = self._parse_summary(sections)
            
            self.logger.info(f"简历解析完成: {file_path}")
            return resume_data
            
        except PackageNotFoundError:
            raise ValueError("无效的DOCX文件")
        except Exception as e:
            self.logger.error(f"简历解析失败: {str(e)}")
            raise ValueError(f"解析失败: {str(e)}")
    
    def _extract_full_text(self, doc: Document) -> str:
        """提取文档的所有文本内容"""
        text_parts = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_parts.append(paragraph.text.strip())
        
        # 提取表格中的文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    if cell.text.strip():
                        text_parts.append(cell.text.strip())
        
        return '\n'.join(text_parts)
    
    def _split_into_sections(self, text: str) -> Dict[str, str]:
        """将文本分割成不同的部分"""
        sections = {}
        current_section = 'unknown'
        current_content = []
        
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否为新的部分标题
            section_type = self._identify_section_type(line)
            if section_type:
                # 保存之前的部分
                if current_content:
                    sections[current_section] = '\n'.join(current_content)
                
                # 开始新的部分
                current_section = section_type
                current_content = []
            else:
                current_content.append(line)
        
        # 保存最后一个部分
        if current_content:
            sections[current_section] = '\n'.join(current_content)
        
        return sections
    
    def _identify_section_type(self, line: str) -> Optional[str]:
        """识别行是否为部分标题"""
        line_lower = line.lower()
        
        for section_type, patterns in self.section_patterns.items():
            for pattern in patterns:
                if re.search(pattern, line_lower):
                    return section_type
        
        return None
    
    def _parse_personal_info(self, sections: Dict[str, str]) -> Dict[str, str]:
        """解析个人信息"""
        personal_info = {}
        
        # 在多个部分中寻找个人信息
        search_sections = ['personal_info', 'unknown'] + list(sections.keys())
        
        for section_name in search_sections:
            if section_name not in sections:
                continue
            
            content = sections[section_name]
            
            # 提取姓名（通常在文档开头）
            if 'name' not in personal_info:
                name = self._extract_name(content)
                if name:
                    personal_info['name'] = name
            
            # 提取电话
            phone_match = self.contact_patterns['phone'].search(content)
            if phone_match:
                personal_info['phone'] = phone_match.group().strip()
            
            # 提取邮箱
            email_match = self.contact_patterns['email'].search(content)
            if email_match:
                personal_info['email'] = email_match.group().strip()
            
            # 提取地址
            address = self._extract_address(content)
            if address:
                personal_info['address'] = address
        
        return personal_info
    
    def _extract_name(self, text: str) -> Optional[str]:
        """提取姓名"""
        lines = text.split('\n')
        
        for line in lines[:5]:  # 只在前5行寻找姓名
            line = line.strip()
            if len(line) > 1 and len(line) < 20:
                # 排除明显不是姓名的内容
                if not any(char in line for char in ['@', 'http', '电话', '手机', '邮箱']):
                    # 简单的中文姓名判断
                    if re.match(r'^[\u4e00-\u9fa5]{2,4}$', line):
                        return line
                    # 英文姓名判断
                    elif re.match(r'^[A-Za-z\s]{2,20}$', line):
                        return line
        
        return None
    
    def _extract_address(self, text: str) -> Optional[str]:
        """提取地址信息"""
        address_keywords = ['地址', '住址', '居住地', '所在地', 'address', 'location']
        
        lines = text.split('\n')
        for line in lines:
            line_lower = line.lower()
            for keyword in address_keywords:
                if keyword in line_lower:
                    # 提取冒号或空格后的内容
                    parts = re.split(r'[：:]\s*', line)
                    if len(parts) > 1:
                        return parts[1].strip()
        
        return None
    
    def _parse_education(self, sections: Dict[str, str]) -> List[Dict[str, str]]:
        """解析教育背景"""
        education_list = []
        
        if 'education' not in sections:
            return education_list
        
        content = sections['education']
        
        # 按段落分割
        paragraphs = [p.strip() for p in content.split('\n') if p.strip()]
        
        current_edu = {}
        for paragraph in paragraphs:
            # 尝试匹配学校和专业信息
            school_match = self._extract_school_info(paragraph)
            if school_match:
                if current_edu:
                    education_list.append(current_edu)
                current_edu = school_match
            else:
                # 尝试提取时间信息
                dates = self._extract_dates(paragraph)
                if dates:
                    current_edu.update(dates)
        
        if current_edu:
            education_list.append(current_edu)
        
        return education_list
    
    def _extract_school_info(self, text: str) -> Optional[Dict[str, str]]:
        """提取学校信息"""
        # 常见的学校后缀
        school_suffixes = ['大学', '学院', '学校', 'University', 'College', 'School', 'Institute']
        
        for suffix in school_suffixes:
            if suffix in text:
                # 可能包含学校信息
                parts = text.split()
                school_info = {}
                
                # 寻找包含学校后缀的部分
                for part in parts:
                    if suffix in part:
                        school_info['school_name'] = part
                        break
                
                # 尝试提取专业信息
                major_keywords = ['专业', '系', 'major', 'department']
                for keyword in major_keywords:
                    if keyword in text:
                        # 提取专业信息的逻辑
                        continue
                
                if school_info:
                    return school_info
        
        return None
    
    def _parse_work_experience(self, sections: Dict[str, str]) -> List[Dict[str, str]]:
        """解析工作经历"""
        work_list = []
        
        if 'work_experience' not in sections:
            return work_list
        
        content = sections['work_experience']
        
        # 按段落分割工作经历
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        for paragraph in paragraphs:
            work_info = self._extract_work_info(paragraph)
            if work_info:
                work_list.append(work_info)
        
        return work_list
    
    def _extract_work_info(self, text: str) -> Optional[Dict[str, str]]:
        """提取单个工作经历信息"""
        work_info = {}
        
        lines = text.split('\n')
        
        # 通常第一行包含公司和职位信息
        if lines:
            first_line = lines[0].strip()
            # 尝试分离公司和职位
            if '|' in first_line or '·' in first_line or '-' in first_line:
                parts = re.split(r'[|·-]', first_line)
                if len(parts) >= 2:
                    work_info['company_name'] = parts[0].strip()
                    work_info['position'] = parts[1].strip()
            else:
                work_info['company_name'] = first_line
        
        # 提取时间信息
        dates = self._extract_dates(text)
        work_info.update(dates)
        
        # 提取工作描述
        if len(lines) > 1:
            description_lines = []
            for line in lines[1:]:
                line = line.strip()
                if line and not self._is_date_line(line):
                    description_lines.append(line)
            
            if description_lines:
                work_info['description'] = '\n'.join(description_lines)
        
        return work_info if work_info else None
    
    def _parse_project_experience(self, sections: Dict[str, str]) -> List[Dict[str, str]]:
        """解析项目经验"""
        project_list = []
        
        if 'project_experience' not in sections:
            return project_list
        
        content = sections['project_experience']
        
        # 类似工作经历的解析逻辑
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        for paragraph in paragraphs:
            project_info = self._extract_project_info(paragraph)
            if project_info:
                project_list.append(project_info)
        
        return project_list
    
    def _extract_project_info(self, text: str) -> Optional[Dict[str, str]]:
        """提取项目信息"""
        project_info = {}
        
        lines = text.split('\n')
        
        if lines:
            # 项目名称通常在第一行
            project_info['name'] = lines[0].strip()
        
        # 提取时间信息
        dates = self._extract_dates(text)
        project_info.update(dates)
        
        # 提取项目描述
        if len(lines) > 1:
            description_lines = []
            for line in lines[1:]:
                line = line.strip()
                if line and not self._is_date_line(line):
                    description_lines.append(line)
            
            if description_lines:
                project_info['description'] = '\n'.join(description_lines)
        
        return project_info if project_info else None
    
    def _parse_skills(self, sections: Dict[str, str]) -> List[Dict[str, str]]:
        """解析技能信息"""
        skills_list = []
        
        if 'skills' not in sections:
            return skills_list
        
        content = sections['skills']
        
        # 按行分割技能
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        for line in lines:
            # 尝试分离技能类别和具体技能
            if ':' in line or '：' in line:
                parts = re.split(r'[：:]', line, 1)
                if len(parts) == 2:
                    category = parts[0].strip()
                    skills_text = parts[1].strip()
                    
                    # 分割具体技能
                    skill_items = re.split(r'[,，、;；]', skills_text)
                    for skill_item in skill_items:
                        skill_item = skill_item.strip()
                        if skill_item:
                            skills_list.append({
                                'category': category,
                                'name': skill_item,
                                'proficiency': ''
                            })
            else:
                # 没有明确分类的技能
                skill_items = re.split(r'[,，、;；]', line)
                for skill_item in skill_items:
                    skill_item = skill_item.strip()
                    if skill_item:
                        skills_list.append({
                            'category': '其他',
                            'name': skill_item,
                            'proficiency': ''
                        })
        
        return skills_list
    
    def _parse_certifications(self, sections: Dict[str, str]) -> List[Dict[str, str]]:
        """解析证书信息"""
        cert_list = []
        
        if 'certifications' not in sections:
            return cert_list
        
        content = sections['certifications']
        
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        for line in lines:
            cert_info = {'name': line, 'issuer': '', 'issue_date': ''}
            
            # 尝试提取时间信息
            dates = self._extract_dates(line)
            if dates and 'start_date' in dates:
                cert_info['issue_date'] = dates['start_date']
            
            cert_list.append(cert_info)
        
        return cert_list
    
    def _parse_summary(self, sections: Dict[str, str]) -> str:
        """解析个人总结"""
        if 'summary' in sections:
            return sections['summary']
        
        # 如果没有明确的总结部分，尝试从其他部分提取
        for section_name, content in sections.items():
            if len(content) > 50 and len(content) < 500:
                # 可能是个人总结
                if any(keyword in content for keyword in ['热爱', '擅长', '具备', '经验丰富']):
                    return content
        
        return ''
    
    def _extract_dates(self, text: str) -> Dict[str, str]:
        """提取时间信息"""
        dates = {}
        
        for pattern in self.date_patterns:
            matches = pattern.findall(text)
            if matches:
                if len(matches) == 1:
                    dates['start_date'] = self._format_date(matches[0])
                elif len(matches) >= 2:
                    dates['start_date'] = self._format_date(matches[0])
                    dates['end_date'] = self._format_date(matches[1])
                break
        
        # 检查"至今"、"现在"等关键词
        if '至今' in text or '现在' in text or '目前' in text or 'present' in text.lower():
            dates['is_current'] = True
        
        return dates
    
    def _format_date(self, date_tuple) -> str:
        """格式化日期"""
        if isinstance(date_tuple, tuple):
            if len(date_tuple) == 2:
                year, month = date_tuple
                return f"{year}-{month.zfill(2)}"
            elif len(date_tuple) == 3:
                year, month, day = date_tuple
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
        return str(date_tuple)
    
    def _is_date_line(self, line: str) -> bool:
        """判断是否为日期行"""
        for pattern in self.date_patterns:
            if pattern.search(line):
                return True
        return False 