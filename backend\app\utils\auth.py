"""
用户会话和权限管理工具
"""
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, cast
from datetime import datetime, timedelta
from flask import current_app, g, request
from flask_jwt_extended import get_jwt_identity, verify_jwt_in_request
from app.utils.api_response import Authentication<PERSON>rror, AuthorizationError
from app.utils.flask_types import FlaskWithPermissions
import logging

logger = logging.getLogger(__name__)

class SessionManager:
    """会话管理器"""
    
    def __init__(self, redis_client):
        self.redis = redis_client
        self.session_prefix = 'session:'
        self.expiry_time = timedelta(hours=24)
    
    def create_session(self, user_id: int, data: Dict[str, Any]) -> str:
        """
        创建新会话
        :param user_id: 用户ID
        :param data: 会话数据
        :return: 会话ID
        """
        from uuid import uuid4
        session_id = str(uuid4())
        key = f"{self.session_prefix}{session_id}"
        
        # 保存会话数据
        session_data = {
            'user_id': user_id,
            'created_at': datetime.now().isoformat(),
            **data
        }
        self.redis.hmset(key, session_data)
        self.redis.expire(key, int(self.expiry_time.total_seconds()))
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话数据
        :param session_id: 会话ID
        :return: 会话数据或None
        """
        key = f"{self.session_prefix}{session_id}"
        data = self.redis.hgetall(key)
        if not data:
            return None
        return {k.decode(): v.decode() for k, v in data.items()}
    
    def update_session(self, session_id: str, data: Dict[str, Any]) -> bool:
        """
        更新会话数据
        :param session_id: 会话ID
        :param data: 新的会话数据
        :return: 是否成功
        """
        key = f"{self.session_prefix}{session_id}"
        if not self.redis.exists(key):
            return False
        
        self.redis.hmset(key, data)
        self.redis.expire(key, int(self.expiry_time.total_seconds()))
        return True
    
    def delete_session(self, session_id: str) -> bool:
        """
        删除会话
        :param session_id: 会话ID
        :return: 是否成功
        """
        key = f"{self.session_prefix}{session_id}"
        return bool(self.redis.delete(key))
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        pattern = f"{self.session_prefix}*"
        for key in self.redis.scan_iter(pattern):
            if not self.redis.ttl(key):
                self.redis.delete(key)

class PermissionManager:
    """权限管理器"""
    
    def __init__(self):
        self.permissions = {}
        self.roles = {}
    
    def add_permission(self, name: str, description: str):
        """
        添加权限
        :param name: 权限名称
        :param description: 权限描述
        """
        self.permissions[name] = description
    
    def add_role(self, name: str, permissions: List[str]):
        """
        添加角色
        :param name: 角色名称
        :param permissions: 权限列表
        """
        self.roles[name] = permissions
    
    def has_permission(self, user_role: str, permission: str) -> bool:
        """
        检查角色是否有权限
        :param user_role: 用户角色
        :param permission: 权限名称
        :return: 是否有权限
        """
        if user_role not in self.roles:
            return False
        return permission in self.roles[user_role]
    
    def get_role_permissions(self, role: str) -> List[str]:
        """
        获取角色的所有权限
        :param role: 角色名称
        :return: 权限列表
        """
        return self.roles.get(role, [])

def require_auth(func: Callable) -> Callable:
    """要求用户认证的装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            verify_jwt_in_request()
            user_id = get_jwt_identity()
            if not user_id:
                raise AuthenticationError('无效的认证信息')
            return func(*args, **kwargs)
        except Exception as e:
            raise AuthenticationError('需要认证') from e
    return wrapper

def require_permission(permission: str) -> Callable:
    """
    要求特定权限的装饰器
    :param permission: 所需权限
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 确保用户已认证
            verify_jwt_in_request()
            user_id = get_jwt_identity()
            
            # 获取用户角色
            from app.models import User
            user = User.query.get(user_id)
            if not user:
                raise AuthenticationError('用户不存在')
            
            # 检查权限
            app = cast(FlaskWithPermissions, current_app)
            if not app.permission_manager.has_permission(
                user.role, permission):
                raise AuthorizationError('没有权限执行此操作')
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def admin_required(func: Callable) -> Callable:
    """要求管理员权限的装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 确保用户已认证
        verify_jwt_in_request()
        user_id = get_jwt_identity()
        
        # 检查是否为管理员
        from app.models import User
        user = User.query.get(user_id)
        if not user or not user.is_admin:
            raise AuthorizationError('需要管理员权限')
        
        return func(*args, **kwargs)
    return wrapper

def init_permission_manager(app):
    """初始化权限管理器"""
    manager = PermissionManager()
    
    # 添加基本权限
    manager.add_permission('user:read', '查看用户信息')
    manager.add_permission('user:write', '修改用户信息')
    manager.add_permission('content:read', '查看内容')
    manager.add_permission('content:write', '修改内容')
    manager.add_permission('admin:access', '访问管理功能')
    
    # 添加角色
    manager.add_role('user', ['user:read', 'content:read'])
    manager.add_role('editor', ['user:read', 'content:read', 'content:write'])
    manager.add_role('admin', ['user:read', 'user:write', 'content:read', 
                              'content:write', 'admin:access'])
    
    app.permission_manager = manager
