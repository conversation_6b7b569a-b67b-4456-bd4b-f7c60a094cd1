from flask import Blueprint, request, jsonify, abort
from app import db
from ..models.photo_template import PhotoTemplate
from ..models.resume import ResumeTemplate
from pydantic import BaseModel
from typing import Optional

bp = Blueprint('templates', __name__, url_prefix='/api/templates')

class TemplateBase(BaseModel):
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    preview_url: Optional[str] = None
    default_color: Optional[str] = None
    enabled: bool = True
    order: int = 0
    type: str  # 'business_card' or 'resume'

class TemplateCreate(TemplateBase):
    pass

class TemplateUpdate(TemplateBase):
    pass

class TemplateOut(TemplateBase):
    id: int

def to_dict(obj):
    # 只返回 TemplateOut 需要的字段
    return {
        'id': obj.id,
        'name': obj.name,
        'description': obj.description,
        'category': obj.category,
        'preview_url': obj.preview_url,
        'default_color': obj.default_color,
        'enabled': obj.enabled,
        'order': obj.order,
        'type': getattr(obj, 'type', None)
    }

@bp.route('/', methods=['GET'])
def list_templates():
    type_ = request.args.get('type')
    model = PhotoTemplate if type_ == 'business_card' else ResumeTemplate
    query = db.session.query(model)
    if type_:
        query = query.filter_by(type=type_)
    # 统一排序字段
    query = query.order_by(model.order)
    return jsonify([to_dict(obj) for obj in query.all()])

@bp.route('/', methods=['POST'])
def create_template():
    data = request.get_json()
    tpl = TemplateCreate(**data)
    model = PhotoTemplate if tpl.type == 'business_card' else ResumeTemplate
    obj = model(**tpl.dict())
    db.session.add(obj)
    db.session.commit()
    db.session.refresh(obj)
    return jsonify(to_dict(obj))

@bp.route('/<int:tpl_id>', methods=['PUT'])
def update_template(tpl_id):
    data = request.get_json()
    tpl = TemplateUpdate(**data)
    model = PhotoTemplate if tpl.type == 'business_card' else ResumeTemplate
    obj = db.session.query(model).filter_by(id=tpl_id).first()
    if not obj:
        abort(404, description='Template not found')
    for k, v in tpl.dict(exclude_unset=True).items():
        setattr(obj, k, v)
    db.session.commit()
    db.session.refresh(obj)
    return jsonify(to_dict(obj))

@bp.route('/<int:tpl_id>', methods=['DELETE'])
def delete_template(tpl_id):
    type_ = request.args.get('type')
    model = PhotoTemplate if type_ == 'business_card' else ResumeTemplate
    obj = db.session.query(model).filter_by(id=tpl_id).first()
    if not obj:
        abort(404, description='Template not found')
    db.session.delete(obj)
    db.session.commit()
    return jsonify({'ok': True})

@bp.route('/<int:tpl_id>/enable', methods=['POST'])
def enable_template(tpl_id):
    type_ = request.args.get('type')
    model = PhotoTemplate if type_ == 'business_card' else ResumeTemplate
    obj = db.session.query(model).filter_by(id=tpl_id).first()
    if not obj:
        abort(404, description='Template not found')
    obj.enabled = True
    db.session.commit()
    return jsonify({'ok': True})

@bp.route('/<int:tpl_id>/disable', methods=['POST'])
def disable_template(tpl_id):
    type_ = request.args.get('type')
    model = PhotoTemplate if type_ == 'business_card' else ResumeTemplate
    obj = db.session.query(model).filter_by(id=tpl_id).first()
    if not obj:
        abort(404, description='Template not found')
    obj.enabled = False
    db.session.commit()
    return jsonify({'ok': True})

@bp.route('/<int:tpl_id>/order', methods=['POST'])
def update_template_order(tpl_id):
    data = request.get_json()
    order = data.get('order')
    type_ = request.args.get('type')
    model = PhotoTemplate if type_ == 'business_card' else ResumeTemplate
    obj = db.session.query(model).filter_by(id=tpl_id).first()
    if not obj:
        abort(404, description='Template not found')
    obj.order = order
    db.session.commit()
    return jsonify({'ok': True}) 