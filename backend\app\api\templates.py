from flask import request, jsonify, current_app
from app import db
from app.api import api_bp
from app.models.photo_template import PhotoTemplate
from app.models.resume_template import ResumeTemplate
from app.models.business_card import BusinessCardTemplate
from app.api.errors import success_response, error_response, not_found

@api_bp.route('/templates/photo', methods=['GET'])
def get_photo_templates():
    """获取证件照模板"""
    try:
        templates = PhotoTemplate.query.filter_by(is_active=True).order_by(PhotoTemplate.sort_order).all()

        templates_data = []
        for template in templates:
            template_data = {
                'id': template.id,
                'name': template.name,
                'category': template.category,
                'width': template.width,
                'height': template.height,
                'width_mm': template.width_mm,
                'height_mm': template.height_mm,
                'description': template.description,
                'is_active': template.is_active,
                'sort_order': template.sort_order
            }
            templates_data.append(template_data)

        return success_response(
            message='证件照模板加载成功',
            data={'templates': templates_data}
        )

    except Exception as e:
        current_app.logger.error(f"获取证件照模板失败: {str(e)}")
        return error_response('加载证件照模板失败')


@api_bp.route('/templates/business-cards', methods=['GET'])
def get_business_card_templates_list():
    """获取名片模板"""
    try:
        templates = BusinessCardTemplate.query.filter_by(is_active=True).order_by(BusinessCardTemplate.sort_order).all()

        templates_data = []
        for template in templates:
            template_data = {
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'category': template.category,
                'industry': template.industry,
                'layout_type': template.layout_type,
                'width_mm': template.width_mm,
                'height_mm': template.height_mm,
                'template_html': template.template_html,
                'template_css': template.template_css,
                'default_colors': template.default_colors,
                'is_active': template.is_active,
                'sort_order': template.sort_order
            }
            templates_data.append(template_data)

        return success_response(
            message='名片模板加载成功',
            data={'templates': templates_data}
        )

    except Exception as e:
        current_app.logger.error(f"获取名片模板失败: {str(e)}")
        return error_response('加载名片模板失败')


@api_bp.route('/templates/resumes', methods=['GET'])
def get_resume_templates_list():
    """获取简历模板"""
    try:
        templates = ResumeTemplate.query.filter_by(is_active=True).order_by(ResumeTemplate.sort_order).all()

        templates_data = []
        for template in templates:
            template_data = {
                'id': template.id,
                'name': template.name,
                'description': template.description,
                'category': template.category,
                'layout': template.layout,
                'style': template.style,
                'sections': template.sections,
                'is_active': template.is_active,
                'sort_order': template.sort_order
            }
            templates_data.append(template_data)

        return success_response(
            message='简历模板加载成功',
            data={'templates': templates_data}
        )

    except Exception as e:
        current_app.logger.error(f"获取简历模板失败: {str(e)}")
        return error_response('加载简历模板失败')

