"""
配置管理工具
"""
import os
import json
import logging
from typing import Any, Dict, Optional
from flask import current_app

logger = logging.getLogger(__name__)

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self._config = {}
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
            else:
                self._config = {}
                self.save_config()  # 创建新的配置文件
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            self._config = {}

    def save_config(self):
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self._config.get(key, default)

    def set(self, key: str, value: Any):
        """设置配置值"""
        self._config[key] = value
        self.save_config()

    def delete(self, key: str):
        """删除配置项"""
        if key in self._config:
            del self._config[key]
            self.save_config()

    def get_all(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()

    def update(self, new_config: Dict[str, Any]):
        """批量更新配置"""
        self._config.update(new_config)
        self.save_config()

    def get_nested(self, path: str, default: Any = None) -> Any:
        """
        获取嵌套配置值
        :param path: 以点分隔的配置路径，如 "database.host"
        :param default: 默认值
        :return: 配置值
        """
        current = self._config
        for key in path.split('.'):
            if isinstance(current, dict):
                current = current.get(key)
            else:
                return default
        return current if current is not None else default

    def set_nested(self, path: str, value: Any):
        """
        设置嵌套配置值
        :param path: 以点分隔的配置路径
        :param value: 要设置的值
        """
        keys = path.split('.')
        current = self._config
        
        # 遍历到倒数第二个键
        for key in keys[:-1]:
            if key not in current or not isinstance(current[key], dict):
                current[key] = {}
            current = current[key]
        
        # 设置最后一个键的值
        current[keys[-1]] = value
        self.save_config()

class SystemConfig:
    """系统配置管理"""
    
    def __init__(self):
        config_path = os.path.join(current_app.instance_path, 'system_config.json')
        self.config_manager = ConfigManager(config_path)

    def get_upload_limits(self) -> Dict[str, int]:
        """获取上传限制配置"""
        return {
            'max_image_size': self.config_manager.get('upload.max_image_size', 10 * 1024 * 1024),
            'max_document_size': self.config_manager.get('upload.max_document_size', 20 * 1024 * 1024),
            'allowed_image_types': self.config_manager.get('upload.allowed_image_types', 
                ['jpg', 'jpeg', 'png', 'gif']),
            'allowed_document_types': self.config_manager.get('upload.allowed_document_types',
                ['pdf', 'doc', 'docx', 'txt'])
        }

    def get_processing_config(self) -> Dict[str, Any]:
        """获取处理配置"""
        return {
            'image_quality': self.config_manager.get('processing.image_quality', 85),
            'max_dimension': self.config_manager.get('processing.max_dimension', 2048),
            'thumbnail_size': self.config_manager.get('processing.thumbnail_size', [200, 200]),
            'compress_images': self.config_manager.get('processing.compress_images', True)
        }

    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return {
            'rate_limit_max': self.config_manager.get('security.rate_limit_max', 60),
            'rate_limit_window': self.config_manager.get('security.rate_limit_window', 60),
            'session_timeout': self.config_manager.get('security.session_timeout', 1800),
            'password_min_length': self.config_manager.get('security.password_min_length', 8)
        }

    def get_email_config(self) -> Dict[str, Any]:
        """获取邮件配置"""
        return {
            'smtp_server': self.config_manager.get('email.smtp_server', ''),
            'smtp_port': self.config_manager.get('email.smtp_port', 587),
            'smtp_user': self.config_manager.get('email.smtp_user', ''),
            'smtp_password': self.config_manager.get('email.smtp_password', ''),
            'use_tls': self.config_manager.get('email.use_tls', True),
            'from_address': self.config_manager.get('email.from_address', '')
        }

# 创建全局配置实例
system_config: Optional[SystemConfig] = None

def init_system_config() -> SystemConfig:
    """初始化系统配置"""
    global system_config
    if system_config is None:
        system_config = SystemConfig()
    return system_config

def get_system_config() -> SystemConfig:
    """获取系统配置实例"""
    global system_config
    if system_config is None:
        system_config = init_system_config()
    assert system_config is not None, "系统配置初始化失败"
    return system_config
