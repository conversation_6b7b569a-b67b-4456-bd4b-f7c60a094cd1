<template>
  <footer class="pdf-statusbar">
    <button class="status-btn" @click="$emit('prev-page')" :disabled="page <= 1" title="上一页">
      <svg width="18" height="18" viewBox="0 0 24 24"><path d="M15 18l-6-6 6-6" stroke="#3182ce" stroke-width="2" fill="none"/></svg>
    </button>
    <span class="status-label">第 {{ page }}/{{ pageCount }} 页</span>
    <button class="status-btn" @click="$emit('next-page')" :disabled="page >= pageCount" title="下一页">
      <svg width="18" height="18" viewBox="0 0 24 24"><path d="M9 6l6 6-6 6" stroke="#3182ce" stroke-width="2" fill="none"/></svg>
    </button>
    <span class="status-label">| 缩放 {{ zoom }}%</span>
    <button class="status-btn export" @click="$emit('export')" title="导出PDF">
      <svg width="18" height="18" viewBox="0 0 24 24"><path d="M5 20h14v-2H5v2zm7-18C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 14.5h-2v-6h2v6zm0-8h-2V7h2v1.5z" fill="#3182ce"/></svg>
      <span>导出</span>
    </button>
  </footer>
</template>
<script setup>
defineProps({
  page: Number,
  pageCount: Number,
  zoom: Number
})
</script>
<style scoped>
.pdf-statusbar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-top: 1px solid #e5e6eb;
  min-height: 44px;
  font-size: 15px;
  gap: 1.2em;
  color: #3182ce;
  box-shadow: 0 -2px 8px #e5e6eb22;
}
.status-btn {
  background: none;
  border: none;
  color: #3182ce;
  border-radius: 5px;
  padding: 0.3em 0.7em;
  font-size: 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.2em;
  transition: background 0.2s, color 0.2s;
}
.status-btn:disabled {
  color: #bbb;
  background: #f5f5f5;
  cursor: not-allowed;
}
.status-btn.export {
  background: #e3eaff;
  color: #2563eb;
  font-weight: 500;
  gap: 0.4em;
}
.status-btn.export:hover {
  background: #d0e3ff;
}
.status-label {
  color: #3182ce;
  font-size: 15px;
}
</style> 