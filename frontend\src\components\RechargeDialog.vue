<template>
  <el-dialog v-model="visible" title="积分充值" width="400px" @close="onClose">
    <div v-if="!orderNo">
      <el-input v-model="amount" placeholder="请输入充值金额（元）" style="width:200px; margin-bottom:1em;" />
      <div style="margin-bottom:1em;">
        <el-radio-group v-model="channel">
          <el-radio label="wechat">微信支付</el-radio>
          <el-radio label="alipay">支付宝支付</el-radio>
        </el-radio-group>
      </div>
      <el-button type="primary" @click="createOrder">生成订单</el-button>
    </div>
    <div v-else style="text-align:center;">
      <div style="margin-bottom:1em;">请使用{{ channel==='wechat'?'微信':'支付宝' }}扫码支付</div>
      <img :src="qrUrl" alt="二维码" style="width:180px;height:180px;" v-if="qrUrl" />
      <div v-if="payUrl && !qrUrl"><a :href="payUrl" target="_blank">点击跳转支付</a></div>
      <div style="margin-top:1em; color:#888;">支付完成后自动到账</div>
    </div>
  </el-dialog>
</template>
<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
const props = defineProps({
  modelValue: Boolean
})
const emit = defineEmits(['update:modelValue', 'success'])
const visible = ref(props.modelValue)
watch(() => props.modelValue, v => visible.value = v)
watch(visible, v => emit('update:modelValue', v))
const amount = ref('');
const channel = ref('wechat');
const orderNo = ref('');
const payUrl = ref('');
const qrUrl = ref('');
let pollTimer = null;

async function createOrder() {
  if (!amount.value || isNaN(amount.value) || Number(amount.value) <= 0) {
    ElMessage.warning('请输入有效金额');
    return;
  }
  const res = await axios.post('/api/user/points/recharge_order', {
    amount: Number(amount.value),
    channel: channel.value
  });
  orderNo.value = res.data.order_no;
  payUrl.value = res.data.pay_url;
  // 可用第三方库生成二维码，或直接用 payUrl 作为 src
  qrUrl.value = `https://api.qrserver.com/v1/create-qr-code/?size=180x180&data=${encodeURIComponent(payUrl.value)}`;
  pollPayStatus();
}
async function pollPayStatus() {
  pollTimer && clearTimeout(pollTimer);
  // 模拟支付状态，实际应请求后端订单状态
  pollTimer = setTimeout(async () => {
    // 这里应请求后端订单状态，若已支付则 success
    // 模拟3秒后自动支付成功
    await axios.post('/api/user/points/payment_callback', { order_no: orderNo.value, trade_no: 'MOCK123456' });
    ElMessage.success('支付成功，积分已到账！');
    emit('success');
    visible.value = false;
  }, 3000);
}
function onClose() {
  pollTimer && clearTimeout(pollTimer);
  orderNo.value = '';
  payUrl.value = '';
  qrUrl.value = '';
  amount.value = '';
}
</script>
<style scoped>
</style> 