<template>
  <div class="modal-bg" @click.self="$emit('close')">
    <div class="modal">
      <h3>生成长图</h3>
      <div class="page-previews">
        <div v-for="page in pages" :key="page.id" class="preview-item">
          <img :src="page.previewUrl" />
        </div>
      </div>
      <div class="options">
        <label>
          图片格式:
          <select v-model="options.format">
            <option value="png">PNG</option>
            <option value="jpeg">JPEG</option>
          </select>
        </label>
        <label v-if="options.format === 'jpeg'">
          质量:
          <input type="range" v-model="options.quality" min="10" max="100" />
          <span>{{ options.quality }}</span>
        </label>
      </div>
      <div class="modal-actions">
        <button class="btn btn-primary" @click="$emit('generate', options)">确认生成</button>
        <button class="btn" @click="$emit('close')">取消</button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
defineProps(['pages']);
const options = ref({
  format: 'png',
  quality: 90
});
</script>
<style scoped>
.modal-bg { position: fixed; top:0; left:0; right:0; bottom:0; background:rgba(0,0,0,0.2); display:flex; align-items:center; justify-content:center; z-index:1000; }
.modal { background:#fff; border-radius:8px; padding:2rem; min-width:300px; }
.btn { margin: 1rem 1rem 0 0; }
.page-previews {
  display: flex;
  gap: 1rem;
  overflow-x: auto;
  padding: 1rem;
  border: 1px solid #eee;
  border-radius: 6px;
  background: #f9f9f9;
}
.preview-item img {
  height: 120px;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}
.options {
  margin: 1.5rem 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.options label {
  display: flex;
  align-items: center;
  gap: 1rem;
}
</style> 