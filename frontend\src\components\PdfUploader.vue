<template>
  <div class="uploader" @dragover.prevent @drop.prevent="onDrop">
    <input ref="fileInput" type="file" accept="application/pdf" style="display:none" @change="onFileChange" />
    <div class="uploader-box" @click="triggerFileInput">
      <svg width="48" height="48" viewBox="0 0 48 48"><rect x="8" y="8" width="32" height="32" rx="8" fill="#e3eaff"/><path d="M24 16v16M16 24h16" stroke="#3182ce" stroke-width="3" stroke-linecap="round"/></svg>
      <div class="uploader-text">点击或拖拽PDF文件到此处上传</div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';
const emit = defineEmits(['file-loaded']);
const fileInput = ref(null);
function triggerFileInput() {
  fileInput.value && fileInput.value.click();
}
function onFileChange(e) {
  const file = e.target.files[0];
  if (file) processFile(file);
}
function onDrop(e) {
  const file = e.dataTransfer.files[0];
  if (file && file.type === 'application/pdf') processFile(file);
}
async function processFile(file) {
  const reader = new FileReader();
  reader.onload = async (evt) => {
    const arrayBuffer = evt.target.result;
    const pdfjsLib = await import('pdfjs-dist');
    pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    let pages = [];
    for (let i = 1; i <= pdf.numPages; i++) {
      const pdfPage = await pdf.getPage(i);
      const viewport = pdfPage.getViewport({ scale: 0.2 });
      const canvas = document.createElement('canvas');
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      const ctx = canvas.getContext('2d');
      await pdfPage.render({ canvasContext: ctx, viewport }).promise;
      const previewUrl = canvas.toDataURL();
      pages.push({ id: `${file.name}_p${i}_${Date.now()}`, previewUrl });
    }
    emit('file-loaded', {
      name: file.name,
      arrayBuffer,
      pages
    });
  };
  reader.readAsArrayBuffer(file);
}
</script>
<style scoped>
.uploader {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  min-height: 0;
}
.uploader-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px #3182ce22;
  padding: 3em 4em;
  cursor: pointer;
  transition: box-shadow 0.2s, background 0.2s;
  border: 2px dashed #e3eaff;
}
.uploader-box:hover {
  background: #e3eaff33;
  box-shadow: 0 4px 24px #3182ce33;
}
.uploader-text {
  margin-top: 1.2em;
  color: #3182ce;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
}
</style> 