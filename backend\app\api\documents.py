import os
import uuid
import asyncio
from datetime import datetime
import traceback
from threading import Thread
from urllib.parse import unquote
from flask import request, jsonify, current_app, send_from_directory
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
from werkzeug.exceptions import BadRequest, NotFound
import sqlalchemy.exc
import time

from app import db
from app.models.document import Document, ComparisonTask, DocumentType, TaskStatus
from app.models.user import User
from app.api.errors import validation_error, not_found, bad_request, internal_server_error
from app.services.document_comparison.document_parser import DocumentParser
from app.services.document_comparison.comparison_engine import EnhancedComparisonEngine
from app.services.document_comparison.report_generator import ReportGenerator

# 允许的文档类型
ALLOWED_EXTENSIONS = {'docx', 'pdf', 'wps', 'doc'}
ALLOWED_MIME_TYPES = {
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-works',
    'application/msword',
    'application/zip'  # DOCX文件实际上是ZIP格式
}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

def allowed_file(filename):
    """检查文件是否允许上传"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_documents():
    """获取用户的文档列表"""
    try:
        user_id = get_jwt_identity()
        document_type = request.args.get('type', 'all')
        
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
        except ValueError:
            return bad_request('页码参数无效')
        
        try:
            query = Document.query.filter_by(user_id=user_id, status=1)
            
            if document_type != 'all':
                if document_type in ['standard', 'modified', 'report']:
                    query = query.filter_by(document_type=DocumentType(document_type))
                else:
                    return bad_request('无效的文档类型')
            
            documents = query.order_by(Document.upload_time.desc()).paginate(
                page=page, per_page=per_page, error_out=False
            )
            
            return jsonify({
                'success': True,
                'data': {
                    'documents': [doc.to_dict() for doc in documents.items],
                    'total': documents.total,
                    'pages': documents.pages,
                    'current_page': page,
                    'per_page': per_page
                }
            })
            
        except sqlalchemy.exc.SQLAlchemyError as e:
            current_app.logger.error(f"数据库查询失败: {str(e)}\n{traceback.format_exc()}")
            db.session.rollback()
            return internal_server_error('数据库查询失败')
            
    except Exception as e:
        current_app.logger.error(f"获取文档列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('获取文档列表失败')

def validate_file(file):
    """详细验证上传的文件"""
    errors = []
    
    if not file or file.filename == '':
        errors.append('没有选择文件')
        return errors
        
    # 检查文件名
    if not '.' in file.filename:
        errors.append('文件名无效')
        return errors
        
    # 检查文件扩展名
    extension = file.filename.rsplit('.', 1)[1].lower()
    if extension not in ALLOWED_EXTENSIONS:
        errors.append(f'不支持的文件类型: {extension}')
        return errors
        
    # 检查MIME类型
    if file.mimetype not in ALLOWED_MIME_TYPES:
        errors.append(f'不支持的文件格式: {file.mimetype}')
        return errors
        
    # 检查文件大小
    try:
        file.seek(0, os.SEEK_END)
        size = file.tell()
        file.seek(0)
        
        if size > MAX_FILE_SIZE:
            errors.append(f'文件大小超过限制（最大50MB）')
            return errors
            
        if size == 0:
            errors.append('文件内容为空')
            return errors
    except Exception as e:
        errors.append('文件读取失败')
        current_app.logger.error(f"文件验证失败: {str(e)}")
        return errors
        
    return errors

def upload_document():
    """上传文档的改进版本"""
    file_path = None
    try:
        user_id = get_jwt_identity()
        
        # 基本验证
        if 'file' not in request.files:
            return bad_request('请选择要上传的文件')
            
        file = request.files['file']
        
        # 详细验证
        validation_errors = validate_file(file)
        if validation_errors:
            return bad_request(validation_errors[0])
            
        # 获取文档类型
        document_type = request.form.get('document_type', 'standard')
        if document_type not in ['standard', 'modified']:
            return bad_request('无效的文档类型')
            
        try:
            # 生成文件ID和处理文件名
            file_id = uuid.uuid4().hex
            # 解码文件名中的URL编码字符（包括中文）
            original_filename = unquote(file.filename)
            # 获取文件扩展名
            file_ext = original_filename.rsplit('.', 1)[1].lower()
            # 使用UUID作为存储文件名
            filename = f"{file_id}.{file_ext}"
            
            # 创建用户文档目录
            user_doc_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'documents', str(user_id))
            os.makedirs(user_doc_dir, exist_ok=True)
            
            # 保存文件
            file_path = os.path.join(user_doc_dir, filename)
            file.save(file_path)
            
            # 验证文件是否成功保存
            if not os.path.exists(file_path):
                raise IOError('文件保存失败')
                
            # 验证保存的文件大小
            saved_size = os.path.getsize(file_path)
            if saved_size == 0:
                raise IOError('保存的文件为空')
                
            # 保存到数据库
            document = Document(
                file_id=file_id,
                user_id=user_id,
                filename=filename,
                original_filename=original_filename,  # 保存原始文件名（包含中文）
                file_path=file_path,
                file_size=saved_size,
                file_type=file_ext,
                mime_type=file.mimetype,
                document_type=DocumentType(document_type),
                status=1
            )
            
            db.session.add(document)
            db.session.commit()
            
            current_app.logger.info(f"文档上传成功: {original_filename}, ID: {file_id}")
            
            return jsonify({
                'success': True,
                'message': '文档上传成功',
                'data': document.to_dict()
            })
            
        except IOError as e:
            current_app.logger.error(f"文件保存失败: {str(e)}")
            # 清理可能部分保存的文件
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as cleanup_error:
                    current_app.logger.error(f"清理失败的文件时出错: {str(cleanup_error)}")
            return jsonify({
                'success': False,
                'message': '文件保存失败，请重试'
            }), 500
            
        except sqlalchemy.exc.SQLAlchemyError as e:
            current_app.logger.error(f"数据库操作失败: {str(e)}")
            # 清理已保存的文件
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except Exception as cleanup_error:
                    current_app.logger.error(f"清理失败的文件时出错: {str(cleanup_error)}")
            return jsonify({
                'success': False,
                'message': '数据库操作失败，请重试'
            }), 500
            
    except Exception as e:
        current_app.logger.error(f"文档上传过程中发生未知错误: {str(e)}")
        # 清理可能存在的文件
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
            except Exception as cleanup_error:
                current_app.logger.error(f"清理失败的文件时出错: {str(cleanup_error)}")
        return jsonify({
            'success': False,
            'message': '上传失败，请重试'
        }), 500

def delete_document(doc_id):
    """删除文档"""
    try:
        user_id = get_jwt_identity()
        
        document = Document.query.filter_by(id=doc_id, user_id=user_id, status=1).first()
        if not document:
            return not_found('文档不存在')
        
        # 检查是否有关联的对比任务
        active_tasks = ComparisonTask.query.filter(
            (ComparisonTask.standard_doc_id == doc_id) | 
            (ComparisonTask.modified_doc_id == doc_id),
            ComparisonTask.status.in_([TaskStatus.PENDING, TaskStatus.PROCESSING])
        ).first()
        
        if active_tasks:
            return bad_request('文档正在使用中，无法删除')
        
        # 软删除
        document.status = 0
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文档删除成功'
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除文档失败: {e}")
        return jsonify({'success': False, 'message': '删除文档失败'}), 500

def create_comparison_task():
    """创建文档对比任务"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # 验证参数
        if not data or 'standard_doc_id' not in data or 'modified_doc_id' not in data:
            return bad_request('缺少必要参数')
        
        standard_doc_id = data['standard_doc_id']
        modified_doc_id = data['modified_doc_id']
        task_name = data.get('task_name', '')
        
        # 处理所有对比配置选项
        ignore_whitespace = data.get('ignore_whitespace', True)
        detailed_report = data.get('detailed_report', True)
        extract_tables = data.get('extract_tables', True)
        use_ocr = data.get('use_ocr', False)
        visual_comparison = data.get('visual_comparison', False)
        
        # 验证文档存在
        standard_doc = Document.query.filter_by(
            id=standard_doc_id, user_id=user_id, status=1
        ).first()
        modified_doc = Document.query.filter_by(
            id=modified_doc_id, user_id=user_id, status=1
        ).first()
        
        if not standard_doc or not modified_doc:
            return bad_request('文档不存在或无权访问')
        
        # 创建任务（包含所有新字段）
        task = ComparisonTask(
            user_id=user_id,
            standard_doc_id=standard_doc_id,
            modified_doc_id=modified_doc_id,
            task_name=task_name,
            ignore_whitespace=ignore_whitespace,
            detailed_report=detailed_report,
            extract_tables=extract_tables,
            use_ocr=use_ocr,
            visual_comparison=visual_comparison,
            status=TaskStatus.PENDING,
            created_time=datetime.utcnow(),
            status_message="等待处理",
            progress=0
        )
        
        db.session.add(task)
        db.session.commit()
        
        current_app.logger.info(f"创建对比任务成功: {task.task_id}, 用户: {user_id}")
        
        # 启动后台任务
        thread = Thread(target=process_comparison_task, args=(task.task_id,))
        thread.daemon = False  # 使用非守护线程
        thread.start()
        
        return jsonify({
            'success': True,
            'message': '任务创建成功',
            'task_id': task.task_id
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建任务失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': '创建任务失败',
            'error': str(e)
        }), 500

def get_comparison_tasks():
    """获取对比任务列表"""
    try:
        user_id = get_jwt_identity()
        
        try:
            page = int(request.args.get('page', 1))
            per_page = int(request.args.get('per_page', 20))
        except ValueError:
            return bad_request('页码参数无效')
        
        try:
            tasks = ComparisonTask.query.filter_by(user_id=user_id).order_by(
                ComparisonTask.created_time.desc()
            ).paginate(page=page, per_page=per_page, error_out=False)
            
            return jsonify({
                'success': True,
                'data': {
                    'tasks': [task.to_dict() for task in tasks.items],
                    'total': tasks.total,
                    'pages': tasks.pages,
                    'current_page': page,
                    'per_page': per_page
                }
            })
            
        except sqlalchemy.exc.SQLAlchemyError as e:
            current_app.logger.error(f"数据库查询失败: {str(e)}\n{traceback.format_exc()}")
            db.session.rollback()
            return internal_server_error('数据库查询失败')
            
    except Exception as e:
        current_app.logger.error(f"获取任务列表时发生错误: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('获取任务列表失败')

def get_task_status(task_id):
    """获取任务状态"""
    try:
        user_id = get_jwt_identity()
        
        task = ComparisonTask.query.filter_by(
            task_id=task_id, user_id=user_id
        ).first()
        
        if not task:
            return not_found('任务不存在')
        
        return jsonify({
            'success': True,
            'data': task.to_dict()
        })
        
    except Exception as e:
        current_app.logger.error(f"获取任务状态失败: {e}")
        return jsonify({'success': False, 'message': '获取任务状态失败'}), 500

def get_comparison_report(task_id):
    """获取对比报告"""
    try:
        user_id = get_jwt_identity()
        
        task = ComparisonTask.query.filter_by(
            task_id=task_id, user_id=user_id
        ).first()
        
        if not task:
            return not_found('任务不存在')
        
        if task.status != TaskStatus.COMPLETED:
            return bad_request('任务尚未完成')
        
        if not task.report_path:
            return not_found('报告文件不存在')
        
        # 返回报告文件
        if not os.path.exists(task.report_path):
            return not_found('报告文件不存在')
        
        return send_from_directory(
            os.path.dirname(task.report_path),
            os.path.basename(task.report_path),
            as_attachment=False
        )
        
    except Exception as e:
        current_app.logger.error(f"获取对比报告失败: {e}")
        return jsonify({'success': False, 'message': '获取对比报告失败'}), 500

def delete_comparison_task(task_id):
    """删除对比任务"""
    try:
        user_id = get_jwt_identity()
        
        task = ComparisonTask.query.filter_by(
            task_id=task_id, user_id=user_id
        ).first()
        
        if not task:
            return not_found('任务不存在')
        
        # 检查任务是否正在处理中
        if task.status == TaskStatus.PROCESSING:
            return bad_request('任务正在处理中，无法删除')
        
        try:
            # 删除关联的报告文件（如果存在）
            if task.report_path and os.path.exists(task.report_path):
                try:
                    os.remove(task.report_path)
                    current_app.logger.info(f"已删除报告文件: {task.report_path}")
                except Exception as file_error:
                    current_app.logger.warning(f"删除报告文件失败: {file_error}")
                    # 继续删除数据库记录，不因文件删除失败而中断
            
            # 删除数据库记录
            db.session.delete(task)
            db.session.commit()
            
            current_app.logger.info(f"任务删除成功: {task_id}, 用户: {user_id}")
            
            return jsonify({
                'success': True,
                'message': '任务删除成功'
            })
            
        except sqlalchemy.exc.SQLAlchemyError as e:
            db.session.rollback()
            current_app.logger.error(f"数据库删除失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': '删除任务失败，请重试'
            }), 500
        
    except Exception as e:
        current_app.logger.error(f"删除任务失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({
            'success': False,
            'message': '删除任务失败'
        }), 500

def process_comparison_task(task_id):
    """处理文档对比任务"""
    import traceback
    from app import create_app, db
    from app.models import ComparisonTask, Document, TaskStatus
    from app.services.document_comparison import DocumentParser, EnhancedComparisonEngine, ReportGenerator
    
    # 创建新的应用实例和上下文
    app = create_app()
    
    try:
        with app.app_context():
            # 获取任务信息
            task = ComparisonTask.query.filter_by(task_id=task_id).first()
            if not task:
                app.logger.error(f"任务不存在: {task_id}")
                return
            
            try:
                # 记录开始时间
                start_time = time.time()
                
                # 更新任务状态
                task.status = TaskStatus.PROCESSING
                task.started_time = datetime.utcnow()
                task.status_message = "正在解析文档..."
                task.progress = 10
                db.session.commit()
                
                # 获取文档路径
                standard_doc = Document.query.get(task.standard_doc_id)
                modified_doc = Document.query.get(task.modified_doc_id)
                
                if not standard_doc or not modified_doc:
                    raise ValueError("找不到对比文档")
                
                standard_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 
                                           f"documents/{standard_doc.user_id}", 
                                           standard_doc.filename)
                modified_path = os.path.join(current_app.config['UPLOAD_FOLDER'], 
                                           f"documents/{modified_doc.user_id}", 
                                           modified_doc.filename)
                
                # 检查文件是否存在
                if not os.path.exists(standard_path) or not os.path.exists(modified_path):
                    raise FileNotFoundError(f"文档文件不存在: 标准文档={os.path.exists(standard_path)}, 修改文档={os.path.exists(modified_path)}")
                
                # 初始化增强的解析器和对比引擎
                parser = DocumentParser()
                from app.services.document_comparison.comparison_engine import EnhancedComparisonEngine, ComparisonOptions
                engine = EnhancedComparisonEngine()
                
                # 配置对比选项 - 按about.md技术方案
                comparison_options = ComparisonOptions(
                    ignore_whitespace=task.ignore_whitespace,
                    extract_tables=task.extract_tables,
                    visual_comparison=task.visual_comparison and (standard_doc.file_type == 'pdf' and modified_doc.file_type == 'pdf')
                )
                
                # 配置解析选项
                parse_options = {
                    'extract_tables': task.extract_tables,
                    'use_ocr': task.use_ocr,  # 根据任务配置决定是否使用OCR
                }
                
                app.logger.info(f"开始解析文档，选项: {parse_options}")
                
                # 解析标准文档
                task.progress = 30
                task.status_message = "正在解析标准文档..."
                db.session.commit()
                
                standard_data = parser.parse_document(standard_path, parse_options)
                
                # 解析修改文档
                task.progress = 40
                task.status_message = "正在解析修改文档..."
                db.session.commit()
                
                modified_data = parser.parse_document(modified_path, parse_options)
                
                task.progress = 50
                task.status_message = "正在执行智能对比分析..."
                db.session.commit()
                
                # 添加文档元数据
                standard_data['metadata'] = standard_data.get('metadata', {})
                modified_data['metadata'] = modified_data.get('metadata', {})
                standard_data['metadata']['title'] = standard_doc.original_filename
                modified_data['metadata']['title'] = modified_doc.original_filename
                
                # 执行智能对比
                app.logger.info(f"开始智能对比，类型检测中...")
                comparison_result = engine.compare_documents(
                    standard_data, 
                    modified_data,
                    comparison_options
                )
                
                app.logger.info(f"对比完成，类型: {comparison_result.comparison_type.value}, 差异数: {len(comparison_result.differences)}")
                task.progress = 80
                task.status_message = "正在生成对比报告..."
                db.session.commit()
                
                # 生成增强报告 - 按about.md建议提供详细可视化
                report_generator = ReportGenerator()
                report_path = os.path.join(
                    app.config['UPLOAD_FOLDER'],
                    'reports',
                    str(task.user_id),
                    f"{task.task_id}.html"
                )
                
                # 确保报告目录存在
                os.makedirs(os.path.dirname(report_path), exist_ok=True)
                
                # 生成增强报告元数据
                report_metadata = {
                    'standard_doc': standard_doc.original_filename,
                    'modified_doc': modified_doc.original_filename,
                    'task_config': {
                        'ignore_whitespace': task.ignore_whitespace,
                        'detailed_report': task.detailed_report,
                        'extract_tables': task.extract_tables,
                        'use_ocr': task.use_ocr,
                        'visual_comparison': task.visual_comparison
                    },
                    'comparison_type': comparison_result.comparison_type.value,
                    'processing_time': time.time() - start_time,
                    'similarity': getattr(comparison_result, 'similarity', comparison_result.summary.get('similarity', 0.0))
                }
                
                # 转换为ReportGenerator期望的格式
                report_data = {
                    'differences': comparison_result.differences,
                    'summary': {
                        'total_differences': len(comparison_result.differences),
                        'additions': sum(1 for d in comparison_result.differences if d.get('type') == 1),  # INSERTION
                        'deletions': sum(1 for d in comparison_result.differences if d.get('type') == -1),  # DELETION
                        'replacements': sum(1 for d in comparison_result.differences if d.get('type') == 2),  # REPLACEMENT
                        'similarity_percentage': getattr(comparison_result, 'similarity', comparison_result.summary.get('similarity', 0.0)) * 100
                    }
                }
                
                report_html = report_generator.generate_html_report(
                    report_data,
                    standard_doc.original_filename,
                    modified_doc.original_filename,
                    task.task_name or f"对比任务 {task.task_id[:8]}",
                    task.detailed_report
                )
                
                # 保存报告到文件
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write(report_html)
                
                # 计算相似度
                similarity = getattr(comparison_result, 'similarity', None)
                if similarity is None:
                    similarity = comparison_result.summary.get('similarity', 0.0)
                
                if isinstance(similarity, (int, float)):
                    similarity_percentage = similarity * 100 if similarity <= 1 else similarity
                else:
                    similarity_percentage = 0.0
                
                # 更新任务状态
                task.status = TaskStatus.COMPLETED
                task.completed_time = datetime.utcnow()
                task.progress = 100
                task.status_message = "智能对比完成"
                task.report_path = report_path
                task.similarity = similarity_percentage
                task.diff_count = comparison_result.statistics.get('total_differences', 0)
                task.processing_time = time.time() - start_time
                db.session.commit()
                
                app.logger.info(f"任务完成: {task_id}")
                
            except Exception as e:
                app.logger.error(f"任务处理失败: {str(e)}\n{traceback.format_exc()}")
                try:
                    task.status = TaskStatus.FAILED
                    task.status_message = f"处理失败: {str(e)}"
                    task.completed_time = datetime.utcnow()
                    db.session.commit()
                except Exception as db_error:
                    app.logger.error(f"更新任务状态失败: {db_error}")
                raise
                
    except Exception as e:
        # 如果外部错误导致无法更新数据库，至少记录日志
        print(f"任务处理失败（外部错误）: {str(e)}")
        print(traceback.format_exc())
        return

def preview_document(doc_id):
    """预览文档"""
    try:
        user_id = get_jwt_identity()
        document = Document.query.filter_by(id=doc_id, user_id=user_id, status=1).first()
        
        if not document:
            return not_found('文档不存在')
            
        # 检查文件是否存在
        if not os.path.exists(document.file_path):
            return not_found('文件不存在')
            
        # 获取文件目录和文件名
        directory = os.path.dirname(document.file_path)
        filename = os.path.basename(document.file_path)
        
        # 根据文件类型设置不同的Content-Disposition
        file_ext = document.file_type.lower()
        if file_ext == 'pdf' or file_ext in ['docx', 'doc', 'wps']:
            disposition = 'inline'  # 在线预览
        else:
            disposition = 'attachment'  # 下载
        
        # 返回文件，设置正确的响应头
        response = send_from_directory(
            directory,
            filename,
            as_attachment=False,
            mimetype=document.mime_type
        )
        
        # 设置响应头
        response.headers['Content-Disposition'] = f'{disposition}; filename="{document.original_filename}"'
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Expose-Headers'] = 'Content-Disposition'
        
        return response
        
    except Exception as e:
        current_app.logger.error(f"预览文档时发生错误: {str(e)}\n{traceback.format_exc()}")
        return internal_server_error('预览文档失败')

# 注册路由函数
def register_document_routes(bp):
    """注册文档相关路由"""
    bp.route('/documents', methods=['GET'])(jwt_required()(get_documents))
    bp.route('/documents/upload', methods=['POST'])(jwt_required()(upload_document))
    bp.route('/documents/<int:doc_id>', methods=['DELETE'])(jwt_required()(delete_document))
    bp.route('/documents/compare', methods=['POST'])(jwt_required()(create_comparison_task))
    bp.route('/documents/tasks', methods=['GET'])(jwt_required()(get_comparison_tasks))
    bp.route('/documents/tasks/<task_id>', methods=['GET'])(jwt_required()(get_task_status))
    bp.route('/documents/tasks/<task_id>/report', methods=['GET'])(jwt_required()(get_comparison_report))
    bp.route('/documents/tasks/<task_id>', methods=['DELETE'])(jwt_required()(delete_comparison_task))
    bp.route('/documents/<int:doc_id>/preview', methods=['GET'])(jwt_required()(preview_document)) 