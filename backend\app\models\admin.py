"""
管理员相关模型
"""
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from .base import BaseModel

class AdminRole(BaseModel):
    """管理员角色模型"""
    __tablename__ = 'admin_roles'
    
    name = Column(String(50), unique=True, nullable=False)
    code = Column(String(50), unique=True, nullable=False)
    description = Column(String(200))
    permissions = Column(JSON)  # 权限列表
    is_system = Column(Boolean, default=False)  # 是否为系统角色
    sort_order = Column(Integer, default=0)
    
    # 关联管理员
    admins = relationship('Admin', secondary='admin_role_users', back_populates='roles')
    
    def __init__(self, **kwargs):
        """初始化角色"""
        if 'permissions' not in kwargs:
            kwargs['permissions'] = []
        super().__init__(**kwargs)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        created_at = getattr(self, 'created_at', None)
        updated_at = getattr(self, 'updated_at', None)
        
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'description': self.description,
            'permissions': self.permissions,
            'is_system': self.is_system,
            'sort_order': self.sort_order,
            'created_at': created_at.isoformat() if created_at else None,
            'updated_at': updated_at.isoformat() if updated_at else None
        }

class Admin(BaseModel):
    """管理员模型"""
    __tablename__ = 'admins'
    
    # 基本信息
    username = Column(String(50), unique=True, nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    password_hash = Column(String(200), nullable=False)
    phone = Column(String(20))
    avatar_url = Column(String(500))
    
    # 状态信息
    is_active = Column(Boolean, default=True)
    is_super_admin = Column(Boolean, default=False)  # 超级管理员标记
    last_login_at = Column(DateTime)
    last_login_ip = Column(String(50))
    
    # 安全相关
    failed_attempts = Column(Integer, default=0)  # 登录失败次数
    locked_until = Column(DateTime)  # 账号锁定时间
    
    # 其他信息
    remark = Column(String(500))  # 备注
    settings = Column(JSON)  # 个人设置
    
    # 关联关系
    roles = relationship('AdminRole', secondary='admin_role_users', back_populates='admins')
    operation_logs = relationship('AdminOperationLog', back_populates='admin')
    
    def __init__(self, **kwargs):
        """初始化管理员"""
        if 'settings' not in kwargs:
            kwargs['settings'] = {
                'theme': 'light',
                'language': 'zh_CN',
                'notifications': {
                    'email': True,
                    'browser': True
                }
            }
        super().__init__(**kwargs)
    
    def to_dict(self, include_roles: bool = False) -> Dict[str, Any]:
        """转换为字典格式"""
        created_at = getattr(self, 'created_at', None)
        updated_at = getattr(self, 'updated_at', None)
        last_login_at = getattr(self, 'last_login_at', None)
        
        admin_dict = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'phone': self.phone,
            'avatar_url': self.avatar_url,
            'is_active': self.is_active,
            'is_super_admin': self.is_super_admin,
            'last_login_at': last_login_at.isoformat() if last_login_at else None,
            'last_login_ip': self.last_login_ip,
            'remark': self.remark,
            'settings': self.settings,
            'created_at': created_at.isoformat() if created_at else None,
            'updated_at': updated_at.isoformat() if updated_at else None
        }
        
        if include_roles:
            admin_dict['roles'] = [role.to_dict() for role in self.roles]
        
        return admin_dict
    
    def has_permission(self, permission: str) -> bool:
        """
        检查是否有指定权限
        :param permission: 权限标识
        :return: 是否有权限
        """
        is_super = getattr(self, 'is_super_admin', False)
        if is_super:
            return True
            
        for role in self.roles:
            if role.permissions and permission in role.permissions:
                return True
        return False
    
    def has_any_permission(self, permissions: List[str]) -> bool:
        """
        检查是否有任意一个指定权限
        :param permissions: 权限标识列表
        :return: 是否有权限
        """
        is_super = getattr(self, 'is_super_admin', False)
        if is_super:
            return True
            
        for permission in permissions:
            if self.has_permission(permission):
                return True
        return False
    
    def has_all_permissions(self, permissions: List[str]) -> bool:
        """
        检查是否有所有指定权限
        :param permissions: 权限标识列表
        :return: 是否有权限
        """
        is_super = getattr(self, 'is_super_admin', False)
        if is_super:
            return True
            
        for permission in permissions:
            if not self.has_permission(permission):
                return False
        return True
    
    def record_login(self, ip: str) -> None:
        """
        记录登录信息
        :param ip: 登录IP
        """
        self.last_login_at = datetime.utcnow()
        self.last_login_ip = ip
        self.failed_attempts = 0
        self.locked_until = None
        self.save()
    
    def record_login_failure(self) -> None:
        """记录登录失败"""
        failed_attempts = getattr(self, 'failed_attempts', 0)
        new_failed_attempts = failed_attempts + 1
        setattr(self, 'failed_attempts', new_failed_attempts)
        
        if new_failed_attempts >= 5:  # 5次失败后锁定账号
            setattr(self, 'locked_until', datetime.utcnow() + timedelta(minutes=30))
        self.save()
    
    def is_locked(self) -> bool:
        """检查账号是否被锁定"""
        locked_until = getattr(self, 'locked_until', None)
        if locked_until is None:
            return False
        return locked_until > datetime.utcnow()

class AdminOperationLog(BaseModel):
    """管理员操作日志模型"""
    __tablename__ = 'admin_operation_logs'
    
    # 关联管理员
    admin_id = Column(Integer, ForeignKey('admins.id', ondelete='CASCADE'), nullable=False)
    admin = relationship('Admin', back_populates='operation_logs')
    
    # 操作信息
    module = Column(String(50), nullable=False, index=True)  # 模块
    action = Column(String(50), nullable=False, index=True)  # 操作
    resource_type = Column(String(50), index=True)  # 资源类型
    resource_id = Column(String(100), index=True)  # 资源ID
    details = Column(JSON)  # 操作详情
    
    # 请求信息
    ip_address = Column(String(50))
    user_agent = Column(String(200))
    request_method = Column(String(10))
    request_url = Column(String(500))
    request_params = Column(JSON)
    
    # 其他信息
    status = Column(String(20), default='success')
    error_message = Column(Text)
    duration = Column(Integer)  # 操作耗时（毫秒）
    
    def __init__(self, **kwargs):
        """初始化日志"""
        created_at = getattr(kwargs, 'created_at', None)
        if created_at is None:
            kwargs['created_at'] = datetime.utcnow()
        super().__init__(**kwargs)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        created_at = getattr(self, 'created_at', None)
        return {
            'id': self.id,
            'admin_id': self.admin_id,
            'module': self.module,
            'action': self.action,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'details': self.details,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'request_method': self.request_method,
            'request_url': self.request_url,
            'request_params': self.request_params,
            'status': self.status,
            'error_message': self.error_message,
            'duration': self.duration,
            'created_at': created_at.isoformat() if created_at else None
        }
