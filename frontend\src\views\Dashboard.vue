<template>
  <div class="dashboard-page">
    <div class="dashboard-hero">
      <div class="hero-content">
        <img src="/logo.png" alt="Logo" class="brand-logo" />
        <div class="brand-title">
          <div class="brand-name">白泽AI文件助手</div>
          <div class="brand-welcome">欢迎，{{ user?.username || '用户' }}！</div>
          <div class="slogan">AI驱动 · 极速证照 · 智能文档 · 一站式办公</div>
        </div>
      </div>
    </div>
    <div class="dashboard-grid">
      <PhotoQuickUpload />
      <DocQuickCompare />
      <ResumeQuickCreate />
      <CardQuickCreate />
    </div>
    <div class="dashboard-sellpoints">
      <div class="sellpoints-divider"></div>
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="sellpoint">
            <el-icon class="sp-icon sp-blue"><PictureFilled /></el-icon>
            <div class="sp-title">证件照极速美化</div>
            <div class="sp-desc">一键美颜换底，智能裁剪，极速出片，满足各类证件需求。</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="sellpoint">
            <el-icon class="sp-icon sp-blue"><Document /></el-icon>
            <div class="sp-title">AI文档智能对比</div>
            <div class="sp-desc">支持Word/PDF/WPS，AI精准比对，报告清晰直观。</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="sellpoint">
            <el-icon class="sp-icon sp-gold"><User /></el-icon>
            <div class="sp-title">专业简历一键生成</div>
            <div class="sp-desc">多模板选择，AI润色内容，专业美观，提升求职竞争力。</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="sellpoint">
            <el-icon class="sp-icon sp-gold"><CreditCard /></el-icon>
            <div class="sp-title">电子名片高效管理</div>
            <div class="sp-desc">多样模板，二维码分享，电子化管理，商务沟通更高效。</div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <!-- 调试按钮 -->
    <div class="debug-section" style="margin-top: 20px; padding: 15px; background: #f5f5f5; border-radius: 8px;">
      <h4>调试信息</h4>
      <el-button size="small" @click="debugAuth">检查认证状态</el-button>
      <el-button size="small" @click="testRefresh">测试页面刷新</el-button>
      <div v-if="debugInfo" style="margin-top: 10px; font-size: 12px; color: #666;">
        <pre>{{ debugInfo }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import PhotoQuickUpload from '@/components/dashboard/PhotoQuickUpload.vue'
import DocQuickCompare from '@/components/dashboard/DocQuickCompare.vue'
import ResumeQuickCreate from '@/components/dashboard/ResumeQuickCreate.vue'
import CardQuickCreate from '@/components/dashboard/CardQuickCreate.vue'
import { PictureFilled, Document, User, CreditCard } from '@element-plus/icons-vue'
const authStore = useAuthStore()
const debugInfo = ref('')

const user = computed(() => authStore.user)

const debugAuth = () => {
  authStore.debugAuthState()
  debugInfo.value = JSON.stringify({
    token: authStore.token ? '存在' : '不存在',
    user: authStore.user ? '存在' : '不存在',
    isAuthenticated: authStore.isAuthenticated,
    isLoggedIn: authStore.isLoggedIn,
    localStorage: {
      token: localStorage.getItem('token') ? '存在' : '不存在',
      user: localStorage.getItem('user') ? '存在' : '不存在'
}
  }, null, 2)
}

const testRefresh = () => {
  window.location.reload()
}

onMounted(() => {
  debugAuth()
})
</script>

<style scoped>
:root {
  --brand-main: #4A90E2;
  --brand-gold: #F5C242;
  --brand-bg: #f8fafc;
  --brand-gray: #D6E4F0;
}
.dashboard-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 0 48px 0;
  background: var(--brand-bg);
}
.dashboard-hero {
  background: linear-gradient(90deg, #4A90E2 0%, #D6E4F0 100%);
  color: #fff;
  border-radius: 0 0 24px 24px;
  padding: 36px 32px 28px 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 24px #4A90E222;
}
.hero-content {
  display: flex;
  align-items: center;
  gap: 32px;
}
.brand-logo {
  width: 72px;
  height: 72px;
  border-radius: 18px;
  box-shadow: 0 2px 8px #4A90E244;
  background: #fff;
  object-fit: cover;
}
.brand-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.brand-name {
  font-size: 2rem;
  font-weight: 800;
  color: var(--brand-main);
  letter-spacing: 2px;
  margin-bottom: 2px;
}
.brand-welcome {
  font-size: 1.1rem;
  color: #333;
  font-weight: 500;
}
.slogan {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4A90E2;
  margin-top: 2px;
}
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
  margin: 32px 0 40px 0;
}
.dashboard-sellpoints {
  margin-top: 32px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px #4A90E211;
  padding: 32px 16px 16px 16px;
  position: relative;
}
.sellpoints-divider {
  height: 4px;
  width: 100px;
  background: linear-gradient(90deg, #4A90E2 0%, #F5C242 100%);
  border-radius: 2px;
  margin: 0 auto 24px auto;
}
.sellpoint {
  text-align: center;
  padding: 8px 0;
}
.sp-icon {
  font-size: 40px;
  margin-bottom: 8px;
}
.sp-blue {
  color: #4A90E2;
}
.sp-gold {
  color: #F5C242;
}
.sp-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #222;
}
.sp-desc {
  font-size: 0.95rem;
  color: #666;
}
/**** 卡片主色边框/阴影 ****/
:deep(.el-card.quick-card) {
  border: 1.5px solid #4A90E2;
  box-shadow: 0 2px 12px #4A90E222;
  border-radius: 18px;
  transition: box-shadow 0.2s;
}
:deep(.el-card.quick-card):hover {
  box-shadow: 0 6px 24px #4A90E244;
}
/**** 按钮金黄高亮 ****/
:deep(.el-button--primary) {
  background: linear-gradient(90deg, #F5C242 0%, #FFD700 100%);
  border: none;
  color: #333;
  font-weight: 700;
}
:deep(.el-button--primary:hover) {
  background: linear-gradient(90deg, #FFD700 0%, #F5C242 100%);
  color: #222;
}
</style> 