#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
启用所有广告位的显示状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.ad_position import AdPosition
from app.database import db

def enable_ad_positions():
    """启用所有广告位的显示状态"""
    app = create_app()
    
    with app.app_context():
        print("🔧 启用所有广告位的显示状态...")
        
        # 获取所有广告位
        positions = AdPosition.query.all()
        
        print(f"找到 {len(positions)} 个广告位:")
        
        for position in positions:
            print(f"  - {position.name} ({position.code})")
            print(f"    当前状态: 启用={position.is_enabled}, 显示={position.is_visible}")
            
            # 启用显示状态
            if not position.is_visible:
                position.is_visible = True
                print(f"    ✅ 已启用显示状态")
            else:
                print(f"    ℹ️  显示状态已经是启用")
        
        # 提交更改
        db.session.commit()
        print("\n✅ 所有广告位显示状态已更新")

if __name__ == "__main__":
    enable_ad_positions() 