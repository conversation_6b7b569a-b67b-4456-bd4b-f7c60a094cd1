<template>
  <div class="resume-preview-page">
    <!-- 顶部导航栏 -->
    <div class="preview-header">
      <div class="header-title">简历预览</div>
      <div class="header-actions">
        <!-- 模板选择器 -->
        <div class="template-selector-wrapper">
          <el-select 
            v-model="selectedTemplateId" 
            placeholder="选择模板"
            size="default"
            style="width: 200px;"
          >
            <el-option
              v-for="template in templates"
              :key="template.id"
              :label="template.name"
              :value="template.id"
            >
              <div class="template-option">
                <span class="template-name">{{ template.name }}</span>
                <span class="template-desc">{{ template.description }}</span>
              </div>
            </el-option>
          </el-select>
        </div>
        
        <router-link :to="`/resume/edit/${resumeData.id}`" v-if="resumeData">
          <el-button>
            <i class="el-icon-edit"></i> 返回编辑
          </el-button>
        </router-link>
        <el-button type="primary" @click="exportPDF" :loading="isGeneratingPdf">
          <i class="el-icon-download"></i> 
          {{ isGeneratingPdf ? '正在导出...' : '导出PDF' }}
        </el-button>
        <el-button @click="exportDOCX" :loading="isGeneratingDocx">
          <i class="el-icon-document"></i> 
          {{ isGeneratingDocx ? '正在导出...' : '导出DOCX' }}
        </el-button>
        <el-button @click="printResume">
          <i class="el-icon-printer"></i> 打印
        </el-button>
      </div>
    </div>

    <div class="preview-layout">
      <!-- 预览区域 -->
      <div class="preview-panel">
        <div class="preview-container">
          <div v-if="loading" class="loading-container">
            <div class="loading-spinner">加载中...</div>
          </div>
          <div v-else-if="error" class="error-container">
            <el-alert
              :title="error"
              type="error"
              show-icon
              :closable="false"
            />
          </div>
          <div v-else ref="resumeContentRef" class="resume-preview-content">
            <component
              :is="currentTemplateComponent"
              :resume-data="resumeData"
              :key="`${resumeData.id}-${selectedTemplateId}`"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElAlert, ElButton, ElSelect, ElOption } from 'element-plus'
import { useResumeStore } from '@/stores/resume'
import html2pdf from 'html2pdf.js'
import { getAllTemplates, getTemplateComponent } from '@/config/templateMapping'
import { createPrintWindow, getCurrentStyles } from '@/utils/printUtils'

export default {
  name: 'ResumePreview',
  
  components: {
    ElAlert,
    ElButton,
    ElSelect,
    ElOption
  },

  setup() {
    const route = useRoute()
    const resumeStore = useResumeStore()
    
    const resumeContentRef = ref(null)
    const resumeData = ref(null)
    const loading = ref(true)
    const error = ref(null)
    const isGeneratingPdf = ref(false)
    const isGeneratingDocx = ref(false)
    const selectedTemplateId = ref('basic')

    const templates = ref(getAllTemplates())

    const currentTemplateComponent = computed(() => {
      return getTemplateComponent(selectedTemplateId.value)
    })
    
    const exportPDF = async () => {
      if (!resumeContentRef.value || !resumeData.value) {
        ElMessage.error('简历内容尚未准备好，无法导出。')
        return
      }

      isGeneratingPdf.value = true

      try {
        await nextTick()

        const element = resumeContentRef.value.parentElement; // .preview-panel
        // 先移除缩放
        element.style.transform = '';
        element.style.transformOrigin = '';
        await nextTick();

        // 计算内容高度（像素）
        const contentHeight = element.scrollHeight;
        const a4HeightPx = 1122; // 297mm @ 96dpi

        let scale = 1;
        if (contentHeight > a4HeightPx) {
          scale = a4HeightPx / contentHeight;
          element.style.transform = `scale(${scale})`;
          element.style.transformOrigin = 'top left';
          await nextTick();
        }

        const resumeTitle = resumeData.value.title || 'resume'
        const fileName = `${resumeTitle.replace(/ /g, '_')}_${resumeData.value.id}.pdf`

        const opt = {
          margin: 0,
          filename: fileName,
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: {
            scale: 2,
            useCORS: true,
            scrollY: 0,
            scrollX: 0
          },
          jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'portrait'
          }
        }

        await html2pdf().from(element).set(opt).save();
      } catch (err) {
        console.error('导出PDF失败:', err)
        ElMessage.error('导出PDF失败，请重试。')
      } finally {
        // 导出后还原缩放
        const element = resumeContentRef.value.parentElement;
        element.style.transform = '';
        element.style.transformOrigin = '';
        isGeneratingPdf.value = false
      }
    }

    // 动态加载 html-docx-js 脚本
    const loadHtmlDocxScript = () => {
      return new Promise((resolve, reject) => {
        // 如果已经加载，直接返回
        if (window.htmlDocx) {
          resolve(window.htmlDocx)
          return
        }
        
        // CDN 备用列表
        const cdnUrls = [
          'https://cdn.jsdelivr.net/npm/html-docx-js/dist/html-docx.js',
          'https://unpkg.com/html-docx-js/dist/html-docx.js',
          'https://cdn.skypack.dev/html-docx-js/dist/html-docx.js'
        ]
        
        let currentIndex = 0
        
        const tryLoadScript = () => {
          if (currentIndex >= cdnUrls.length) {
            reject(new Error('所有CDN源都加载失败'))
            return
          }
          
          const script = document.createElement('script')
          script.src = cdnUrls[currentIndex]
          script.onload = () => {
            if (window.htmlDocx) {
              resolve(window.htmlDocx)
            } else {
              currentIndex++
              tryLoadScript()
            }
          }
          script.onerror = () => {
            currentIndex++
            tryLoadScript()
          }
          document.head.appendChild(script)
        }
        
        tryLoadScript()
      })
    }

    // 将元素样式转为内联样式
    const convertToInlineStyles = (element) => {
      const clonedElement = element.cloneNode(true)
      
      // 递归处理所有元素
      const processElement = (el) => {
        if (el.nodeType === 1) { // 元素节点
          const computedStyle = window.getComputedStyle(el)
          
          // Word兼容的样式属性
          const wordCompatibleStyles = [
            'color', 'background-color', 'font-family', 'font-size', 'font-weight',
            'font-style', 'text-align', 'text-decoration', 'line-height',
            'margin-top', 'margin-bottom', 'margin-left', 'margin-right',
            'padding-top', 'padding-bottom', 'padding-left', 'padding-right',
            'border', 'border-top', 'border-bottom', 'border-left', 'border-right',
            'width', 'height', 'display'
          ]
          
          let inlineStyles = ''
          wordCompatibleStyles.forEach(prop => {
            const value = computedStyle.getPropertyValue(prop)
            if (value && value !== 'initial' && value !== 'normal') {
              // 特殊处理某些属性
              if (prop === 'font-family') {
                // 确保使用Word支持的字体
                const fontFamily = value.includes('Microsoft YaHei') ? 'Microsoft YaHei, Arial, sans-serif' : value
                inlineStyles += `${prop}:${fontFamily};`
              } else if (prop === 'line-height' && value.includes('px')) {
                // 转换px为em或百分比
                const fontSize = parseFloat(computedStyle.fontSize)
                const lineHeightPx = parseFloat(value)
                if (fontSize > 0) {
                  const lineHeightEm = (lineHeightPx / fontSize).toFixed(2)
                  inlineStyles += `${prop}:${lineHeightEm}em;`
                }
              } else {
                inlineStyles += `${prop}:${value};`
              }
            }
          })
          
          if (inlineStyles) {
            el.style.cssText = inlineStyles
          }
          
          // 递归处理子元素
          Array.from(el.children).forEach(child => processElement(child))
        }
      }
      
      processElement(clonedElement)
      return clonedElement
    }

    const exportDOCX = async () => {
      if (!resumeContentRef.value || !resumeData.value) {
        ElMessage.error('简历内容尚未准备好，无法导出。')
        return
      }

      isGeneratingDocx.value = true
      
      try {
        // 动态加载 html-docx-js
        await loadHtmlDocxScript()
        
        await nextTick()
        const element = resumeContentRef.value
        
        // 转换样式为内联样式
        const styledElement = convertToInlineStyles(element)
        
        // 获取HTML内容
        const htmlContent = styledElement.innerHTML
        const fullHtml = `<!DOCTYPE html>
          <html>
          <head>
            <meta charset="UTF-8">
            <title>简历</title>
            <style>
              body { 
                font-family: 'Microsoft YaHei', Arial, sans-serif; 
                margin: 20px; 
                font-size: 14px;
                line-height: 1.4;
                color: #333;
              }
              * { 
                box-sizing: border-box; 
                max-width: none !important;
                margin: 0 auto;
              }
              .resume-content-wrapper { 
                max-width: none; 
                padding: 0; 
                width: 100%;
              }
              /* 确保表格和布局在Word中正确显示 */
              table { width: 100%; border-collapse: collapse; }
              .resume-body { display: block !important; }
              .sidebar, .main-content { display: block !important; float: left; }
              .sidebar { width: 30%; margin-right: 2%; }
              .main-content { width: 68%; }
              /* 清除浮动 */
              .resume-body::after { content: ""; display: table; clear: both; }
            </style>
          </head>
          <body>${htmlContent}</body>
          </html>`
        
        // 转为docx blob
        const docxBlob = window.htmlDocx.asBlob(fullHtml)
        
        // 文件名
        const resumeTitle = resumeData.value.title || 'resume'
        const fileName = `${resumeTitle.replace(/ /g, '_')}_${resumeData.value.id}.docx`
        
        // 触发下载
        const link = document.createElement('a')
        link.href = URL.createObjectURL(docxBlob)
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(link.href)
        
        ElMessage.success('DOCX文档导出成功！')
      } catch (error) {
        console.error('导出DOCX失败:', error)
        ElMessage.error(`导出DOCX失败：${error.message || '请检查网络连接或重试'}`)
      } finally {
        isGeneratingDocx.value = false
      }
    }

    const printResume = () => {
      if (!resumeContentRef.value || !resumeData.value) {
        ElMessage.error('简历内容尚未准备好，无法打印。')
        return
      }
      
      try {
        // 获取简历内容
        const resumeElement = resumeContentRef.value
        const resumeContent = resumeElement.innerHTML
        const resumeTitle = resumeData.value.title || '简历'
        
        // 获取当前样式
        const currentStyles = getCurrentStyles()
        
        // 创建打印窗口
        createPrintWindow(resumeContent, resumeTitle, currentStyles)
        
        ElMessage.success('打印窗口已打开，请选择打印机进行打印。')
      } catch (error) {
        console.error('打印失败:', error)
        ElMessage.error(error.message || '打印失败，请重试。')
      }
    }

    // 监听模板变化
    watch(selectedTemplateId, () => {
      // 模板切换时重新渲染
      nextTick(() => {
        if (resumeContentRef.value) {
          // 触发重新渲染
          resumeContentRef.value.style.opacity = '0.99'
          setTimeout(() => {
            resumeContentRef.value.style.opacity = '1'
          }, 10)
        }
      })
    })

    onMounted(async () => {
      try {
        loading.value = true
        error.value = null
        const resumeId = route.params.id
        if (!resumeId) {
          throw new Error('简历ID不存在。')
        }
        const data = await resumeStore.loadResume(resumeId)
        if (!data) {
          throw new Error(`无法找到ID为 ${resumeId} 的简历。`)
        }
        resumeData.value = data
      } catch (err) {
        console.error('加载简历数据失败:', err)
        error.value = err.message || '加载简历数据失败。'
        ElMessage.error(error.value)
      } finally {
        loading.value = false
      }
    })

    return {
      resumeContentRef,
      resumeData,
      loading,
      error,
      isGeneratingPdf,
      isGeneratingDocx,
      selectedTemplateId,
      templates,
      currentTemplateComponent,
      exportPDF,
      printResume,
      exportDOCX
    }
  }
}
</script>

<style scoped>
.resume-preview-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 60px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  flex-shrink: 0;
}

.header-title {
  font-size: 1.25rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.template-selector-wrapper {
  margin-right: 1rem;
}

.template-option {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.template-name {
  font-weight: 500;
  color: #333;
}

.template-desc {
  font-size: 0.8rem;
  color: #666;
}

.preview-layout {
  flex-grow: 1;
  overflow: visible;
  padding: 24px;
  display: flex;
  justify-content: center;
}

.preview-panel {
  width: 210mm;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  padding: 0;
  overflow-y: visible;
  min-height: 297mm;
}

.preview-container {
  width: 100%;
  height: 100%;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100%;
  font-size: 1rem;
  color: #666;
}

.resume-preview-content {
  background: white;
  transition: opacity 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    height: auto;
    padding: 1rem;
    gap: 1rem;
}

  .header-actions {
    flex-wrap: wrap;
    justify-content: center;
}

  .template-selector-wrapper {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }

  .preview-layout {
    padding: 12px;
  }

  .preview-panel {
    width: 100%;
    max-width: 210mm;
  }
}
</style> 