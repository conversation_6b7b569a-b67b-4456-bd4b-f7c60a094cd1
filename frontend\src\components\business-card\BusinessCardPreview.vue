<template>
  <div class="business-card-preview">
    <div 
      class="card-container"
      :style="cardContainerStyle"
      ref="cardContainer"
    >
      <component
        v-if="currentComponent"
        :is="currentComponent"
        :card-data="businessCard.card_data"
        :style-config="businessCard.style_config"
        :qr-code-url="businessCard.qr_code_image"
      />
      <div v-else class="template-placeholder">
        <p v-if="!template">请选择一个模板</p>
        <p v-else>模板组件 '{{ template.name }}' 加载失败</p>
      </div>
    </div>
    
    <div class="preview-info">
      <p>标准名片尺寸：90mm × 54mm</p>
      <p>当前模板：{{ template?.name || '未选择' }}</p>
    </div>
  </div>
</template>

<script>
import { computed, ref } from 'vue';
import templateMapping from './templateMapping.js';

export default {
  name: 'BusinessCardPreview',
  
  props: {
    businessCard: {
      type: Object,
      required: true
    },
    template: {
      type: Object,
      default: null
    }
  },
  
  setup(props) {
    const cardContainer = ref(null);

    const currentComponent = computed(() => {
      if (!props.template || !props.template.name) {
        return null;
      }
      return templateMapping[props.template.name] || null;
    });

    // 动态计算缩放以适应容器
    const cardContainerStyle = computed(() => {
        const baseWidth = 900; // 模板设计基础宽度
        const baseHeight = 540; // 模板设计基础高度
        const containerWidth = 360; // 容器显示宽度
        const containerHeight = 216; // 容器显示高度

        const scaleX = containerWidth / baseWidth;
        const scaleY = containerHeight / baseHeight;
        const scale = Math.min(scaleX, scaleY);

        return {
            width: `${baseWidth}px`,
            height: `${baseHeight}px`,
            transform: `scale(${scale})`,
            transformOrigin: 'top left',
            border: 'none',
            overflow: 'hidden',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            background: 'white'
        };
    });
    
    return {
      cardContainer,
      currentComponent,
      cardContainerStyle,
    };
  }
}
</script>

<style scoped>
.business-card-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.card-container {
  display: block;
  margin: 0 auto;
  /* 尺寸由style绑定控制，居中靠margin: 0 auto; */
}

.template-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 2px dashed #ccc;
  border-radius: 8px;
  color: #999;
  font-size: 16px;
  text-align: center;
}

.preview-info {
  text-align: center;
  font-size: 12px;
  color: #909399;
}

.preview-info p {
  margin: 2px 0;
}
</style> 