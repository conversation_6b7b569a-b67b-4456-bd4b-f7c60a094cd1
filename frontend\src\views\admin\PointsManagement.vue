<template>
  <div class="points-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">积分管理</h2>
        <p class="page-description">管理系统积分规则，查看积分变动日志，手动调整用户积分</p>
      </div>
    </div>

    <!-- 积分统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">💰</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalPoints }}</div>
                <div class="stat-label">总积分发放</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.totalUsers }}</div>
                <div class="stat-label">有积分用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📈</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.todayPoints }}</div>
                <div class="stat-label">今日发放</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">⚡</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.avgPoints }}</div>
                <div class="stat-label">平均积分</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 积分配置 -->
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>积分配置</span>
          <el-button type="primary" @click="saveConfig" :loading="savingConfig">
            保存配置
          </el-button>
        </div>
      </template>

      <el-form :model="configForm" label-width="200px" class="config-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="注册奖励积分">
              <el-input-number 
                v-model="configForm.register_reward" 
                :min="0" 
                :max="1000"
                placeholder="新用户注册奖励积分"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邀请奖励积分">
              <el-input-number 
                v-model="configForm.invite_reward" 
                :min="0" 
                :max="1000"
                placeholder="邀请好友奖励积分"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="广告任务奖励">
              <el-input-number 
                v-model="configForm.ad_reward" 
                :min="0" 
                :max="100"
                placeholder="观看广告奖励积分"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="充值兑换比例">
              <el-input-number 
                v-model="configForm.recharge_ratio" 
                :min="1" 
                :max="100"
                placeholder="1元兑换积分数量"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="证件照处理消耗">
              <el-input-number 
                v-model="configForm.photo_process_cost" 
                :min="0" 
                :max="100"
                placeholder="处理一张证件照消耗积分"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="简历生成消耗">
              <el-input-number 
                v-model="configForm.resume_generate_cost" 
                :min="0" 
                :max="100"
                placeholder="生成一份简历消耗积分"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="名片生成消耗">
              <el-input-number 
                v-model="configForm.business_card_cost" 
                :min="0" 
                :max="100"
                placeholder="生成一张名片消耗积分"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文档对比消耗">
              <el-input-number 
                v-model="configForm.document_compare_cost" 
                :min="0" 
                :max="100"
                placeholder="对比一次文档消耗积分"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 手动调整积分 -->
    <el-card class="adjust-card">
      <template #header>
        <div class="card-header">
          <span>手动调整积分</span>
        </div>
      </template>

      <el-form :model="adjustForm" label-width="120px" class="adjust-form">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="用户ID">
              <el-input 
                v-model="adjustForm.user_id" 
                placeholder="请输入用户ID"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="调整积分">
              <el-input-number 
                v-model="adjustForm.change" 
                :min="-10000" 
                :max="10000"
                placeholder="正数为增加，负数为减少"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="调整原因">
              <el-input 
                v-model="adjustForm.remark" 
                placeholder="请输入调整原因"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button type="primary" @click="adjustPoints" :loading="adjustingPoints">
            确认调整
          </el-button>
          <el-button @click="resetAdjustForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 积分日志 -->
    <el-card class="logs-card">
      <template #header>
        <div class="card-header">
          <span>积分变动日志</span>
          <div class="header-actions">
            <el-button @click="exportLogs">
              <el-icon><Download /></el-icon>
              导出日志
            </el-button>
            <el-button @click="refreshLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 日志筛选 -->
      <div class="logs-filter">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input
              v-model="logFilters.user_id"
              placeholder="用户ID"
              clearable
              @input="handleLogSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-select v-model="logFilters.type" placeholder="变动类型" clearable @change="handleLogSearch">
              <el-option label="注册奖励" value="register" />
              <el-option label="邀请奖励" value="invite" />
              <el-option label="广告任务" value="ad" />
              <el-option label="充值" value="recharge" />
              <el-option label="消费" value="consume" />
              <el-option label="管理员调整" value="admin" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-date-picker
              v-model="logFilters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleLogSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleLogSearch">搜索</el-button>
            <el-button @click="resetLogFilters">重置</el-button>
          </el-col>
        </el-row>
      </div>

      <el-table
        v-loading="logsLoading"
        :data="logs"
        style="width: 100%"
        max-height="500"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="user_id" label="用户ID" width="100" />
        <el-table-column label="变动积分" width="120">
          <template #default="{ row }">
            <span :class="row.change > 0 ? 'positive' : 'negative'">
              {{ row.change > 0 ? '+' : '' }}{{ row.change }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getLogTypeTag(row.type)">
              {{ getLogTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" />
        <el-table-column prop="balance" label="变动后余额" width="120" />
        <el-table-column prop="created_at" label="时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 日志分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="logPagination.page"
          v-model:page-size="logPagination.per_page"
          :page-sizes="[50, 100, 200, 500]"
          :total="logPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleLogSizeChange"
          @current-change="handleLogCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Refresh } from '@element-plus/icons-vue'
import api from '@/api'

// 响应式数据
const savingConfig = ref(false)
const adjustingPoints = ref(false)
const logsLoading = ref(false)

const stats = reactive({
  totalPoints: 0,
  totalUsers: 0,
  todayPoints: 0,
  avgPoints: 0
})

const configForm = reactive({
  register_reward: 10,
  invite_reward: 20,
  ad_reward: 5,
  recharge_ratio: 10,
  photo_process_cost: 1,
  resume_generate_cost: 5,
  business_card_cost: 3,
  document_compare_cost: 2
})

const adjustForm = reactive({
  user_id: '',
  change: 0,
  remark: ''
})

const logs = ref([])
const logFilters = reactive({
  user_id: '',
  type: '',
  dateRange: []
})

const logPagination = reactive({
  page: 1,
  per_page: 100,
  total: 0
})

// 方法
const loadStats = async () => {
  try {
    const response = await api.admin.getPointsStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载积分统计失败:', error)
  }
}

const loadConfig = async () => {
  try {
    const response = await api.admin.getPointsConfig()
    const configs = response.data
    configs.forEach(config => {
      if (Object.prototype.hasOwnProperty.call(configForm, config.key)) {
        configForm[config.key] = parseInt(config.value) || 0
      }
    })
  } catch (error) {
    ElMessage.error('加载积分配置失败')
  }
}

const saveConfig = async () => {
  savingConfig.value = true
  try {
    const configs = Object.entries(configForm).map(([key, value]) => ({
      key,
      value: value.toString()
    }))
    await api.admin.savePointsConfig(configs)
    ElMessage.success('配置保存成功')
  } catch (error) {
    ElMessage.error('配置保存失败')
  } finally {
    savingConfig.value = false
  }
}

const adjustPoints = async () => {
  if (!adjustForm.user_id) {
    ElMessage.warning('请输入用户ID')
    return
  }
  if (!adjustForm.change) {
    ElMessage.warning('请输入调整积分数量')
    return
  }
  if (!adjustForm.remark) {
    ElMessage.warning('请输入调整原因')
    return
  }

  adjustingPoints.value = true
  try {
    await api.admin.adjustUserPoints(adjustForm.user_id, {
      change: adjustForm.change,
      remark: adjustForm.remark
    })
    ElMessage.success('积分调整成功')
    resetAdjustForm()
    loadStats()
    loadLogs()
  } catch (error) {
    ElMessage.error('积分调整失败')
  } finally {
    adjustingPoints.value = false
  }
}

const resetAdjustForm = () => {
  Object.assign(adjustForm, {
    user_id: '',
    change: 0,
    remark: ''
  })
}

const loadLogs = async () => {
  logsLoading.value = true
  try {
    const params = {
      page: logPagination.page,
      per_page: logPagination.per_page,
      ...logFilters
    }
    
    if (logFilters.dateRange && logFilters.dateRange.length === 2) {
      params.start_date = logFilters.dateRange[0]
      params.end_date = logFilters.dateRange[1]
    }
    
    const response = await api.admin.getPointsLogs(params)
    logs.value = response.data.logs
    logPagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载积分日志失败')
  } finally {
    logsLoading.value = false
  }
}

const handleLogSearch = () => {
  logPagination.page = 1
  loadLogs()
}

const resetLogFilters = () => {
  Object.assign(logFilters, {
    user_id: '',
    type: '',
    dateRange: []
  })
  handleLogSearch()
}

const handleLogSizeChange = (size) => {
  logPagination.per_page = size
  loadLogs()
}

const handleLogCurrentChange = (page) => {
  logPagination.page = page
  loadLogs()
}

const refreshLogs = () => {
  loadLogs()
}

const exportLogs = async () => {
  try {
    const response = await api.admin.exportPointsLogs(logFilters)
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `积分日志_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const getLogTypeLabel = (type) => {
  const typeMap = {
    'register': '注册奖励',
    'invite': '邀请奖励',
    'ad': '广告任务',
    'recharge': '充值',
    'consume': '消费',
    'admin': '管理员调整'
  }
  return typeMap[type] || type
}

const getLogTypeTag = (type) => {
  const tagMap = {
    'register': 'success',
    'invite': 'warning',
    'ad': 'info',
    'recharge': 'primary',
    'consume': 'danger',
    'admin': 'warning'
  }
  return tagMap[type] || 'info'
}

onMounted(() => {
  loadStats()
  loadConfig()
  loadLogs()
})
</script>

<style scoped>
.points-management {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  margin-bottom: 0;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
}

.config-card,
.adjust-card,
.logs-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.config-form {
  margin-top: 16px;
}

.adjust-form {
  margin-top: 16px;
}

.logs-filter {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.positive {
  color: #27ae60;
  font-weight: 500;
}

.negative {
  color: #e74c3c;
  font-weight: 500;
}
</style> 