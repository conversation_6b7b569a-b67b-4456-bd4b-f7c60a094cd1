import json
from models.points_config import PointsConfig
from extensions import db

config = PointsConfig.query.first()
if config:
    packages = json.loads(config.payment_packages)
    print('数据库套餐数:', len(packages))
    for i, pkg in enumerate(packages):
        name = pkg.get('name', '未命名')
        points = pkg.get('points', 0)
        price = pkg.get('price', 0)
        enabled = pkg.get('enabled', True)
        print(f'{i+1}. 套餐: {name}, 积分: {points}, 价格: ¥{price}, 启用: {enabled}')
else:
    print('未找到PointsConfig配置')