<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="创意设计风格名片">
    <title>{{ card.name|default('名片', true) }}</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ style.font_family|default('Arial, sans-serif', true) }};
            background-color: {{ style.background_color|default('#667eea', true) }};
            line-height: 1.4;
            color: {{ style.text_color|default('#ffffff', true) }};
        }
        
        /* 名片容器 */
        .creative-design .business-card {
            width: 90mm;
            height: 54mm;
            padding: 6mm;
            background: {{ style.background_gradient|default('linear-gradient(135deg, #667eea 0%, #764ba2 100%)', true) }};
            color: {{ style.text_color|default('#ffffff', true) }};
            font-family: {{ style.font_family|default('Arial, sans-serif', true) }};
            position: relative;
            overflow: hidden;
        }
        
        /* 背景图案 */
        .creative-design .background-pattern {
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='20' cy='20' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            opacity: 0.3;
            animation: float 20s ease-in-out infinite;
        }
        
        /* 内容区域 */
        .creative-design .content {
            position: relative;
            z-index: 2;
        }
        
        /* 姓名块 */
        .creative-design .name-block {
            position: relative;
            margin-bottom: 2mm;
        }
        
        /* 姓名样式 */
        .creative-design .name {
            font-size: {{ style.name_size|default(style.font_size_name, true)|default('20px', true) }};
            font-weight: {{ style.name_weight|default(style.name_font_weight, true)|default('bold', true) }};
            color: {{ style.name_color|default(style.primary_color, true)|default('#ffffff', true) }};
            margin: 0;
            position: relative;
            z-index: 2;
            line-height: 1.2;
        }
        
        /* 姓名背景 */
        .creative-design .name-bg {
            position: absolute;
            top: 0;
            left: 0;
            font-size: {{ style.name_bg_size|default('16px', true) }};
            font-weight: {{ style.name_bg_weight|default('normal', true) }};
            color: rgba(255,255,255,{{ style.name_bg_opacity|default('0.3', true) }});
            z-index: 1;
        }
        
        /* 职位标题 */
        .creative-design .title {
            font-size: {{ style.title_size|default('12px', true) }};
            margin: 0;
            color: rgba(255,255,255,0.9);
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: normal;
        }
        
        /* 公司名称 */
        .creative-design .company {
            font-size: {{ style.company_size|default(style.font_size_company, true)|default('14px', true) }};
            color: {{ style.company_color|default(style.secondary_color, true)|default('rgba(255,255,255,0.8)', true) }};
            margin: 0 0 4mm 0;
            font-weight: {{ style.company_weight|default(style.company_font_weight, true)|default('500', true) }};
        }
        
        /* 联系信息网格 */
        .creative-design .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2mm;
        }
        
        /* 联系项目 */
        .creative-design .contact-item {
            display: flex;
            flex-direction: column;
            font-size: {{ style.contact_size|default(style.font_size_contact, true)|default('11px', true) }};
            color: {{ style.contact_color|default(style.light_color, true)|default('rgba(255,255,255,0.7)', true) }};
            font-weight: {{ style.contact_weight|default(style.contact_font_weight, true)|default('normal', true) }};
        }
        
        /* 标签样式 */
        .creative-design .label {
            font-size: {{ style.label_size|default('10px', true) }};
            color: {{ style.label_color|default('rgba(255,255,255,0.6)', true) }};
            margin-bottom: 1mm;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* 值样式 */
        .creative-design .value {
            font-size: {{ style.value_size|default('11px', true) }};
            color: {{ style.value_color|default('rgba(255,255,255,0.9)', true) }};
            word-break: break-all;
        }
        
        /* 二维码区域 */
        .creative-design .qr-section {
            position: absolute;
            right: 6mm;
            bottom: 6mm;
            width: 12mm;
            height: 12mm;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .creative-design .qr-section img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
        }
        
        /* 动画效果 */
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* 响应式设计 */
        @media print {
            body {
                background: white;
            }
            .business-card {
                border: none;
                box-shadow: none;
            }
        }
        
        /* 可访问性增强 */
        .creative-design .name:focus,
        .creative-design .title:focus,
        .creative-design .company:focus {
            outline: 2px solid rgba(255,255,255,0.8);
            outline-offset: 1px;
        }
    </style>
</head>
<body>
    <div class="business-card creative-design" role="article" aria-label="创意设计名片">
        <div class="background-pattern" aria-hidden="true"></div>
        <div class="content">
            <header class="header">
                <div class="name-block">
                    <h1 class="name" id="card-name">{{ card.name|default('姓名', true) }}</h1>
                    <span class="name-bg" aria-hidden="true">{{ card.name|default('姓名', true) }}</span>
                </div>
                <p class="title" id="card-title">{{ card.title|default('职位', true) }}</p>
            </header>
            
            <section class="company" aria-labelledby="card-company">
                <p id="card-company">{{ card.company|default('公司名称', true) }}</p>
            </section>
            
            <section class="contact" aria-label="联系信息">
                <div class="contact-grid">
                    {% if card.phone %}
                    <div class="contact-item" role="group" aria-labelledby="phone-label">
                        <span class="label">Phone</span>
                        <span class="value" id="phone-label">{{ card.phone }}</span>
                    </div>
                    {% endif %}
                    {% if card.email %}
                    <div class="contact-item" role="group" aria-labelledby="email-label">
                        <span class="label">Email</span>
                        <span class="value" id="email-label">{{ card.email }}</span>
                    </div>
                    {% endif %}
                </div>
            </section>
        </div>
        
        {% if qr_code_url %}
        <div class="qr-section" role="img" aria-label="名片二维码">
            <img src="{{ qr_code_url }}" alt="二维码" loading="lazy">
        </div>
        {% endif %}
    </div>
</body>
</html> 