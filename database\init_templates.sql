-- 简历模板数据初始化脚本
-- 清空现有模板数据
DELETE FROM resume_templates;

-- 1. 基础简历模板
INSERT INTO resume_templates (id, name, description, category, industry, preview_image, template_html, template_css, is_active, sort_order) VALUES
(1, '基础简历模板', '清晰简洁的基础简历模板，适合大多数求职场景', 'simple', 'other', '/templates/basic/preview.png', 
'<div class="resume-basic"><!-- 基础简历模板HTML --></div>', 
'/* 基础简历模板CSS */', 
true, 1);

-- 2. 现代专业模板
INSERT INTO resume_templates (id, name, description, category, industry, preview_image, template_html, template_css, is_active, sort_order) VALUES
(2, '现代专业模板', '富有现代感的专业格式模板，突出重点信息', 'modern', 'it', '/templates/modern_two_column/preview.png', 
'<div class="resume-modern-two-column"><!-- 现代专业模板HTML --></div>', 
'/* 现代专业模板CSS */', 
true, 2);

-- 重置自增ID (SQLite不需要此操作) 