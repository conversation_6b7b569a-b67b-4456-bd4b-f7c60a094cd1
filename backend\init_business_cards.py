#!/usr/bin/env python
"""
名片功能初始化脚本
用于初始化名片相关的数据库表和默认数据
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from app import create_app, db
from app.models.business_card import BusinessCard, BusinessCardTemplate

def init_business_card_templates():
    """初始化名片模板数据"""
    
    # 检查是否已有模板数据
    existing_count = BusinessCardTemplate.query.count()
    if existing_count > 0:
        print(f"已存在 {existing_count} 个名片模板，跳过初始化")
        return
    
    templates_data = [
        {
            'name': '商务经典',
            'description': '简洁大方的商务名片模板，适合各行各业',
            'category': 'business',
            'industry': 'other',
            'layout_type': 'standard',
            'width_mm': 90.00,
            'height_mm': 54.00,
            'template_html': '''<div class="business-card business-classic">
  <div class="header">
    <h1 class="name">{{name}}</h1>
    <p class="title">{{title}}</p>
  </div>
  <div class="company">
    <p class="company-name">{{company}}</p>
  </div>
  <div class="contact">
    <div class="contact-item" v-if="phone">
      <i class="icon-phone"></i>
      <span>{{phone}}</span>
    </div>
    <div class="contact-item" v-if="email">
      <i class="icon-email"></i>
      <span>{{email}}</span>
    </div>
    <div class="contact-item" v-if="address">
      <i class="icon-location"></i>
      <span>{{address}}</span>
    </div>
  </div>
  <div class="qr-code" v-if="qr_image">
    <img :src="qr_image" alt="二维码">
  </div>
</div>''',
            'template_css': '''.business-card { width: 90mm; height: 54mm; padding: 8mm; background: white; border: 1px solid #ddd; box-sizing: border-box; font-family: "Microsoft YaHei", Arial, sans-serif; position: relative; }
.business-classic .header { margin-bottom: 4mm; }
.business-classic .name { font-size: 18px; font-weight: bold; color: #2c3e50; margin: 0 0 2mm 0; }
.business-classic .title { font-size: 14px; color: #34495e; margin: 0; }
.business-classic .company { margin-bottom: 4mm; }
.business-classic .company-name { font-size: 14px; color: #3498db; font-weight: 500; margin: 0; }
.business-classic .contact { font-size: 11px; line-height: 1.4; }
.business-classic .contact-item { margin-bottom: 1mm; color: #7f8c8d; }
.business-classic .qr-code { position: absolute; right: 8mm; bottom: 8mm; width: 12mm; height: 12mm; }
.business-classic .qr-code img { width: 100%; height: 100%; }''',
            'default_colors': {
                "primary": "#3498db",
                "secondary": "#2c3e50", 
                "text": "#34495e",
                "light": "#7f8c8d"
            },
            'sort_order': 1
        },
        {
            'name': '现代简约',
            'description': '极简风格，注重留白和层次感',
            'category': 'minimal',
            'industry': 'design',
            'layout_type': 'standard',
            'width_mm': 90.00,
            'height_mm': 54.00,
            'template_html': '''<div class="business-card modern-minimal">
  <div class="content">
    <div class="name-section">
      <h1 class="name">{{name}}</h1>
      <div class="divider"></div>
      <p class="title">{{title}}</p>
    </div>
    <div class="company-section">
      <p class="company">{{company}}</p>
    </div>
    <div class="contact-section">
      <p class="contact-line">{{phone}}</p>
      <p class="contact-line">{{email}}</p>
    </div>
  </div>
  <div class="accent-bar"></div>
</div>''',
            'template_css': '''.modern-minimal { background: #fafafa; border: none; padding: 6mm 8mm; position: relative; }
.modern-minimal .content { height: 100%; display: flex; flex-direction: column; justify-content: space-between; }
.modern-minimal .name { font-size: 20px; font-weight: 300; color: #2c3e50; margin: 0; letter-spacing: 1px; }
.modern-minimal .divider { width: 30mm; height: 1px; background: #e74c3c; margin: 3mm 0; }
.modern-minimal .title { font-size: 12px; color: #7f8c8d; margin: 0; text-transform: uppercase; letter-spacing: 0.5px; }
.modern-minimal .company { font-size: 14px; color: #34495e; margin: 0; font-weight: 500; }
.modern-minimal .contact-line { font-size: 11px; color: #95a5a6; margin: 0 0 1mm 0; }
.modern-minimal .accent-bar { position: absolute; right: 0; top: 0; width: 3mm; height: 100%; background: linear-gradient(180deg, #e74c3c 0%, #c0392b 100%); }''',
            'default_colors': {
                "primary": "#e74c3c",
                "secondary": "#2c3e50",
                "text": "#34495e", 
                "light": "#95a5a6"
            },
            'sort_order': 2
        },
        {
            'name': '创意设计',
            'description': '富有创意的设计，适合设计师和创意工作者',
            'category': 'creative',
            'industry': 'design', 
            'layout_type': 'standard',
            'width_mm': 90.00,
            'height_mm': 54.00,
            'template_html': '''<div class="business-card creative-design">
  <div class="background-pattern"></div>
  <div class="content">
    <div class="header">
      <div class="name-block">
        <h1 class="name">{{name}}</h1>
        <span class="name-bg">{{name}}</span>
      </div>
      <p class="title">{{title}}</p>
    </div>
    <div class="company">
      <p>{{company}}</p>
    </div>
    <div class="contact">
      <div class="contact-grid">
        <div class="contact-item">
          <span class="label">Phone</span>
          <span class="value">{{phone}}</span>
        </div>
        <div class="contact-item">
          <span class="label">Email</span>
          <span class="value">{{email}}</span>
        </div>
      </div>
    </div>
  </div>
  <div class="qr-section" v-if="qr_image">
    <img :src="qr_image" alt="QR Code">
  </div>
</div>''',
            'template_css': '''.creative-design { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 6mm; position: relative; overflow: hidden; }
.creative-design .background-pattern { position: absolute; top: -50%; right: -50%; width: 200%; height: 200%; background: url("data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Ccircle cx="20" cy="20" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E"); opacity: 0.3; animation: float 20s ease-in-out infinite; }
.creative-design .name-block { position: relative; margin-bottom: 2mm; }
.creative-design .name { font-size: 18px; font-weight: bold; margin: 0; position: relative; z-index: 2; }
.creative-design .name-bg { position: absolute; top: 0; left: 0; font-size: 24px; font-weight: 900; color: rgba(255,255,255,0.1); z-index: 1; }
.creative-design .title { font-size: 12px; margin: 0; color: rgba(255,255,255,0.9); text-transform: uppercase; letter-spacing: 1px; }
.creative-design .company p { font-size: 14px; margin: 4mm 0; color: rgba(255,255,255,0.9); }
.creative-design .contact-grid { display: grid; grid-template-columns: 1fr; gap: 2mm; }
.creative-design .contact-item { display: flex; flex-direction: column; }
.creative-design .label { font-size: 9px; color: rgba(255,255,255,0.7); text-transform: uppercase; margin-bottom: 0.5mm; }
.creative-design .value { font-size: 11px; color: white; }
.creative-design .qr-section { position: absolute; bottom: 6mm; right: 6mm; width: 12mm; height: 12mm; background: white; padding: 1mm; border-radius: 2mm; }
.creative-design .qr-section img { width: 100%; height: 100%; }''',
            'default_colors': {
                "primary": "#667eea",
                "secondary": "#764ba2",
                "text": "#ffffff",
                "light": "rgba(255,255,255,0.7)"
            },
            'sort_order': 3
        },
        {
            'name': '优雅商务',
            'description': '高端商务风格，适合高级管理人员',
            'category': 'business',
            'industry': 'finance',
            'layout_type': 'standard',
            'width_mm': 90.00,
            'height_mm': 54.00,
            'template_html': '''<div class="business-card elegant-business">
  <div class="gold-line"></div>
  <div class="content">
    <div class="header">
      <h1 class="name">{{name}}</h1>
      <p class="title">{{title}}</p>
    </div>
    <div class="company">
      <p class="company-name">{{company}}</p>
    </div>
    <div class="contact">
      <div class="contact-row">
        <span class="icon">📞</span>
        <span>{{phone}}</span>
      </div>
      <div class="contact-row">
        <span class="icon">✉</span>
        <span>{{email}}</span>
      </div>
      <div class="contact-row">
        <span class="icon">📍</span>
        <span>{{address}}</span>
      </div>
    </div>
  </div>
  <div class="corner-decoration"></div>
</div>''',
            'template_css': '''.elegant-business { background: #1a1a1a; color: #ffffff; padding: 8mm; position: relative; border: 1px solid #444; }
.elegant-business .gold-line { position: absolute; top: 0; left: 8mm; right: 8mm; height: 2px; background: linear-gradient(90deg, transparent, #d4af37, transparent); }
.elegant-business .name { font-size: 18px; font-weight: 700; color: #d4af37; margin: 0 0 2mm 0; letter-spacing: 0.5px; }
.elegant-business .title { font-size: 13px; color: #cccccc; margin: 0 0 4mm 0; text-transform: uppercase; letter-spacing: 1px; }
.elegant-business .company-name { font-size: 14px; color: #ffffff; margin: 0 0 4mm 0; font-weight: 500; }
.elegant-business .contact-row { display: flex; align-items: center; margin-bottom: 2mm; font-size: 11px; color: #cccccc; }
.elegant-business .icon { margin-right: 3mm; font-size: 10px; color: #d4af37; width: 4mm; }
.elegant-business .corner-decoration { position: absolute; bottom: 6mm; right: 6mm; width: 8mm; height: 8mm; border: 1px solid #d4af37; border-radius: 50%; }
.elegant-business .corner-decoration::after { content: ""; position: absolute; top: 2mm; left: 2mm; width: 4mm; height: 4mm; background: radial-gradient(circle, #d4af37 30%, transparent 30%); border-radius: 50%; }''',
            'default_colors': {
                "primary": "#d4af37",
                "secondary": "#1a1a1a",
                "text": "#ffffff",
                "light": "#cccccc"
            },
            'sort_order': 4
        }
    ]
    
    print("开始初始化名片模板...")
    
    for template_data in templates_data:
        template = BusinessCardTemplate(**template_data)
        db.session.add(template)
    
    try:
        db.session.commit()
        print(f"成功初始化 {len(templates_data)} 个名片模板")
    except Exception as e:
        db.session.rollback()
        print(f"初始化名片模板失败: {str(e)}")
        raise

def main():
    """主函数"""
    app = create_app()
    
    with app.app_context():
        print("开始初始化名片功能...")
        
        # 创建数据库表
        print("创建数据库表...")
        db.create_all()
        
        # 初始化模板数据
        init_business_card_templates()
        
        print("名片功能初始化完成！")

if __name__ == '__main__':
    main() 