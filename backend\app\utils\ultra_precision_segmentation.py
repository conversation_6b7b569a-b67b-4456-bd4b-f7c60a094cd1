#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超精准人体轮廓识别和背景替换系统
使用多层次算法实现像素级精度的人体分割
"""

import cv2
import numpy as np
from PIL import Image
import time
import logging
from typing import Tuple, Optional, List
import scipy.ndimage as ndimage
from scipy.spatial.distance import cdist
import gc

class UltraPrecisionSegmentation:
    """超精准人体分割系统"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.face_cascade = None
        self.profile_cascade = None
        self.body_cascade = None
        self._cascades_loaded = False
        self._last_use_time = 0
        self._cleanup_interval = 300  # 5分钟清理一次
    
    def _lazy_load_cascades(self):
        """懒加载级联分类器"""
        if self._cascades_loaded:
            return
        
        current_time = time.time()
        
        # 如果距离上次使用超过清理间隔，先清理内存
        if current_time - self._last_use_time > self._cleanup_interval:
            self._cleanup_memory()
        
        try:
            if self.face_cascade is None:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            
            if self.profile_cascade is None:
                self.profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
            
            if self.body_cascade is None:
                self.body_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_upperbody.xml')
            
            self._cascades_loaded = True
            self._last_use_time = current_time
            self.logger.info("超精准分割级联分类器懒加载成功")
            
        except Exception as e:
            self.logger.error(f"级联分类器懒加载失败: {e}")
            self._cleanup_memory()
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            # 清理级联分类器
            if self.face_cascade is not None:
                del self.face_cascade
                self.face_cascade = None
            
            if self.profile_cascade is not None:
                del self.profile_cascade
                self.profile_cascade = None
            
            if self.body_cascade is not None:
                del self.body_cascade
                self.body_cascade = None
            
            self._cascades_loaded = False
            
            # 强制垃圾回收
            gc.collect()
            
            self.logger.info("超精准分割内存清理完成")
            
        except Exception as e:
            self.logger.warning(f"超精准分割内存清理失败: {e}")
    
    def ultra_precise_background_replace(self, img_cv: np.ndarray, bg_color: Tuple[int, int, int]) -> Image.Image:
        """
        超精准背景替换主函数
        
        Args:
            img_cv: OpenCV格式输入图像
            bg_color: 背景颜色 (R, G, B)
            
        Returns:
            PIL Image对象
        """
        start_time = time.time()
        height, width = img_cv.shape[:2]
        
        try:
            # 懒加载级联分类器
            self._lazy_load_cascades()
            
            self.logger.info(f"开始超精准背景替换，图像尺寸: {width}x{height}")
            
            # 第一步：多模态人体检测
            person_mask = self._multi_modal_person_detection(img_cv)
            
            # 第二步：精细化轮廓提取
            refined_mask = self._refine_person_contour(img_cv, person_mask)
            
            # 第三步：智能边缘羽化
            feathered_mask = self._intelligent_edge_feathering(img_cv, refined_mask)
            
            # 第四步：高质量背景合成
            result = self._high_quality_background_composition(img_cv, feathered_mask, bg_color)
            
            processing_time = time.time() - start_time
            self.logger.info(f"超精准背景替换完成，耗时: {processing_time:.2f}秒")
            
            return Image.fromarray(cv2.cvtColor(result, cv2.COLOR_BGR2RGB))
            
        except Exception as e:
            self.logger.error(f"超精准背景替换失败: {e}")
            # 处理失败时清理内存
            self._cleanup_memory()
            raise
        finally:
            # 清理临时变量
            if 'person_mask' in locals():
                del person_mask
            if 'refined_mask' in locals():
                del refined_mask
            if 'feathered_mask' in locals():
                del feathered_mask
            if 'result' in locals():
                del result
            gc.collect()
    
    def _multi_modal_person_detection(self, img_cv: np.ndarray) -> np.ndarray:
        """多模态人体检测"""
        height, width = img_cv.shape[:2]
        
        # 方法1：基于人脸的身体估算
        face_based_mask = self._face_guided_body_estimation(img_cv)
        
        # 方法2：基于肤色的人体检测
        skin_based_mask = self._advanced_skin_detection(img_cv)
        
        # 方法3：基于边缘和纹理的前景检测
        edge_based_mask = self._edge_texture_foreground_detection(img_cv)
        
        # 方法4：基于颜色聚类的前景检测
        cluster_based_mask = self._color_clustering_foreground(img_cv)
        
        # 融合所有检测结果
        combined_mask = self._fuse_detection_results([
            face_based_mask,
            skin_based_mask, 
            edge_based_mask,
            cluster_based_mask
        ])
        
        return combined_mask
    
    def _face_guided_body_estimation(self, img_cv: np.ndarray) -> np.ndarray:
        """基于人脸的精准身体估算"""
        height, width = img_cv.shape[:2]
        mask = np.zeros((height, width), dtype=np.float32)
        
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # 多角度人脸检测
        faces = self.face_cascade.detectMultiScale(gray, 1.05, 3, minSize=(30, 30))
        if len(faces) == 0:
            faces = self.profile_cascade.detectMultiScale(gray, 1.05, 3, minSize=(30, 30))
        
        if len(faces) > 0:
            # 选择最大人脸
            face = max(faces, key=lambda f: f[2] * f[3])
            fx, fy, fw, fh = face
            
            # 人脸区域确定为前景
            face_mask = np.zeros((height, width), dtype=np.float32)
            cv2.rectangle(face_mask, (fx, fy), (fx + fw, fy + fh), 1.0, -1)
            
            # 估算身体各部位
            body_parts = self._estimate_body_parts(face, height, width)
            
            # 头发区域（向上扩展）
            hair_region = body_parts['hair']
            cv2.rectangle(mask, (hair_region[0], hair_region[1]), 
                         (hair_region[2], hair_region[3]), 0.9, -1)
            
            # 颈部区域
            neck_region = body_parts['neck']
            cv2.rectangle(mask, (neck_region[0], neck_region[1]), 
                         (neck_region[2], neck_region[3]), 1.0, -1)
            
            # 肩膀和上身区域
            torso_region = body_parts['torso']
            cv2.rectangle(mask, (torso_region[0], torso_region[1]), 
                         (torso_region[2], torso_region[3]), 0.8, -1)
            
            # 手臂区域（估算）
            arms_regions = body_parts['arms']
            for arm in arms_regions:
                cv2.rectangle(mask, (arm[0], arm[1]), (arm[2], arm[3]), 0.7, -1)
            
            # 人脸区域最高权重
            mask[fy:fy+fh, fx:fx+fw] = 1.0
            
            # 平滑过渡
            mask = cv2.GaussianBlur(mask, (15, 15), 5)
        
        return mask
    
    def _estimate_body_parts(self, face: Tuple[int, int, int, int], height: int, width: int) -> dict:
        """精确估算身体各部位位置"""
        fx, fy, fw, fh = face
        face_center_x = fx + fw // 2
        face_center_y = fy + fh // 2
        
        # 头发区域（向上扩展）
        hair_expand_up = int(fh * 0.6)
        hair_expand_side = int(fw * 0.3)
        hair_region = (
            max(0, fx - hair_expand_side),
            max(0, fy - hair_expand_up),
            min(width, fx + fw + hair_expand_side),
            fy + int(fh * 0.2)
        )
        
        # 颈部区域
        neck_width = int(fw * 0.6)
        neck_height = int(fh * 0.4)
        neck_region = (
            max(0, face_center_x - neck_width // 2),
            fy + fh,
            min(width, face_center_x + neck_width // 2),
            min(height, fy + fh + neck_height)
        )
        
        # 上身躯干区域
        torso_width = int(fw * 2.0)
        torso_height = int(fh * 3.5)
        torso_region = (
            max(0, face_center_x - torso_width // 2),
            fy + fh + neck_height,
            min(width, face_center_x + torso_width // 2),
            min(height, fy + fh + neck_height + torso_height)
        )
        
        # 手臂区域估算
        arm_width = int(fw * 0.4)
        arm_height = int(fh * 2.5)
        arm_start_y = fy + fh + int(neck_height * 0.5)
        
        # 左臂
        left_arm = (
            max(0, torso_region[0] - arm_width),
            arm_start_y,
            torso_region[0],
            min(height, arm_start_y + arm_height)
        )
        
        # 右臂
        right_arm = (
            torso_region[2],
            arm_start_y,
            min(width, torso_region[2] + arm_width),
            min(height, arm_start_y + arm_height)
        )
        
        return {
            'hair': hair_region,
            'neck': neck_region,
            'torso': torso_region,
            'arms': [left_arm, right_arm]
        }
    
    def _advanced_skin_detection(self, img_cv: np.ndarray) -> np.ndarray:
        """高级肤色检测"""
        height, width = img_cv.shape[:2]
        
        # 转换到多个颜色空间
        hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
        ycrcb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2YCrCb)
        lab = cv2.cvtColor(img_cv, cv2.COLOR_BGR2LAB)
        
        # HSV肤色检测
        hsv_mask1 = cv2.inRange(hsv, (0, 20, 70), (20, 255, 255))
        hsv_mask2 = cv2.inRange(hsv, (160, 20, 70), (180, 255, 255))
        hsv_mask = cv2.bitwise_or(hsv_mask1, hsv_mask2)
        
        # YCrCb肤色检测
        ycrcb_mask = cv2.inRange(ycrcb, (0, 133, 77), (255, 173, 127))
        
        # LAB肤色检测
        lab_mask = cv2.inRange(lab, (20, 130, 130), (255, 150, 150))
        
        # 融合多个肤色蒙版
        skin_mask = cv2.bitwise_and(hsv_mask, ycrcb_mask)
        skin_mask = cv2.bitwise_or(skin_mask, lab_mask)
        
        # 形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_OPEN, kernel)
        skin_mask = cv2.morphologyEx(skin_mask, cv2.MORPH_CLOSE, kernel)
        
        # 高斯平滑
        skin_mask = cv2.GaussianBlur(skin_mask, (7, 7), 2)
        
        return skin_mask.astype(np.float32) / 255.0
    
    def _edge_texture_foreground_detection(self, img_cv: np.ndarray) -> np.ndarray:
        """基于边缘和纹理的前景检测"""
        height, width = img_cv.shape[:2]
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        # 多尺度边缘检测
        edges1 = cv2.Canny(gray, 30, 100)
        edges2 = cv2.Canny(gray, 50, 150)
        edges3 = cv2.Canny(gray, 80, 200)
        
        # 融合边缘
        combined_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))
        
        # 寻找主要轮廓
        contours, _ = cv2.findContours(combined_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return np.zeros((height, width), dtype=np.float32)
        
        # 选择最大的几个轮廓
        contours = sorted(contours, key=cv2.contourArea, reverse=True)[:3]
        
        # 创建前景蒙版
        mask = np.zeros((height, width), dtype=np.uint8)
        for contour in contours:
            if cv2.contourArea(contour) > height * width * 0.01:  # 过滤小轮廓
                cv2.fillPoly(mask, [contour], 255)
        
        # 形态学处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        # 距离变换和平滑
        dist_transform = cv2.distanceTransform(mask, cv2.DIST_L2, 5)
        dist_transform = cv2.normalize(dist_transform, None, 0, 1, cv2.NORM_MINMAX, dtype=cv2.CV_32F)
        
        return cv2.GaussianBlur(dist_transform, (9, 9), 3)
    
    def _color_clustering_foreground(self, img_cv: np.ndarray) -> np.ndarray:
        """基于颜色聚类的前景检测"""
        height, width = img_cv.shape[:2]
        
        # 重塑图像为像素向量
        pixels = img_cv.reshape(-1, 3).astype(np.float32)
        
        # K-means聚类
        criteria = (cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 20, 1.0)
        k = 4  # 聚类数量
        _, labels, centers = cv2.kmeans(pixels, k, None, criteria, 10, cv2.KMEANS_RANDOM_CENTERS)
        
        # 重塑标签
        labels = labels.reshape(height, width)
        
        # 假设中心区域的聚类是前景
        center_region = labels[height//4:3*height//4, width//4:3*width//4]
        center_labels = np.bincount(center_region.flatten())
        foreground_label = np.argmax(center_labels)
        
        # 创建前景蒙版
        mask = (labels == foreground_label).astype(np.float32)
        
        # 平滑处理
        mask = cv2.GaussianBlur(mask, (11, 11), 4)
        
        return mask
    
    def _fuse_detection_results(self, masks: List[np.ndarray]) -> np.ndarray:
        """融合多个检测结果"""
        if not masks:
            return np.zeros_like(masks[0])
        
        # 权重设置
        weights = [0.4, 0.25, 0.2, 0.15]  # 人脸引导 > 肤色 > 边缘 > 聚类
        
        # 加权融合
        fused_mask = np.zeros_like(masks[0], dtype=np.float32)
        total_weight = 0
        
        for i, mask in enumerate(masks):
            if mask is not None:
                weight = weights[i] if i < len(weights) else 0.1
                fused_mask += weight * mask.astype(np.float32)
                total_weight += weight
        
        if total_weight > 0:
            fused_mask /= total_weight
        
        # 后处理
        fused_mask = np.clip(fused_mask, 0, 1)
        fused_mask = cv2.GaussianBlur(fused_mask, (7, 7), 2)
        
        return fused_mask
    
    def _refine_person_contour(self, img_cv: np.ndarray, initial_mask: np.ndarray) -> np.ndarray:
        """精细化人体轮廓"""
        height, width = img_cv.shape[:2]
        
        # 将概率蒙版转换为GrabCut输入
        gc_mask = np.full((height, width), cv2.GC_PR_BGD, dtype=np.uint8)
        
        # 高置信度区域设为前景
        high_conf = initial_mask > 0.8
        gc_mask[high_conf] = cv2.GC_FGD
        
        # 中等置信度区域设为可能前景
        medium_conf = (initial_mask > 0.3) & (initial_mask <= 0.8)
        gc_mask[medium_conf] = cv2.GC_PR_FGD
        
        # 低置信度区域设为可能背景
        low_conf = (initial_mask > 0.1) & (initial_mask <= 0.3)
        gc_mask[low_conf] = cv2.GC_PR_BGD
        
        # 极低置信度区域设为背景
        very_low_conf = initial_mask <= 0.1
        gc_mask[very_low_conf] = cv2.GC_BGD
        
        # 运行GrabCut精细化
        bgd_model = np.zeros((1, 65), np.float64)
        fgd_model = np.zeros((1, 65), np.float64)
        
        try:
            cv2.grabCut(img_cv, gc_mask, None, bgd_model, fgd_model, 5, cv2.GC_INIT_WITH_MASK)
            refined_mask = np.where((gc_mask == 2) | (gc_mask == 0), 0, 1).astype(np.float32)
        except:
            refined_mask = (initial_mask > 0.5).astype(np.float32)
        
        # 形态学后处理
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        refined_mask = cv2.morphologyEx(refined_mask, cv2.MORPH_CLOSE, kernel)
        refined_mask = cv2.morphologyEx(refined_mask, cv2.MORPH_OPEN, kernel)
        
        return refined_mask
    
    def _intelligent_edge_feathering(self, img_cv: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """智能边缘羽化"""
        height, width = img_cv.shape[:2]
        
        # 计算边缘距离
        binary_mask = (mask > 0.5).astype(np.uint8)
        
        # 内部距离变换
        dist_inside = cv2.distanceTransform(binary_mask, cv2.DIST_L2, 5)
        
        # 外部距离变换
        dist_outside = cv2.distanceTransform(1 - binary_mask, cv2.DIST_L2, 5)
        
        # 自适应羽化半径
        feather_radius = max(3, min(width, height) // 80)
        
        # 创建羽化蒙版
        feathered_mask = np.ones_like(mask, dtype=np.float32)
        
        # 内部羽化
        inside_feather = np.clip(dist_inside / feather_radius, 0, 1)
        feathered_mask = np.minimum(feathered_mask, inside_feather)
        
        # 外部羽化
        outside_feather = np.clip(1 - dist_outside / feather_radius, 0, 1)
        feathered_mask = np.maximum(feathered_mask, outside_feather)
        
        # 基于图像内容的自适应羽化
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # 在边缘区域增强羽化
        edge_regions = cv2.dilate(edges, np.ones((5, 5), np.uint8), iterations=1)
        edge_factor = edge_regions.astype(np.float32) / 255.0
        
        # 在边缘区域应用更强的羽化
        enhanced_feather = cv2.GaussianBlur(feathered_mask, (7, 7), 2)
        feathered_mask = feathered_mask * (1 - edge_factor * 0.3) + enhanced_feather * edge_factor * 0.3
        
        # 最终平滑
        feathered_mask = cv2.GaussianBlur(feathered_mask, (3, 3), 1)
        
        return np.clip(feathered_mask, 0, 1)
    
    def _high_quality_background_composition(self, img_cv: np.ndarray, mask: np.ndarray, bg_color: Tuple[int, int, int]) -> np.ndarray:
        """高质量背景合成"""
        height, width = img_cv.shape[:2]
        
        # 创建背景图像
        bg_bgr = bg_color[::-1]  # RGB转BGR
        background = np.full((height, width, 3), bg_bgr, dtype=np.uint8)
        
        # 三通道蒙版
        mask_3d = np.stack([mask] * 3, axis=2)
        
        # 高质量Alpha混合
        result = img_cv.astype(np.float32) * mask_3d + background.astype(np.float32) * (1 - mask_3d)
        
        # 边缘增强（减少颜色渗透）
        edge_enhanced_result = self._edge_color_enhancement(img_cv, result, mask_3d, background)
        
        return edge_enhanced_result.astype(np.uint8)
    
    def _edge_color_enhancement(self, original: np.ndarray, blended: np.ndarray, mask: np.ndarray, background: np.ndarray) -> np.ndarray:
        """边缘颜色增强，减少颜色渗透"""
        # 检测边缘区域
        mask_single = mask[:, :, 0]
        gradient = np.gradient(mask_single)
        edge_strength = np.sqrt(gradient[0]**2 + gradient[1]**2)
        edge_regions = edge_strength > 0.1
        
        # 在边缘区域应用颜色校正
        result = blended.copy()
        
        if np.any(edge_regions):
            # 计算原始前景颜色
            foreground_regions = mask_single > 0.8
            if np.any(foreground_regions):
                # 在边缘区域增强原始颜色
                edge_mask = edge_regions[:, :, np.newaxis]
                foreground_boost = 0.2  # 增强系数
                
                # 增强前景颜色，减少背景颜色渗透
                original_boost = original.astype(np.float32) * foreground_boost
                result = result + original_boost * edge_mask * mask
        
        return result

# 全局实例
ultra_precision_segmentation = UltraPrecisionSegmentation()

def ultra_precise_background_replace(img_cv: np.ndarray, bg_color: Tuple[int, int, int]) -> Image.Image:
    """
    超精准背景替换函数（对外接口）
    
    Args:
        img_cv: OpenCV格式的输入图像
        bg_color: 背景颜色 (R, G, B)
        
    Returns:
        PIL Image对象
    """
    return ultra_precision_segmentation.ultra_precise_background_replace(img_cv, bg_color)

def test_ultra_precision():
    """测试超精准处理器"""
    print("🎯 测试超精准人体轮廓识别")
    print("="*50)
    
    # 创建测试图像
    test_img = np.random.randint(0, 255, (600, 400, 3), dtype=np.uint8)
    
    try:
        start_time = time.time()
        result = ultra_precise_background_replace(test_img, (255, 255, 255))
        processing_time = time.time() - start_time
        
        print(f"✅ 超精准处理成功，耗时: {processing_time:.2f}秒")
        print(f"📊 输出尺寸: {result.size}")
        print("🎨 新系统特性:")
        print("  - 多模态人体检测")
        print("  - 精细化轮廓提取") 
        print("  - 智能边缘羽化")
        print("  - 高质量背景合成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ultra_precision() 