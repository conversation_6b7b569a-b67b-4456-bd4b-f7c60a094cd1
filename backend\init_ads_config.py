#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
初始化广告配置数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.system_config import SystemConfig
from app.database import db

def init_ads_config():
    """初始化广告配置数据"""
    app = create_app()
    
    with app.app_context():
        print("🔧 初始化广告配置数据...")
        
        # 广告配置项
        ads_configs = [
            # 百度广告配置
            ('baidu_appid', '', 'string', '百度广告AppID'),
            ('baidu_appkey', '', 'string', '百度广告AppKey'),
            ('baidu_ad_unit_id', '', 'string', '百度广告单元ID'),
            ('baidu_enabled', 'false', 'boolean', '百度广告是否启用'),
            
            # 头条广告配置
            ('toutiao_appid', '', 'string', '头条广告AppID'),
            ('toutiao_appkey', '', 'string', '头条广告AppKey'),
            ('toutiao_ad_unit_id', '', 'string', '头条广告单元ID'),
            ('toutiao_enabled', 'false', 'boolean', '头条广告是否启用'),
            
            # AdMob广告配置
            ('admob_app_id', '', 'string', 'AdMob应用ID'),
            ('admob_ad_unit_id', '', 'string', 'AdMob广告单元ID'),
            ('admob_enabled', 'false', 'boolean', 'AdMob广告是否启用'),
        ]
        
        for key, value, config_type, description in ads_configs:
            existing = SystemConfig.query.filter_by(config_key=key).first()
            if existing:
                print(f"ℹ️  配置已存在: {key}")
            else:
                config = SystemConfig(
                    config_key=key,
                    config_value=value,
                    config_type=config_type,
                    description=description
                )
                db.session.add(config)
                print(f"✅ 创建配置: {key}")
        
        db.session.commit()
        print("\n✅ 广告配置数据初始化完成")

if __name__ == "__main__":
    init_ads_config() 