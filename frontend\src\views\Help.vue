<template>
  <div class="help-page">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">帮助中心</h1>
        <p class="page-description">使用指南和常见问题，助您高效使用本系统</p>
      </div>
    </div>
    <div class="help-body">
      <nav class="help-nav">
        <ul>
          <li><a href="#overview">功能总览</a></li>
          <li><a href="#photo">证件照功能</a></li>
          <li><a href="#doc">文档对比功能</a></li>
          <li><a href="#resume">简历功能</a></li>
          <li><a href="#card">名片功能</a></li>
          <li><a href="#points">积分规则</a></li>
          <li><a href="#account">账号安全</a></li>
          <li><a href="#privacy">数据隐私</a></li>
          <li><a href="#faq">常见问题</a></li>
          <li><a href="#contact">客服与反馈</a></li>
        </ul>
      </nav>
      <main class="help-content">
        <section id="overview">
          <h2>功能总览</h2>
          <p>白泽AI文件助手为您提供证件照制作、文档智能对比、简历生成、名片设计等一站式服务。</p>
          <img src="https://assets-global.website-files.com/5f6b7190899c3a6c7b6c3c4e/63e3b7e2e2e2e2e2e2e2e2e2_help-center-illustration.png" alt="功能总览插图" class="help-img" />
        </section>
        <section id="photo">
          <h2>证件照功能</h2>
          <ol>
            <li>点击首页“生成证件照”按钮，上传照片。</li>
            <li>选择尺寸、背景色等参数，点击生成。</li>
            <li>预览并下载证件照。</li>
          </ol>
          <img src="https://cdn.pixabay.com/photo/2017/01/31/13/14/photo-2020273_1280.png" alt="证件照功能插图" class="help-img" />
          <div class="help-tip">支持多种规格，自动美颜与背景替换。</div>
        </section>
        <section id="doc">
          <h2>文档对比功能</h2>
          <ol>
            <li>点击“文档差异对比”，上传需对比的两个文档。</li>
            <li>点击“开始对比”，系统自动分析差异。</li>
            <li>查看高亮差异报告，支持下载。</li>
          </ol>
          <img src="https://cdn.pixabay.com/photo/2016/03/31/19/56/computer-1294045_1280.png" alt="文档对比插图" class="help-img" />
        </section>
        <section id="resume">
          <h2>简历功能</h2>
          <ol>
            <li>进入“简历管理”，新建或导入简历。</li>
            <li>选择模板，填写/编辑内容。</li>
            <li>一键导出PDF或图片格式。</li>
          </ol>
          <img src="https://cdn.pixabay.com/photo/2017/01/10/19/05/meeting-1979261_1280.png" alt="简历功能插图" class="help-img" />
        </section>
        <section id="card">
          <h2>名片功能</h2>
          <ol>
            <li>进入“名片管理”，新建名片。</li>
            <li>选择模板，填写信息，添加二维码。</li>
            <li>预览并下载名片。</li>
          </ol>
          <img src="https://cdn.pixabay.com/photo/2017/01/10/19/05/meeting-1979261_1280.png" alt="名片功能插图" class="help-img" />
        </section>
        <section id="points">
          <h2>积分规则</h2>
          <ul>
            <li>积分获取：注册、每日签到、邀请好友、购买套餐等。</li>
            <li>积分消耗：生成证件照、文档对比、简历导出等。</li>
            <li>积分明细：可在“积分中心”查看详细记录。</li>
            <li>限制说明：部分功能需消耗积分，积分不足时可充值。</li>
          </ul>
        </section>
        <section id="account">
          <h2>账号注册与安全</h2>
          <ul>
            <li>支持手机号、邮箱注册。</li>
            <li>忘记密码可通过邮箱/手机找回。</li>
            <li>建议定期更换密码，保护账号安全。</li>
          </ul>
        </section>
        <section id="privacy">
          <h2>数据隐私与安全承诺</h2>
          <ul>
            <li>用户数据仅用于功能实现，不会泄露给第三方。</li>
            <li>所有数据传输均加密，保障隐私安全。</li>
            <li>可随时申请删除账号及数据。</li>
          </ul>
        </section>
        <section id="faq">
          <h2>常见问题</h2>
          <div class="faq-item">
            <strong>Q: 登录后页面空白怎么办？</strong>
            <p>A: 请尝试刷新页面或重新登录，若仍有问题请联系客服。</p>
          </div>
          <div class="faq-item">
            <strong>Q: 如何获取更多积分？</strong>
            <p>A: 可通过每日签到、邀请好友、购买套餐等方式获得。</p>
          </div>
          <div class="faq-item">
            <strong>Q: 证件照生成后如何下载？</strong>
            <p>A: 生成后页面会有下载按钮，点击即可保存到本地。</p>
          </div>
        </section>
        <section id="contact">
          <h2>客服与反馈</h2>
          <p>如有疑问或建议，请通过以下方式联系我们：</p>
          <ul>
            <li>在线客服：页面右下角“联系客服”按钮</li>
            <li>邮箱：<EMAIL></li>
            <li>微信客服：<img src="https://via.placeholder.com/120x120?text=WeChat+QR" alt="微信客服二维码" class="help-img-small" /></li>
          </ul>
        </section>
      </main>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Help'
}
</script>

<style scoped>
.help-page {
  min-height: 100vh;
  background: #f4f8ff;
  padding: 0;
}
.page-header {
  background: linear-gradient(90deg, #4A90E2 0%, #D6E4F0 100%);
  color: #fff;
  border-radius: 0 0 24px 24px;
  padding: 36px 32px 28px 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 24px #4A90E222;
  display: flex;
  align-items: flex-end;
  justify-content: flex-start;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}
.header-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.page-description {
  font-size: 1.1rem;
  color: rgba(255,255,255,0.9);
  margin: 0;
  line-height: 1.5;
}
.help-body {
  display: flex;
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px 32px 16px;
}
.help-nav {
  min-width: 180px;
  border-right: 1px solid #e6e6e6;
  padding-right: 24px;
}
.help-nav ul {
  list-style: none;
  padding: 0;
}
.help-nav li {
  margin-bottom: 16px;
}
.help-nav a {
  color: #4A90E2;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}
.help-nav a:hover {
  color: #F5C242;
}
.help-content {
  flex: 1;
  min-width: 0;
}
.help-content section {
  margin-bottom: 48px;
  background: #fff;
  border-radius: 12px;
  padding: 32px 24px;
  box-shadow: 0 2px 8px rgba(74,144,226,0.04);
}
.help-content h2 {
  color: #222;
  margin-bottom: 16px;
}
.help-img {
  width: 100%;
  max-width: 400px;
  margin: 16px 0;
  border-radius: 8px;
  background: #f0f4fa;
}
.help-img-small {
  width: 120px;
  margin-top: 8px;
  border-radius: 8px;
}
.help-tip {
  background: #eaf6ff;
  color: #2176c1;
  padding: 8px 16px;
  border-radius: 6px;
  margin: 12px 0;
  font-size: 0.98rem;
}
.faq-item {
  margin-bottom: 18px;
  background: #f9f6f2;
  padding: 12px 16px;
  border-radius: 6px;
}
@media (max-width: 900px) {
  .help-body { flex-direction: column; }
  .help-nav { border-right: none; border-bottom: 1px solid #e6e6e6; padding-right: 0; margin-bottom: 24px; }
}
</style> 