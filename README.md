# 🚀 智能背景替换系统 v2.0

## 📖 简介

这是一个基于深度学习的高精度人体分割与背景替换系统，集成了多种先进的分割技术：

- **SegFormer**: 高精度人体解析模型
- **FastSAM**: 快速分割任何物体
- **GrabCut**: 传统交互式分割
- **集成方法**: 多模型融合提升精度

## ✨ 特性

- 🎯 **高精度分割**: 基于深度学习的人体解析
- ⚡ **快速处理**: 优化的推理流程
- 🔧 **多模型集成**: 自动选择最佳分割方法
- 📱 **GPU加速**: 支持CUDA加速
- 🎨 **边缘羽化**: 自然的背景融合效果
- 📊 **质量评估**: 自动评估处理质量

## 🚀 快速开始

### 1. 安装系统
```bash
python deployment_guide.py
```

### 2. 基础使用
```python
from integrated_system import IntegratedBackgroundReplacementSystem
import cv2

# 初始化系统
system = IntegratedBackgroundReplacementSystem()

# 加载图像
image = cv2.imread("your_image.jpg")
background = cv2.imread("new_background.jpg")

# 执行背景替换
result = system.replace_background(image, background)

if result['success']:
    cv2.imwrite("result.jpg", result['result_image'])
    print(f"处理完成! 质量评分: {result['quality_score']:.1f}")
```

## 📁 文件结构

```
智能背景替换系统/
├── integrated_system.py           # 主系统文件
├── advanced_human_parsing_system.py  # SegFormer解析系统
├── grounded_sam_background_replacement.py  # SAM背景替换
├── deployment_guide.py            # 部署指南
├── example_basic.py               # 基础使用示例
├── example_batch.py               # 批量处理示例
├── requirements_advanced.txt      # 依赖包列表
└── README.md                      # 说明文档
```

## 🔧 系统要求

- Python 3.8+
- 内存: 8GB+ (推荐)
- CUDA 11.0+ (可选，GPU加速)

## 📦 依赖包

主要依赖：
- torch, torchvision
- transformers
- ultralytics
- opencv-python
- numpy, matplotlib

## 🎯 使用场景

- 📸 **证件照制作**: 快速更换证件照背景
- 🎬 **视频制作**: 人物抠图与背景合成
- 🛍️ **电商产品**: 商品图片背景统一
- 🎨 **创意设计**: 艺术创作与图像编辑

## 📊 性能指标

| 模型 | 精度 | 速度 | 内存占用 |
|------|------|------|----------|
| SegFormer | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| FastSAM | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| GrabCut | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 集成方法 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |

## 🆘 常见问题

**Q: 模型下载失败怎么办？**
A: 检查网络连接，或使用国内镜像源。

**Q: 内存不足怎么办？**
A: 1) 调整图像尺寸 2) 关闭不需要的模型 3) 使用单一模型而非集成

**Q: 分割效果不理想？**
A: 1) 确保人物清晰 2) 避免复杂背景 3) 调整图像对比度

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**开发者**: AI Assistant  
**版本**: v2.0.0  
**更新时间**: 2024年

# 智能简历生成器

基于 Vue.js + Django 开发的现代化智能简历生成系统，支持 DOCX 解析、AI 辅助写作、多模板导出等功能。

## 🌟 核心功能

### 📄 简历创建与管理
- **DOCX 一键解析**：上传现有简历，自动提取并格式化信息
- **引导式创建**：分步向导式填写，支持多模块信息录入
- **实时预览**：左侧编辑，右侧实时预览，所见即所得
- **多格式导出**：支持 PDF 和 DOCX 格式导出

### 🤖 AI 智能辅助
- **工作描述生成**：根据职位信息生成专业的工作描述
- **项目描述优化**：智能生成项目经验描述
- **个人总结优化**：AI 优化个人总结，突出核心竞争力

### 🎨 专业模板系统
- **多样化模板**：内置经典简约、现代商务、科技风格等模板
- **分类筛选**：按风格和行业进行模板分类
- **ATS 友好**：所有模板遵循 ATS 系统友好原则

### 📊 用户中心
- **简历管理**：创建、编辑、删除、复制简历
- **历史记录**：查看简历创建和修改历史
- **数据统计**：简历使用情况统计

## 🛠 技术架构

### 前端技术栈
- **Vue.js 3** - 现代化前端框架
- **Element Plus** - 丰富的 UI 组件库
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP 客户端

### 后端技术栈
- **Python Django** - 强大的 Web 框架
- **Django REST Framework** - API 开发框架
- **SQLAlchemy** - ORM 数据库操作
- **python-docx** - DOCX 文件解析
- **pdfkit** - PDF 生成
- **AI API 集成** - 第三方大语言模型

### 数据库
- **PostgreSQL** - 主数据库
- **完整的关系设计** - 支持复杂的简历数据结构

## 🚀 快速开始

### 环境要求
- Node.js 16+ 
- Python 3.8+
- PostgreSQL 12+
- npm/yarn
- Git

### 1. 克隆项目
```bash
git clone [项目地址]
cd photoNew
```

### 2. 安装依赖

#### 前端依赖
```bash
cd frontend
npm install
```

#### 后端依赖
```bash
cd backend
pip install -r requirements.txt
```

### 3. 数据库配置
```bash
# 创建数据库
createdb photo_maker

# 执行数据库初始化脚本
psql -d photo_maker -f database/init.sql
```

### 4. 环境配置

创建后端环境配置文件 `backend/.env`：
```env
# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/photo_maker

# JWT 配置
JWT_SECRET_KEY=your-secret-key

# AI 服务配置（可选）
AI_API_KEY=your-ai-api-key
AI_API_BASE_URL=https://api.openai.com/v1
AI_MODEL_NAME=gpt-3.5-turbo

# 其他配置
FLASK_ENV=development
```

### 5. 启动服务

#### 启动后端服务
```bash
cd backend
python run.py
```
后端服务将在 `http://localhost:5000` 启动

#### 启动前端服务
```bash
cd frontend
npm run dev
```
前端服务将在 `http://localhost:3000` 启动

### 6. 访问应用
打开浏览器访问 `http://localhost:3000` 即可使用系统。

## 📖 使用指南

### 注册和登录
1. 访问首页，点击"注册"创建新账户
2. 使用邮箱和密码完成注册
3. 登录后进入系统主界面

### 创建简历

#### 方式一：从零开始创建
1. 点击"新建简历"
2. 填写基本信息（姓名、联系方式等）
3. 逐步添加教育背景、工作经历、项目经验等
4. 使用 AI 辅助功能优化内容
5. 选择合适的模板
6. 预览并导出简历

#### 方式二：上传现有简历
1. 选择"上传解析"
2. 上传 DOCX 格式的现有简历
3. 系统自动解析并提取信息
4. 校对和修改解析结果
5. 进行进一步的编辑和优化

### AI 辅助功能使用
- **工作描述**：在工作经历中点击"AI生成描述"
- **项目描述**：在项目经验中点击"AI生成描述"  
- **个人总结**：在个人总结区域点击"AI优化总结"

### 模板选择和预览
- 编辑页面右侧实时预览
- 点击"选择模板"浏览和切换模板
- 支持不同风格和行业的专业模板

### 导出简历
- 支持 PDF 格式（适合投递）
- 支持 DOCX 格式（便于继续编辑）

## 🔧 开发说明

### 项目结构
```
photoNew/
├── frontend/           # Vue.js 前端应用
│   ├── src/
│   │   ├── components/ # 公共组件
│   │   ├── views/      # 页面组件
│   │   ├── stores/     # Pinia 状态管理
│   │   ├── router/     # 路由配置
│   │   └── api/        # API 接口
├── backend/            # Django 后端应用
│   ├── app/
│   │   ├── models/     # 数据模型
│   │   ├── api/        # API 接口
│   │   ├── services/   # 业务服务
│   │   └── utils/      # 工具函数
│   ├── config.py       # 配置文件
│   └── run.py          # 启动文件
├── database/           # 数据库脚本
└── static/             # 静态文件
```

### API 接口文档
主要 API 端点：

#### 用户认证
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息

#### 简历管理
- `GET /api/resumes` - 获取简历列表
- `POST /api/resumes` - 创建简历
- `GET /api/resumes/{id}` - 获取简历详情
- `PUT /api/resumes/{id}` - 更新简历
- `DELETE /api/resumes/{id}` - 删除简历

#### 简历功能
- `POST /api/resumes/parse` - 解析 DOCX 简历
- `GET /api/resumes/{id}/preview` - 获取简历预览
- `GET /api/resumes/{id}/export` - 导出简历
- `GET /api/resumes/templates` - 获取模板列表

#### AI 辅助
- `POST /api/resumes/ai/work-description` - 生成工作描述
- `POST /api/resumes/ai/project-description` - 生成项目描述
- `POST /api/resumes/ai/optimize-summary` - 优化个人总结

## 📄 许可证

本项目基于 MIT 许可证开源。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- 邮箱：[<EMAIL>]

---

**享受智能简历创建的便捷体验！** ✨


