/* 基础样式 */
:root {
    --font-family: var(--font-family, 'Microsoft YaHei', sans-serif);
    --base-font-size: var(--base-font-size, 14px);
    --primary-color: var(--primary-color, #2c3e50);
    --secondary-color: var(--secondary-color, #7f8c8d);
    --heading-color: var(--heading-color, #2c3e50);
    --link-color: var(--link-color, #3498db);
    --section-spacing: var(--section-spacing, 2rem);
    --line-height: var(--line-height, 1.6);
}

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--base-font-size);
    line-height: var(--line-height);
    color: var(--primary-color);
    background-color: #fff;
}

/* 容器样式 */
.resume-container {
    max-width: 210mm;
    margin: 0 auto;
    padding: 2rem;
    background-color: #fff;
}

/* 头部样式 */
.resume-header {
    margin-bottom: var(--section-spacing);
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color);
}

.header-main {
    margin-bottom: 1rem;
}

.full-name {
    font-size: 2rem;
    color: var(--heading-color);
    margin-bottom: 0.5rem;
}

.title {
    font-size: 1.5rem;
    color: var(--secondary-color);
}

.contact-info {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--secondary-color);
}

/* 主体部分样式 */
.resume-body {
    display: flex;
    flex-direction: column;
    gap: var(--section-spacing);
}

/* 章节通用样式 */
.resume-section {
    margin-bottom: var(--section-spacing);
}

.section-title {
    font-size: 1.25rem;
    color: var(--heading-color);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--secondary-color);
}

.section-content {
    padding: 0 1rem;
}

/* 个人总结样式 */
.summary-text {
    font-size: 1rem;
    line-height: 1.8;
    color: var(--primary-color);
}

/* 工作经历样式 */
.work-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
    break-inside: avoid;
    page-break-inside: avoid;
}

.work-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.company-name {
    font-size: 1.2rem;
    color: var(--heading-color);
    margin-bottom: 0.25rem;
}

.position {
    font-size: 1.1rem;
    color: var(--secondary-color);
}

.work-period {
    color: var(--secondary-color);
}

.work-description {
    margin-bottom: 1rem;
}

.achievements-list {
    list-style-position: inside;
    padding-left: 1rem;
}

.achievements-list li {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* 教育背景样式 */
.education-item {
    margin-bottom: 1.5rem;
}

.school-name {
    font-size: 1.2rem;
    color: var(--heading-color);
    margin-bottom: 0.25rem;
}

.major {
    font-size: 1.1rem;
    color: var(--secondary-color);
}

.degree {
    color: var(--primary-color);
    font-weight: bold;
}

/* 项目经验样式 */
.project-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.project-name {
    font-size: 1.2rem;
    color: var(--heading-color);
    margin-bottom: 0.5rem;
}

.project-period {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

.tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 1rem 0;
}

.tech-tag {
    padding: 0.25rem 0.75rem;
    background-color: #f0f2f5;
    border-radius: 4px;
    font-size: 0.9rem;
    color: var(--primary-color);
}

/* 技能特长样式 */
.skills-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.skill-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.skill-name {
    font-weight: bold;
    color: var(--primary-color);
}

.skill-level {
    height: 8px;
    background-color: #eee;
    border-radius: 4px;
    overflow: hidden;
}

.skill-bar {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 4px;
}

/* 证书资质样式 */
.certifications-list {
    list-style: none;
}

.certification-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #eee;
}

.cert-name {
    color: var(--primary-color);
    font-weight: bold;
}

.cert-date {
    color: var(--secondary-color);
}

/* 打印样式优化 */
@media print {
    body {
        margin: 0;
        padding: 0;
    }
    
    .resume-container {
        width: 210mm;
        min-height: 297mm;
        padding: 20mm;
        margin: 0 auto;
        box-shadow: none;
    }
    
    .resume-section {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .work-section {
        break-before: auto;
        page-break-before: auto;
    }
    
    .work-item {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .work-header {
        break-after: avoid;
        page-break-after: avoid;
    }
    
    .work-content {
        break-before: avoid;
        page-break-before: avoid;
    }
} 