<template>
  <el-card class="quick-card">
    <div class="quick-header">
      <el-icon><Document /></el-icon>
      <span>文档对比</span>
    </div>
    <div class="quick-compare">
      <el-upload
        class="quick-upload"
        drag
        :show-file-list="false"
        :action="uploadUrl"
        :on-success="handleSuccess"
        :on-error="handleError"
      >
        <div class="el-upload__text">上传标准版文档</div>
      </el-upload>
      <el-upload
        class="quick-upload"
        drag
        :show-file-list="false"
        :action="uploadUrl"
        :on-success="handleSuccess"
        :on-error="handleError"
      >
        <div class="el-upload__text">上传修改版文档</div>
      </el-upload>
    </div>
    <div class="quick-desc">
      <el-alert type="info" show-icon :closable="false">
        支持Word/PDF/WPS，上传两份文档后自动生成差异报告。
      </el-alert>
    </div>
  </el-card>
</template>
<script setup>
import { ElMessage, ElAlert } from 'element-plus'
import { Document } from '@element-plus/icons-vue'
const uploadUrl = '/api/documents/upload'
const handleSuccess = () => ElMessage.success('上传成功')
const handleError = () => ElMessage.error('上传失败，请重试')
</script>
<style scoped>
.quick-card { min-width: 320px; }
.quick-header { display: flex; align-items: center; gap: 8px; font-size: 18px; font-weight: bold; margin-bottom: 12px; }
.quick-compare { display: flex; gap: 8px; }
.quick-upload { flex: 1; }
.quick-desc { margin-top: 8px; }
</style> 