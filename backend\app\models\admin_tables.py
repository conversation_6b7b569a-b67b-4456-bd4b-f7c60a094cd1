"""
管理员角色关联表定义
"""
from sqlalchemy import Table, Column, Integer, ForeignKey, DateTime
from .base import db

# 管理员与角色关联表
admin_role_users = Table(
    'admin_role_users',
    db.metadata,
    Column('admin_id', Integer, ForeignKey('admins.id', ondelete='CASCADE'), primary_key=True),
    Column('role_id', Integer, ForeignKey('admin_roles.id', ondelete='CASCADE'), primary_key=True),
    Column('created_at', DateTime)
)
