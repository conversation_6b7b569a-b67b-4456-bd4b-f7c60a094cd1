<template>
  <el-dialog v-model="visible" title="字段配置" width="600px" @close="close">
    <el-table :data="fields" row-key="key" style="margin-bottom:16px">
      <el-table-column label="字段" prop="key" width="120">
        <template #default="{row}">{{ row.key }}</template>
      </el-table-column>
      <el-table-column label="显示">
        <template #default="{row}">
          <el-switch v-model="row.visible" />
        </template>
      </el-table-column>
      <el-table-column label="必填">
        <template #default="{row}">
          <el-switch v-model="row.required" />
        </template>
      </el-table-column>
      <el-table-column label="标签">
        <template #default="{row}">
          <el-input v-model="row.label" size="small" />
        </template>
      </el-table-column>
      <el-table-column label="顺序" width="80">
        <template #default="{row, $index}">
          <el-button icon="ArrowUp" size="mini" @click="moveUp($index)" :disabled="$index===0" />
          <el-button icon="ArrowDown" size="mini" @click="moveDown($index)" :disabled="$index===fields.length-1" />
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
const props = defineProps({
  modelValue: Boolean,
  fieldsConfig: Array
})
const emit = defineEmits(['update:modelValue', 'save'])
const visible = ref(false)
const fields = ref([])
watch(() => props.modelValue, v => { visible.value = v })
watch(() => props.fieldsConfig, v => { fields.value = v ? JSON.parse(JSON.stringify(v)) : [] }, { immediate: true })
const close = () => emit('update:modelValue', false)
const save = () => {
  emit('save', fields.value)
  close()
}
const moveUp = (i) => {
  if (i > 0) [fields.value[i-1], fields.value[i]] = [fields.value[i], fields.value[i-1]]
}
const moveDown = (i) => {
  if (i < fields.value.length-1) [fields.value[i+1], fields.value[i]] = [fields.value[i], fields.value[i+1]]
}
</script> 