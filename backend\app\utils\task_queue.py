"""
异步任务队列管理工具
"""
from celery import Celery
from typing import Any, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class TaskQueue:
    """任务队列管理器"""
    
    def __init__(self, celery_app: Celery):
        self.celery = celery_app
        self.tasks = {}
    
    def submit_task(self, task_name: str, *args, **kwargs) -> Optional[str]:
        """
        提交异步任务
        :param task_name: 任务名称
        :param args: 位置参数
        :param kwargs: 关键字参数
        :return: 任务ID
        """
        try:
            task = self.celery.send_task(task_name, args=args, kwargs=kwargs)
            self.tasks[task.id] = task
            return task.id
        except Exception as e:
            logger.error(f"提交任务失败: {str(e)}")
            return None
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        :param task_id: 任务ID
        :return: 任务状态信息
        """
        if task_id not in self.tasks:
            return {'status': 'unknown', 'message': '任务不存在'}
        
        task = self.tasks[task_id]
        try:
            result = task.result
            if task.failed():
                return {
                    'status': 'failed',
                    'message': str(result) if result else '任务执行失败'
                }
            elif task.successful():
                return {
                    'status': 'completed',
                    'result': result
                }
            else:
                return {
                    'status': task.status,
                    'message': '任务处理中'
                }
        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return {'status': 'error', 'message': str(e)}
    
    def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        :param task_id: 任务ID
        :return: 是否成功取消
        """
        if task_id not in self.tasks:
            return False
        
        try:
            task = self.tasks[task_id]
            task.revoke(terminate=True)
            del self.tasks[task_id]
            return True
        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            return False
    
    def cleanup_tasks(self):
        """清理已完成的任务"""
        completed_tasks = []
        for task_id, task in self.tasks.items():
            if task.ready():
                completed_tasks.append(task_id)
        
        for task_id in completed_tasks:
            del self.tasks[task_id]
    
    def get_active_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有活动任务的状态"""
        return {
            task_id: self.get_task_status(task_id)
            for task_id in self.tasks.keys()
        }
    
    def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务进度
        :param task_id: 任务ID
        :return: 进度信息
        """
        if task_id not in self.tasks:
            return {'progress': 0, 'message': '任务不存在'}
        
        task = self.tasks[task_id]
        try:
            # 尝试从任务状态中获取进度信息
            if hasattr(task, 'info') and isinstance(task.info, dict):
                return {
                    'progress': task.info.get('progress', 0),
                    'message': task.info.get('status', '处理中'),
                    'details': task.info.get('details', {})
                }
            return {
                'progress': 0 if task.status == 'PENDING' else 100,
                'message': task.status
            }
        except Exception as e:
            logger.error(f"获取任务进度失败: {str(e)}")
            return {'progress': 0, 'message': str(e)}
    
    def retry_failed_task(self, task_id: str) -> Optional[str]:
        """
        重试失败的任务
        :param task_id: 原任务ID
        :return: 新任务ID
        """
        if task_id not in self.tasks:
            return None
        
        original_task = self.tasks[task_id]
        if not original_task.failed():
            return None
        
        try:
            # 获取原任务信息并重新提交
            task_name = original_task.name
            args = original_task.args
            kwargs = original_task.kwargs
            
            # 删除原任务
            del self.tasks[task_id]
            
            # 提交新任务
            return self.submit_task(task_name, *args, **kwargs)
        except Exception as e:
            logger.error(f"重试任务失败: {str(e)}")
            return None
