/* 专业时间线模板样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f4f4f4;
}

.resume-container {
    max-width: 210mm;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    min-height: 297mm;
}

.resume-header {
    background: #2c3e50;
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.profile-photo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid white;
}

.profile-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info h1 {
    font-size: 2.2rem;
    margin-bottom: 0.5rem;
}

.profile-info h2 {
    font-size: 1.2rem;
    opacity: 0.9;
}

.contact-info p {
    margin-bottom: 0.3rem;
}

.resume-main {
    padding: 2rem;
}

.section {
    margin-bottom: 2rem;
}

.section h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #3498db;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -0.5rem;
    top: 0.5rem;
    width: 1rem;
    height: 1rem;
    background: #3498db;
    border-radius: 50%;
}

.timeline-date {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.timeline-content h4 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
}

.timeline-content h5 {
    color: #3498db;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.education-item {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 5px;
    border-left: 4px solid #3498db;
}

.education-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.skill-tag {
    background: #3498db;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
}

@media print {
    body {
        background: white;
    }
    
    .resume-container {
        box-shadow: none;
        margin: 0;
    }
} 