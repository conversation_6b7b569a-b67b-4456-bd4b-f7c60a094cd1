"""名片基础模板定义

此模块定义了系统默认的名片模板，包括布局、样式和结构定义。
"""

from typing import Dict, Any

def get_business_classic_template() -> Dict[str, Any]:
    """获取商务经典名片模板定义"""
    return {
        "id": "business_classic",
        "name": "商务经典",
        "description": "简洁大方的商务名片模板，适合各行各业",
        "preview_image": "templates/business_classic/preview.png",
        "category": "business",
        "industry": "other",
        "layout_type": "standard",
        "width_mm": 90.0,
        "height_mm": 54.0,
        "styles": {
            "font_family": "Microsoft YaHei, Arial, sans-serif",
            "base_font_size": "14px",
            "primary_color": "#2c3e50",
            "secondary_color": "#3498db",
            "text_color": "#34495e",
            "light_color": "#7f8c8d",
            "background_color": "#ffffff",
            "border_color": "#ddd",
            "section_spacing": "4mm",
            "line_height": "1.4"
        },
        "layout": {
            "header": {
                "type": "vertical",
                "style": {
                    "margin_bottom": "4mm",
                    "text_align": "left"
                }
            },
            "company": {
                "type": "section",
                "style": {
                    "margin_bottom": "4mm",
                    "color": "var(--secondary-color)"
                }
            },
            "contact": {
                "type": "list",
                "style": {
                    "font_size": "11px",
                    "line_height": "1.4"
                }
            },
            "qr_code": {
                "type": "absolute",
                "position": "bottom_right",
                "style": {
                    "width": "12mm",
                    "height": "12mm",
                    "margin": "8mm"
                }
            }
        },
        "sections": {
            "header": {
                "order": 1,
                "fields": ["name", "title"],
                "styles": {
                    "name_size": "18px",
                    "name_weight": "bold",
                    "title_size": "14px",
                    "title_color": "var(--text-color)"
                }
            },
            "company": {
                "order": 2,
                "fields": ["company"],
                "styles": {
                    "font_size": "14px",
                    "font_weight": "500",
                    "color": "var(--secondary-color)"
                }
            },
            "contact": {
                "order": 3,
                "fields": ["phone", "email", "address"],
                "item_layout": "icon_text",
                "styles": {
                    "font_size": "11px",
                    "color": "var(--light-color)",
                    "icon_size": "10px",
                    "icon_color": "var(--secondary-color)"
                }
            }
        },
        "export_settings": {
            "png": {
                "resolution": 300,
                "background": "white",
                "format": "PNG"
            },
            "jpg": {
                "resolution": 300,
                "background": "white",
                "format": "JPEG",
                "quality": 95
            },
            "pdf": {
                "page_size": "A4",
                "margin": "10mm",
                "cards_per_page": 10
            }
        }
    }

def get_modern_minimal_template() -> Dict[str, Any]:
    """获取现代简约名片模板定义"""
    return {
        "id": "modern_minimal",
        "name": "现代简约",
        "description": "极简风格，注重留白和层次感",
        "preview_image": "templates/modern_minimal/preview.png",
        "category": "minimal",
        "industry": "design",
        "layout_type": "standard",
        "width_mm": 90.0,
        "height_mm": 54.0,
        "styles": {
            "font_family": "PingFang SC, Microsoft YaHei, sans-serif",
            "base_font_size": "14px",
            "primary_color": "#2c3e50",
            "secondary_color": "#e74c3c",
            "text_color": "#34495e",
            "light_color": "#95a5a6",
            "background_color": "#fafafa",
            "accent_color": "#e74c3c",
            "section_spacing": "3mm",
            "line_height": "1.4"
        },
        "layout": {
            "type": "flex_column",
            "content": {
                "type": "flex",
                "direction": "column",
                "justify": "space-between",
                "height": "100%"
            },
            "accent_bar": {
                "type": "absolute",
                "position": "right",
                "style": {
                    "width": "3mm",
                    "height": "100%",
                    "background": "linear-gradient(180deg, var(--accent-color) 0%, #c0392b 100%)"
                }
            }
        },
        "sections": {
            "name_section": {
                "order": 1,
                "fields": ["name", "title"],
                "styles": {
                    "name_size": "20px",
                    "name_weight": "300",
                    "name_letter_spacing": "1px",
                    "divider_width": "30mm",
                    "divider_height": "1px",
                    "divider_color": "var(--accent-color)",
                    "title_size": "12px",
                    "title_transform": "uppercase",
                    "title_letter_spacing": "0.5px"
                }
            },
            "company_section": {
                "order": 2,
                "fields": ["company"],
                "styles": {
                    "font_size": "14px",
                    "font_weight": "500",
                    "color": "var(--text-color)"
                }
            },
            "contact_section": {
                "order": 3,
                "fields": ["phone", "email"],
                "item_layout": "simple_text",
                "styles": {
                    "font_size": "11px",
                    "color": "var(--light-color)"
                }
            }
        }
    }

def get_creative_design_template() -> Dict[str, Any]:
    """获取创意设计名片模板定义"""
    return {
        "id": "creative_design",
        "name": "创意设计",
        "description": "富有创意的设计，适合设计师和创意工作者",
        "preview_image": "templates/creative_design/preview.png",
        "category": "creative",
        "industry": "design",
        "layout_type": "standard",
        "width_mm": 90.0,
        "height_mm": 54.0,
        "styles": {
            "font_family": "PingFang SC, Microsoft YaHei, sans-serif",
            "base_font_size": "14px",
            "primary_color": "#ffffff",
            "secondary_color": "#667eea",
            "accent_color": "#764ba2",
            "background_gradient": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            "text_color": "#ffffff",
            "section_spacing": "2mm",
            "line_height": "1.4"
        },
        "layout": {
            "type": "absolute_positioned",
            "background_pattern": {
                "type": "svg_pattern",
                "opacity": 0.3,
                "animation": "float 20s ease-in-out infinite"
            },
            "content": {
                "type": "relative",
                "z_index": 2
            }
        },
        "sections": {
            "header": {
                "order": 1,
                "fields": ["name", "title"],
                "styles": {
                    "name_size": "18px",
                    "name_weight": "bold",
                    "name_bg_size": "24px",
                    "name_bg_weight": "900",
                    "name_bg_opacity": "0.1",
                    "title_size": "12px",
                    "title_transform": "uppercase",
                    "title_letter_spacing": "1px"
                }
            },
            "company": {
                "order": 2,
                "fields": ["company"],
                "styles": {
                    "font_size": "14px",
                    "color": "var(--text-color)"
                }
            },
            "contact": {
                "order": 3,
                "fields": ["phone", "email"],
                "item_layout": "label_value",
                "styles": {
                    "label_size": "10px",
                    "label_color": "rgba(255,255,255,0.7)",
                    "value_size": "12px",
                    "value_color": "var(--text-color)"
                }
            }
        }
    }

def get_elegant_business_template() -> Dict[str, Any]:
    """获取优雅商务名片模板定义"""
    return {
        "id": "elegant_business",
        "name": "优雅商务",
        "description": "高端商务风格，适合金融、法律等专业领域",
        "preview_image": "templates/elegant_business/preview.png",
        "category": "elegant",
        "industry": "finance",
        "layout_type": "standard",
        "width_mm": 90.0,
        "height_mm": 54.0,
        "styles": {
            "font_family": "Times New Roman, serif",
            "base_font_size": "14px",
            "primary_color": "#d4af37",
            "secondary_color": "#1a1a1a",
            "text_color": "#ffffff",
            "light_color": "#cccccc",
            "background_color": "#1a1a1a",
            "border_color": "#444",
            "gold_color": "#d4af37",
            "section_spacing": "4mm",
            "line_height": "1.4"
        },
        "layout": {
            "type": "elegant",
            "gold_line": {
                "type": "absolute",
                "position": "top",
                "style": {
                    "height": "2px",
                    "background": "linear-gradient(90deg, transparent, var(--gold-color), transparent)",
                    "margin": "0 8mm"
                }
            },
            "corner_decoration": {
                "type": "absolute",
                "position": "bottom_right",
                "style": {
                    "width": "8mm",
                    "height": "8mm",
                    "border": "1px solid var(--gold-color)",
                    "border_radius": "50%",
                    "margin": "6mm"
                }
            }
        },
        "sections": {
            "header": {
                "order": 1,
                "fields": ["name", "title"],
                "styles": {
                    "name_size": "18px",
                    "name_weight": "700",
                    "name_color": "var(--gold-color)",
                    "name_letter_spacing": "0.5px",
                    "title_size": "13px",
                    "title_transform": "uppercase",
                    "title_letter_spacing": "1px"
                }
            },
            "company": {
                "order": 2,
                "fields": ["company"],
                "styles": {
                    "font_size": "14px",
                    "font_weight": "500",
                    "color": "var(--text-color)"
                }
            },
            "contact": {
                "order": 3,
                "fields": ["phone", "email", "address"],
                "item_layout": "icon_text",
                "styles": {
                    "font_size": "11px",
                    "color": "var(--light-color)",
                    "icon_size": "10px",
                    "icon_color": "var(--gold-color)",
                    "icon_width": "4mm"
                }
            }
        }
    } 