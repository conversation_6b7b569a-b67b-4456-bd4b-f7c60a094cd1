<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-content">
      <el-icon size="48" class="error-icon"><Warning /></el-icon>
      <h3>页面出现了一些问题</h3>
      <p>这可能是由于页面元素大小变化导致的临时问题，请尝试刷新页面。</p>
      <el-button type="primary" @click="handleRetry">重新加载</el-button>
    </div>
  </div>
  <div v-else>
    <slot />
  </div>
</template>

<script>
import { ref, onErrorCaptured } from 'vue'
import { Warning } from '@element-plus/icons-vue'

export default {
  name: 'ErrorBoundary',
  components: {
    Warning
  },
  setup() {
    const hasError = ref(false)
    const error = ref(null)

    onErrorCaptured((err, instance, info) => {
      // 忽略 ResizeObserver 循环错误
      if (err.message && err.message.includes('ResizeObserver loop')) {
        console.warn('ResizeObserver 循环错误已忽略:', err.message)
        return false // 阻止错误继续传播
      }
      
      // 处理其他错误
      console.error('ErrorBoundary 捕获到错误:', err)
      console.error('组件实例:', instance)
      console.error('错误信息:', info)
      
      hasError.value = true
      error.value = err
      
      return false // 阻止错误继续传播
    })

    const handleRetry = () => {
      hasError.value = false
      error.value = null
      window.location.reload()
    }

    return {
      hasError,
      error,
      handleRetry
    }
  }
}
</script>

<style scoped>
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 40px;
  background: #f8f9fa;
  border-radius: 12px;
  text-align: center;
}

.error-content {
  max-width: 400px;
}

.error-icon {
  color: #f56c6c;
  margin-bottom: 16px;
}

.error-content h3 {
  margin: 0 0 12px 0;
  color: #1d1d1f;
  font-size: 20px;
  font-weight: 600;
}

.error-content p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}
</style> 