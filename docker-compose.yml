version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: photo_maker_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: photo_maker_2024
      MYSQL_DATABASE: photo_maker
      MYSQL_USER: photo_user
      MYSQL_PASSWORD: photo_pass_2024
      TZ: Asia/Shanghai
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: photo_maker_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_pass_2024
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

  # 后端API服务
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: photo_maker_backend
    restart: unless-stopped
    environment:
      FLASK_ENV: development
      MYSQL_HOST: mysql
      MYSQL_USER: photo_user
      MYSQL_PASSWORD: photo_pass_2024
      MYSQL_DB: photo_maker
      REDIS_HOST: redis
      REDIS_PASSWORD: redis_pass_2024
      SECRET_KEY: your_secret_key_here_2024
      JWT_SECRET_KEY: jwt_secret_key_here_2024
    depends_on:
      - mysql
      - redis
    ports:
      - "5000:5000"
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads
      - ./logs:/app/logs

  # Celery异步任务处理
  celery:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: photo_maker_celery
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=info --concurrency=4
    environment:
      FLASK_ENV: development
      MYSQL_HOST: mysql
      MYSQL_USER: photo_user
      MYSQL_PASSWORD: photo_pass_2024
      MYSQL_DB: photo_maker
      REDIS_HOST: redis
      REDIS_PASSWORD: redis_pass_2024
      SECRET_KEY: your_secret_key_here_2024
    depends_on:
      - mysql
      - redis
    volumes:
      - ./backend:/app
      - uploads_data:/app/uploads

  # Celery监控
  flower:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: photo_maker_flower
    restart: unless-stopped
    command: celery -A app.celery flower --port=5555
    environment:
      FLASK_ENV: development
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      REDIS_PASSWORD: redis_pass_2024
    depends_on:
      - redis
    ports:
      - "5555:5555"

  # 前端开发服务
  frontend:
    build: 
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: photo_maker_frontend
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      VUE_APP_API_BASE_URL: http://localhost:5000
    depends_on:
      - backend

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: photo_maker_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - uploads_data:/var/www/uploads
    depends_on:
      - frontend
      - backend

volumes:
  mysql_data:
  redis_data:
  uploads_data: 