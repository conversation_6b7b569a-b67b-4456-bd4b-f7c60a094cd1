from datetime import datetime
import json
from app import db

class UserLog(db.Model):
    """用户操作日志模型"""
    __tablename__ = 'user_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('users.id'), nullable=False, index=True)
    action = db.Column(db.String(100), nullable=False, index=True)  # 操作类型
    resource_type = db.Column(db.String(50))  # 资源类型
    resource_id = db.Column(db.Integer)  # 资源ID
    ip_address = db.Column(db.String(45))  # IP地址
    user_agent = db.Column(db.Text)  # 用户代理
    request_data = db.Column(db.JSON)  # 请求数据
    response_status = db.Column(db.Integer)  # 响应状态码
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    
    def __init__(self, **kwargs):
        super(UserLog, self).__init__(**kwargs)
    
    @classmethod
    def log_action(cls, user_id, action, **kwargs):
        """记录用户操作"""
        log = cls(
            user_id=user_id,
            action=action,
            **kwargs
        )
        db.session.add(log)
        return log
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'action': self.action,
            'resource_type': self.resource_type,
            'resource_id': self.resource_id,
            'ip_address': self.ip_address,
            'response_status': self.response_status,
            'created_at': self.created_at.isoformat()
        }
    
    def __repr__(self):
        return f'<UserLog {self.user_id}: {self.action}>' 