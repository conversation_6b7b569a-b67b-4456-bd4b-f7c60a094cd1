#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查数据库中的广告位数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models.ad_position import AdPosition
from app.database import db

def check_ad_positions():
    """检查广告位数据"""
    app = create_app()
    
    with app.app_context():
        print("🔍 检查数据库中的广告位数据...")
        
        # 查询所有广告位
        positions = AdPosition.query.all()
        
        print(f"找到 {len(positions)} 个广告位:")
        
        if positions:
            for position in positions:
                print(f"  - {position.name} ({position.code})")
                print(f"    类型: {position.position_type}")
                print(f"    尺寸: {position.width}×{position.height}")
                print(f"    启用: {position.is_enabled}")
                print(f"    显示: {position.is_visible}")
                print(f"    页面位置: {position.page_location}")
                print()
        else:
            print("  没有找到广告位数据")
            print("  需要运行初始化脚本: python scripts/init_ad_positions.py")

if __name__ == "__main__":
    check_ad_positions() 