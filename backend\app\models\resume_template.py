"""
简历模板模型
"""
from datetime import datetime
from typing import Dict, Any, Optional
from .base import db, BaseModel

class ResumeTemplate(BaseModel):
    """简历模板模型"""
    __tablename__ = 'resume_templates'
    
    # 基本信息
    name = db.Column(db.String(100), nullable=False, comment='模板名称')
    description = db.Column(db.String(500), comment='模板描述')
    thumb_url = db.Column(db.String(500), comment='缩略图URL')
    
    # 模板配置
    layout = db.Column(db.JSON, nullable=False, comment='布局配置')
    style = db.Column(db.JSON, nullable=False, comment='样式配置')
    sections = db.Column(db.JSON, nullable=False, comment='区块配置')
    
    # 分类信息
    category = db.Column(db.String(50), index=True, comment='模板分类')
    tags = db.Column(db.String(200), comment='标签，逗号分隔')
    
    # 使用统计
    use_count = db.Column(db.Integer, default=0, comment='使用次数')
    rating = db.Column(db.Float, default=0, comment='评分')
    
    # 控制字段
    is_active = db.Column(db.Boolean, default=True, comment='是否启用')
    sort_order = db.Column(db.Integer, default=0, comment='排序顺序')
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'thumb_url': self.thumb_url,
            'layout': self.layout,
            'style': self.style,
            'sections': self.sections,
            'category': self.category,
            'tags': self.tags.split(',') if self.tags else [],
            'use_count': self.use_count,
            'rating': self.rating,
            'is_active': self.is_active,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def create(cls, **kwargs) -> 'ResumeTemplate':
        """创建新模板"""
        template = cls(**kwargs)
        db.session.add(template)
        db.session.commit()
        return template
    
    def update(self, **kwargs) -> bool:
        """更新模板"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now()
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            return False
    
    def increment_use_count(self) -> bool:
        """增加使用次数"""
        try:
            self.use_count += 1
            db.session.commit()
            return True
        except Exception:
            db.session.rollback()
            return False
    
    def update_rating(self, new_rating: float) -> bool:
        """更新评分"""
        try:
            self.rating = (self.rating * self.use_count + new_rating) / (self.use_count + 1)
            db.session.commit()
            return True
        except Exception:
            db.session.rollback()
            return False
