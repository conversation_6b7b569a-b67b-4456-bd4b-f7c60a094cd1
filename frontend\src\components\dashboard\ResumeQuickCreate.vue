<template>
  <el-card class="quick-card">
    <div class="quick-header">
      <el-icon><User /></el-icon>
      <span>简历生成</span>
    </div>
    <el-button type="primary" @click="goToResume">新建简历</el-button>
    <div class="quick-desc">
      <el-alert type="info" show-icon :closable="false">
        支持多模板，AI优化内容，快速生成专业简历。
      </el-alert>
    </div>
  </el-card>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { User } from '@element-plus/icons-vue'
const router = useRouter()
const goToResume = () => router.push('/resumes')
</script>
<style scoped>
.quick-card { min-width: 320px; }
.quick-header { display: flex; align-items: center; gap: 8px; font-size: 18px; font-weight: bold; margin-bottom: 12px; }
.quick-desc { margin-top: 8px; }
</style> 