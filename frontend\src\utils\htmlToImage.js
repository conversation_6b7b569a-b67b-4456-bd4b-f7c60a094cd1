/**
 * HTML转图片工具函数
 * 使用html2canvas库将Vue组件转换为图片
 */

import html2canvas from 'html2canvas';

/**
 * 将DOM元素转换为图片
 * @param {HTMLElement} element - 要转换的DOM元素
 * @param {Object} options - 转换选项
 * @returns {Promise<Blob>} 返回图片blob
 */
export const elementToImage = async (element, options = {}) => {
  const defaultOptions = {
    scale: 2, // 提高清晰度
    useCORS: true, // 允许跨域图片
    allowTaint: true, // 允许污染画布
    backgroundColor: '#ffffff', // 背景色
    width: 900, // 名片标准宽度
    height: 540, // 名片标准高度
    ...options
  };

  try {
    const canvas = await html2canvas(element, defaultOptions);
    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob);
      }, 'image/png', 1.0);
    });
  } catch (error) {
    console.error('HTML转图片失败:', error);
    throw new Error('生成图片失败');
  }
};

/**
 * 下载图片
 * @param {Blob} blob - 图片blob
 * @param {string} filename - 文件名
 */
export const downloadImage = (blob, filename) => {
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

/**
 * 将Vue组件转换为图片并下载
 * @param {Object} componentInstance - Vue组件实例
 * @param {string} filename - 文件名
 * @param {Object} options - 转换选项
 */
export const componentToImageAndDownload = async (componentInstance, filename, options = {}) => {
  try {
    // 等待组件渲染完成
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 获取组件的DOM元素
    const element = componentInstance.$el;
    if (!element) {
      throw new Error('组件DOM元素不存在');
    }

    // 转换为图片
    const blob = await elementToImage(element, options);
    
    // 下载
    downloadImage(blob, filename);
    
    return blob;
  } catch (error) {
    console.error('组件转图片下载失败:', error);
    throw error;
  }
}; 