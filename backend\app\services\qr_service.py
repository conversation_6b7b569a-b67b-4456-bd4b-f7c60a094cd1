import os
import io
import uuid
import json
import base64
import qrcode
from PIL import Image, ImageDraw, ImageFont
from flask import current_app, url_for
import logging

logger = logging.getLogger(__name__)

class QRCodeService:
    """二维码生成服务"""
    
    def __init__(self):
        self.default_settings = {
            'version': None,  # 自动选择版本
            'error_correction': qrcode.constants.ERROR_CORRECT_L,  # 低错误纠正，提高可读性
            'box_size': 10,
            'border': 4,
            'fill_color': "black",
            'back_color': "white"
        }
    
    def generate_business_card_qr(self, business_card, custom_settings=None):
        """
        为名片生成二维码
        
        Args:
            business_card: BusinessCard对象
            custom_settings: 自定义设置
            
        Returns:
            dict: 包含二维码图片路径和base64数据的字典
        """
        try:
            # 准备二维码数据
            qr_data = self._prepare_qr_data(business_card)
            
            # 生成二维码
            qr_img = self._create_qr_code(qr_data, custom_settings)
            
            # 保存二维码图片
            file_path, file_url = self._save_qr_image(qr_img, business_card.user_id, business_card.id)
            
            # 生成base64数据
            base64_data = self._image_to_base64(qr_img)
            
            return {
                'file_path': file_path,
                'file_url': file_url,
                'base64_data': base64_data,
                'qr_data': qr_data,
                'format': 'json'
            }
            
        except Exception as e:
            logger.error(f"生成名片二维码失败: {str(e)}")
            raise
    
    def generate_vcard_qr(self, card_data, custom_settings=None):
        """
        生成vCard格式的二维码
        
        Args:
            card_data: 名片数据字典
            custom_settings: 自定义设置
            
        Returns:
            dict: 二维码结果
        """
        try:
            # 生成vCard格式数据
            vcard_data = self._create_vcard_data(card_data)
            
            # 生成二维码
            qr_img = self._create_qr_code(vcard_data, custom_settings)
            
            # 保存二维码图片（如果需要持久化）
            if hasattr(card_data, 'id') and hasattr(card_data, 'user_id'):
                file_path, file_url = self._save_qr_image(qr_img, card_data.user_id, card_data.id)
            else:
                file_path, file_url = None, None
            
            # 转换为base64
            base64_data = self._image_to_base64(qr_img)
            
            return {
                'base64_data': base64_data,
                'vcard_data': vcard_data,
                'format': 'vcard',
                'file_path': file_path,
                'file_url': file_url
            }
            
        except Exception as e:
            logger.error(f"生成vCard二维码失败: {str(e)}")
            raise
    
    def generate_url_qr(self, business_card_id, custom_settings=None):
        """
        生成指向名片查看页面的URL二维码
        
        Args:
            business_card_id: 名片ID
            custom_settings: 自定义设置
            
        Returns:
            dict: 二维码结果
        """
        try:
            # 构建查看URL
            view_url = f"{current_app.config.get('FRONTEND_URL', 'http://localhost:8097')}/business-card/preview/{business_card_id}"
            
            logger.info(f"生成URL二维码: {view_url}")
            
            # 生成二维码
            qr_img = self._create_qr_code(view_url, custom_settings)
            
            # 转换为base64
            base64_data = self._image_to_base64(qr_img)
            
            return {
                'base64_data': base64_data,
                'url': view_url,
                'format': 'url',
                'file_path': None,
                'file_url': None
            }
            
        except Exception as e:
            logger.error(f"生成URL二维码失败: {str(e)}")
            raise
    
    def _prepare_qr_data(self, business_card):
        """准备二维码数据"""
        # 提取名片基本信息
        card_data = business_card.card_data or {}
        
        # 构建JSON格式的名片数据
        qr_data = {
            'type': 'business_card',
            'id': business_card.id,
            'name': card_data.get('name', ''),
            'title': card_data.get('title', ''),
            'company': card_data.get('company', ''),
            'phone': card_data.get('phone', ''),
            'email': card_data.get('email', ''),
            'address': card_data.get('address', ''),
            'website': card_data.get('website', ''),
            'view_url': f"{current_app.config.get('FRONTEND_URL', 'http://localhost:8097')}/business-card/preview/{business_card.id}"
        }
        
        result = json.dumps(qr_data, ensure_ascii=False, sort_keys=True)
        logger.info(f"准备二维码数据: {result[:100]}...")
        return result
    
    def _create_vcard_data(self, card_data):
        """创建vCard格式数据"""
        vcard_lines = [
            "BEGIN:VCARD",
            "VERSION:3.0"
        ]
        
        # 姓名
        if card_data.get('name'):
            vcard_lines.append(f"FN:{card_data['name']}")
            # 分解姓名（简单处理）
            name_parts = card_data['name'].split()
            if len(name_parts) >= 2:
                vcard_lines.append(f"N:{name_parts[-1]};{' '.join(name_parts[:-1])};;;")
            else:
                vcard_lines.append(f"N:{card_data['name']};;;;;")
        
        # 职位和公司
        if card_data.get('title') and card_data.get('company'):
            vcard_lines.append(f"ORG:{card_data['company']}")
            vcard_lines.append(f"TITLE:{card_data['title']}")
        
        # 电话
        if card_data.get('phone'):
            vcard_lines.append(f"TEL;TYPE=WORK,VOICE:{card_data['phone']}")
        
        # 邮箱
        if card_data.get('email'):
            vcard_lines.append(f"EMAIL;TYPE=WORK:{card_data['email']}")
        
        # 地址
        if card_data.get('address'):
            vcard_lines.append(f"ADR;TYPE=WORK:;;{card_data['address']};;;;")
        
        # 网站
        if card_data.get('website'):
            vcard_lines.append(f"URL:{card_data['website']}")
        
        vcard_lines.append("END:VCARD")
        
        result = "\n".join(vcard_lines)
        logger.info(f"创建vCard数据: {result[:100]}...")
        return result
    
    def _create_qr_code(self, data, custom_settings=None):
        """创建二维码图像"""
        # 合并设置
        settings = self.default_settings.copy()
        if custom_settings:
            # 处理前端传递的样式配置
            if 'size' in custom_settings:
                # 根据size调整box_size，确保二维码足够大
                size = custom_settings['size']
                settings['box_size'] = max(8, size // 20)  # 确保box_size至少为8
            if 'fill_color' in custom_settings:
                settings['fill_color'] = custom_settings['fill_color']
            if 'back_color' in custom_settings:
                settings['back_color'] = custom_settings['back_color']
            if 'border' in custom_settings:
                settings['border'] = custom_settings['border']
        
        # 创建二维码对象
        qr = qrcode.QRCode(
            version=settings['version'],
            error_correction=settings['error_correction'],
            box_size=settings['box_size'],
            border=settings['border'],
        )
        
        # 添加数据
        qr.add_data(data)
        qr.make(fit=True)
        
        # 记录二维码信息
        logger.info(f"二维码生成信息: 版本={qr.version}, 大小={qr.modules_count}x{qr.modules_count}, 数据长度={len(data)}")
        
        # 生成图像
        qr_img = qr.make_image(
            fill_color=settings['fill_color'],
            back_color=settings['back_color']
        )
        
        return qr_img
    
    def _save_qr_image(self, qr_img, user_id, business_card_id):
        """保存二维码图片"""
        try:
            # 生成文件名 - 使用固定的命名方式，避免每次生成不同文件名
            filename = f"qr_business_card_{business_card_id}.png"
            
            # 创建保存目录
            qr_dir = os.path.join(current_app.config['STATIC_FOLDER'], 'qr_codes', str(user_id))
            os.makedirs(qr_dir, exist_ok=True)
            
            # 保存文件
            file_path = os.path.join(qr_dir, filename)
            qr_img.save(file_path)
            
            # 生成访问URL
            file_url = f"/static/qr_codes/{user_id}/{filename}"
            
            logger.info(f"二维码图片已保存: {file_path}")
            return file_path, file_url
            
        except Exception as e:
            logger.error(f"保存二维码图片失败: {str(e)}")
            raise
    
    def _image_to_base64(self, img):
        """将PIL图像转换为base64字符串"""
        try:
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            img_bytes = buffer.getvalue()
            buffer.close()
            
            base64_string = base64.b64encode(img_bytes).decode('utf-8')
            result = f"data:image/png;base64,{base64_string}"
            
            logger.info(f"二维码已转换为base64，长度: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"转换base64失败: {str(e)}")
            raise
    
    def create_styled_qr(self, data, style_config=None):
        """
        创建带样式的二维码
        
        Args:
            data: 二维码数据
            style_config: 样式配置
            
        Returns:
            PIL Image对象
        """
        try:
            # 默认样式配置
            default_style = {
                'size': 200,
                'border': 4,
                'fill_color': 'black',
                'back_color': 'white',
                'logo': None,  # Logo图片路径
                'logo_size_ratio': 0.2  # Logo占二维码的比例
            }
            
            if style_config:
                default_style.update(style_config)
            
            # 创建二维码
            qr = qrcode.QRCode(
                version=None,  # 自动选择版本
                error_correction=qrcode.constants.ERROR_CORRECT_L,  # 低错误纠正，提高可读性
                box_size=10,
                border=default_style['border'],
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # 生成二维码图像
            qr_img = qr.make_image(
                fill_color=default_style['fill_color'],
                back_color=default_style['back_color']
            ).convert('RGB')
            
            # 调整大小
            qr_img = qr_img.resize((default_style['size'], default_style['size']), Image.Resampling.LANCZOS)
            
            # 添加Logo（如果有）
            if default_style.get('logo') and os.path.exists(default_style['logo']):
                qr_img = self._add_logo_to_qr(qr_img, default_style['logo'], default_style['logo_size_ratio'])
            
            return qr_img
            
        except Exception as e:
            logger.error(f"创建样式化二维码失败: {str(e)}")
            raise
    
    def _add_logo_to_qr(self, qr_img, logo_path, logo_size_ratio):
        """为二维码添加Logo"""
        try:
            # 打开Logo图片
            logo = Image.open(logo_path)
            
            # 计算Logo大小
            qr_size = qr_img.size[0]
            logo_size = int(qr_size * logo_size_ratio)
            
            # 调整Logo大小
            logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
            
            # 如果Logo有透明度，创建白色背景
            if logo.mode in ('RGBA', 'LA'):
                # 创建白色背景
                background = Image.new('RGB', logo.size, 'white')
                if logo.mode == 'RGBA':
                    background.paste(logo, mask=logo.split()[-1])  # 使用alpha通道作为mask
                else:
                    background.paste(logo, mask=logo.split()[-1])
                logo = background
            
            # 计算Logo位置（居中）
            logo_pos = ((qr_size - logo_size) // 2, (qr_size - logo_size) // 2)
            
            # 将Logo粘贴到二维码上
            qr_img.paste(logo, logo_pos)
            
            return qr_img
            
        except Exception as e:
            logger.error(f"添加Logo到二维码失败: {str(e)}")
            return qr_img  # 返回原二维码 