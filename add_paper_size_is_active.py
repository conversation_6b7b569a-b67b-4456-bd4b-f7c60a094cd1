#!/usr/bin/env python3
"""
添加PaperSize模型的is_active字段
"""
import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)
os.chdir(backend_dir)

try:
    from app import create_app, db
    from app.models.paper_size import PaperSize
    
    def add_is_active_field():
        """添加is_active字段到paper_sizes表"""
        app = create_app('development')
        
        with app.app_context():
            try:
                # 检查is_active字段是否已存在
                inspector = db.inspect(db.engine)
                columns = [col['name'] for col in inspector.get_columns('paper_sizes')]
                
                if 'is_active' not in columns:
                    print("正在添加is_active字段到paper_sizes表...")
                    
                    # 添加is_active字段
                    db.engine.execute('ALTER TABLE paper_sizes ADD COLUMN is_active BOOLEAN DEFAULT TRUE')
                    
                    # 更新所有现有记录为活跃状态
                    db.engine.execute('UPDATE paper_sizes SET is_active = TRUE WHERE is_active IS NULL')
                    
                    print("✅ is_active字段添加成功")
                else:
                    print("✅ is_active字段已存在")
                
                # 验证字段是否正确添加
                paper_sizes = PaperSize.query.all()
                print(f"✅ 找到 {len(paper_sizes)} 个相纸规格")
                
                for paper in paper_sizes:
                    print(f"   - {paper.name}: {paper.width_mm}x{paper.height_mm}mm, is_active={getattr(paper, 'is_active', 'N/A')}")
                
                return True
                
            except Exception as e:
                print(f"❌ 添加字段失败: {e}")
                return False
    
    if __name__ == '__main__':
        add_is_active_field()
        
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
