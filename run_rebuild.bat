@echo off
chcp 65001 >nul
echo 🔄 开始自动重构智能证件照系统...
echo.
echo ⚠️  警告：此操作将删除现有数据库和部分文件
echo 📦 现有重要文件将备份到 backup 目录
echo.
set /p confirm="确认继续？(y/N): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 🚀 开始重构...
python rebuild_system.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 重构完成！
    echo 🌐 启动命令: python run_system.py
    echo 👤 管理员账号: admin/admin123
    echo.
    set /p start="是否立即启动系统？(y/N): "
    if /i "%start%"=="y" (
        python run_system.py
    )
) else (
    echo ❌ 重构失败，请检查错误信息
)

pause