:root {
  --background: oklch(0.95 0.05 250);
  --foreground: oklch(0.15 0 0);
  --card: oklch(0.98 0 0);
  --card-foreground: oklch(0.15 0 0);
  --popover: oklch(0.95 0.05 250);
  --popover-foreground: oklch(0.15 0 0);
  --primary: oklch(0.65 0.2 25);
  --primary-foreground: oklch(0.98 0 0);
  --secondary: oklch(0.85 0.1 250);
  --secondary-foreground: oklch(0.15 0 0);
  --muted: oklch(0.9 0.05 250);
  --muted-foreground: oklch(0.6 0 0);
  --accent: oklch(0.75 0.15 25);
  --accent-foreground: oklch(0.98 0 0);
  --destructive: oklch(0.55 0.2 25);
  --destructive-foreground: oklch(0.98 0 0);
  --border: oklch(0.8 0.05 250);
  --input: oklch(0.9 0.05 250);
  --ring: oklch(0.65 0.2 25);
  --chart-1: oklch(0.65 0.2 25);
  --chart-2: oklch(0.75 0.15 25);
  --chart-3: oklch(0.85 0.1 250);
  --chart-4: oklch(0.9 0.05 250);
  --chart-5: oklch(0.95 0.05 250);
  --sidebar: oklch(0.9 0.05 250);
  --sidebar-foreground: oklch(0.15 0 0);
  --sidebar-primary: oklch(0.65 0.2 25);
  --sidebar-primary-foreground: oklch(0.98 0 0);
  --sidebar-accent: oklch(0.75 0.15 25);
  --sidebar-accent-foreground: oklch(0.98 0 0);
  --sidebar-border: oklch(0.8 0.05 250);
  --sidebar-ring: oklch(0.65 0.2 25);
  --font-sans: 'Outfit', sans-serif;
  --font-serif: 'Playfair Display', serif;
  --font-mono: 'Space Mono', monospace;
  --radius: 1rem;
  --shadow-2xs: 0 1px 2px 0 hsl(210 10% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0 hsl(210 10% 0% / 0.1);
  --shadow-sm: 0 2px 4px 0 hsl(210 10% 0% / 0.1), 0 1px 2px -1px hsl(210 10% 0% / 0.1);
  --shadow: 0 4px 6px -1px hsl(210 10% 0% / 0.1), 0 2px 4px -2px hsl(210 10% 0% / 0.1);
  --shadow-md: 0 6px 8px -1px hsl(210 10% 0% / 0.1), 0 4px 6px -2px hsl(210 10% 0% / 0.1);
  --shadow-lg: 0 10px 15px -3px hsl(210 10% 0% / 0.1), 0 4px 6px -4px hsl(210 10% 0% / 0.1);
  --shadow-xl: 0 20px 25px -5px hsl(210 10% 0% / 0.1), 0 8px 10px -6px hsl(210 10% 0% / 0.1);
  --shadow-2xl: 0 25px 50px -12px hsl(210 10% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.5rem;
}