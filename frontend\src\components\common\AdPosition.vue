<template>
  <div 
    v-if="shouldShow" 
    :class="['ad-position', position.css_class]"
    :style="adStyle"
  >
    <!-- 广告内容 -->
    <div class="ad-content">
      <!-- 百度广告 -->
      <div v-if="showBaiduAd" class="baidu-ad">
        <div class="ad-placeholder">
          <div class="ad-info">
            <span class="ad-label">百度广告</span>
            <span class="ad-size">{{ position.width }}×{{ position.height }}</span>
          </div>
          <div class="ad-mock">
            <div class="ad-mock-content">
              <div class="ad-mock-title">广告标题</div>
              <div class="ad-mock-desc">广告描述信息</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 头条广告 -->
      <div v-else-if="showToutiaoAd" class="toutiao-ad">
        <div class="ad-placeholder">
          <div class="ad-info">
            <span class="ad-label">头条广告</span>
            <span class="ad-size">{{ position.width }}×{{ position.height }}</span>
          </div>
          <div class="ad-mock">
            <div class="ad-mock-content">
              <div class="ad-mock-title">广告标题</div>
              <div class="ad-mock-desc">广告描述信息</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- AdMob广告 -->
      <div v-else-if="showAdMobAd" class="admob-ad">
        <div class="ad-placeholder">
          <div class="ad-info">
            <span class="ad-label">AdMob广告</span>
            <span class="ad-size">{{ position.width }}×{{ position.height }}</span>
          </div>
          <div class="ad-mock">
            <div class="ad-mock-content">
              <div class="ad-mock-title">广告标题</div>
              <div class="ad-mock-desc">广告描述信息</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 默认广告占位 -->
      <div v-else class="default-ad">
        <div class="ad-placeholder">
          <div class="ad-info">
            <span class="ad-label">广告位</span>
            <span class="ad-size">{{ position.width }}×{{ position.height }}</span>
          </div>
          <div class="ad-mock">
            <div class="ad-mock-content">
              <div class="ad-mock-title">广告位</div>
              <div class="ad-mock-desc">{{ position.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { apiEndpoints } from '@/api'

const props = defineProps({
  positionCode: {
    type: String,
    required: true
  },
  pageLocation: {
    type: String,
    default: ''
  }
})

// 响应式数据
const position = ref(null)
const adsConfig = ref({})
const loading = ref(true)
const screenWidth = ref(window.innerWidth)

// 监听窗口大小变化
const handleResize = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // 加载数据
  Promise.all([
    loadPosition(),
    loadAdsConfig()
  ])
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 计算属性
const shouldShow = computed(() => {
  return position.value && 
         position.value.is_enabled && 
         position.value.is_visible
})

const adStyle = computed(() => {
  if (!position.value) return {}
  
  // 根据屏幕宽度调整广告位尺寸
  let width = position.value.width
  let height = position.value.height
  
  if (screenWidth.value <= 768) {
    // 移动端：使用较小的尺寸
    if (position.value.position_type === 'banner') {
      width = Math.min(screenWidth.value - 32, 320) // 320px或屏幕宽度减去边距
      height = Math.round((height / position.value.width) * width) // 保持比例
    } else if (position.value.position_type === 'sidebar') {
      width = Math.min(screenWidth.value - 32, 300)
      height = Math.round((height / position.value.width) * width)
    }
  } else if (screenWidth.value <= 1024) {
    // 平板端：适当缩小
    width = Math.min(screenWidth.value - 64, position.value.width)
    height = Math.round((height / position.value.width) * width)
  }
  
  return {
    width: `${width}px`,
    height: `${height}px`,
    maxWidth: '100%'
  }
})

const showBaiduAd = computed(() => {
  return adsConfig.value.baidu_enabled && 
         adsConfig.value.baidu_appid && 
         adsConfig.value.baidu_ad_unit_id
})

const showToutiaoAd = computed(() => {
  return adsConfig.value.toutiao_enabled && 
         adsConfig.value.toutiao_appid && 
         adsConfig.value.toutiao_ad_unit_id
})

const showAdMobAd = computed(() => {
  return adsConfig.value.admob_enabled && 
         adsConfig.value.admob_app_id && 
         adsConfig.value.admob_ad_unit_id
})

// 方法
const loadPosition = async () => {
  try {
    const response = await apiEndpoints.common.getAdPosition(props.positionCode)
    position.value = response.data
  } catch (error) {
    console.error('加载广告位失败:', error)
    // 如果获取失败，使用默认配置
    position.value = {
      name: '广告位',
      code: props.positionCode,
      width: 728,
      height: 90,
      is_enabled: true,
      is_visible: true,
      css_class: `ad-${props.positionCode}`
    }
  }
}

const loadAdsConfig = async () => {
  try {
    const response = await apiEndpoints.common.getAdsConfig()
    adsConfig.value = response.data
  } catch (error) {
    console.error('加载广告配置失败:', error)
  } finally {
    loading.value = false
  }
}

</script>

<style scoped>
.ad-position {
  margin: 16px 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.ad-content {
  width: 100%;
  height: 100%;
}

.ad-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
}

.ad-info {
  position: absolute;
  top: 4px;
  left: 8px;
  display: flex;
  gap: 8px;
  font-size: 10px;
  opacity: 0.8;
}

.ad-label {
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 4px;
}

.ad-size {
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 4px;
}

.ad-mock {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

.ad-mock-content {
  text-align: center;
}

.ad-mock-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.ad-mock-desc {
  font-size: 14px;
  opacity: 0.9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ad-position {
    margin: 12px 0;
  }
  
  .ad-mock-title {
    font-size: 16px;
  }
  
  .ad-mock-desc {
    font-size: 12px;
  }
}

/* 特定广告位样式 */
.ad-home-top-banner {
  margin: 0 0 24px 0;
}

.ad-home-sidebar {
  margin: 16px 0;
}

.ad-profile-top {
  margin: 0 0 24px 0;
}

.ad-photo-editor-sidebar {
  margin: 16px 0;
}

.ad-resume-editor-bottom {
  margin: 24px 0 0 0;
}

.ad-business-card-sidebar {
  margin: 16px 0;
}

.ad-document-manager-top {
  margin: 0 0 24px 0;
}

.ad-credits-sidebar {
  margin: 16px 0;
}

.ad-help-bottom {
  margin: 24px 0 0 0;
}
</style> 