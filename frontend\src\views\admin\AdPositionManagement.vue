<template>
  <div class="ad-position-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">广告位管理</h2>
        <p class="page-description">管理系统中的广告位，控制广告显示位置和状态</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          新增广告位
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.total }}</div>
                <div class="stat-label">总广告位</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">✅</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.enabled }}</div>
                <div class="stat-label">已启用</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">👁️</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.visible }}</div>
                <div class="stat-label">显示中</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">🚫</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.hidden }}</div>
                <div class="stat-label">已隐藏</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 广告位列表 -->
    <el-card class="list-card">
      <template #header>
        <div class="card-header">
          <span>广告位列表</span>
          <div class="header-actions">
            <el-button size="small" @click="loadPositions" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table 
        :data="positions" 
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="name" label="广告位名称" min-width="150">
          <template #default="{ row }">
            <div class="position-name">
              <span class="name">{{ row.name }}</span>
              <el-tag size="small" :type="getPositionTypeColor(row.position_type)">
                {{ getPositionTypeLabel(row.position_type) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="代码" width="120" />
        <el-table-column prop="page_location" label="页面位置" width="120" />
        <el-table-column label="尺寸" width="100">
          <template #default="{ row }">
            {{ row.width }}×{{ row.height }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="row.is_enabled ? 'success' : 'danger'" size="small">
              {{ row.is_enabled ? '已启用' : '已禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="显示" width="100">
          <template #default="{ row }">
            <el-switch 
              v-model="row.is_visible" 
              @change="updatePositionVisibility(row)"
              :loading="row.updating"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editPosition(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="deletePosition(row)"
              :loading="row.deleting"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedPositions.length > 0">
        <el-button size="small" @click="batchEnable">
          <el-icon><Check /></el-icon>
          批量启用
        </el-button>
        <el-button size="small" @click="batchDisable">
          <el-icon><Close /></el-icon>
          批量禁用
        </el-button>
        <el-button size="small" @click="batchShow">
          <el-icon><View /></el-icon>
          批量显示
        </el-button>
        <el-button size="small" @click="batchHide">
          <el-icon><Hide /></el-icon>
          批量隐藏
        </el-button>
        <el-button size="small" type="danger" @click="batchDelete">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑广告位' : '新增广告位'"
      width="600px"
    >
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="广告位名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入广告位名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="广告位代码" prop="code">
              <el-input v-model="form.code" placeholder="请输入广告位代码" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="广告位描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入广告位描述"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="广告位类型" prop="position_type">
              <el-select v-model="form.position_type" placeholder="请选择广告位类型">
                <el-option label="横幅广告" value="banner" />
                <el-option label="侧边栏广告" value="sidebar" />
                <el-option label="弹窗广告" value="popup" />
                <el-option label="内容广告" value="content" />
                <el-option label="底部广告" value="footer" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="页面位置" prop="page_location">
              <el-select v-model="form.page_location" placeholder="请选择页面位置">
                <el-option label="首页" value="home" />
                <el-option label="用户中心" value="profile" />
                <el-option label="证件照编辑" value="photo_editor" />
                <el-option label="简历编辑" value="resume_editor" />
                <el-option label="名片编辑" value="business_card" />
                <el-option label="文档管理" value="document_manager" />
                <el-option label="积分中心" value="credits" />
                <el-option label="帮助页面" value="help" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="宽度(px)" prop="width">
              <el-input-number v-model="form.width" :min="1" :max="2000" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="高度(px)" prop="height">
              <el-input-number v-model="form.height" :min="1" :max="2000" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="CSS类名" prop="css_class">
              <el-input v-model="form.css_class" placeholder="请输入CSS类名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number v-model="form.sort_order" :min="0" :max="999" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否启用">
              <el-switch v-model="form.is_enabled" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否显示">
              <el-switch v-model="form.is_visible" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="savePosition" :loading="saving">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Edit, Delete, Refresh, Check, Close, View, Hide 
} from '@element-plus/icons-vue'
import { apiEndpoints } from '@/api'

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const selectedPositions = ref([])

const stats = reactive({
  total: 0,
  enabled: 0,
  visible: 0,
  hidden: 0
})

const positions = ref([])

const form = reactive({
  name: '',
  code: '',
  description: '',
  position_type: 'banner',
  width: 728,
  height: 90,
  is_enabled: true,
  is_visible: true,
  page_location: 'home',
  css_class: '',
  sort_order: 0
})

const rules = {
  name: [
    { required: true, message: '请输入广告位名称', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入广告位代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '代码只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  position_type: [
    { required: true, message: '请选择广告位类型', trigger: 'change' }
  ],
  page_location: [
    { required: true, message: '请选择页面位置', trigger: 'change' }
  ]
}

// 方法
const loadStats = async () => {
  try {
    const response = await apiEndpoints.admin.ads.getPositionStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计失败:', error)
  }
}

const loadPositions = async () => {
  loading.value = true
  try {
    const response = await apiEndpoints.admin.ads.getPositions()
    positions.value = response.data
  } catch (error) {
    ElMessage.error('加载广告位列表失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const editPosition = (position) => {
  isEdit.value = true
  Object.assign(form, position)
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    name: '',
    code: '',
    description: '',
    position_type: 'banner',
    width: 728,
    height: 90,
    is_enabled: true,
    is_visible: true,
    page_location: 'home',
    css_class: '',
    sort_order: 0
  })
}

const savePosition = async () => {
  saving.value = true
  try {
    if (isEdit.value) {
      // 只传递允许更新的字段
      const updateData = {
        name: form.name,
        code: form.code,
        description: form.description,
        position_type: form.position_type,
        width: form.width,
        height: form.height,
        is_enabled: form.is_enabled,
        is_visible: form.is_visible,
        page_location: form.page_location,
        css_class: form.css_class,
        sort_order: form.sort_order
      }
      await apiEndpoints.admin.ads.updatePosition(form.id, updateData)
      ElMessage.success('广告位更新成功')
    } else {
      await apiEndpoints.admin.ads.createPosition(form)
      ElMessage.success('广告位创建成功')
    }
    dialogVisible.value = false
    loadPositions()
    loadStats()
  } catch (error) {
    ElMessage.error(error.response?.data?.error || '操作失败')
  } finally {
    saving.value = false
  }
}

const deletePosition = async (position) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除广告位"${position.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    position.deleting = true
    await apiEndpoints.admin.ads.deletePosition(position.id)
    ElMessage.success('删除成功')
    loadPositions()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  } finally {
    position.deleting = false
  }
}

const updatePositionVisibility = async (position) => {
  try {
    position.updating = true
    await apiEndpoints.admin.ads.updatePosition(position.id, {
      is_visible: position.is_visible
    })
    ElMessage.success('更新成功')
    loadStats()
  } catch (error) {
    position.is_visible = !position.is_visible // 恢复原状态
    ElMessage.error('更新失败')
  } finally {
    position.updating = false
  }
}

const handleSelectionChange = (selection) => {
  selectedPositions.value = selection
}

const batchEnable = async () => {
  await batchUpdate({ is_enabled: true })
}

const batchDisable = async () => {
  await batchUpdate({ is_enabled: false })
}

const batchShow = async () => {
  await batchUpdate({ is_visible: true })
}

const batchHide = async () => {
  await batchUpdate({ is_visible: false })
}

const batchUpdate = async (updates) => {
  try {
    const positionIds = selectedPositions.value.map(p => p.id)
    await apiEndpoints.admin.ads.batchUpdatePositions({
      position_ids: positionIds,
      updates: updates
    })
    ElMessage.success('批量更新成功')
    loadPositions()
    loadStats()
  } catch (error) {
    ElMessage.error('批量更新失败')
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedPositions.value.length} 个广告位吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    for (const position of selectedPositions.value) {
      await apiEndpoints.admin.ads.deletePosition(position.id)
    }
    
    ElMessage.success('批量删除成功')
    loadPositions()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const getPositionTypeLabel = (type) => {
  const typeMap = {
    'banner': '横幅',
    'sidebar': '侧边栏',
    'popup': '弹窗',
    'content': '内容',
    'footer': '底部'
  }
  return typeMap[type] || type
}

const getPositionTypeColor = (type) => {
  const colorMap = {
    'banner': 'primary',
    'sidebar': 'success',
    'popup': 'warning',
    'content': 'info',
    'footer': 'danger'
  }
  return colorMap[type] || 'info'
}

onMounted(() => {
  loadPositions()
  loadStats()
})
</script>

<style scoped>
.ad-position-management {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  margin-bottom: 0;
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

.list-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.position-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.position-name .name {
  font-weight: 500;
}

.batch-actions {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 