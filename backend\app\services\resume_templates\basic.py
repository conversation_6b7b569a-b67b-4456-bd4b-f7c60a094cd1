"""基础简历模板定义

此模块定义了系统默认的简历模板，包括布局、样式和结构定义。
"""

from typing import Dict, Any

def get_basic_template() -> Dict[str, Any]:
    """获取基础简历模板定义"""
    return {
        "id": "basic",
        "name": "基础简历模板",
        "description": "清晰简洁的基础简历模板，适合大多数求职场景",
        "preview_image": "templates/basic/preview.png",
        "category": "general",
        "styles": {
            "font_family": "Microsoft YaHei, sans-serif",
            "base_font_size": "14px",
            "primary_color": "#2c3e50",
            "secondary_color": "#7f8c8d",
            "heading_color": "#2c3e50",
            "link_color": "#3498db",
            "section_spacing": "2rem",
            "line_height": "1.6"
        },
        "layout": {
            "header": {
                "type": "flex",
                "style": {
                    "justify_content": "space-between",
                    "align_items": "center",
                    "padding": "2rem",
                    "background": "#f8f9fa"
                }
            },
            "sidebar": {
                "width": "30%",
                "background": "#f8f9fa",
                "padding": "2rem"
            },
            "main": {
                "width": "70%",
                "padding": "2rem"
            }
        },
        "sections": {
            "header": {
                "order": 1,
                "fields": ["full_name", "title", "contact_info"],
                "styles": {
                    "name_size": "2rem",
                    "title_size": "1.5rem"
                }
            },
            "summary": {
                "order": 2,
                "title": "个人总结",
                "styles": {
                    "font_size": "1rem",
                    "line_height": "1.8"
                }
            },
            "work_experience": {
                "order": 3,
                "title": "工作经历",
                "item_layout": "timeline",
                "styles": {
                    "company_size": "1.2rem",
                    "position_size": "1.1rem",
                    "date_color": "#666"
                }
            },
            "education": {
                "order": 4,
                "title": "教育背景",
                "item_layout": "card",
                "styles": {
                    "school_size": "1.2rem",
                    "degree_size": "1.1rem"
                }
            },
            "projects": {
                "order": 5,
                "title": "项目经验",
                "item_layout": "card",
                "styles": {
                    "title_size": "1.2rem",
                    "tech_stack_style": "tag"
                }
            },
            "skills": {
                "order": 6,
                "title": "技能特长",
                "layout": "tag_cloud",
                "styles": {
                    "tag_background": "#f0f2f5",
                    "tag_color": "#2c3e50"
                }
            },
            "certifications": {
                "order": 7,
                "title": "证书资质",
                "layout": "list",
                "styles": {
                    "item_spacing": "1rem"
                }
            }
        },
        "export_settings": {
            "pdf": {
                "page_size": "A4",
                "margin": "2cm",
                "font_settings": {
                    "enable_custom_fonts": True,
                    "font_paths": [
                        "static/fonts/microsoft_yahei.ttf"
                    ]
                }
            },
            "docx": {
                "template_path": "templates/basic/template.docx",
                "styles": {
                    "normal": {
                        "font": "Microsoft YaHei",
                        "size": 28
                    },
                    "heading1": {
                        "font": "Microsoft YaHei",
                        "size": 36,
                        "bold": True
                    }
                }
            }
        }
    }

def get_creative_sidebar_template() -> Dict[str, Any]:
    """获取创意左侧栏简历模板定义"""
    return {
        "id": "creative_sidebar",
        "name": "创意左侧栏模板",
        "description": "创意设计，左侧栏放置照片和基本信息，右侧展示详细经历",
        "preview_image": "templates/creative_sidebar/preview.png",
        "category": "creative",
        "styles": {
            "font_family": "PingFang SC, Microsoft YaHei, sans-serif",
            "base_font_size": "14px",
            "primary_color": "#2c3e50",
            "secondary_color": "#7f8c8d",
            "accent_color": "#e74c3c",
            "heading_color": "#2c3e50",
            "link_color": "#3498db",
            "sidebar_bg": "#34495e",
            "sidebar_text": "#ffffff",
            "section_spacing": "1.5rem",
            "line_height": "1.6"
        },
        "layout": {
            "type": "two_column",
            "sidebar": {
                "width": "35%",
                "position": "left",
                "background": "var(--sidebar-bg)",
                "color": "var(--sidebar-text)",
                "padding": "2rem"
            },
            "main": {
                "width": "65%",
                "padding": "2rem"
            }
        }
    }

def get_creative_icon_template() -> Dict[str, Any]:
    """获取创意图标简历模板定义"""
    return {
        "id": "creative_icon",
        "name": "创意图标模板",
        "description": "使用精美图标突出各个信息板块，现代感强",
        "preview_image": "templates/creative_icon/preview.png",
        "category": "creative",
        "styles": {
            "font_family": "PingFang SC, Microsoft YaHei, sans-serif",
            "base_font_size": "14px",
            "primary_color": "#2c3e50",
            "secondary_color": "#7f8c8d",
            "accent_color": "#e74c3c",
            "heading_color": "#2c3e50",
            "link_color": "#3498db",
            "icon_color": "#3498db",
            "section_spacing": "2rem",
            "line_height": "1.6"
        }
    }

def get_professional_timeline_template() -> Dict[str, Any]:
    """获取专业时间轴简历模板定义"""
    return {
        "id": "professional_timeline",
        "name": "专业时间轴模板",
        "description": "以时间轴形式展现职业和教育经历，突出发展历程",
        "preview_image": "templates/professional_timeline/preview.png",
        "category": "professional",
        "styles": {
            "font_family": "PingFang SC, Microsoft YaHei, sans-serif",
            "base_font_size": "14px",
            "primary_color": "#2c3e50",
            "secondary_color": "#7f8c8d",
            "timeline_color": "#3498db",
            "heading_color": "#2c3e50",
            "link_color": "#3498db",
            "section_spacing": "2rem",
            "line_height": "1.6"
        }
    }

def get_modern_two_column_template() -> Dict[str, Any]:
    """获取现代双栏简历模板定义"""
    return {
        "id": "modern_two_column",
        "name": "现代双栏模板",
        "description": "现代简约的双栏布局，突出重要信息",
        "preview_image": "templates/modern_two_column/preview.png",
        "category": "modern",
        "styles": {
            "font_family": "PingFang SC, Microsoft YaHei, sans-serif",
            "base_font_size": "14px",
            "primary_color": "#2c3e50",
            "secondary_color": "#7f8c8d",
            "heading_color": "#2c3e50",
            "link_color": "#3498db",
            "section_spacing": "2rem",
            "line_height": "1.6"
        }
    } 