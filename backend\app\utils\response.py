from flask import jsonify

def success_response(message="Success", data=None):
    """Generate a success response"""
    response = {
        'success': True,
        'message': message
    }
    if data is not None:
        response['data'] = data
    return jsonify(response)

def error_response(message, status_code=400):
    """Generate an error response"""
    response = jsonify({
        'success': False,
        'message': message
    })
    response.status_code = status_code
    return response 