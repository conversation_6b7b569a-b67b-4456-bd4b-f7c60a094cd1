<template>
  <div class="resume-manager">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">📝 简历管理</h1>
        <p class="page-description">智能简历制作平台，支持多种模板和专业排版，助您打造完美简历</p>
      </div>
      <div class="header-actions">
        <button class="create-btn primary" @click="createResume">
          <span class="btn-icon">✨</span>
          <span class="btn-text">新建简历</span>
        </button>
      </div>
    </div>

    <div class="resume-content" v-loading="loading">
      <div v-if="resumes.length === 0 && !loading" class="empty-state">
        <div class="empty-icon">📄</div>
        <h3 class="empty-title">暂无简历</h3>
        <p class="empty-description">创建您的第一份专业简历，开启职业新篇章</p>
        <button class="create-btn primary large" @click="createResume">
          <span class="btn-icon">✨</span>
          <span class="btn-text">创建第一份简历</span>
        </button>
      </div>
      
      <div v-else class="resume-grid">
        <div v-for="resume in resumes" :key="resume.id" class="resume-card">
          <div class="card-header">
            <div class="resume-icon">📋</div>
            <div class="resume-info">
              <h3 class="resume-title">{{ resume.title }}</h3>
              <p class="resume-owner">{{ resume.full_name }}</p>
              <div class="resume-meta">
                <span class="meta-item">
                  <span class="meta-icon">📅</span>
                  <span class="meta-text">{{ formatDate(resume.updated_at || resume.created_at) }}</span>
                </span>
              </div>
            </div>
          </div>
          
          <div class="card-actions">
            <button class="action-btn edit" @click="editResume(resume.id)" title="编辑简历">
              <span class="btn-icon">✏️</span>
            </button>
            <button class="action-btn copy" @click="copyResume(resume)" title="复制简历">
              <span class="btn-icon">📋</span>
            </button>
            <button class="action-btn preview" @click="previewResume(resume.id)" title="预览简历">
              <span class="btn-icon">👁️</span>
            </button>
            <button class="action-btn delete" @click="deleteResume(resume.id)" title="删除简历">
              <span class="btn-icon">🗑️</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/api'

const router = useRouter()
const loading = ref(false)
const resumes = ref([])

onMounted(() => {
  loadResumes()
})

async function loadResumes() {
  loading.value = true
  try {
    const response = await api.resumes.getList()
    if (response.data.success) {
      resumes.value = response.data.data.resumes || []
    }
  } catch (error) {
    ElMessage.error('加载简历列表失败')
  } finally {
    loading.value = false
  }
}

function createResume() {
  router.push('/resume/edit/new')
}

function editResume(id) {
  router.push(`/resume/edit/${id}`)
}

function previewResume(id) {
  router.push(`/resume/preview/${id}`)
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

async function deleteResume(id) {
  try {
    await ElMessageBox.confirm('确定要删除这份简历吗？', '删除确认', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'custom-message-box'
    })

    const response = await api.resumes.delete(id)
    if (response.data.success) {
      ElMessage.success('简历删除成功')
      loadResumes()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败，请重试')
    }
  }
}

async function copyResume(resume) {
  try {
    // 弹窗输入新简历名称
    const { value: newTitle } = await ElMessageBox.prompt(
      '请输入新简历的名称：',
      '复制简历',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入简历名称',
        inputValue: `${resume.title} - 副本`,
        customClass: 'custom-message-box',
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return '简历名称不能为空'
          }
          if (value.length > 50) {
            return '简历名称不能超过50个字符'
          }
          return true
        }
      }
    )

    // 获取原简历的详细数据
    const detailResponse = await api.resumes.getDetail(resume.id)
    if (!detailResponse.data.success) {
      ElMessage.error('获取简历详情失败')
      return
    }

    const originalData = detailResponse.data.data
    
    // 构造新简历数据（排除id和创建时间等字段）
    const newResumeData = {
      title: newTitle.trim(),
      full_name: originalData.full_name,
      gender: originalData.gender,
      age: originalData.age,
      phone: originalData.phone,
      email: originalData.email,
      address: originalData.address,
      photo_url: originalData.photo_url,
      objective: originalData.objective,
      summary: originalData.summary,
      work_experiences: originalData.work_experiences || [],
      educations: originalData.educations || [],
      projects: originalData.projects || [],
      skills: originalData.skills || [],
      certifications: originalData.certifications || [],
      template_id: originalData.template_id || 'basic'
    }

    // 创建新简历
    const createResponse = await api.resumes.create(newResumeData)
    if (createResponse.data.success) {
      ElMessage.success('简历复制成功')
      loadResumes() // 刷新列表
    } else {
      ElMessage.error('简历复制失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制简历失败:', error)
      ElMessage.error('简历复制失败，请重试')
    }
  }
}
</script>

<style scoped>
:root {
  --brand-main: #4A90E2;
  --brand-gold: #F5C242;
  --brand-bg: #f8fafc;
  --brand-gray: #D6E4F0;
  --center-bg: #f4f8ff;
}

.resume-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 0 48px 0;
  background: var(--brand-bg);
}

/* 页面头部 - 统一首页风格 */
.page-header {
  background: linear-gradient(90deg, #4A90E2 0%, #D6E4F0 100%);
  color: #fff;
  border-radius: 0 0 24px 24px;
  padding: 36px 32px 28px 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 24px #4A90E222;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 1rem;
}

/* 按钮样式 */
.create-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.create-btn.primary {
  background: linear-gradient(90deg, #F5C242 0%, #FFD700 100%);
  color: #333;
  box-shadow: 0 2px 12px #4A90E222;
}

.create-btn.large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.create-btn:hover {
  background: linear-gradient(90deg, #FFD700 0%, #F5C242 100%);
  color: #222;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px #4A90E244;
}

.create-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.create-btn:hover::before {
  transform: translateX(0);
}

.btn-icon {
  font-size: 1.1em;
}

.btn-text {
  font-weight: 600;
}

/* 内容区域 */
.resume-content {
  min-height: 400px;
}

/* 空状态 - 统一首页风格 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
  background: var(--center-bg);
  border-radius: 32px;
  box-shadow: 0 8px 40px #4A90E222;
  margin-bottom: 32px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.8rem 0;
}

.empty-description {
  font-size: 1rem;
  color: #718096;
  margin: 0 0 2rem 0;
  max-width: 400px;
}

/* 简历网格 - 统一首页风格 */
.resume-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 固定为每行3个卡片 */
  gap: 32px;
  background: var(--center-bg);
  border-radius: 32px;
  box-shadow: 0 8px 40px #4A90E222;
  padding: 32px;
  margin-bottom: 32px;
}

/* 简历卡片 - 统一首页风格 */
.resume-card {
  background: white;
  border: 1.5px solid #4A90E2;
  border-radius: 18px;
  padding: 24px;
  box-shadow: 0 2px 12px #4A90E222;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.resume-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.resume-card:hover {
  box-shadow: 0 8px 32px #4A90E244;
  transform: translateY(-2px);
}

.resume-card:hover::before {
  transform: scaleX(1);
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.resume-icon {
  font-size: 2.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.resume-info {
  flex: 1;
  min-width: 0;
}

.resume-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
  word-break: break-word;
}

.resume-owner {
  font-size: 1rem;
  color: #667eea;
  margin: 0 0 0.8rem 0;
  font-weight: 500;
}

.resume-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.meta-icon {
  font-size: 0.9rem;
}

.meta-text {
  font-size: 0.85rem;
  color: #718096;
}

/* 卡片操作按钮 */
.card-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end; /* 右对齐 */
  align-items: center;
  opacity: 1; /* 始终可见 */
  margin-top: auto;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1rem;
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.action-btn:hover::before {
  transform: translateX(0);
}

.action-btn.edit {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
}

.action-btn.copy {
  background: linear-gradient(135deg, #4A90E2 0%, #F5C242 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.action-btn.preview {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.action-btn.delete {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
}

.action-btn:hover {
  transform: scale(1.1) translateY(-1px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.25);
}

/* 加载动画 */
:deep(.el-loading-spinner) {
  .circular {
    width: 50px;
    height: 50px;
  }
  
  .path {
    stroke: #667eea;
    stroke-width: 3;
  }
}

:deep(.el-loading-mask) {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

/* 自定义消息框样式 */
:deep(.custom-message-box) {
  border-radius: 12px;
  
  .el-message-box__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
  }
  
  .el-message-box__title {
    color: white;
  }
  
  .el-message-box__btns .el-button--primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
  }
}

/* 响应式设计 - 统一首页风格 */
@media (max-width: 768px) {
  .resume-manager {
    padding: 0 16px 48px 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 24px;
    padding: 24px 16px;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .resume-grid {
    grid-template-columns: 1fr;
    gap: 24px;
    padding: 24px 16px;
  }
  
  .resume-card {
    padding: 20px;
  }
  
  .card-actions {
    opacity: 1;
    justify-content: center;
    margin-top: 16px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }
  
  .page-description {
    font-size: 1rem;
  }
  
  .card-header {
    flex-direction: column;
    align-items: center;
  text-align: center;
  }
  
  .resume-icon {
    margin-bottom: 0.5rem;
  }
}
</style> 