<template>
  <div class="pdf-preview" ref="containerRef">
    <div v-if="pageToShow" class="pdf-page-wrapper">
      <div style="position:relative; display:inline-block;">
        <canvas ref="pageCanvasRef" class="pdf-canvas-render" :style="canvasStyle" />
        <canvas ref="fabricCanvasRef" class="pdf-canvas-fabric" :style="canvasStyle" />
      </div>
    </div>
    <div v-else class="empty-preview">
      <div class="empty-icon">
        <svg width="80" height="80" viewBox="0 0 48 48"><rect x="8" y="8" width="32" height="40" rx="4" fill="#f0f0f0" stroke="#bbb"/><rect x="14" y="16" width="20" height="2" fill="#bbb"/><rect x="14" y="22" width="20" height="2" fill="#bbb"/><rect x="14" y="28" width="12" height="2" fill="#bbb"/></svg>
      </div>
      <div class="empty-text">请上传PDF文件</div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch, onMounted, computed, nextTick } from 'vue';
import { fabric } from 'fabric';

const props = defineProps(['file', 'page', 'annotations', 'annotateMode', 'scale']);
const emit = defineEmits(['update:annotations']);

const pageCanvasRef = ref(null);
const fabricCanvasRef = ref(null);
const fabricInstance = ref(null);
const containerRef = ref(null);
const baseScale = ref(1);

const pageToShow = computed(() => props.file && props.file.pages && props.file.pages[props.page] ? { ...props.file.pages[props.page], index: props.page } : null);

const scale = computed(() => (props.scale ? props.scale * baseScale.value : baseScale.value));
const canvasStyle = computed(() => ({ transform: `scale(${scale.value})`, transformOrigin: 'top left' }));

onMounted(() => {
  updateBaseScale();
  window.addEventListener('resize', updateBaseScale);
  renderPage();
});
watch(() => [props.file, props.page], async () => {
  await nextTick();
  updateBaseScale();
  renderPage();
});
watch(() => scale.value, renderPage);

function updateBaseScale() {
  if (!containerRef.value || !pageToShow.value) return;
  const containerWidth = containerRef.value.clientWidth;
  const img = new window.Image();
  img.src = pageToShow.value.previewUrl;
  img.onload = () => {
    const pageWidth = img.width;
    // 90% 宽度自适应
    baseScale.value = Math.min(1.5, Math.max(0.2, (containerWidth * 0.9) / pageWidth));
  };
}

watch(() => props.annotateMode, (newMode) => {
  if (!fabricInstance.value) return;
  fabricInstance.value.isDrawingMode = ['pen', 'highlight'].includes(newMode);
  if (newMode === 'pen') {
    fabricInstance.value.freeDrawingBrush.color = '#3182ce';
    fabricInstance.value.freeDrawingBrush.width = 2;
    fabricInstance.value.defaultCursor = 'crosshair';
  } else if (newMode === 'highlight') {
    fabricInstance.value.freeDrawingBrush.color = 'rgba(255, 255, 0, 0.3)';
    fabricInstance.value.freeDrawingBrush.width = 20;
    fabricInstance.value.defaultCursor = 'crosshair';
  } else if (newMode === 'text') {
    fabricInstance.value.isDrawingMode = false;
    fabricInstance.value.defaultCursor = 'text';
    fabricInstance.value.on('mouse:down', function addText(e) {
      if (props.annotateMode !== 'text') return;
      const pointer = fabricInstance.value.getPointer(e.e);
      const text = new fabric.IText('文本', {
        left: pointer.x,
        top: pointer.y,
        fontSize: 18,
        fill: '#222'
      });
      fabricInstance.value.add(text);
      const pageIndex = pageToShow.value.index;
      const newAnnotations = { ...props.annotations };
      if (!newAnnotations[pageIndex]) newAnnotations[pageIndex] = [];
      newAnnotations[pageIndex].push(text.toObject());
      emit('update:annotations', newAnnotations);
      fabricInstance.value.off('mouse:down', addText);
    });
  } else {
    fabricInstance.value.defaultCursor = 'default';
  }
});

async function setupFabricCanvas(width, height) {
  if (fabricInstance.value) {
    fabricInstance.value.dispose();
  }
  const canvasEl = fabricCanvasRef.value;
  if (!canvasEl) return;
  const fabricCanvas = new fabric.Canvas(canvasEl, {
    width,
    height,
    isDrawingMode: ['pen', 'highlight'].includes(props.annotateMode)
  });
  fabricCanvas.__pageIndex = pageToShow.value.index;
  fabricCanvas.freeDrawingBrush.color = '#3182ce';
  fabricCanvas.freeDrawingBrush.width = 2;
  fabricCanvas.on('path:created', (e) => {
    const pageIndex = pageToShow.value.index;
    const path = e.path;
    path.set({});
    const newAnnotations = { ...props.annotations };
    if (!newAnnotations[pageIndex]) {
      newAnnotations[pageIndex] = [];
    }
    newAnnotations[pageIndex].push(path.toObject());
    emit('update:annotations', newAnnotations);
  });
  // Load existing annotations
  const pageIndex = pageToShow.value.index;
  const pageAnnotations = props.annotations[pageIndex];
  if (pageAnnotations) {
    fabric.util.enlivenObjects(pageAnnotations, (objects) => {
      objects.forEach(obj => fabricCanvas.add(obj));
    });
  }
  fabricInstance.value = fabricCanvas;
}

async function renderPage() {
  if (!pageToShow.value) return;
  const canvas = pageCanvasRef.value;
  if (canvas && pageToShow.value.previewUrl) {
    const img = new window.Image(); img.src = pageToShow.value.previewUrl;
    img.onload = async () => {
      canvas.width=img.width; canvas.height=img.height;
      const pageCtx = canvas.getContext('2d');
      pageCtx.clearRect(0,0,canvas.width,canvas.height);
      pageCtx.drawImage(img, 0, 0, img.width, img.height);
      // Setup fabric on top
      await setupFabricCanvas(canvas.width, canvas.height);
    }
  }
}
</script>
<style scoped>
.pdf-preview {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fff;
  position: relative;
  overflow-y: auto;
  padding: 2rem 0;
}
.pdf-page-wrapper {
  position: relative;
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}
.pdf-canvas-render, .pdf-canvas-fabric {
  position: absolute;
  top: 0;
  left: 0;
  box-shadow: 0 2px 16px 0 #e5e6eb;
  border-radius: 8px;
  transition: transform 0.2s;
}
.pdf-canvas-fabric {
  z-index: 1;
  cursor: inherit;
}
.annotation { position: absolute; }
.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #bbb;
  margin-top: 2em;
  text-align: center;
}
.empty-icon {
  margin-bottom: 0.5em;
}
.empty-text {
  font-size: 17px;
  line-height: 1.7;
}
@media (max-width: 900px) {
  .pdf-preview {
    padding: 1rem 0;
  }
  .pdf-page canvas {
    max-width: 98vw;
    max-height: 60vh;
  }
  .empty-text {
    font-size: 15px;
  }
}
</style> 