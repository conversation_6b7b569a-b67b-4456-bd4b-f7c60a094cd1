<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ resume.full_name }} - 简历</title>
    <!-- 样式将通过CSS文件加载 -->
</head>
<body>
    <div class="resume-container">
        <header class="resume-header">
            <div class="header-left">
                {% if resume.photo_url %}
                <div class="profile-photo">
                    <img src="{{ resume.photo_url }}" alt="{{ resume.full_name }}">
                </div>
                {% endif %}
                <div class="profile-info">
                    <h1>{{ resume.full_name }}</h1>
                    <h2>{{ resume.objective or '专业人士' }}</h2>
                </div>
            </div>
            <div class="header-right">
                <div class="contact-info">
                    {% if resume.phone %}<p><strong>电话:</strong> {{ resume.phone }}</p>{% endif %}
                    {% if resume.email %}<p><strong>邮箱:</strong> {{ resume.email }}</p>{% endif %}
                    {% if resume.address %}<p><strong>地址:</strong> {{ resume.address }}</p>{% endif %}
                </div>
            </div>
        </header>

        <main class="resume-main">
            {% if resume.summary %}
            <section class="section">
                <h3>个人总结</h3>
                <p>{{ resume.summary }}</p>
            </section>
            {% endif %}

            {% if resume.work_experiences %}
            <section class="section">
                <h3>工作经历</h3>
                <div class="timeline">
                    {% for exp in resume.work_experiences %}
                    <div class="timeline-item">
                        <div class="timeline-date">{{ exp.start_date }} - {{ exp.end_date or '至今' }}</div>
                        <div class="timeline-content">
                            <h4>{{ exp.position }}</h4>
                            <h5>{{ exp.company_name }}</h5>
                            {% if exp.description %}<p>{{ exp.description }}</p>{% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            {% if resume.educations %}
            <section class="section">
                <h3>教育背景</h3>
                {% for edu in resume.educations %}
                <div class="education-item">
                    <h4>{{ edu.school_name }}</h4>
                    <p>{{ edu.degree }} - {{ edu.major }}</p>
                    <p>{{ edu.start_date }} - {{ edu.end_date }}</p>
                </div>
                {% endfor %}
            </section>
            {% endif %}

            {% if resume.skills %}
            <section class="section">
                <h3>专业技能</h3>
                <div class="skills-list">
                    {% for skill in resume.skills %}
                    <span class="skill-tag">{{ skill.name }}</span>
                    {% endfor %}
                </div>
            </section>
            {% endif %}
        </main>
    </div>
</body>
</html> 