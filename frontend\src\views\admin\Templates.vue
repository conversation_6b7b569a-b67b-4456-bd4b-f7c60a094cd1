<template>
  <div class="template-admin">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="名片模板" name="business_card">
        <TemplateList type="business_card" />
      </el-tab-pane>
      <el-tab-pane label="简历模板" name="resume">
        <TemplateList type="resume" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import TemplateList from './components/TemplateList.vue'
const activeTab = ref('business_card')
</script>
<style scoped>
.template-admin {
  padding: 24px;
}
</style> 