<template>
  <div class="print-layout-settings">
    <el-form :model="settings" label-width="120px">
      <el-form-item label="纸张规格">
        <el-select v-model="settings.paperSize" placeholder="请选择纸张规格" @change="updateSettings">
          <el-option label="A4 (210×297mm)" value="A4"></el-option>
          <el-option label="A5 (148×210mm)" value="A5"></el-option>
          <el-option label="A6 (105×148mm)" value="A6"></el-option>
          <el-option label="Letter (216×279mm)" value="Letter"></el-option>
        </el-select>
      </el-form-item>
      
      <el-form-item label="页面方向">
        <el-radio-group v-model="settings.orientation" @change="updateSettings">
          <el-radio value="portrait" label="portrait">竖向</el-radio>
          <el-radio value="landscape" label="landscape">横向</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="页边距 (mm)">
        <el-input-number 
          v-model="settings.margin" 
          :min="5" 
          :max="50" 
          :step="5"
          @change="updateSettings"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="名片间距 (mm)">
        <el-input-number 
          v-model="settings.cardSpacing" 
          :min="0" 
          :max="20" 
          :step="1"
          @change="updateSettings"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="每页数量">
        <el-input-number 
          v-model="settings.cardsPerPage" 
          :min="1" 
          :max="20" 
          :step="1"
          @change="updateSettings"
        ></el-input-number>
      </el-form-item>
      
      <el-form-item label="预览">
        <div class="layout-preview">
          <div class="page-preview" :class="settings.orientation">
            <div 
              v-for="i in settings.cardsPerPage" 
              :key="i" 
              class="card-preview"
            ></div>
          </div>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      paperSize: 'A4',
      orientation: 'portrait',
      margin: 15,
      cardSpacing: 5,
      cardsPerPage: 8
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const settings = ref({ ...props.modelValue })

const updateSettings = () => {
  emit('update:modelValue', { ...settings.value })
}

watch(() => props.modelValue, (newVal) => {
  settings.value = { ...newVal }
}, { deep: true })
</script>

<style scoped>
.print-layout-settings {
  padding: 20px;
}

.layout-preview {
  border: 1px solid #ddd;
  padding: 10px;
  background: #f9f9f9;
}

.page-preview {
  width: 150px;
  height: 200px;
  border: 1px solid #ccc;
  background: white;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  padding: 5px;
  gap: 2px;
}

.page-preview.landscape {
  width: 200px;
  height: 150px;
}

.card-preview {
  width: 30px;
  height: 20px;
  background: #e6f3ff;
  border: 1px solid #409eff;
  flex-shrink: 0;
}
</style>