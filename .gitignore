# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Frontend build
frontend/dist/
frontend/.temp/
frontend/.cache/

# Database
*.db-wal
*.db-shm
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# Temporary files
temp/
tmp/
*.tmp
*.temp

# Test results
*_test_results/
*_results/
test_*.py
*_test.py
test_*.html
*_test.html
simple_test.py
test_comparison.py

# Uploaded files (keep structure but ignore content)
backend/uploads/images/*/
backend/uploads/documents/*/
backend/uploads/reports/*/
backend/uploads/thumbnails/
!backend/uploads/images/.gitkeep
!backend/uploads/documents/.gitkeep
!backend/uploads/reports/.gitkeep

# Static files
backend/static/processed/*/
!backend/static/processed/.gitkeep

# Configuration files with sensitive data
.env.local
.env.production
config_local.py 