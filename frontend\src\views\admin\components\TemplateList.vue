<template>
  <div>
    <el-button type="primary" @click="showEditDialog()">新增模板</el-button>
    <el-input v-model="keyword" placeholder="搜索模板" @input="loadTemplates" style="width:200px;margin-left:16px"/>
    <el-table :data="templates" style="margin-top:16px">
      <el-table-column prop="id" label="ID" width="60"/>
      <el-table-column prop="name" label="名称"/>
      <el-table-column prop="description" label="描述"/>
      <el-table-column prop="category" label="分类"/>
      <el-table-column prop="is_active" label="状态">
        <template #default="{row}">
          <el-tag :type="row.is_active ? 'success' : 'info'">{{ row.is_active ? '启用' : '禁用' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220">
        <template #default="{row}">
          <el-button size="small" @click="showEditDialog(row)">编辑</el-button>
          <el-button size="small" @click="toggleActive(row)">{{ row.is_active ? '禁用' : '启用' }}</el-button>
          <el-button size="small" type="danger" @click="deleteTemplate(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="perPage"
      :total="total"
      @current-change="loadTemplates"
      @size-change="loadTemplates"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top:16px"
    />
    <TemplateEditDialog v-model="editDialogVisible" :template="editTemplate" :type="type" @refresh="loadTemplates"/>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import api from '@/api'
import TemplateEditDialog from './TemplateEditDialog.vue'
const props = defineProps({ type: String })
const templates = ref([])
const total = ref(0)
const page = ref(1)
const perPage = ref(20)
const keyword = ref('')
const editDialogVisible = ref(false)
const editTemplate = ref(null)
const loadTemplates = async () => {
  const res = await api.admin.getTemplates({ type: props.type, page: page.value, per_page: perPage.value, keyword: keyword.value })
  templates.value = res.data.templates
  total.value = res.data.total
}
const showEditDialog = (tpl = null) => {
  editTemplate.value = tpl
  editDialogVisible.value = true
}
const toggleActive = async (row) => {
  await api.admin[row.is_active ? 'disableTemplate' : 'enableTemplate'](row.id)
  loadTemplates()
}
const deleteTemplate = async (row) => {
  await api.admin.deleteTemplate(row.id)
  loadTemplates()
}
onMounted(loadTemplates)
</script> 