# 智能证件照制作系统使用说明

## 🎯 系统概述

智能证件照制作系统是一个基于AI技术的多功能平台，主要提供以下功能：
- 📸 证件照制作及排版
- 📄 文档对比功能
- 📋 简历生成及排版预览
- 💼 名片生成及排版预览

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）
1. 双击运行 `start_system.bat`
2. 等待系统自动启动前后端服务
3. 浏览器访问 http://localhost:8102

### 方法二：手动启动
1. 启动后端服务：
   ```bash
   cd backend
   python run.py
   ```

2. 启动前端服务：
   ```bash
   cd frontend
   npm run serve
   ```

## 👤 登录账号

### 管理员账号
- 用户名：`admin`
- 密码：`admin123`
- 权限：完整系统管理权限

### 测试账号
- 用户名：`test`
- 密码：`test123`
- 权限：普通用户权限

## 🔧 主要功能

### 1. 证件照制作
- **功能**：上传照片，自动抠图，更换背景，调整尺寸
- **支持格式**：JPG, JPEG, PNG, BMP, GIF
- **预设规格**：一寸照、二寸照、护照照片、签证照片、驾驶证照片、身份证照片
- **操作流程**：
  1. 选择证件照规格
  2. 上传原始照片
  3. 系统自动处理（抠图、美化）
  4. 选择背景颜色
  5. 预览并下载

### 2. 文档对比
- **功能**：对比两个文档的差异，生成对比报告
- **支持格式**：PDF, DOC, DOCX, TXT
- **对比选项**：
  - 忽略空白字符
  - 详细报告
  - 提取表格
  - 使用OCR
  - 可视化对比
- **操作流程**：
  1. 上传标准文档
  2. 上传待对比文档
  3. 设置对比选项
  4. 开始对比
  5. 查看对比报告

### 3. 简历生成
- **功能**：使用模板快速生成专业简历
- **模板类型**：经典简历、现代简历等
- **包含模块**：
  - 个人信息
  - 求职意向
  - 工作经历
  - 教育背景
  - 专业技能
  - 项目经验
  - 证书资质
- **操作流程**：
  1. 选择简历模板
  2. 填写个人信息
  3. 添加工作经历
  4. 添加教育背景
  5. 预览并导出

### 4. 名片制作
- **功能**：设计和制作个人或企业名片
- **模板风格**：经典商务、现代简约、科技风格等
- **包含元素**：
  - 姓名和职位
  - 联系方式
  - 公司信息
  - 二维码
- **操作流程**：
  1. 选择名片模板
  2. 填写个人/企业信息
  3. 自定义样式和颜色
  4. 预览效果
  5. 导出打印

## 🛠️ 系统管理

### 管理员功能
- 用户管理：查看、编辑、禁用用户
- 模板管理：添加、编辑、删除模板
- 系统配置：修改系统参数
- 数据统计：查看使用统计

### 系统配置
- 文件上传限制
- 图片处理参数
- 功能开关控制
- 用户权限设置

## 📊 技术架构

### 前端技术栈
- Vue 3 + TypeScript
- Element Plus UI框架
- Axios HTTP客户端
- Pinia 状态管理

### 后端技术栈
- Python Flask框架
- SQLAlchemy ORM
- SQLite数据库
- PIL图像处理
- OpenCV计算机视觉

## 🔧 故障排除

### 常见问题

1. **网络异常，请稍后重试**
   - 检查后端服务是否正常启动
   - 确认端口5000未被占用
   - 重启后端服务

2. **前端页面无法访问**
   - 检查前端服务是否启动
   - 确认端口8097未被占用
   - 清除浏览器缓存

3. **图片上传失败**
   - 检查图片格式是否支持
   - 确认图片大小不超过16MB
   - 检查uploads目录权限

4. **数据库连接错误**
   - 运行系统重建脚本：`python system_rebuild.py`
   - 检查数据库文件权限
   - 重新初始化数据库

### 重建系统
如果遇到严重问题，可以运行系统重建脚本：
```bash
python system_rebuild.py
```

此脚本会：
- 清理现有数据库
- 重新创建数据库结构
- 初始化系统数据和模板
- 修复网络配置问题

## 📞 技术支持

如果遇到其他问题，请检查：
1. Python环境是否正确安装
2. Node.js环境是否正确安装
3. 依赖包是否完整安装
4. 防火墙是否阻止了端口访问

## 📝 更新日志

### v2.0.0 (2025-07-20)
- 完全重构系统架构
- 修复网络异常问题
- 重建数据库结构
- 优化用户界面
- 增强系统稳定性
- 添加完整的模板系统
- 改进错误处理机制

---

**系统版本**: 2.0.0  
**最后更新**: 2025-07-20  
**开发团队**: 智能证件照制作系统开发组
