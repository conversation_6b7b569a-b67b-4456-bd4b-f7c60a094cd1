<template>
  <div class="dashboard-page">
    <!-- 首页顶部广告位 -->
    <div class="home-ad-container">
      <AdPosition position-code="home_top_banner" page-location="home" />
    </div>
    
    <div class="dashboard-hero">
        <div class="hero-content">
        <img src="/logo.png" alt="Logo" class="brand-logo" />
        <div class="brand-title">
          <div class="brand-name">白泽AI文件助手</div>
          <div class="brand-welcome">欢迎，{{ user?.username || '访客' }}！</div>
          <div class="slogan">AI驱动 · 极速证照 · 智能文档 · 专业简历 · 创意名片 · 一站式办公平台</div>
        </div>
      </div>
      <div class="hero-decoration">
        <img src="@/utils/img/1.jpg" alt="装饰" class="decoration-img" />
      </div>
            </div>
    <!-- 中间功能区加独立背景 -->
    <div class="dashboard-center-area">
      <div class="dashboard-grid">
        <el-card class="quick-card photo-card">
          <div class="card-image-container">
            <img src="@/utils/img/1.jpg" alt="证件照美化" class="card-theme-img" />
            <div class="image-overlay photo-overlay"></div>
          </div>
          <img class="card-bg-img" src="@/utils/img/1.jpg" alt="bg" />
          <div class="card-content">
            <div class="quick-header">
              <el-icon><PictureFilled /></el-icon>
              <span>证件照极速美化</span>
            </div>
            <el-upload
              class="quick-upload"
              drag
              :show-file-list="false"
              :http-request="handlePhotoUpload"
              :disabled="uploadingPhoto || !authStore.isAuthenticated"
            >
                              <i class="el-icon-upload"></i>
                <div class="el-upload__text">{{ authStore.isAuthenticated ? '点击或拖拽上传照片' : '请先登录后上传照片' }}</div>
                <div class="el-upload__tip">{{ authStore.isAuthenticated ? '支持JPG/PNG，AI抠图美颜，多尺寸多底色，3秒出片，隐私安全' : '登录后即可使用所有功能' }}</div>
            </el-upload>
            <div class="quick-btn-row">
              <el-button type="primary" size="large" style="width: 100%; margin: 12px 0 0 0;" @click="onGeneratePhoto">{{ authStore.isAuthenticated ? '生成证件照' : '登录后使用' }}</el-button>
              </div>
            <div class="quick-desc">
              <el-alert type="info" show-icon :closable="false">
                AI智能抠图与美颜，支持1寸/2寸/护照等多规格，高清输出，数据本地/云端可选。
              </el-alert>
            </div>
          </div>
        </el-card>
        
        <el-card class="quick-card document-card">
          <div class="card-image-container">
            <img src="@/utils/img/2.jpg" alt="文档对比" class="card-theme-img" />
            <div class="image-overlay document-overlay"></div>
          </div>
          <img class="card-bg-img" src="@/utils/img/2.jpg" alt="bg" />
          <div class="card-content">
            <div class="quick-header">
              <el-icon><Document /></el-icon>
              <span>AI文档智能对比</span>
            </div>
            <div class="quick-compare">
              <div class="upload-row">
                <el-upload
                  class="quick-upload"
                  drag
                  :show-file-list="false"
                  :action="'/api/documents/upload'"
                  :on-success="handleStandardDocUploadSuccess"
                  :on-error="() => $message.error('上传失败，请重试')"
                  :disabled="!authStore.isAuthenticated"
                >
                  <div class="el-upload__text">{{ authStore.isAuthenticated ? '上传标准版文档' : '请先登录' }}</div>
                </el-upload>
                <el-upload
                  class="quick-upload"
                  drag
                  :show-file-list="false"
                  :action="'/api/documents/upload'"
                  :on-success="handleModifiedDocUploadSuccess"
                  :on-error="() => $message.error('上传失败，请重试')"
                  :disabled="!authStore.isAuthenticated"
                >
                  <div class="el-upload__text">{{ authStore.isAuthenticated ? '上传修改版文档' : '请先登录' }}</div>
                </el-upload>
              </div>
                          <div class="quick-btn-row">
              <el-button type="primary" size="large" style="width: 100%; margin: 12px 0 0 0;" @click="onCompareDocs">{{ authStore.isAuthenticated ? '文档差异对比' : '登录后使用' }}</el-button>
            </div>
            </div>
            <div class="quick-desc">
              <el-alert type="info" show-icon :closable="false">
                支持Word/PDF/WPS，AI精准比对文本/表格/图片，自动生成高亮报告，批量管理高效便捷。
              </el-alert>
            </div>
          </div>
        </el-card>
        
        <el-card class="quick-card resume-card">
          <div class="card-image-container">
            <img src="@/utils/img/3.jpg" alt="简历生成" class="card-theme-img" />
            <div class="image-overlay resume-overlay"></div>
          </div>
          <img class="card-bg-img" src="@/utils/img/3.jpg" alt="bg" />
          <div class="card-content">
            <div class="quick-header">
              <el-icon><User /></el-icon>
              <span>专业简历一键生成</span>
            </div>
            <el-button type="primary" @click="() => authStore.isAuthenticated ? $router.push('/resumes') : $router.push('/login')">{{ authStore.isAuthenticated ? '新建简历' : '登录后使用' }}</el-button>
            <div class="quick-desc">
              <el-alert type="info" show-icon :closable="false">
                多模板选择，AI内容优化，在线编辑与实时预览，一键导出PDF/Word，多版本管理，隐私安全。
              </el-alert>
            </div>
          </div>
        </el-card>

        <el-card class="quick-card business-card">
          <div class="card-image-container">
            <img src="@/utils/img/4.jpg" alt="电子名片" class="card-theme-img" />
            <div class="image-overlay business-overlay"></div>
          </div>
          <img class="card-bg-img" src="@/utils/img/4.jpg" alt="bg" />
          <div class="card-content">
            <div class="quick-header">
              <el-icon><CreditCard /></el-icon>
              <span>电子名片高效管理</span>
            </div>
            <el-button type="primary" @click="() => authStore.isAuthenticated ? $router.push('/business-cards') : $router.push('/login')">{{ authStore.isAuthenticated ? '新建名片' : '登录后使用' }}</el-button>
            <div class="quick-desc">
              <el-alert type="info" show-icon :closable="false">
                多风格模板，二维码集成，电子化一键分享，批量管理，个性化定制。
              </el-alert>
            </div>
          </div>
        </el-card>
      </div>
      
      <!-- 添加功能区之间的装饰分割 -->
      
      <div class="dashboard-sellpoints">
        <div class="sellpoints-divider"></div>
        <el-row :gutter="24">
          <el-col :span="6">
            <div class="sellpoint">
              <el-icon class="sp-icon sp-blue"><PictureFilled /></el-icon>
              <div class="sp-title">AI智能赋能</div>
              <div class="sp-desc">全流程AI驱动，智能抠图、比对、润色，效率与智能兼备。</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="sellpoint">
              <el-icon class="sp-icon sp-gold"><Document /></el-icon>
              <div class="sp-title">极速体验</div>
              <div class="sp-desc">3秒出片，批量处理，操作极简，零学习成本。</div>
          </div>
          </el-col>
          <el-col :span="6">
            <div class="sellpoint">
              <el-icon class="sp-icon sp-blue"><User /></el-icon>
              <div class="sp-title">数据安全</div>
              <div class="sp-desc">多重加密，隐私保护，数据本地/云端可选，合规可靠。</div>
          </div>
          </el-col>
          <el-col :span="6">
            <div class="sellpoint">
              <el-icon class="sp-icon sp-gold"><CreditCard /></el-icon>
              <div class="sp-title">美观易用</div>
              <div class="sp-desc">现代UI，极简美观，移动端适配，持续升级优化。</div>
        </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { PictureFilled, Document, User, CreditCard } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import api from '@/api'
import { ElMessage } from 'element-plus'
import AdPosition from '@/components/common/AdPosition.vue'
const authStore = useAuthStore()
const user = computed(() => authStore.user)
const $router = useRouter()

// 直接上传图片并跳转到编辑页
const uploadingPhoto = ref(false)
const handlePhotoUpload = async (option) => {
  if (!authStore.isAuthenticated) {
    $router.push('/login')
    return
  }
  uploadingPhoto.value = true
  try {
    const file = option.file
    const response = await api.images.upload(file)
    if (response.data.success && response.data.image && response.data.image.id) {
      // 跳转到编辑页，带上图片ID参数
      $router.push({ path: '/editor', query: { imageId: response.data.image.id } })
    } else {
      ElMessage.error('上传失败，请重试')
    }
  } catch (e) {
    ElMessage.error('上传失败，请重试')
  } finally {
    uploadingPhoto.value = false
  }
}

const onGeneratePhoto = () => {
  if (!authStore.isAuthenticated) {
    $router.push('/login')
    return
  }
  if (uploadedPhotoUrl.value) {
    $router.push({ path: '/editor', query: { image: uploadedPhotoUrl.value } })
  } else {
    $router.push('/editor')
  }
}

// 新增：照片上传后的图片URL
const uploadedPhotoUrl = ref(null)
// 恢复文档上传后的ID定义
const standardDocId = ref(null)
const modifiedDocId = ref(null)
function handleStandardDocUploadSuccess(response) {
  if (!authStore.isAuthenticated) {
    $router.push('/login')
    return
  }
  if (response.success && response.data && response.data.id) {
    standardDocId.value = response.data.id
  }
}
function handleModifiedDocUploadSuccess(response) {
  if (!authStore.isAuthenticated) {
    $router.push('/login')
    return
  }
  if (response.success && response.data && response.data.id) {
    modifiedDocId.value = response.data.id
  }
}
const onCompareDocs = () => {
  if (!authStore.isAuthenticated) {
    $router.push('/login')
    return
  }
  if (standardDocId.value && modifiedDocId.value) {
    $router.push({ path: '/documents', query: { standard: standardDocId.value, modified: modifiedDocId.value } })
  } else {
    $router.push('/documents')
  }
}
</script>

<style scoped>
:root {
  --brand-main: #4A90E2;
  --brand-gold: #F5C242;
  --brand-bg: #f8fafc;
  --brand-gray: #D6E4F0;
  --center-bg: #f4f8ff;
}
.dashboard-page {
  max-width: 100vw;
  min-height: 100vh;
  background: var(--brand-bg);
  position: relative;
  overflow-x: hidden;
}
.dashboard-hero {
  background: linear-gradient(90deg, #4A90E2 0%, #D6E4F0 100%);
  color: #fff;
  border-radius: 0 0 24px 24px;
  padding: 36px 32px 28px 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 24px #4A90E222;
  position: relative;
  overflow: hidden;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}
.hero-content {
  display: flex;
  align-items: center;
  gap: 32px;
  position: relative;
  z-index: 2;
}
.brand-logo {
  width: 72px;
  height: 72px;
  border-radius: 18px;
  box-shadow: 0 2px 8px #4A90E244;
  background: #fff;
  object-fit: cover;
}
.brand-title {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
.brand-name {
  font-size: 2rem;
  font-weight: 800;
  color: var(--brand-main);
  letter-spacing: 2px;
  margin-bottom: 2px;
}
.brand-welcome {
  font-size: 1.1rem;
  color: #333;
  font-weight: 500;
}
.slogan {
  font-size: 1.1rem;
  font-weight: 600;
  color: #4A90E2;
  margin-top: 2px;
}
.hero-decoration {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}
.decoration-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.15;
  filter: blur(2px);
  transform: scale(1.1);
}
/* 中间功能区独立背景 */
.dashboard-center-area {
  max-width: 1200px;
  margin: 0 auto 48px auto;
  background: var(--center-bg);
  border-radius: 32px;
  box-shadow: 0 8px 40px #4A90E222;
  padding: 32px 32px 40px 32px;
  position: relative;
  z-index: 2;
}
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
  margin: 0;
  padding: 0;
}
.dashboard-sellpoints {
  margin-top: 32px;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px #4A90E211;
  padding: 32px 16px 16px 16px;
  position: relative;
}
.sellpoints-divider {
  height: 4px;
  width: 100px;
  background: linear-gradient(90deg, #4A90E2 0%, #F5C242 100%);
  border-radius: 2px;
  margin: 0 auto 24px auto;
}
.sellpoint {
  text-align: center;
  padding: 8px 0;
}
.sp-icon {
  font-size: 40px;
  margin-bottom: 8px;
}
.sp-blue {
  color: #4A90E2;
}
.sp-gold {
  color: #F5C242;
}
.sp-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #222;
}
.sp-desc {
  font-size: 0.95rem;
  color: #666;
}
:deep(.el-card.quick-card) {
  border: 1.5px solid #4A90E2;
  box-shadow: 0 2px 12px #4A90E222;
  border-radius: 18px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  background: #fff;
}
:deep(.el-card.quick-card):hover {
  box-shadow: 0 8px 32px #4A90E244;
  transform: translateY(-2px);
}
.card-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 120px;
  overflow: hidden;
  z-index: 1;
}
.card-theme-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.7) contrast(1.2);
  transition: transform 0.3s ease;
}
:deep(.el-card.quick-card):hover .card-theme-img {
  transform: scale(1.05);
}
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.8) 0%, rgba(245, 194, 66, 0.6) 100%);
  z-index: 2;
}
.quick-header {
  position: relative;
  z-index: 3;
  margin-top: 140px;
  padding: 0 20px;
}
.quick-upload {
  position: relative;
  z-index: 3;
  margin: 20px;
}
.quick-desc {
  position: relative;
  z-index: 3;
  margin: 20px;
}
.quick-compare {
  position: relative;
  z-index: 3;
  margin: 20px;
}
.quick-btn-row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
}
:deep(.el-button--primary) {
  background: linear-gradient(90deg, #F5C242 0%, #FFD700 100%);
  border: none;
  color: #333;
  font-weight: 700;
  position: relative;
  z-index: 3;
}
:deep(.el-button--primary:hover) {
  background: linear-gradient(90deg, #FFD700 0%, #F5C242 100%);
  color: #222;
}
/* 删除 .section-divider 和 .divider-img 相关样式 */
/* 卡片内容区淡化底图背景 */
:deep(.el-card__body) {
  position: relative;
  z-index: 3;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  overflow: hidden;
}
.card-bg-img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0.1;
  z-index: 1;
  pointer-events: none;
}
.card-content {
  position: relative;
  z-index: 2;
}

/* 优化上传区域样式 */
:deep(.el-upload-dragger) {
  background: rgba(255, 255, 255, 0.9);
  border: 2px dashed #4A90E2;
  border-radius: 12px;
  transition: all 0.3s ease;
}
:deep(.el-upload-dragger:hover) {
  background: rgba(255, 255, 255, 1);
  border-color: #F5C242;
  transform: translateY(-1px);
}
/* 优化警告框样式 */
:deep(.el-alert) {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #4A90E233;
  border-radius: 8px;
}

/* 证件照卡片专属样式 */
.photo-card .image-overlay {
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.8) 0%, rgba(255, 107, 107, 0.6) 100%);
}
.photo-card .quick-header {
  color: #4A90E2;
}
.photo-card :deep(.el-upload-dragger) {
  border-color: #4A90E2;
}
.photo-card :deep(.el-upload-dragger:hover) {
  border-color: #FF6B6B;
}

/* 文档对比卡片专属样式 */
.document-card .image-overlay {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.8) 0%, rgba(155, 89, 182, 0.6) 100%);
}
.document-card .quick-header {
  color: #3498DB;
}
.document-card :deep(.el-upload-dragger) {
  border-color: #3498DB;
}
.document-card :deep(.el-upload-dragger:hover) {
  border-color: #9B59B6;
}

/* 简历卡片专属样式 */
.resume-card .image-overlay {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.8) 0%, rgba(52, 73, 94, 0.6) 100%);
  }
.resume-card .quick-header {
  color: #2ECC71;
  }
.resume-card :deep(.el-button--primary) {
  background: linear-gradient(90deg, #2ECC71 0%, #27AE60 100%);
  color: #fff;
  }
.resume-card :deep(.el-button--primary:hover) {
  background: linear-gradient(90deg, #27AE60 0%, #2ECC71 100%);
  color: #fff;
  }
  
/* 名片卡片专属样式 */
.business-card .image-overlay {
  background: linear-gradient(135deg, rgba(230, 126, 34, 0.8) 0%, rgba(241, 196, 15, 0.6) 100%);
  }
.business-card .quick-header {
  color: #E67E22;
  }
.business-card :deep(.el-button--primary) {
  background: linear-gradient(90deg, #E67E22 0%, #F39C12 100%);
  color: #fff;
  }
.business-card :deep(.el-button--primary:hover) {
  background: linear-gradient(90deg, #F39C12 0%, #E67E22 100%);
  color: #fff;
  }
  
/* 卡片悬停效果增强 */
.photo-card:hover {
  border-color: #FF6B6B;
  }
.document-card:hover {
  border-color: #9B59B6;
}
.resume-card:hover {
  border-color: #27AE60;
}
.business-card:hover {
  border-color: #F39C12;
  }
  
/* 图片悬停效果 */
.photo-card:hover .card-theme-img {
  filter: brightness(0.8) contrast(1.3) saturate(1.2);
  }
.document-card:hover .card-theme-img {
  filter: brightness(0.8) contrast(1.3) saturate(1.2);
  }
.resume-card:hover .card-theme-img {
  filter: brightness(0.8) contrast(1.3) saturate(1.2);
  }
.business-card:hover .card-theme-img {
  filter: brightness(0.8) contrast(1.3) saturate(1.2);
  }
  
/* 首页广告位容器样式 */
.home-ad-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
}

.home-ad-container :deep(.ad-position) {
  width: 100% !important;
  max-width: 1200px;
  margin: 0 auto;
}

/* 新增上传框横向排列样式 */
.upload-row {
  display: flex;
  flex-direction: row;
  gap: 24px;
  justify-content: flex-start;
  align-items: stretch;
  margin-bottom: 8px;
}
@media (max-width: 700px) {
  .upload-row {
    flex-direction: column;
    gap: 12px;
  }
}
</style> 