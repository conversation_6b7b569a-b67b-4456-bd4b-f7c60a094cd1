<template>
  <el-card class="quick-card">
    <div class="quick-header">
      <el-icon><PictureFilled /></el-icon>
      <span>证件照制作</span>
    </div>
    <el-upload
      class="quick-upload"
      drag
      :show-file-list="false"
      :action="uploadUrl"
      :on-success="handleSuccess"
      :on-error="handleError"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">点击或拖拽上传照片</div>
      <div class="el-upload__tip">支持JPG/PNG，自动美颜换底色</div>
    </el-upload>
    <div class="quick-desc">
      <el-alert type="info" show-icon :closable="false">
        上传后自动处理，支持多种尺寸和背景色。
      </el-alert>
    </div>
  </el-card>
</template>
<script setup>
import { ElMessage, ElAlert } from 'element-plus'
import { PictureFilled } from '@element-plus/icons-vue'
const uploadUrl = '/api/images/upload'
const handleSuccess = () => ElMessage.success('上传成功，正在处理...')
const handleError = () => ElMessage.error('上传失败，请重试')
</script>
<style scoped>
.quick-card { min-width: 320px; }
.quick-header { display: flex; align-items: center; gap: 8px; font-size: 18px; font-weight: bold; margin-bottom: 12px; }
.quick-upload { margin-bottom: 8px; }
.quick-desc { margin-top: 8px; }
</style> 