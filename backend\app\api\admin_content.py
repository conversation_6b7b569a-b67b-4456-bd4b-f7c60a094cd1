#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员内容管理API
Admin Content Management API
"""

from flask import Blueprint, request, jsonify, send_file, current_app
from flask_jwt_extended import jwt_required
from sqlalchemy import or_, and_, func
from datetime import datetime, timedelta
import os
import pandas as pd
from io import BytesIO

from app import db
from app.models.user import User
from app.models.business_card import BusinessCard
from app.models.resume import Resume
from app.models.document import Document
from app.models.image import Image

admin_content_bp = Blueprint('admin_content', __name__, url_prefix='/api/admin')

# 名片管理
@admin_content_bp.route('/business-cards', methods=['GET'])
@jwt_required()
def get_business_cards():
    """获取名片列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    keyword = request.args.get('keyword', '')
    
    query = BusinessCard.query.join(User, BusinessCard.user_id == User.id)
    
    # 关键词搜索
    if keyword:
        query = query.filter(
            or_(
                BusinessCard.title.contains(keyword),
                User.username.contains(keyword)
            )
        )
    
    # 分页
    pagination = query.order_by(BusinessCard.created_at.desc()).paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    business_cards = []
    for card in pagination.items:
        card_data = {
            'id': card.id,
            'user_id': card.user_id,
            'user': {
                'username': card.user.username if card.user else '未知用户'
            },
            'title': card.title,
            'template_name': card.template.name if card.template else '未知模板',
            'status': card.status,
            'created_at': card.created_at.isoformat() if card.created_at else None,
            'updated_at': card.updated_at.isoformat() if card.updated_at else None,
            'card_data': card.card_data,  # 新增
            'style_config': card.style_config,  # 新增
        }
        business_cards.append(card_data)
    
    return jsonify({
        'business_cards': business_cards,
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page,
        'per_page': per_page
    })

@admin_content_bp.route('/business-cards/<int:card_id>', methods=['DELETE'])
@jwt_required()
def delete_business_card(card_id):
    """删除名片"""
    card = BusinessCard.query.get(card_id)
    if not card:
        return jsonify({'error': '名片不存在'}), 404
    
    try:
        db.session.delete(card)
        db.session.commit()
        return jsonify({'message': '名片删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '删除失败'}), 500

@admin_content_bp.route('/business-cards/export', methods=['GET'])
@jwt_required()
def export_business_cards():
    """导出名片数据"""
    keyword = request.args.get('keyword', '')
    
    query = BusinessCard.query.join(User, BusinessCard.user_id == User.id)
    
    if keyword:
        query = query.filter(
            or_(
                BusinessCard.title.contains(keyword),
                User.username.contains(keyword)
            )
        )
    
    cards = query.order_by(BusinessCard.created_at.desc()).all()
    
    # 准备数据
    data = []
    for card in cards:
        data.append({
            '名片ID': card.id,
            '用户ID': card.user_id,
            '用户名': card.user.username if card.user else '未知用户',
            '名片标题': card.title,
            '模板名称': card.template.name if card.template else '未知模板',
            '状态': card.status,
            '创建时间': card.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            '更新时间': card.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 创建Excel文件
    df = pd.DataFrame(data)
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='名片数据', index=False)
    
    output.seek(0)
    
    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'名片数据_{datetime.now().strftime("%Y%m%d")}.xlsx'
    )

# 简历管理
@admin_content_bp.route('/resumes', methods=['GET'])
@jwt_required()
def get_resumes():
    """获取简历列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    keyword = request.args.get('keyword', '')
    
    query = Resume.query.join(User, Resume.user_id == User.id)
    
    # 关键词搜索
    if keyword:
        query = query.filter(
            or_(
                Resume.title.contains(keyword),
                Resume.full_name.contains(keyword),
                User.username.contains(keyword)
            )
        )
    
    # 分页
    pagination = query.order_by(Resume.created_at.desc()).paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    resumes = []
    for resume in pagination.items:
        resume_data = {
            'id': resume.id,
            'user_id': resume.user_id,
            'user': {
                'username': getattr(resume.user, 'username', '未知用户')
            },
            'title': resume.title,
            'full_name': resume.full_name,
            'template_name': getattr(getattr(resume, 'template', None), 'name', '未知模板'),
            'status': resume.status or 'draft',
            'created_at': resume.created_at.isoformat() if resume.created_at else None,
            'updated_at': resume.updated_at.isoformat() if resume.updated_at else None,
            'summary': getattr(resume, 'summary', ''),  # 新增
            'objective': getattr(resume, 'objective', ''),  # 新增
        }
        resumes.append(resume_data)
    
    return jsonify({
        'resumes': resumes,
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page,
        'per_page': per_page
    })

@admin_content_bp.route('/resumes/<int:resume_id>', methods=['DELETE'])
@jwt_required()
def delete_resume(resume_id):
    """删除简历"""
    resume = Resume.query.get(resume_id)
    if not resume:
        return jsonify({'error': '简历不存在'}), 404
    
    try:
        db.session.delete(resume)
        db.session.commit()
        return jsonify({'message': '简历删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '删除失败'}), 500

@admin_content_bp.route('/resumes/export', methods=['GET'])
@jwt_required()
def export_resumes():
    """导出简历数据"""
    try:
        keyword = request.args.get('keyword', '')
        
        query = Resume.query.join(User, Resume.user_id == User.id)
        
        if keyword:
            query = query.filter(
                or_(
                    Resume.title.contains(keyword),
                    Resume.full_name.contains(keyword),
                    User.username.contains(keyword)
                )
            )
        
        resumes = query.order_by(Resume.created_at.desc()).all()
        
        # 准备数据
        data = []
        for resume in resumes:
            # 安全获取模板名称
            template_name = '未知模板'
            try:
                if resume.template and hasattr(resume.template, 'name'):
                    template_name = resume.template.name
            except:
                pass
            
            # 安全获取状态
            status = 'draft'
            try:
                if resume.status and hasattr(resume.status, 'value'):
                    status = resume.status.value
                elif resume.status:
                    status = str(resume.status)
            except:
                pass
            
            # 安全获取时间
            created_time = ''
            updated_time = ''
            try:
                if resume.created_at:
                    created_time = resume.created_at.strftime('%Y-%m-%d %H:%M:%S')
                if resume.updated_at:
                    updated_time = resume.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
            
            data.append({
                '简历ID': resume.id,
                '用户ID': resume.user_id,
                '用户名': resume.user.username if resume.user else '未知用户',
                '简历标题': resume.title or '',
                '姓名': resume.full_name or '',
                '模板名称': template_name,
                '状态': status,
                '创建时间': created_time,
                '更新时间': updated_time
            })
        
        # 创建Excel文件
        df = pd.DataFrame(data)
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='简历数据', index=False)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'简历数据_{datetime.now().strftime("%Y%m%d")}.xlsx'
        )
    except Exception as e:
        import traceback
        current_app.logger.error(f"导出简历数据失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({'error': '导出失败，请稍后重试'}), 500

# 文档管理
@admin_content_bp.route('/documents', methods=['GET'])
@jwt_required()
def get_documents():
    """获取文档列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    keyword = request.args.get('keyword', '')
    
    query = Document.query.join(User, Document.user_id == User.id)
    
    # 关键词搜索
    if keyword:
        query = query.filter(
            or_(
                Document.filename.contains(keyword),
                User.username.contains(keyword)
            )
        )
    
    # 分页
    pagination = query.order_by(Document.upload_time.desc()).paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    documents = []
    for doc in pagination.items:
        doc_data = {
            'id': doc.id,
            'user_id': doc.user_id,
            'user': {
                'username': getattr(doc.user, 'username', '未知用户')
            },
            'filename': doc.filename,
            'file_type': getattr(doc, 'file_type', 'unknown'),
            'file_size': doc.file_size,
            'document_type': getattr(getattr(doc, 'document_type', None), 'value', 'standard'),
            'upload_time': doc.upload_time.isoformat() if doc.upload_time else None,
            'status': doc.status
        }
        documents.append(doc_data)
    
    return jsonify({
        'documents': documents,
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page,
        'per_page': per_page
    })

@admin_content_bp.route('/documents/<int:doc_id>', methods=['DELETE'])
@jwt_required()
def delete_document(doc_id):
    """删除文档"""
    doc = Document.query.get(doc_id)
    if not doc:
        return jsonify({'error': '文档不存在'}), 404
    
    try:
        # 删除物理文件
        if doc.file_path and os.path.exists(doc.file_path):
            os.remove(doc.file_path)
        
        db.session.delete(doc)
        db.session.commit()
        return jsonify({'message': '文档删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '删除失败'}), 500

@admin_content_bp.route('/documents/<int:doc_id>/download', methods=['GET'])
@jwt_required()
def download_document(doc_id):
    """下载文档"""
    doc = Document.query.get(doc_id)
    if not doc:
        return jsonify({'error': '文档不存在'}), 404
    
    if not doc.file_path or not os.path.exists(doc.file_path):
        return jsonify({'error': '文件不存在'}), 404
    
    return send_file(
        doc.file_path,
        as_attachment=True,
        download_name=doc.filename
    )

@admin_content_bp.route('/documents/export', methods=['GET'])
@jwt_required()
def export_documents():
    """导出文档数据"""
    try:
        keyword = request.args.get('keyword', '')
        
        query = Document.query.join(User, Document.user_id == User.id)
        
        if keyword:
            query = query.filter(
                or_(
                    Document.filename.contains(keyword),
                    User.username.contains(keyword)
                )
            )
        
        documents = query.order_by(Document.upload_time.desc()).all()
        
        # 准备数据
        data = []
        for doc in documents:
            # 安全获取文件类型
            file_type = 'unknown'
            try:
                if doc.file_type and hasattr(doc.file_type, 'value'):
                    file_type = doc.file_type.value
                elif doc.file_type:
                    file_type = str(doc.file_type)
            except:
                pass
            
            # 安全获取文档类型
            document_type = 'standard'
            try:
                if doc.document_type and hasattr(doc.document_type, 'value'):
                    document_type = doc.document_type.value
                elif doc.document_type:
                    document_type = str(doc.document_type)
            except:
                pass
            
            # 安全获取时间
            upload_time = ''
            try:
                if doc.upload_time:
                    upload_time = doc.upload_time.strftime('%Y-%m-%d %H:%M:%S')
            except:
                pass
            
            data.append({
                '文档ID': doc.id,
                '用户ID': doc.user_id,
                '用户名': doc.user.username if doc.user else '未知用户',
                '文件名': doc.filename or '',
                '文件类型': file_type,
                '文件大小': doc.file_size or 0,
                '文档类型': document_type,
                '上传时间': upload_time,
                '状态': doc.status or ''
            })
        
        # 创建Excel文件
        df = pd.DataFrame(data)
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='文档数据', index=False)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'文档数据_{datetime.now().strftime("%Y%m%d")}.xlsx'
        )
    except Exception as e:
        import traceback
        current_app.logger.error(f"导出文档数据失败: {str(e)}\n{traceback.format_exc()}")
        return jsonify({'error': '导出失败，请稍后重试'}), 500

# 图片管理
@admin_content_bp.route('/images', methods=['GET'])
@jwt_required()
def get_images():
    """获取图片列表"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    keyword = request.args.get('keyword', '')
    
    query = Image.query.join(User, Image.user_id == User.id)
    
    # 关键词搜索
    if keyword:
        query = query.filter(
            or_(
                Image.original_filename.contains(keyword),
                User.username.contains(keyword)
            )
        )
    
    # 分页
    pagination = query.order_by(Image.created_at.desc()).paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    images = []
    for img in pagination.items:
        img_data = {
            'id': img.id,
            'user_id': img.user_id,
            'user': {
                'username': getattr(img.user, 'username', '未知用户')
            },
            'filename': img.original_filename,
            'file_type': img.mime_type,
            'file_size': img.file_size,
            'width': img.width,
            'height': img.height,
            'upload_source': img.upload_source,
            'status': img.status,
            'created_at': img.created_at.isoformat() if img.created_at else None,
            'file_path': img.file_path,  # 新增
        }
        images.append(img_data)
    
    return jsonify({
        'images': images,
        'total': pagination.total,
        'pages': pagination.pages,
        'current_page': page,
        'per_page': per_page
    })

@admin_content_bp.route('/images/<int:image_id>', methods=['DELETE'])
@jwt_required()
def delete_image(image_id):
    """删除图片"""
    image = Image.query.get(image_id)
    if not image:
        return jsonify({'error': '图片不存在'}), 404
    
    try:
        # 删除物理文件
        image.delete_files()
        
        db.session.delete(image)
        db.session.commit()
        return jsonify({'message': '图片删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': '删除失败'}), 500

@admin_content_bp.route('/images/<int:image_id>/download', methods=['GET'])
@jwt_required()
def download_image(image_id):
    """下载图片"""
    image = Image.query.get(image_id)
    if not image:
        return jsonify({'error': '图片不存在'}), 404
    
    file_path = image.get_full_path()
    if not os.path.exists(file_path):
        return jsonify({'error': '文件不存在'}), 404
    
    return send_file(
        file_path,
        as_attachment=True,
        download_name=image.original_filename
    )

@admin_content_bp.route('/images/export', methods=['GET'])
@jwt_required()
def export_images():
    """导出图片数据"""
    keyword = request.args.get('keyword', '')
    
    query = Image.query.join(User, Image.user_id == User.id)
    
    if keyword:
        query = query.filter(
            or_(
                Image.original_filename.contains(keyword),
                User.username.contains(keyword)
            )
        )
    
    images = query.order_by(Image.created_at.desc()).all()
    
    # 准备数据
    data = []
    for img in images:
        data.append({
            '图片ID': img.id,
            '用户ID': img.user_id,
            '用户名': img.user.username if img.user else '未知用户',
            '文件名': img.original_filename,
            '文件类型': img.mime_type,
            '文件大小': img.file_size,
            '图片尺寸': f"{img.width}x{img.height}" if img.width and img.height else '未知',
            '上传来源': img.upload_source,
            '状态': img.status,
            '创建时间': img.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 创建Excel文件
    df = pd.DataFrame(data)
    output = BytesIO()
    with pd.ExcelWriter(output, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='图片数据', index=False)
    
    output.seek(0)
    
    return send_file(
        output,
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        as_attachment=True,
        download_name=f'图片数据_{datetime.now().strftime("%Y%m%d")}.xlsx'
    )

# 内容统计
@admin_content_bp.route('/content/stats', methods=['GET'])
@jwt_required()
def get_content_stats():
    """获取内容统计信息"""
    try:
        # 名片统计
        total_business_cards = BusinessCard.query.count()
        today_business_cards = BusinessCard.query.filter(
            BusinessCard.created_at >= datetime.utcnow().date()
        ).count()
        
        # 简历统计
        total_resumes = Resume.query.count()
        today_resumes = Resume.query.filter(
            Resume.created_at >= datetime.utcnow().date()
        ).count()
        
        # 文档统计
        total_documents = Document.query.count()
        today_documents = Document.query.filter(
            Document.upload_time >= datetime.utcnow().date()
        ).count()
        
        # 图片统计
        total_images = Image.query.count()
        today_images = Image.query.filter(
            Image.created_at >= datetime.utcnow().date()
        ).count()
        
        # 总文件大小
        total_file_size = db.session.query(func.sum(Image.file_size)).scalar() or 0
        total_file_size += db.session.query(func.sum(Document.file_size)).scalar() or 0
        
        return jsonify({
            'business_cards': {
                'total': total_business_cards,
                'today': today_business_cards
            },
            'resumes': {
                'total': total_resumes,
                'today': today_resumes
            },
            'documents': {
                'total': total_documents,
                'today': today_documents
            },
            'images': {
                'total': total_images,
                'today': today_images
            },
            'storage': {
                'total_size_bytes': total_file_size,
                'total_size_mb': round(total_file_size / (1024 * 1024), 2)
            }
        })
        
    except Exception as e:
        return jsonify({'error': '获取统计失败'}), 500 