"""
用户模型类
"""
from datetime import datetime
from typing import Dict, Optional, Tuple, Any
from app import db
from app.utils.security_utils import generate_password_hash, verify_password
from flask_jwt_extended import create_access_token, create_refresh_token

class User(db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    email = db.Column(db.String(100), unique=True, nullable=False, index=True)
    phone = db.Column(db.String(20), index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    salt = db.Column(db.String(32), nullable=False)  # 新增盐值字段
    avatar_url = db.Column(db.String(500))
    nickname = db.Column(db.String(100))
    gender = db.Column(db.SmallInteger)  # 1:男 2:女
    birthday = db.Column(db.Date)
    status = db.Column(db.SmallInteger, default=1)  # 1:正常 2:禁用
    is_admin = db.Column(db.Boolean, default=False)  # 是否为管理员
    is_active = db.Column(db.Boolean, default=True)  # 是否激活
    vip_level = db.Column(db.SmallInteger, default=0)  # 0:普通用户
    vip_expire_time = db.Column(db.DateTime)
    
    # 积分相关
    points = db.Column(db.Integer, default=0, comment='用户积分')
    points_used = db.Column(db.Integer, default=0, comment='已使用积分')

    last_login_time = db.Column(db.DateTime)
    last_login_ip = db.Column(db.String(45))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    images = db.relationship('Image', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    business_cards = db.relationship('BusinessCard', back_populates='user', lazy='dynamic', cascade='all, delete-orphan')
    processing_tasks = db.relationship('ProcessingTask', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    user_logs = db.relationship('UserLog', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        password = kwargs.pop('password', None)
        super(User, self).__init__(**kwargs)
        if password:
            self.set_password(password)
    
    def set_password(self, password: str) -> None:
        """设置密码"""
        self.password_hash, self.salt = generate_password_hash(password)
    
    def check_password(self, password: str) -> bool:
        """验证密码"""
        return verify_password(password, self.password_hash, self.salt)
    
    def generate_tokens(self) -> Dict[str, str]:
        """生成JWT令牌"""
        access_token = create_access_token(identity=str(self.id))
        refresh_token = create_refresh_token(identity=str(self.id))
        return {
            'access_token': access_token,
            'refresh_token': refresh_token
        }
    
    @property
    def is_vip(self) -> bool:
        """检查是否为VIP用户"""
        if self.vip_level > 0 and self.vip_expire_time:
            return self.vip_expire_time > datetime.utcnow()
        return False
    

    
    def get_daily_usage_count(self, date: Optional[datetime] = None) -> int:
        """
        获取每日使用次数
        :param date: 指定日期，默认为当天
        :return: 使用次数
        """
        query_date = date.date() if date else datetime.utcnow().date()
        
        from app.models.processing_task import ProcessingTask
        return ProcessingTask.query.filter(
            ProcessingTask.user_id == self.id,
            db.func.date(ProcessingTask.created_at) == query_date
        ).count()
    
    def can_process_image(self) -> Tuple[bool, str]:
        """
        检查是否可以处理图像
        :return: (是否可以处理, 原因消息)
        """
        if not self.is_active:
            return False, "用户账户未激活"
        
        if self.status != 1:
            return False, "用户账户已被禁用"
        
        if self.points <= 0:
            return False, "积分不足"
        
        return True, "可以处理"
    
    def consume_credits(self, amount: int, description: str, related_id: str = None) -> bool:
        """
        消费积分
        :param amount: 消费数量
        :param description: 消费描述
        :param related_id: 关联ID
        :return: 是否成功
        """
        if self.points < amount:
            return False
        
        self.points -= amount
        self.points_used += amount
        
        # 记录积分日志
        from app.models.points_log import PointsLog
        log = PointsLog(
            user_id=self.id,
            points=-amount,
            balance=self.points,
            type='consume',
            description=description,
            related_id=related_id
        )
        db.session.add(log)
        db.session.commit()
        return True
    
    def add_credits(self, amount: int, description: str, related_id: str = None) -> None:
        """
        增加积分
        :param amount: 增加数量
        :param description: 增加描述
        :param related_id: 关联ID
        """
        self.points += amount
        
        # 记录积分日志
        from app.models.points_log import PointsLog
        log = PointsLog(
            user_id=self.id,
            points=amount,
            balance=self.points,
            type='earn',
            description=description,
            related_id=related_id
        )
        db.session.add(log)
        db.session.commit()
    
    def update_login_info(self, ip_address: str) -> None:
        """
        更新登录信息
        :param ip_address: 登录IP地址
        """
        self.last_login_time = datetime.utcnow()
        self.last_login_ip = ip_address
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """
        转换为字典格式
        :param include_sensitive: 是否包含敏感信息
        :return: 字典格式的用户信息
        """
        result = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'nickname': self.nickname,
            'avatar_url': self.avatar_url,
            'gender': self.gender,
            'birthday': self.birthday.isoformat() if self.birthday else None,
            'status': self.status,
            'is_admin': self.is_admin,
            'is_active': self.is_active,
            'vip_level': self.vip_level,
            'vip_expire_time': self.vip_expire_time.isoformat() if self.vip_expire_time else None,
            'points': self.points,
            'points_used': self.points_used,
            'is_vip': self.is_vip,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_sensitive:
            result.update({
                'phone': self.phone,
                'last_login_time': self.last_login_time.isoformat() if self.last_login_time else None,
                'last_login_ip': self.last_login_ip
            })
        
        return result