<template>
  <el-card class="quick-card">
    <div class="quick-header">
      <el-icon><CreditCard /></el-icon>
      <span>名片生成</span>
    </div>
    <el-button type="primary" @click="goToCard">新建名片</el-button>
    <div class="quick-desc">
      <el-alert type="info" show-icon :closable="false">
        多模板，支持二维码，快速生成电子名片。
      </el-alert>
    </div>
  </el-card>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { CreditCard } from '@element-plus/icons-vue'
const router = useRouter()
const goToCard = () => router.push('/business-cards')
</script>
<style scoped>
.quick-card { min-width: 320px; }
.quick-header { display: flex; align-items: center; gap: 8px; font-size: 18px; font-weight: bold; margin-bottom: 12px; }
.quick-desc { margin-top: 8px; }
</style> 