#!/usr/bin/env python3
"""
初始化相纸规格数据
"""
import sys
import os

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)
os.chdir(backend_dir)

try:
    from app import create_app, db
    from app.models.paper_size import PaperSize
    
    def init_paper_sizes():
        """初始化相纸规格数据"""
        app = create_app('development')
        
        with app.app_context():
            try:
                # 检查是否已有数据
                existing_count = PaperSize.query.count()
                if existing_count > 0:
                    print(f"✅ 已存在 {existing_count} 个相纸规格")
                    return True
                
                # 常用相纸规格数据
                paper_sizes_data = [
                    {
                        'name': '4寸照片',
                        'width_mm': 101.6,
                        'height_mm': 152.4,
                        'width_px': 1200,
                        'height_px': 1800,
                        'margin_mm': 5.0,
                        'is_common': True,
                        'sort_order': 1
                    },
                    {
                        'name': '5寸照片',
                        'width_mm': 127.0,
                        'height_mm': 178.0,
                        'width_px': 1500,
                        'height_px': 2100,
                        'margin_mm': 5.0,
                        'is_common': True,
                        'sort_order': 2
                    },
                    {
                        'name': '6寸照片',
                        'width_mm': 152.4,
                        'height_mm': 203.2,
                        'width_px': 1800,
                        'height_px': 2400,
                        'margin_mm': 5.0,
                        'is_common': True,
                        'sort_order': 3
                    },
                    {
                        'name': '7寸照片',
                        'width_mm': 177.8,
                        'height_mm': 254.0,
                        'width_px': 2100,
                        'height_px': 3000,
                        'margin_mm': 5.0,
                        'is_common': True,
                        'sort_order': 4
                    },
                    {
                        'name': '8寸照片',
                        'width_mm': 203.2,
                        'height_mm': 254.0,
                        'width_px': 2400,
                        'height_px': 3000,
                        'margin_mm': 5.0,
                        'is_common': True,
                        'sort_order': 5
                    },
                    {
                        'name': 'A4纸',
                        'width_mm': 210.0,
                        'height_mm': 297.0,
                        'width_px': 2480,
                        'height_px': 3508,
                        'margin_mm': 10.0,
                        'is_common': True,
                        'sort_order': 6
                    },
                    {
                        'name': 'A5纸',
                        'width_mm': 148.0,
                        'height_mm': 210.0,
                        'width_px': 1748,
                        'height_px': 2480,
                        'margin_mm': 8.0,
                        'is_common': False,
                        'sort_order': 7
                    },
                    {
                        'name': 'A6纸',
                        'width_mm': 105.0,
                        'height_mm': 148.0,
                        'width_px': 1240,
                        'height_px': 1748,
                        'margin_mm': 5.0,
                        'is_common': False,
                        'sort_order': 8
                    }
                ]
                
                # 创建相纸规格记录
                for data in paper_sizes_data:
                    paper_size = PaperSize(**data)
                    db.session.add(paper_size)
                
                db.session.commit()
                
                print(f"✅ 成功创建 {len(paper_sizes_data)} 个相纸规格")
                
                # 验证数据
                all_papers = PaperSize.query.order_by(PaperSize.sort_order).all()
                for paper in all_papers:
                    print(f"   - {paper.name}: {paper.width_mm}x{paper.height_mm}mm")
                
                return True
                
            except Exception as e:
                print(f"❌ 初始化相纸规格失败: {e}")
                db.session.rollback()
                return False
    
    if __name__ == '__main__':
        init_paper_sizes()
        
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
