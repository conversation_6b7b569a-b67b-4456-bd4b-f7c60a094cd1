<template>
  <div class="business-card-preview-page">
    <div class="preview-container">
      <div class="preview-header">
        <h1>{{ businessCard?.title || '名片预览' }}</h1>
        <div class="preview-actions">
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <el-button type="primary" @click="downloadCard">
            <el-icon><Download /></el-icon>
            下载
          </el-button>
        </div>
      </div>
      
      <div class="preview-content">
        <div class="card-display">
          <BusinessCardPreview 
            v-if="businessCard && template"
            :business-card="businessCard"
            :template="template"
            :key="previewKey"
          />
          <div v-else-if="loading" class="loading-state">
            <el-icon class="is-loading"><Loading /></el-icon>
            <p>加载中...</p>
          </div>
          <div v-else class="error-state">
            <el-icon><Warning /></el-icon>
            <p>{{ errorMessage || '名片不存在或未公开' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Download, Loading, Warning } from '@element-plus/icons-vue'
import api from '@/api'
import { handleApiError } from '@/utils/api'
import BusinessCardPreview from '@/components/business-card/BusinessCardPreview.vue'

export default {
  name: 'BusinessCardPreviewPage',
  
  components: {
    ArrowLeft, Download, Loading, Warning,
    BusinessCardPreview
  },

  setup() {
    const route = useRoute()
    const router = useRouter()
    
    const loading = ref(false)
    const businessCard = ref(null)
    const template = ref(null)
    const errorMessage = ref('')
    const previewKey = ref(0)

    // 加载名片数据
    const loadBusinessCard = async () => {
      loading.value = true
      errorMessage.value = ''
      
      try {
        const response = await api.businessCards.getDetail(route.params.id)
        if (response.data.success) {
          const data = response.data.data.business_card
          businessCard.value = data
          template.value = data.template
          previewKey.value++
        }
      } catch (error) {
        console.error('加载名片失败:', error)
        if (error.response?.status === 404) {
          errorMessage.value = '名片不存在'
        } else {
          errorMessage.value = '加载名片失败'
        }
        handleApiError(error, '加载名片失败')
      } finally {
        loading.value = false
      }
    }

    // 下载名片
    const downloadCard = async () => {
      if (!businessCard.value) {
        ElMessage.warning('名片数据不存在')
        return
      }

      try {
        // 获取预览组件实例
        const previewComponent = document.querySelector('.card-display .business-card-preview')
        if (!previewComponent) {
          throw new Error('预览组件未找到')
        }

        // 导入工具函数
        const { elementToImage, downloadImage } = await import('@/utils/htmlToImage.js')
        
        // 转换为图片
        const blob = await elementToImage(previewComponent, {
          scale: 3, // 高清晰度
          backgroundColor: '#ffffff',
          width: 900,
          height: 540
        })
        
        // 下载
        const filename = `${businessCard.value.title || 'business-card'}.png`
        downloadImage(blob, filename)
        
        ElMessage.success('名片已开始下载')
      } catch (error) {
        console.error('下载失败:', error)
        ElMessage.error('下载失败，请重试')
      }
    }

    // 返回
    const goBack = () => {
      router.go(-1)
    }

    onMounted(() => {
      loadBusinessCard()
    })

    return {
      loading,
      businessCard,
      template,
      errorMessage,
      previewKey,
      downloadCard,
      goBack
    }
  }
}
</script>

<style scoped>
.business-card-preview-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.preview-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.preview-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 10px;
}

.preview-content {
  padding: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.card-display {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-state .el-icon {
  font-size: 40px;
  color: #409eff;
  margin-bottom: 15px;
}

.error-state .el-icon {
  font-size: 40px;
  color: #f56c6c;
  margin-bottom: 15px;
}

.loading-state p,
.error-state p {
  color: #606266;
  font-size: 16px;
  margin: 0;
}

@media (max-width: 768px) {
  .business-card-preview-page {
    padding: 10px;
  }
  
  .preview-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .preview-header h1 {
    font-size: 20px;
    text-align: center;
  }
  
  .preview-actions {
    justify-content: center;
  }
  
  .preview-content {
    padding: 20px;
  }
}
</style>