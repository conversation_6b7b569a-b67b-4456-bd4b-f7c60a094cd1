<template>
  <div class="modal-bg" @click.self="$emit('close')">
    <div class="modal">
      <h3>保存/导出</h3>
      <div class="form-group">
        <label>文件名:</label>
        <input type="text" v-model="options.filename" />
      </div>
      <div class="form-group">
        <label>导出格式:</label>
        <select v-model="options.format">
          <option value="pdf">PDF</option>
          <option value="png">PNG (每页一张图)</option>
          <option value="jpeg">JPEG (每页一张图)</option>
        </select>
      </div>
      <div class="form-group">
        <label><input type="checkbox" v-model="options.includeAnnotations" /> 保留批注</label>
      </div>
      <div class="modal-actions">
        <button class="btn btn-primary" @click="$emit('save', options)">确认导出</button>
        <button class="btn" @click="$emit('close')">取消</button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';

const props = defineProps(['file']);
const options = ref({
  filename: '',
  format: 'pdf',
  includeAnnotations: true
});

watch(
  () => props.file,
  (newFile) => {
    if (newFile) {
      options.value.filename = newFile.name.replace(/\.pdf$/i, '');
    }
  },
  { immediate: true }
);
</script>
<style scoped>
.modal-bg { position: fixed; top:0; left:0; right:0; bottom:0; background:rgba(0,0,0,0.2); display:flex; align-items:center; justify-content:center; z-index:1000; }
.modal { background:#fff; border-radius:8px; padding:2rem; min-width:300px; }
.btn { margin: 1rem 1rem 0 0; }
.form-group {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}
.form-group label { font-weight: 500; }
.form-group input[type="text"], .form-group select {
  width: 100%;
  padding: 0.5em;
  border: 1px solid #ccc;
  border-radius: 4px;
}
</style> 