<template>
  <div class="style-configurator">
    <div v-if="template" class="config-sections">
      
      <!-- 颜色配置 -->
      <div class="config-section">
        <h4>颜色与样式</h4>
        <div class="style-controls">
          <div class="style-item">
            <label>主色调</label>
            <div class="controls">
              <el-color-picker v-model="localConfig.primary" @change="updateStyle"/>
            </div>
          </div>
          <div class="style-item">
            <label>辅助色</label>
            <div class="controls">
              <el-color-picker v-model="localConfig.secondary" @change="updateStyle"/>
            </div>
          </div>
          <div class="style-item">
            <label>岗位文字色</label>
            <div class="controls">
              <el-color-picker v-model="localConfig.text" @change="updateStyle"/>
              <el-select v-model="localConfig.textFontStyle" @change="updateStyle" placeholder="样式">
                <el-option label="常规" value="normal"></el-option>
                <el-option label="粗体" value="bold"></el-option>
                <el-option label="斜体" value="italic"></el-option>
              </el-select>
            </div>
          </div>
          <div class="style-item">
            <label>信息文字色</label>
            <div class="controls">
              <el-color-picker v-model="localConfig.light" @change="updateStyle"/>
              <el-select v-model="localConfig.lightFontStyle" @change="updateStyle" placeholder="样式">
                <el-option label="常规" value="normal"></el-option>
                <el-option label="粗体" value="bold"></el-option>
                <el-option label="斜体" value="italic"></el-option>
              </el-select>
            </div>
          </div>
          <div class="style-item">
            <label>姓名颜色</label>
            <div class="controls">
              <el-color-picker v-model="localConfig.nameColor" @change="updateStyle"/>
              <el-select v-model="localConfig.nameFontStyle" @change="updateStyle" placeholder="样式">
                <el-option label="常规" value="normal"></el-option>
                <el-option label="粗体" value="bold"></el-option>
                <el-option label="斜体" value="italic"></el-option>
              </el-select>
            </div>
          </div>
          <div class="style-item">
            <label>公司颜色</label>
            <div class="controls">
              <el-color-picker v-model="localConfig.companyColor" @change="updateStyle"/>
              <el-select v-model="localConfig.companyFontStyle" @change="updateStyle" placeholder="样式">
                <el-option label="常规" value="normal"></el-option>
                <el-option label="粗体" value="bold"></el-option>
                <el-option label="斜体" value="italic"></el-option>
              </el-select>
            </div>
          </div>
        </div>
      </div>

      <!-- 字体配置 -->
      <div class="config-section">
        <h4>字体设置</h4>
        <div class="font-controls">
          <div class="control-item">
            <label>字体大小</label>
            <el-slider 
              v-model="localConfig.fontSize" 
              :min="50" 
              :max="150"
              :step="10"
              show-stops
              @input="updateStyle"
            />
          </div>
        </div>
      </div>

      <!-- 重置按钮 -->
      <div class="config-section">
        <el-button @click="resetToDefault" size="small">
          重置为默认
        </el-button>
      </div>
    </div>
    
    <div v-else class="no-template">
      <p>请先选择模板</p>
    </div>
  </div>
</template>

<script>
import { reactive, watch, onMounted } from 'vue'
import { ElColorPicker, ElSlider, ElButton, ElSelect, ElOption } from 'element-plus'

export default {
  name: 'StyleConfigurator',
  
  components: {
    ElColorPicker,
    ElSlider,
    ElButton,
    ElSelect,
    ElOption
  },
  
  props: {
    modelValue: {
      type: Object,
      default: () => ({})
    },
    template: {
      type: Object,
      default: null
    }
  },
  
  emits: ['update:modelValue', 'change'],
  
  setup(props, { emit }) {
    const localConfig = reactive({
      primary: '#3498db',
      secondary: '#2c3e50',
      text: '#333333', textFontStyle: 'normal',
      light: '#999999', lightFontStyle: 'normal',
      nameColor: '#000000', nameFontStyle: 'bold',
      companyColor: '#333333', companyFontStyle: 'normal',
      fontSize: 100
    })
    
    // 初始化配置
    const initConfig = () => {
      if (props.template && props.template.default_colors) {
        Object.assign(localConfig, props.template.default_colors)
      }
      
      if (props.modelValue) {
        Object.assign(localConfig, props.modelValue)
      }

      if (!localConfig.fontSize) {
        localConfig.fontSize = 100;
      }
    }
    
    // 更新样式
    const updateStyle = () => {
      const config = { ...localConfig }
      emit('update:modelValue', config)
      emit('change', config)
    }
    
    // 重置为默认
    const resetToDefault = () => {
      if (props.template && props.template.default_colors) {
        Object.assign(localConfig, props.template.default_colors)
        updateStyle()
      }
    }
    
    // 监听模板变化
    watch(() => props.template, () => {
      initConfig()
    }, { immediate: true })
    
    // 监听外部值变化
    watch(() => props.modelValue, (newVal) => {
      if (newVal) {
        Object.assign(localConfig, newVal);
        if(!localConfig.fontSize){
          localConfig.fontSize = 100;
        }
      }
    }, { deep: true, immediate: true })
    
    onMounted(() => {
      // onMounted 的逻辑可以被 watch(immediate: true) 替代
      // initConfig()
    })
    
    return {
      localConfig,
      updateStyle,
      resetToDefault
    }
  }
}
</script>

<style scoped>
.style-configurator {
  padding: 10px 0;
}

.config-sections {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.config-section {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.config-section:last-child {
  border-bottom: none;
}

.config-section h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #303133;
}

.style-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.style-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.style-item label {
  font-size: 13px;
  color: #606266;
  flex-shrink: 0;
  margin-right: 16px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.controls .el-select {
  width: 80px;
}

.font-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.control-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.control-item label {
  font-size: 13px;
  color: #606266;
  margin-right: 10px;
}

.control-item .el-slider {
  flex: 1;
  margin-left: 15px;
}

.no-template {
  text-align: center;
  color: #909399;
  padding: 20px;
}
</style> 