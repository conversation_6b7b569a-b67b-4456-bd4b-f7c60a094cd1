<template>
  <div class="reports-center">
    <h2>报表中心</h2>
    <el-card class="report-card" v-for="report in reports" :key="report.key">
      <div class="report-header">
        <span>{{ report.title }}</span>
        <el-button type="primary" size="small" :loading="loading[report.key]" @click="exportReport(report)">导出</el-button>
      </div>
      <div class="report-desc">{{ report.desc }}</div>
      <!-- 可扩展筛选条件 -->
    </el-card>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

const reports = [
  { key: 'users', title: '用户报表', desc: '导出所有用户信息', api: api.admin.users.exportUsers },
  { key: 'businessCards', title: '名片报表', desc: '导出所有名片信息', api: api.admin.content.exportBusinessCards },
  { key: 'resumes', title: '简历报表', desc: '导出所有简历信息', api: api.admin.content.exportResumes },
  { key: 'documents', title: '文档报表', desc: '导出所有文档信息', api: api.admin.content.exportDocuments },
  { key: 'images', title: '图片报表', desc: '导出所有图片信息', api: api.admin.content.exportImages },
  { key: 'userLogs', title: '用户日志报表', desc: '导出用户操作日志', api: api.admin.users.exportUserLogs },
]

const loading = reactive({})

function exportReport(report) {
  loading[report.key] = true
  report.api({})
    .then(res => {
      const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${report.title}.xlsx`
      a.click()
      window.URL.revokeObjectURL(url)
      ElMessage.success('报表导出成功')
    })
    .catch(() => {
      ElMessage.error('导出失败')
    })
    .finally(() => {
      loading[report.key] = false
    })
}
</script>

<style scoped>
.reports-center {
  max-width: 800px;
  margin: 0 auto;
  padding: 32px 0;
}
.report-card {
  margin-bottom: 24px;
}
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}
.report-desc {
  color: #888;
  margin-top: 8px;
}
</style>