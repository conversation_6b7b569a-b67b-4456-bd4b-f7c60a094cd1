from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, JSON, DECIMAL
from sqlalchemy.orm import relationship
from app import db

class BusinessCardTemplate(db.Model):
    """名片模板表"""
    __tablename__ = 'business_card_templates'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, comment='模板名称')
    description = Column(Text, comment='模板描述')
    category = Column(String(50), default='business', comment='分类')
    industry = Column(String(50), default='other', comment='适用行业')
    preview_image = Column(String(500), comment='预览图URL')
    layout_type = Column(String(30), default='standard', comment='布局类型')
    width_mm = Column(DECIMAL(5, 2), default=90.00, comment='宽度(毫米)')
    height_mm = Column(DECIMAL(5, 2), default=54.00, comment='高度(毫米)')
    template_html = Column(Text, nullable=False, comment='模板HTML结构')
    template_css = Column(Text, nullable=False, comment='模板CSS样式')
    default_colors = Column(JSON, comment='默认颜色方案')
    is_active = Column(Boolean, default=True, comment='是否启用')
    usage_count = Column(Integer, default=0, comment='使用次数')
    sort_order = Column(Integer, default=0, comment='排序')
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    business_cards = relationship("BusinessCard", back_populates="template", lazy=True)
    
    def to_dict(self):
        """转换为字典格式"""
        # 安全地获取日期时间字段
        created_at = getattr(self, 'created_at', None)
        updated_at = getattr(self, 'updated_at', None)
        width = getattr(self, 'width_mm', 90.0)
        height = getattr(self, 'height_mm', 54.0)
        
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'category': self.category,
            'industry': self.industry,
            'preview_image': self.preview_image,
            'layout_type': self.layout_type,
            'width_mm': float(width) if width is not None else 90.0,
            'height_mm': float(height) if height is not None else 54.0,
            'template_html': self.template_html,
            'template_css': self.template_css,
            'default_colors': self.default_colors,
            'is_active': self.is_active,
            'usage_count': self.usage_count,
            'sort_order': self.sort_order,
            'created_at': created_at.isoformat() if created_at else None,
            'updated_at': updated_at.isoformat() if updated_at else None
        }
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count = (self.usage_count or 0) + 1
    
    @classmethod
    def get_by_category(cls, category):
        """根据分类获取模板"""
        return cls.query.filter_by(category=category, is_active=True).order_by(cls.sort_order).all()
    
    @classmethod
    def get_popular_templates(cls, limit=10):
        """获取热门模板"""
        return cls.query.filter_by(is_active=True).order_by(db.desc(cls.usage_count)).limit(limit).all()

class BusinessCard(db.Model):
    """用户名片表"""
    __tablename__ = 'business_cards'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False, index=True)
    template_id = Column(Integer, ForeignKey('business_card_templates.id'), nullable=False)
    title = Column(String(200), nullable=False, comment='名片标题')
    card_data = Column(JSON, nullable=False, comment='名片数据')
    style_config = Column(JSON, comment='样式配置')
    qr_code_data = Column(Text, comment='二维码数据')
    qr_code_image = Column(String(500), comment='二维码图片路径')
    status = Column(String(20), default='draft', comment='状态')
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    user = relationship("User", back_populates="business_cards")
    template = relationship("BusinessCardTemplate", back_populates="business_cards")
    
    def to_dict(self, include_template=False):
        """转换为字典格式"""
        # 安全地获取字段值
        created_at = getattr(self, 'created_at', None)
        updated_at = getattr(self, 'updated_at', None)
        card_data = getattr(self, 'card_data', {})
        style_config = getattr(self, 'style_config', {})
        
        result = {
            'id': self.id,
            'user_id': self.user_id,
            'template_id': self.template_id,
            'title': self.title,
            'card_data': card_data,
            'style_config': style_config,
            'qr_code_data': self.qr_code_data,
            'qr_code_image': self.qr_code_image,
            'status': self.status,
            'created_at': created_at.isoformat() if created_at else None,
            'updated_at': updated_at.isoformat() if updated_at else None
        }
        
        if include_template and self.template:
            result['template'] = self.template.to_dict()
            
        return result
    
    def get_display_name(self):
        """获取显示名称"""
        # 从名片数据中获取名字
        card_data = getattr(self, 'card_data', {})
        if card_data and isinstance(card_data, dict) and 'name' in card_data:
            return card_data['name']
        return self.title
        
    def get_description(self) -> str:
        """获取名片描述"""
        # 从名片数据中获取公司和职位
        card_data = getattr(self, 'card_data', {})
        if not card_data or not isinstance(card_data, dict):
            return ""
        
        company = card_data.get('company', '')
        title = card_data.get('title', '')
        if company and title:
            return f"{title} @ {company}"
        elif company:
            return company
        elif title:
            return title
        return ""
    
    def get_colors(self) -> dict:
        """获取名片颜色配置"""
        style_config = getattr(self, 'style_config', {})
        if not style_config or not isinstance(style_config, dict):
            return {}
    
    def get_qr_url(self):
        """获取二维码查看URL"""
        return f"/api/business-cards/{self.id}/view"
    
    def update_card_data(self, new_data: dict) -> None:
        """更新名片数据"""
        card_data = getattr(self, 'card_data', {})
        if not isinstance(card_data, dict):
            card_data = {}
        
        # 合并数据
        card_data.update(new_data)
        setattr(self, 'card_data', card_data)
        setattr(self, 'updated_at', datetime.utcnow())
        
        # 标记为已修改，确保SQLAlchemy检测到JSON字段的变化
        from sqlalchemy.orm.attributes import flag_modified
        flag_modified(self, 'card_data')
    
    def update_style_config(self, new_config: dict) -> None:
        """更新样式配置"""
        style_config = getattr(self, 'style_config', {})
        if not isinstance(style_config, dict):
            style_config = {}
        
        # 合并配置
        style_config.update(new_config)
        setattr(self, 'style_config', style_config)
        setattr(self, 'updated_at', datetime.utcnow())
        
        # 标记为已修改
        from sqlalchemy.orm.attributes import flag_modified
        flag_modified(self, 'style_config')