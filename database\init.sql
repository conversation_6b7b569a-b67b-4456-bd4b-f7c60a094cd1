-- 相片制作工具数据库初始化脚本
USE photo_maker;

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
CREATE TABLE `users` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    `email` VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
    `avatar_url` VARCHAR(500) DEFAULT NULL COMMENT '头像URL',
    `nickname` VARCHAR(100) DEFAULT NULL COMMENT '昵称',
    `gender` TINYINT(1) DEFAULT NULL COMMENT '性别 1:男 2:女',
    `birthday` DATE DEFAULT NULL COMMENT '生日',
    `status` TINYINT(1) DEFAULT 1 COMMENT '状态 1:正常 2:禁用',
    `vip_level` TINYINT(1) DEFAULT 0 COMMENT 'VIP等级 0:普通用户',
    `vip_expire_time` DATETIME DEFAULT NULL COMMENT 'VIP过期时间',
    `total_credits` INT(11) DEFAULT 100 COMMENT '总积分',
    `used_credits` INT(11) DEFAULT 0 COMMENT '已使用积分',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(45) DEFAULT NULL COMMENT '最后登录IP',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_email` (`email`),
    KEY `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 图像文件表
CREATE TABLE `images` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `file_id` VARCHAR(100) NOT NULL UNIQUE COMMENT '文件唯一标识',
    `original_filename` VARCHAR(255) NOT NULL COMMENT '原始文件名',
    `file_path` VARCHAR(500) NOT NULL COMMENT '文件路径',
    `thumbnail_path` VARCHAR(500) DEFAULT NULL COMMENT '缩略图路径',
    `file_size` BIGINT(20) NOT NULL COMMENT '文件大小(字节)',
    `width` INT(11) DEFAULT NULL COMMENT '图片宽度',
    `height` INT(11) DEFAULT NULL COMMENT '图片高度',
    `mime_type` VARCHAR(100) NOT NULL COMMENT '文件类型',
    `upload_source` VARCHAR(50) DEFAULT 'web' COMMENT '上传来源 web/miniprogram/app',
    `status` TINYINT(1) DEFAULT 1 COMMENT '状态 1:正常 2:删除',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_file_id` (`file_id`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图像文件表';

-- 处理任务表
CREATE TABLE `processing_tasks` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `task_id` VARCHAR(255) NOT NULL UNIQUE COMMENT 'Celery任务ID',
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `image_id` INT(11) NOT NULL COMMENT '原始图像ID',
    `task_type` VARCHAR(50) NOT NULL COMMENT '任务类型',
    `operations` JSON NOT NULL COMMENT '处理操作配置',
    `status` ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending' COMMENT '任务状态',
    `progress` TINYINT(3) DEFAULT 0 COMMENT '处理进度(0-100)',
    `result_data` JSON DEFAULT NULL COMMENT '处理结果数据',
    `result_files` JSON DEFAULT NULL COMMENT '结果文件路径列表',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `processing_time` INT(11) DEFAULT NULL COMMENT '处理耗时(秒)',
    `credits_cost` INT(11) DEFAULT 0 COMMENT '消耗积分',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `started_at` DATETIME DEFAULT NULL COMMENT '开始处理时间',
    `completed_at` DATETIME DEFAULT NULL COMMENT '完成时间',
    PRIMARY KEY (`id`),
    KEY `idx_task_id` (`task_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_image_id` (`image_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`image_id`) REFERENCES `images`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='处理任务表';

-- 证件照模板表
CREATE TABLE `photo_templates` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `category` VARCHAR(50) NOT NULL COMMENT '分类',
    `width` INT(11) NOT NULL COMMENT '宽度(像素)',
    `height` INT(11) NOT NULL COMMENT '高度(像素)',
    `width_mm` DECIMAL(5,2) DEFAULT NULL COMMENT '宽度(毫米)',
    `height_mm` DECIMAL(5,2) DEFAULT NULL COMMENT '高度(毫米)',
    `dpi` INT(11) DEFAULT 300 COMMENT '分辨率',
    `background_color` VARCHAR(7) DEFAULT '#ffffff' COMMENT '默认背景色',
    `head_ratio` DECIMAL(3,2) DEFAULT 0.75 COMMENT '头部占比',
    `description` TEXT DEFAULT NULL COMMENT '描述',
    `usage_count` INT(11) DEFAULT 0 COMMENT '使用次数',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_is_active` (`is_active`),
    KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='证件照模板表';

-- 纸张规格表
CREATE TABLE `paper_sizes` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(50) NOT NULL COMMENT '纸张名称',
    `width_mm` DECIMAL(6,2) NOT NULL COMMENT '宽度(毫米)',
    `height_mm` DECIMAL(6,2) NOT NULL COMMENT '高度(毫米)',
    `width_px` INT(11) NOT NULL COMMENT '宽度(像素,300DPI)',
    `height_px` INT(11) NOT NULL COMMENT '高度(像素,300DPI)',
    `margin_mm` DECIMAL(4,2) DEFAULT 5.00 COMMENT '边距(毫米)',
    `is_common` TINYINT(1) DEFAULT 0 COMMENT '是否常用',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='纸张规格表';

-- 用户操作日志表
CREATE TABLE `user_logs` (
    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,
    `user_id` INT(11) NOT NULL COMMENT '用户ID',
    `action` VARCHAR(100) NOT NULL COMMENT '操作类型',
    `resource_type` VARCHAR(50) DEFAULT NULL COMMENT '资源类型',
    `resource_id` INT(11) DEFAULT NULL COMMENT '资源ID',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `request_data` JSON DEFAULT NULL COMMENT '请求数据',
    `response_status` INT(11) DEFAULT NULL COMMENT '响应状态码',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_action` (`action`),
    KEY `idx_created_at` (`created_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户操作日志表';

-- 系统配置表
CREATE TABLE `system_configs` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `config_key` VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    `config_value` TEXT NOT NULL COMMENT '配置值',
    `config_type` VARCHAR(20) DEFAULT 'string' COMMENT '配置类型',
    `description` VARCHAR(255) DEFAULT NULL COMMENT '描述',
    `is_system` TINYINT(1) DEFAULT 0 COMMENT '是否系统配置',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 插入默认证件照模板数据
INSERT INTO `photo_templates` (`name`, `category`, `width`, `height`, `width_mm`, `height_mm`, `background_color`, `description`, `sort_order`) VALUES
('一寸照', '常用证件照', 295, 413, 25, 35, '#ffffff', '标准一寸证件照，适用于简历、学生证等', 1),
('二寸照', '常用证件照', 413, 531, 35, 45, '#ffffff', '标准二寸证件照，适用于毕业证、工作证等', 2),
('小二寸照', '常用证件照', 413, 472, 35, 40, '#ffffff', '小二寸证件照，适用于护照、签证等', 3),
('护照照片', '出国证件', 354, 472, 30, 40, '#ffffff', '中国护照专用照片规格', 4),
('美国签证照', '出国证件', 600, 600, 51, 51, '#ffffff', '美国签证申请专用照片', 5),
('欧洲签证照', '出国证件', 413, 531, 35, 45, '#ffffff', '欧洲申根签证照片规格', 6),
('驾驶证照片', '证件照', 260, 378, 22, 32, '#ffffff', '驾驶证申请照片规格', 7),
('社保卡照片', '证件照', 358, 441, 26, 32, '#ffffff', '社保卡申请照片规格', 8),
('教师资格证', '职业证件', 288, 384, 25, 35, '#ffffff', '教师资格证申请照片', 9),
('执业证照片', '职业证件', 413, 531, 35, 45, '#ffffff', '各类执业证书照片', 10);

-- 插入纸张规格数据
INSERT INTO `paper_sizes` (`name`, `width_mm`, `height_mm`, `width_px`, `height_px`, `is_common`, `sort_order`) VALUES
('A4', 210.00, 297.00, 2480, 3508, 1, 1),
('A5', 148.00, 210.00, 1748, 2480, 0, 2),
('A6', 105.00, 148.00, 1240, 1748, 0, 3),
('5寸照片', 127.00, 89.00, 1500, 1050, 1, 4),
('6寸照片', 152.00, 102.00, 1800, 1200, 1, 5),
('7寸照片', 178.00, 127.00, 2100, 1500, 0, 6);

-- 插入系统配置数据
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`, `is_system`) VALUES
('max_upload_size', '16777216', 'integer', '最大上传文件大小(字节)', 1),
('allowed_image_types', '["jpg", "jpeg", "png", "bmp", "gif"]', 'json', '允许的图片格式', 1),
('default_background_colors', '["#ffffff", "#ff0000", "#0000ff", "#e6f3ff"]', 'json', '默认背景颜色选项', 1),
('image_quality', '95', 'integer', '图片压缩质量(1-100)', 1),
('max_image_resolution', '4096', 'integer', '最大图片分辨率', 1),
('free_user_daily_limit', '10', 'integer', '免费用户每日处理限制', 1),
('vip_user_daily_limit', '100', 'integer', 'VIP用户每日处理限制', 1),
('enable_face_beautify', 'true', 'boolean', '是否启用美颜功能', 1),
('enable_background_removal', 'true', 'boolean', '是否启用背景移除功能', 1),
('cdn_domain', '', 'string', 'CDN域名', 0);

-- 简历表
CREATE TABLE IF NOT EXISTS `resumes` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT NOT NULL,
    `title` VARCHAR(200) NOT NULL COMMENT '简历标题',
    `template_id` INT DEFAULT 1 COMMENT '模板ID',
    `status` VARCHAR(20) DEFAULT 'draft' COMMENT '状态',
    `full_name` VARCHAR(100) NOT NULL COMMENT '姓名',
    `gender` VARCHAR(10) COMMENT '性别',
    `age` INTEGER COMMENT '年龄',
    `phone` VARCHAR(20) COMMENT '电话',
    `email` VARCHAR(100) COMMENT '邮箱',
    `address` VARCHAR(200) COMMENT '地址',
    `objective` TEXT COMMENT '求职意向',
    `summary` TEXT COMMENT '个人总结',
    `photo_url` VARCHAR(500) COMMENT '照片URL',
    `photo_image_id` INT COMMENT '引用系统中的图片ID',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_template_id` (`template_id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`template_id`) REFERENCES `resume_templates`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历表';

-- 简历模板表
CREATE TABLE `resume_templates` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL COMMENT '模板名称',
    `description` TEXT DEFAULT NULL COMMENT '模板描述',
    `category` VARCHAR(50) DEFAULT 'simple' COMMENT '模板分类 simple/modern/creative/professional',
    `industry` VARCHAR(50) DEFAULT 'other' COMMENT '适用行业 it/finance/education/marketing/sales/design/other',
    `preview_image` VARCHAR(500) DEFAULT NULL COMMENT '预览图片URL',
    `template_html` TEXT NOT NULL COMMENT '模板HTML内容',
    `template_css` TEXT DEFAULT NULL COMMENT '模板CSS样式',
    `is_active` TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category`),
    KEY `idx_industry` (`industry`),
    KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='简历模板表';

-- 教育背景表
CREATE TABLE `resume_educations` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `resume_id` INT(11) NOT NULL COMMENT '简历ID',
    `school_name` VARCHAR(200) NOT NULL COMMENT '学校名称',
    `degree` VARCHAR(100) DEFAULT NULL COMMENT '学历学位',
    `major` VARCHAR(100) DEFAULT NULL COMMENT '专业',
    `start_date` VARCHAR(20) DEFAULT NULL COMMENT '开始时间',
    `end_date` VARCHAR(20) DEFAULT NULL COMMENT '结束时间',
    `gpa` DECIMAL(3,2) DEFAULT NULL COMMENT '绩点',
    `description` TEXT DEFAULT NULL COMMENT '描述',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`),
    FOREIGN KEY (`resume_id`) REFERENCES `resumes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教育背景表';

-- 工作经历表
CREATE TABLE `resume_work_experiences` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `resume_id` INT(11) NOT NULL COMMENT '简历ID',
    `company_name` VARCHAR(200) NOT NULL COMMENT '公司名称',
    `position` VARCHAR(100) NOT NULL COMMENT '职位',
    `start_date` VARCHAR(20) DEFAULT NULL COMMENT '开始时间',
    `end_date` VARCHAR(20) DEFAULT NULL COMMENT '结束时间',
    `is_current` TINYINT(1) DEFAULT 0 COMMENT '是否当前工作',
    `description` TEXT DEFAULT NULL COMMENT '工作描述',
    `achievements` JSON DEFAULT NULL COMMENT '工作成就(JSON数组)',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`),
    FOREIGN KEY (`resume_id`) REFERENCES `resumes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作经历表';

-- 项目经验表
CREATE TABLE `resume_projects` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `resume_id` INT(11) NOT NULL COMMENT '简历ID',
    `name` VARCHAR(200) NOT NULL COMMENT '项目名称',
    `role` VARCHAR(100) DEFAULT NULL COMMENT '担任角色',
    `start_date` VARCHAR(20) DEFAULT NULL COMMENT '开始时间',
    `end_date` VARCHAR(20) DEFAULT NULL COMMENT '结束时间',
    `description` TEXT DEFAULT NULL COMMENT '项目描述',
    `technologies` JSON DEFAULT NULL COMMENT '使用技术(JSON数组)',
    `achievements` JSON DEFAULT NULL COMMENT '项目成果(JSON数组)',
    `project_url` VARCHAR(500) DEFAULT NULL COMMENT '项目链接',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`),
    FOREIGN KEY (`resume_id`) REFERENCES `resumes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目经验表';

-- 技能表
CREATE TABLE `resume_skills` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `resume_id` INT(11) NOT NULL COMMENT '简历ID',
    `category` VARCHAR(100) DEFAULT NULL COMMENT '技能分类',
    `name` VARCHAR(100) NOT NULL COMMENT '技能名称',
    `proficiency` VARCHAR(20) DEFAULT NULL COMMENT '熟练度(初级/中级/高级/专家)',
    `years_experience` DECIMAL(3,1) DEFAULT NULL COMMENT '使用年限',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`),
    FOREIGN KEY (`resume_id`) REFERENCES `resumes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能表';

-- 证书表
CREATE TABLE `resume_certifications` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `resume_id` INT(11) NOT NULL COMMENT '简历ID',
    `name` VARCHAR(200) NOT NULL COMMENT '证书名称',
    `issuer` VARCHAR(200) DEFAULT NULL COMMENT '颁发机构',
    `issue_date` VARCHAR(20) DEFAULT NULL COMMENT '获得时间',
    `expiry_date` VARCHAR(20) DEFAULT NULL COMMENT '过期时间',
    `credential_id` VARCHAR(100) DEFAULT NULL COMMENT '证书编号',
    `credential_url` VARCHAR(500) DEFAULT NULL COMMENT '证书链接',
    `sort_order` INT(11) DEFAULT 0 COMMENT '排序',
    PRIMARY KEY (`id`),
    KEY `idx_resume_id` (`resume_id`),
    FOREIGN KEY (`resume_id`) REFERENCES `resumes`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='证书表';

-- 插入默认简历模板数据
INSERT INTO `resume_templates` (`name`, `description`, `category`, `industry`, `template_html`, `template_css`, `sort_order`) VALUES
('经典简约', '简洁大方的经典模板，适用于各行各业', 'simple', 'other', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ personal_info.name }} - 简历</title>
    <style>{{ css }}</style>
</head>
<body>
    <div class="header">
        {% if personal_info.photo_url %}
        <img src="{{ personal_info.photo_url }}" class="photo" alt="照片">
        {% endif %}
        <div class="name">{{ personal_info.name }}</div>
        <div class="contact-info">
            {% if personal_info.phone %}电话：{{ personal_info.phone }}{% endif %}
            {% if personal_info.email %} | 邮箱：{{ personal_info.email }}{% endif %}
            {% if personal_info.address %} | 地址：{{ personal_info.address }}{% endif %}
        </div>
    </div>
    
    {% if objective %}
    <div class="section">
        <div class="section-title">求职意向</div>
        <div>{{ objective }}</div>
    </div>
    {% endif %}
    
    {% if summary %}
    <div class="section">
        <div class="section-title">个人总结</div>
        <div>{{ summary }}</div>
    </div>
    {% endif %}
    
    {% if educations %}
    <div class="section">
        <div class="section-title">教育背景</div>
        {% for edu in educations %}
        <div class="item">
            <div class="item-header">{{ edu.school_name }} - {{ edu.major }}</div>
            <div class="item-meta">{{ edu.degree }} | {{ edu.start_date }} - {{ edu.end_date }}</div>
            {% if edu.description %}<div class="item-desc">{{ edu.description }}</div>{% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if work_experiences %}
    <div class="section">
        <div class="section-title">工作经历</div>
        {% for exp in work_experiences %}
        <div class="item">
            <div class="item-header">{{ exp.company_name }} - {{ exp.position }}</div>
            <div class="item-meta">{{ exp.start_date }} - {% if exp.is_current %}至今{% else %}{{ exp.end_date }}{% endif %}</div>
            {% if exp.description %}<div class="item-desc">{{ exp.description }}</div>{% endif %}
            {% if exp.achievements %}
            <div class="achievements">
                {% for achievement in exp.achievements %}
                <div>• {{ achievement }}</div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if projects %}
    <div class="section">
        <div class="section-title">项目经验</div>
        {% for project in projects %}
        <div class="item">
            <div class="item-header">{{ project.name }}</div>
            <div class="item-meta">{{ project.role }} | {{ project.start_date }} - {{ project.end_date }}</div>
            {% if project.description %}<div class="item-desc">{{ project.description }}</div>{% endif %}
            {% if project.technologies %}<div class="item-desc">技术栈：{{ project.technologies | join(", ") }}</div>{% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if skills %}
    <div class="section">
        <div class="section-title">专业技能</div>
        <div class="skills-list">
            {% for skill in skills %}
            <span class="skill-item">{{ skill.name }}{% if skill.proficiency %}({{ skill.proficiency }}){% endif %}</span>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    {% if certifications %}
    <div class="section">
        <div class="section-title">证书资质</div>
        {% for cert in certifications %}
        <div class="item">
            <div class="item-header">{{ cert.name }}</div>
            {% if cert.issuer %}<div class="item-meta">颁发机构：{{ cert.issuer }}</div>{% endif %}
            {% if cert.issue_date %}<div class="item-meta">获得时间：{{ cert.issue_date }}</div>{% endif %}
        </div>
        {% endfor %}
    </div>
    {% endif %}
</body>
</html>',
'body { font-family: "SimSun", Arial, sans-serif; font-size: 12px; line-height: 1.6; color: #333; margin: 0; padding: 20px; }
.header { text-align: center; border-bottom: 2px solid #3498db; margin-bottom: 20px; padding-bottom: 15px; }
.name { font-size: 24px; font-weight: bold; color: #2c3e50; margin-bottom: 10px; }
.contact-info { font-size: 11px; color: #666; }
.section { margin-bottom: 20px; }
.section-title { font-size: 14px; font-weight: bold; color: #3498db; border-bottom: 1px solid #bdc3c7; padding-bottom: 5px; margin-bottom: 10px; }
.item { margin-bottom: 15px; }
.item-header { font-weight: bold; margin-bottom: 5px; }
.item-meta { color: #666; font-size: 11px; margin-bottom: 5px; }
.item-desc { margin-left: 15px; margin-bottom: 5px; }
.achievements { margin-left: 15px; }
.skills-list { display: flex; flex-wrap: wrap; gap: 10px; }
.skill-item { background: #ecf0f1; padding: 5px 10px; border-radius: 3px; font-size: 11px; margin-right: 10px; margin-bottom: 5px; }
.photo { width: 120px; height: 160px; border: 1px solid #ddd; border-radius: 5px; float: right; margin-left: 20px; margin-bottom: 20px; }', 1),

('现代商务', '现代化设计风格，适合商务和管理岗位', 'modern', 'finance', 
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ personal_info.name }} - 简历</title>
    <style>{{ css }}</style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            {% if personal_info.photo_url %}
            <img src="{{ personal_info.photo_url }}" class="photo" alt="照片">
            {% endif %}
            <div class="name">{{ personal_info.name }}</div>
            <div class="contact">
                {% if personal_info.phone %}<div><i class="icon">📞</i>{{ personal_info.phone }}</div>{% endif %}
                {% if personal_info.email %}<div><i class="icon">✉</i>{{ personal_info.email }}</div>{% endif %}
                {% if personal_info.address %}<div><i class="icon">📍</i>{{ personal_info.address }}</div>{% endif %}
            </div>
            
            {% if skills %}
            <div class="sidebar-section">
                <h3>专业技能</h3>
                {% for skill in skills %}
                <div class="skill">{{ skill.name }}</div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="main">
            {% if objective %}
            <div class="section">
                <h2>求职意向</h2>
                <p>{{ objective }}</p>
            </div>
            {% endif %}
            
            {% if summary %}
            <div class="section">
                <h2>个人总结</h2>
                <p>{{ summary }}</p>
            </div>
            {% endif %}
            
            {% if work_experiences %}
            <div class="section">
                <h2>工作经历</h2>
                {% for exp in work_experiences %}
                <div class="experience">
                    <h4>{{ exp.position }} | {{ exp.company_name }}</h4>
                    <div class="date">{{ exp.start_date }} - {% if exp.is_current %}至今{% else %}{{ exp.end_date }}{% endif %}</div>
                    {% if exp.description %}<p>{{ exp.description }}</p>{% endif %}
                    {% if exp.achievements %}
                    <ul>
                        {% for achievement in exp.achievements %}
                        <li>{{ achievement }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% if educations %}
            <div class="section">
                <h2>教育背景</h2>
                {% for edu in educations %}
                <div class="education">
                    <h4>{{ edu.school_name }}</h4>
                    <div class="date">{{ edu.start_date }} - {{ edu.end_date }}</div>
                    <p>{{ edu.degree }} · {{ edu.major }}</p>
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% if projects %}
            <div class="section">
                <h2>项目经验</h2>
                {% for project in projects %}
                <div class="project">
                    <h4>{{ project.name }}</h4>
                    <div class="date">{{ project.start_date }} - {{ project.end_date }}</div>
                    {% if project.description %}<p>{{ project.description }}</p>{% endif %}
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
</body>
</html>',
'body { font-family: "Microsoft YaHei", Arial, sans-serif; margin: 0; padding: 0; background: #f8f9fa; }
.container { display: flex; max-width: 1000px; margin: 0 auto; background: white; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
.sidebar { width: 280px; background: #2c3e50; color: white; padding: 30px 20px; }
.photo { width: 120px; height: 160px; border-radius: 8px; margin-bottom: 20px; }
.name { font-size: 24px; font-weight: bold; margin-bottom: 20px; }
.contact div { margin-bottom: 10px; font-size: 14px; }
.icon { margin-right: 8px; }
.sidebar-section { margin-top: 30px; }
.sidebar-section h3 { color: #ecf0f1; font-size: 16px; margin-bottom: 15px; border-bottom: 2px solid #34495e; padding-bottom: 5px; }
.skill { background: #34495e; padding: 8px 12px; margin-bottom: 8px; border-radius: 4px; font-size: 13px; }
.main { flex: 1; padding: 30px; }
.section { margin-bottom: 30px; }
.section h2 { color: #2c3e50; font-size: 18px; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
.experience, .education, .project { margin-bottom: 20px; }
.experience h4, .education h4, .project h4 { color: #2c3e50; margin: 0 0 5px 0; font-size: 16px; }
.date { color: #7f8c8d; font-size: 13px; margin-bottom: 8px; }
.experience ul { margin-left: 20px; }', 2),

('科技风格', '现代科技感设计，适合IT和技术岗位', 'modern', 'it',
'<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ personal_info.name }} - 简历</title>
    <style>{{ css }}</style>
</head>
<body>
    <div class="container">
        <header class="header">
            {% if personal_info.photo_url %}
            <img src="{{ personal_info.photo_url }}" class="photo" alt="照片">
            {% endif %}
            <div class="header-info">
                <h1 class="name">{{ personal_info.name }}</h1>
                <div class="contact-grid">
                    {% if personal_info.phone %}<span>📱 {{ personal_info.phone }}</span>{% endif %}
                    {% if personal_info.email %}<span>📧 {{ personal_info.email }}</span>{% endif %}
                    {% if personal_info.address %}<span>📍 {{ personal_info.address }}</span>{% endif %}
                </div>
            </div>
        </header>
        
        {% if objective %}
        <section class="section">
            <h2 class="section-title">🎯 求职意向</h2>
            <p>{{ objective }}</p>
        </section>
        {% endif %}
        
        {% if summary %}
        <section class="section">
            <h2 class="section-title">👨‍💻 个人总结</h2>
            <p>{{ summary }}</p>
        </section>
        {% endif %}
        
        {% if work_experiences %}
        <section class="section">
            <h2 class="section-title">💼 工作经历</h2>
            {% for exp in work_experiences %}
            <div class="timeline-item">
                <div class="timeline-marker"></div>
                <div class="timeline-content">
                    <h3>{{ exp.position }} @ {{ exp.company_name }}</h3>
                    <div class="period">{{ exp.start_date }} - {% if exp.is_current %}至今{% else %}{{ exp.end_date }}{% endif %}</div>
                    {% if exp.description %}<p>{{ exp.description }}</p>{% endif %}
                    {% if exp.achievements %}
                    <ul class="achievements">
                        {% for achievement in exp.achievements %}
                        <li>{{ achievement }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </section>
        {% endif %}
        
        {% if skills %}
        <section class="section">
            <h2 class="section-title">🛠 技术技能</h2>
            <div class="skills-grid">
                {% for skill in skills %}
                <div class="skill-tag">{{ skill.name }}</div>
                {% endfor %}
            </div>
        </section>
        {% endif %}
        
        {% if projects %}
        <section class="section">
            <h2 class="section-title">🚀 项目经验</h2>
            {% for project in projects %}
            <div class="project-card">
                <h3>{{ project.name }}</h3>
                <div class="period">{{ project.start_date }} - {{ project.end_date }}</div>
                {% if project.description %}<p>{{ project.description }}</p>{% endif %}
                {% if project.technologies %}
                <div class="tech-stack">
                    {% for tech in project.technologies %}
                    <span class="tech-item">{{ tech }}</span>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </section>
        {% endif %}
        
        {% if educations %}
        <section class="section">
            <h2 class="section-title">🎓 教育背景</h2>
            {% for edu in educations %}
            <div class="education-item">
                <h3>{{ edu.school_name }}</h3>
                <div class="degree">{{ edu.degree }} · {{ edu.major }}</div>
                <div class="period">{{ edu.start_date }} - {{ edu.end_date }}</div>
            </div>
            {% endfor %}
        </section>
        {% endif %}
    </div>
</body>
</html>',
'body { font-family: "Consolas", "Monaco", "Lucida Console", monospace; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 20px; }
.container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.2); }
.header { background: linear-gradient(135deg, #2c3e50, #34495e); color: white; padding: 30px; display: flex; align-items: center; }
.photo { width: 100px; height: 120px; border-radius: 8px; margin-right: 20px; border: 3px solid #3498db; }
.name { font-size: 28px; font-weight: bold; margin: 0 0 10px 0; color: #ecf0f1; }
.contact-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 14px; color: #bdc3c7; }
.section { padding: 25px 30px; border-bottom: 1px solid #ecf0f1; }
.section-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; color: #2c3e50; display: flex; align-items: center; }
.timeline-item { position: relative; padding-left: 25px; margin-bottom: 20px; }
.timeline-marker { position: absolute; left: 0; top: 5px; width: 12px; height: 12px; background: #3498db; border-radius: 50%; }
.timeline-content h3 { margin: 0 0 5px 0; color: #2c3e50; font-size: 16px; }
.period { color: #7f8c8d; font-size: 13px; margin-bottom: 8px; }
.achievements { margin-left: 20px; }
.skills-grid { display: flex; flex-wrap: wrap; gap: 8px; }
.skill-tag { background: linear-gradient(135deg, #3498db, #2980b9); color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; }
.project-card { background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; border-left: 4px solid #3498db; }
.project-card h3 { margin: 0 0 5px 0; color: #2c3e50; }
.tech-stack { margin-top: 10px; }
.tech-item { background: #e74c3c; color: white; padding: 3px 8px; border-radius: 12px; font-size: 11px; margin-right: 5px; }
.education-item { background: #ecf0f1; padding: 15px; border-radius: 8px; margin-bottom: 10px; }
.education-item h3 { margin: 0 0 5px 0; color: #2c3e50; }
.degree { color: #34495e; font-weight: 500; margin-bottom: 5px; }', 3);

-- 添加新的简历模板
INSERT INTO `resume_templates` (`name`, `description`, `category`, `industry`, `template_html`, `template_css`, `sort_order`) VALUES
('创意左侧栏', '创意设计，左侧栏放置照片和基本信息，右侧展示详细经历', 'creative', 'design', '', '', 4),
('优雅商务', '优雅的商务风格，顶部照片展示，专业商务布局', 'professional', 'finance', '', '', 5);

-- 创建索引优化
CREATE INDEX `idx_users_status_vip` ON `users` (`status`, `vip_level`);
CREATE INDEX `idx_images_user_status` ON `images` (`user_id`, `status`);
CREATE INDEX `idx_tasks_user_status` ON `processing_tasks` (`user_id`, `status`);
CREATE INDEX `idx_logs_user_time` ON `user_logs` (`user_id`, `created_at`);
CREATE INDEX `idx_resumes_user_status` ON `resumes` (`user_id`, `status`);
CREATE INDEX `idx_templates_category_active` ON `resume_templates` (`category`, `is_active`);

SET FOREIGN_KEY_CHECKS = 1; 