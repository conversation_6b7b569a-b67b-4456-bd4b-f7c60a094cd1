"""
数据库初始化模块
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

# 创建全局单例
db = SQLAlchemy()
migrate = Migrate()

def init_app(app: Flask) -> None:
    """初始化数据库"""
    db.init_app(app)
    migrate.init_app(app, db)

def create_tables(app: Flask) -> None:
    """创建所有表"""
    with app.app_context():
        # 导入所有模型以确保它们被正确注册
        import app.models
        db.create_all()

__all__ = ['db', 'migrate', 'init_app', 'create_tables']
