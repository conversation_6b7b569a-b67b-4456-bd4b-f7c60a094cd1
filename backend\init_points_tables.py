#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分系统数据库初始化脚本
Initialize Points System Database Tables
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.points_config import PointsConfig
from app.models.points_log import PointsLog
from app.models.payment_order import PaymentOrder
from app.models.user import User
import json

def init_points_tables():
    """初始化积分相关表"""
    app = create_app()
    
    with app.app_context():
        print("开始初始化积分系统数据库...")
        
        # 创建表
        db.create_all()
        print("✓ 数据库表创建完成")
        
        # 初始化积分配置
        config = PointsConfig.query.first()
        if not config:
            config = PointsConfig()
            
            # 设置默认配置
            config.register_reward = 100
            config.invite_reward = 50
            config.ad_reward = 5
            config.photo_process_cost = 10
            config.resume_generate_cost = 20
            config.business_card_cost = 15
            config.document_compare_cost = 25
            config.recharge_ratio = 1.0
            
            # 默认套餐配置
            default_packages = [
                {
                    "id": "basic",
                    "name": "基础套餐",
                    "points": 100,
                    "price": 10.0,
                    "original_price": 15.0,
                    "enabled": True
                },
                {
                    "id": "standard",
                    "name": "标准套餐",
                    "points": 300,
                    "price": 25.0,
                    "original_price": 40.0,
                    "enabled": True
                },
                {
                    "id": "premium",
                    "name": "高级套餐",
                    "points": 600,
                    "price": 45.0,
                    "original_price": 75.0,
                    "enabled": True
                }
            ]
            config.payment_packages = json.dumps(default_packages)
            
            db.session.add(config)
            db.session.commit()
            print("✓ 积分配置初始化完成")
        
        # 为现有用户添加积分字段（如果不存在）
        users = User.query.all()
        for user in users:
            if not hasattr(user, 'points') or user.points is None:
                user.points = 0
            if not hasattr(user, 'points_used') or user.points_used is None:
                user.points_used = 0
        
        db.session.commit()
        print(f"✓ 用户积分字段初始化完成，共处理 {len(users)} 个用户")
        
        print("积分系统数据库初始化完成！")

if __name__ == '__main__':
    init_points_tables() 