import os
import logging
from datetime import timedelta
from typing import Dict, Any, Optional

class BaseConfig:
    """基础配置类"""
    
    # 基本应用配置
    SECRET_KEY: str = os.environ.get('SECRET_KEY') or 'dev'
    DEBUG: bool = False
    TESTING: bool = False
    
    # 性能优化配置
    OPTIMIZE_FOR_SPEED: bool = True
    USE_ULTRA_FAST_ALGORITHMS: bool = True
    SKIP_COMPLEX_OPERATIONS: bool = True
    
    # 内存优化配置
    MAX_IMAGE_SIZE: int = 2048
    COMPRESS_LARGE_IMAGES: bool = True
    
    # 超时配置
    REQUEST_TIMEOUT: int = 15
    IMAGE_PROCESSING_TIMEOUT: int = 15
    SINGLE_OPERATION_TIMEOUT: int = 5
    BACKGROUND_REPLACE_TIMEOUT: int = 3
    UPLOAD_TIMEOUT: int = 30
    SEND_FILE_MAX_AGE_DEFAULT: int = 300
    PERMANENT_SESSION_LIFETIME: timedelta = timedelta(minutes=30)
    
    # 日志配置
    LOG_LEVEL: int = logging.INFO
    LOG_FORMAT: str = '%(asctime)s [%(levelname)s] in %(module)s: %(message)s'
    LOG_FILE: str = 'app.log'
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI: str = 'mysql://root:123456@localhost/photonew?charset=utf8mb4'
    SQLALCHEMY_TRACK_MODIFICATIONS: bool = False
    SQLALCHEMY_ENGINE_OPTIONS: Dict[str, Any] = {
        'pool_recycle': 3600,
        'pool_timeout': 60,
        'pool_size': 5,
        'max_overflow': 10,
        'pool_pre_ping': True
    }
    
    # Redis配置
    REDIS_HOST: str = os.environ.get('REDIS_HOST') or 'localhost'
    REDIS_PORT: int = int(os.environ.get('REDIS_PORT') or 6379)
    REDIS_DB: int = int(os.environ.get('REDIS_DB') or 0)
    REDIS_PASSWORD: Optional[str] = os.environ.get('REDIS_PASSWORD')
    
    @property
    def REDIS_URL(self) -> str:
        if self.REDIS_PASSWORD:
            return f'redis://:{self.REDIS_PASSWORD}@{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}'
        return f'redis://{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}'
    
    # Celery配置
    @property
    def CELERY_BROKER_URL(self) -> str:
        return self.REDIS_URL
        
    @property
    def CELERY_RESULT_BACKEND(self) -> str:
        return self.REDIS_URL
    CELERY_TASK_SERIALIZER: str = 'json'
    CELERY_RESULT_SERIALIZER: str = 'json'
    CELERY_ACCEPT_CONTENT: list = ['json']
    CELERY_TIMEZONE: str = 'Asia/Shanghai'
    CELERY_ENABLE_UTC: bool = True
    
    # 文件上传配置
    MAX_CONTENT_LENGTH: int = int(os.environ.get('MAX_CONTENT_LENGTH') or 16 * 1024 * 1024)
    UPLOAD_FOLDER: str = os.path.join(os.path.abspath(os.path.dirname(__file__)), 'uploads')
    STATIC_FOLDER: str = os.path.join(os.path.dirname(__file__), 'static')
    PROCESSED_FOLDER: str = os.path.join(os.path.dirname(__file__), 'static', 'processed')
    ALLOWED_EXTENSIONS: set = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx'}
    
    # JWT配置
    JWT_SECRET_KEY: str = os.environ.get('JWT_SECRET_KEY') or 'jwt-dev'
    JWT_ACCESS_TOKEN_EXPIRES: timedelta = timedelta(hours=24)
    JWT_REFRESH_TOKEN_EXPIRES: timedelta = timedelta(days=30)
    
    # 图像处理配置
    IMAGE_QUALITY: int = int(os.environ.get('IMAGE_QUALITY') or 95)
    MAX_IMAGE_RESOLUTION: int = int(os.environ.get('MAX_IMAGE_RESOLUTION') or 4096)
    THUMBNAIL_SIZE: tuple = (300, 300)
    
    # 功能开关配置
    ENABLE_FACE_BEAUTIFY: bool = os.environ.get('ENABLE_FACE_BEAUTIFY', 'true').lower() == 'true'
    ENABLE_BACKGROUND_REMOVAL: bool = os.environ.get('ENABLE_BACKGROUND_REMOVAL', 'true').lower() == 'true'
    
    # 用户限制配置
    FREE_USER_DAILY_LIMIT: int = int(os.environ.get('FREE_USER_DAILY_LIMIT') or 10)
    VIP_USER_DAILY_LIMIT: int = int(os.environ.get('VIP_USER_DAILY_LIMIT') or 100)
    
    # CDN配置
    CDN_DOMAIN: str = os.environ.get('CDN_DOMAIN') or ''
    STATIC_URL_PREFIX: str = CDN_DOMAIN if CDN_DOMAIN else '/static'
    FRONTEND_URL: str = os.environ.get('FRONTEND_URL') or 'http://localhost:8097'
    
    # 安全配置
    RATE_LIMIT_MAX: int = int(os.environ.get('RATE_LIMIT_MAX') or 60)
    RATE_LIMIT_WINDOW: int = int(os.environ.get('RATE_LIMIT_WINDOW') or 60)
    SESSION_TIMEOUT: int = int(os.environ.get('SESSION_TIMEOUT') or 1800)
    PASSWORD_MIN_LENGTH: int = int(os.environ.get('PASSWORD_MIN_LENGTH') or 8)
    
    @classmethod
    def init_app(cls, app):
        """初始化应用配置"""
        # 确保必要目录存在
        for folder in [app.config['UPLOAD_FOLDER'], 
                      app.config['STATIC_FOLDER'],
                      app.config['PROCESSED_FOLDER']]:
            if not os.path.exists(folder):
                os.makedirs(folder)
        
        # 配置日志
        handler = logging.FileHandler(app.config['LOG_FILE'])
        handler.setLevel(app.config['LOG_LEVEL'])
        formatter = logging.Formatter(app.config['LOG_FORMAT'])
        handler.setFormatter(formatter)
        app.logger.addHandler(handler)
        app.logger.setLevel(app.config['LOG_LEVEL'])
        
        # 初始化系统配置
        from app.utils.config_manager import init_system_config
        init_system_config()

class DevelopmentConfig(BaseConfig):
    """开发环境配置"""
    DEBUG = True
    SQLALCHEMY_ECHO = True
    
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.abspath(os.path.dirname(__file__)), 'photo_maker.db')

class TestingConfig(BaseConfig):
    """测试环境配置"""
    TESTING = True
    DEBUG = True
    
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    REDIS_DB = 1
    MAX_CONTENT_LENGTH = 5 * 1024 * 1024

class ProductionConfig(BaseConfig):
    """生产环境配置"""
    
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'mysql+pymysql://{user}:{password}@{host}:{port}/{db}'.format(
            user=os.environ.get('MYSQL_USER', 'root'),
            password=os.environ.get('MYSQL_PASSWORD', '123456'),
            host=os.environ.get('MYSQL_HOST', 'localhost'),
            port=os.environ.get('MYSQL_PORT', '3306'),
            db=os.environ.get('MYSQL_DB', 'photo_maker')
        )
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # 生产环境性能配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_timeout': 20,
        'max_overflow': 20,
        'pool_size': 10
    }

# 配置字典
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

def get_config():
    """获取当前环境配置"""
    env = os.environ.get('FLASK_ENV', 'development')
    return config.get(env, config['default'])