from flask import Blueprint, request, send_file, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
import io
import tempfile
import traceback
from PyPDF2 import PdfMerger, PdfReader, PdfWriter
from PIL import Image
import os
import fitz  # PyMuPDF
import pytesseract
import json
import zipfile

# You may need to configure the path to the Tesseract executable
# pytesseract.pytesseract.tesseract_cmd = r'<full_path_to_your_tesseract_executable>'

pdf_tools_bp = Blueprint('pdf_tools', __name__, url_prefix='/api/pdf')

@pdf_tools_bp.route('/merge', methods=['POST'])
@jwt_required()
def merge_pdfs():
    try:
        files = request.files.getlist('pdfs')
        if not files:
            return jsonify({'error': '未上传PDF文件'}), 400
        merger = PdfMerger()
        for f in files:
            merger.append(f)
        out_io = io.BytesIO()
        merger.write(out_io)
        merger.close()
        out_io.seek(0)
        return send_file(out_io, mimetype='application/pdf', as_attachment=True, download_name='merged.pdf')
    except Exception as e:
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@pdf_tools_bp.route('/split', methods=['POST'])
@jwt_required()
def split_pdf():
    try:
        file = request.files.get('pdf')
        if not file:
            return jsonify({'error': '未上传PDF文件'}), 400
        reader = PdfReader(file)
        output_files = []
        for i in range(len(reader.pages)):
            writer = PdfWriter()
            writer.add_page(reader.pages[i])
            out_io = io.BytesIO()
            writer.write(out_io)
            out_io.seek(0)
            output_files.append(('page_%d.pdf' % (i+1), out_io.read()))
        # 返回第1页（可扩展为zip）
        return send_file(io.BytesIO(output_files[0][1]), mimetype='application/pdf', as_attachment=True, download_name=output_files[0][0])
    except Exception as e:
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@pdf_tools_bp.route('/long-image', methods=['POST'])
@jwt_required()
def long_image():
    try:
        # 接收图片文件列表
        images = request.files.getlist('images')
        if not images:
            return jsonify({'error': '未上传图片'}), 400
        pil_images = [Image.open(img.stream).convert('RGB') for img in images]
        widths, heights = zip(*(im.size for im in pil_images))
        total_height = sum(heights)
        max_width = max(widths)
        long_img = Image.new('RGB', (max_width, total_height), (255,255,255))
        y = 0
        for im in pil_images:
            long_img.paste(im, (0, y))
            y += im.height
        out_io = io.BytesIO()
        long_img.save(out_io, format='PNG')
        out_io.seek(0)
        return send_file(out_io, mimetype='image/png', as_attachment=True, download_name='long-image.png')
    except Exception as e:
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@pdf_tools_bp.route('/annotate', methods=['POST'])
@jwt_required()
def annotate_pdf():
    try:
        file = request.files.get('pdf')
        annotations = request.form.get('annotations')
        if not file or not annotations:
            return jsonify({'error': '缺少参数'}), 400
        # 这里只做简单转发，实际应调用 PDF 批注处理逻辑
        # TODO: 实现批注功能
        return send_file(file, mimetype='application/pdf', as_attachment=True, download_name='annotated.pdf')
    except Exception as e:
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@pdf_tools_bp.route('/ocr', methods=['POST'])
@jwt_required()
def ocr_pdf():
    try:
        file = request.files.get('pdf')
        page_indices = request.form.getlist('pages', type=int)
        lang = request.form.get('lang', 'chi_sim+eng')

        if not file:
            return jsonify({'error': '未上传PDF文件'}), 400

        doc = fitz.open(stream=file.read(), filetype="pdf")
        
        full_text = ""
        for i in page_indices:
            if 0 <= i < len(doc):
                page = doc.load_page(i)
                pix = page.get_pixmap()
                img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                text = pytesseract.image_to_string(img, lang=lang)
                full_text += f"--- Page {i+1} ---\n{text}\n\n"
        
        doc.close()

        return jsonify({'text': full_text})
    except Exception as e:
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500 

@pdf_tools_bp.route('/export', methods=['POST'])
@jwt_required()
def export_pdf():
    try:
        file = request.files.get('pdf')
        options = json.loads(request.form.get('options'))
        annotations_data = json.loads(request.form.get('annotations'))

        if not file:
            return jsonify({'error': '未上传PDF文件'}), 400

        doc = fitz.open(stream=file.read(), filetype="pdf")
        
        # Apply annotations if requested
        if options.get('includeAnnotations'):
            for page_idx, annots in annotations_data.items():
                page = doc[int(page_idx)]
                for annot in annots:
                    # This is a simplified example. Fabric.js objects need to be
                    # converted to PyMuPDF shapes, which is complex.
                    # For now, we'll just draw a placeholder rectangle.
                    if annot['type'] == 'path':
                        rect = fitz.Rect(annot['left'], annot['top'], annot['left'] + annot['width'], annot['top'] + annot['height'])
                        page.add_highlight_annot(rect)

        # Export based on format
        if options['format'] == 'pdf':
            out_io = io.BytesIO(doc.tobytes())
            return send_file(out_io, mimetype='application/pdf', as_attachment=True, download_name=f"{options['filename']}.pdf")
        else: # Image export
            zip_io = io.BytesIO()
            with zipfile.ZipFile(zip_io, 'w') as zipf:
                for i in range(len(doc)):
                    page = doc.load_page(i)
                    pix = page.get_pixmap()
                    img_io = io.BytesIO()
                    img = Image.frombytes("RGB", [pix.width, pix.height], pix.samples)
                    img.save(img_io, format=options['format'].upper())
                    img_io.seek(0)
                    zipf.writestr(f"{options['filename']}_page_{i+1}.{options['format']}", img_io.getvalue())
            zip_io.seek(0)
            return send_file(zip_io, mimetype='application/zip', as_attachment=True, download_name=f"{options['filename']}.zip")
    
    except Exception as e:
        current_app.logger.error(traceback.format_exc())
        return jsonify({'error': str(e)}), 500 