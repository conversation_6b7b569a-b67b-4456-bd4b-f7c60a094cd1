from flask import Blueprint

# 创建API蓝图
api_bp = Blueprint('api', __name__)

# 导入所有API模块
from . import auth
from . import users
from . import images
from . import processing
from . import templates
from . import common
from . import business_cards
from . import pdf_tools  # 新增导入

# 导入管理员API模块
from . import admin_users
from . import admin_credits
from . import admin_system
from . import admin_statistics

# 注册错误处理器
from app.api.errors import register_error_handlers
register_error_handlers(api_bp)

# 注册文档路由
from app.api.documents import register_document_routes
from app.api.resumes import register_resume_routes
register_document_routes(api_bp) 
register_resume_routes(api_bp) 

# 注册PDF工具蓝图
api_bp.register_blueprint(pdf_tools.pdf_tools_bp)

# 不再在这里注册admin_users_bp和admin_credits_bp
# 由app/__init__.py直接注册

# 注册名片路由
def register_business_card_routes(bp):
    """注册名片相关路由"""
    pass

register_business_card_routes(api_bp) 
api_bp.register_blueprint(admin_system.admin_system_bp) 
api_bp.register_blueprint(admin_statistics.admin_statistics_bp) 