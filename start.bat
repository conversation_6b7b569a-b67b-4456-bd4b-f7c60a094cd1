@echo off
chcp 65001 >nul

echo 🚀 启动智能简历生成器...

REM 检查是否安装了Node.js
where node >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: Node.js 未安装，请先安装 Node.js 16+
    pause
    exit /b 1
)

REM 检查是否安装了Python
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 错误: Python 未安装，请先安装 Python 3.8+
    pause
    exit /b 1
)

echo 📡 启动后端服务...
cd backend

REM 检查虚拟环境
if not exist "venv" (
    echo 🔧 创建Python虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境并安装依赖
call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1

echo 🏃 启动Django服务器...
start "Backend" cmd /k "python run.py"

cd ..

REM 等待后端启动
timeout /t 3 /nobreak >nul

echo 🎨 启动前端服务...
cd frontend

REM 检查node_modules
if not exist "node_modules" (
    echo 📦 安装前端依赖...
    npm install
)

echo 🏃 启动Vue开发服务器...
start "Frontend" cmd /k "npm run dev"

cd ..

echo.
echo ✅ 服务启动完成！
echo 🌐 前端地址: http://localhost:3000
echo 🔗 后端API: http://localhost:5000
echo.
echo 提示：两个终端窗口已打开，分别运行前端和后端服务
echo 要停止服务，请关闭对应的终端窗口
echo.
pause 