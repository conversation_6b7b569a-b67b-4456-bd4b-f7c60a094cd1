<template>
  <div class="template-selector">
    <h3 class="selector-title">简历模板</h3>
    
    <!-- 模板列表 -->
    <div class="template-list">
      <div 
        v-for="template in templates" 
        :key="template.id"
        class="template-item"
        :class="{ 'active': selectedTemplateId === template.id }"
        @click="selectTemplate(template.id)"
      >
        <div class="template-preview">
          <el-image 
            :src="template.preview_image || '/templates/default-preview.png'"
            :alt="template.name"
            fit="cover"
            loading="lazy"
          >
            <template #error>
              <div class="image-error">
                <el-icon><picture-filled /></el-icon>
                <span>预览图加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-if="selectedTemplateId === template.id" class="selected-overlay">
            <el-icon><check /></el-icon>
          </div>
        </div>
        <div class="template-info">
          <h4>{{ template.name }}</h4>
          <p>{{ template.description }}</p>
          <div class="template-tags">
            <el-tag 
              v-if="template.category"
              size="small"
              :type="getCategoryType(template.category)"
            >
              {{ getCategoryLabel(template.category) }}
            </el-tag>
            <el-tag 
              v-if="template.industry"
              size="small"
              :type="getIndustryType(template.industry)"
            >
              {{ getIndustryLabel(template.industry) }}
            </el-tag>
          </div>
          <div class="template-features">
            <span 
              v-for="feature in template.features.slice(0, 2)" 
              :key="feature"
              class="feature-tag"
            >
              {{ feature }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { ElTag, ElImage } from 'element-plus'
import { PictureFilled, Check } from '@element-plus/icons-vue'
import { getAllTemplates } from '@/config/templateMapping'

export default {
  name: 'TemplateSelector',
  
  components: {
    ElTag,
    ElImage,
    PictureFilled,
    Check
  },

  props: {
    modelValue: {
      type: String,
      default: 'basic'
    }
  },

  emits: ['update:modelValue'],

  setup(props, { emit }) {
    const templates = ref(getAllTemplates())
    const selectedTemplateId = ref(props.modelValue)

    // 选择模板
    const selectTemplate = (templateId) => {
      selectedTemplateId.value = templateId
      emit('update:modelValue', templateId)
    }

    // 获取分类标签文本
    const getCategoryLabel = (category) => {
      const labels = {
        simple: '简约',
        classic: '经典',
        modern: '现代',
        creative: '创意',
        minimalist: '极简',
        professional: '专业'
      }
      return labels[category] || category
    }

    // 获取行业标签文本
    const getIndustryLabel = (industry) => {
      const labels = {
        it: 'IT互联网',
        tech: '科技',
        finance: '金融',
        education: '教育',
        marketing: '市场营销',
        sales: '销售',
        design: '设计创意',
        traditional: '传统行业',
        other: '通用'
      }
      return labels[industry] || industry
    }

    // 获取分类标签类型
    const getCategoryType = (category) => {
      const types = {
        simple: '',
        classic: 'info',
        modern: 'success',
        creative: 'warning',
        minimalist: 'info',
        professional: 'primary'
      }
      return types[category] || ''
    }

    // 获取行业标签类型
    const getIndustryType = (industry) => {
      const types = {
        it: 'success',
        tech: 'success',
        finance: 'primary',
        education: 'warning',
        marketing: 'danger',
        sales: 'danger',
        design: 'warning',
        traditional: 'info',
        other: ''
      }
      return types[industry] || ''
    }

    return {
      templates,
      selectedTemplateId,
      selectTemplate,
      getCategoryLabel,
      getIndustryLabel,
      getCategoryType,
      getIndustryType
    }
  }
}
</script>

<style scoped>
.template-selector {
  padding: 1rem;
  background: #f5f5f5;
  border-radius: 8px;
  min-height: 200px;
}

.selector-title {
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: #333;
}

.template-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.template-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.template-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.template-item.active {
  border-color: #409eff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
}

.template-preview {
  height: 180px;
  overflow: hidden;
  position: relative;
}

.template-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.selected-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(64, 158, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
}

.template-info {
  padding: 1rem;
}

.template-info h4 {
  margin: 0 0 0.5rem;
  font-size: 1rem;
  color: #333;
  font-weight: 600;
}

.template-info p {
  margin: 0 0 0.8rem;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.template-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 0.8rem;
}

.template-features {
  display: flex;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.feature-tag {
  font-size: 0.8rem;
  color: #999;
  background: #f5f5f5;
  padding: 0.2rem 0.5rem;
  border-radius: 12px;
}

.image-error {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 0.9rem;
}

.image-error .el-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-list {
    grid-template-columns: 1fr;
  }
  
  .template-item {
    margin-bottom: 1rem;
  }
}
</style> 