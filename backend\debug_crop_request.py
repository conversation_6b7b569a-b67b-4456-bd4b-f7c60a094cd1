import requests
import json
import base64
import os
import sys

# 添加项目路径
sys.path.insert(0, 'e:\\photoNew\\backend')

def test_crop_api():
    """测试裁剪API的具体错误"""
    
    # 测试服务器是否运行
    try:
        health_response = requests.get('http://localhost:5000/api/health', timeout=5)
        print(f"服务器状态: {health_response.status_code}")
    except Exception as e:
        print(f"服务器连接失败: {e}")
        return
    
    # 创建测试图片
    try:
        from PIL import Image
        import numpy as np
        import io
        
        # 创建简单的测试图片
        img = Image.new('RGB', (400, 600), color='blue')
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='PNG')
        img_bytes.seek(0)
        
        # 转换为base64
        img_base64 = base64.b64encode(img_bytes.read()).decode()
        image_data = f"data:image/png;base64,{img_base64}"
        
        print(f"测试图片大小: {len(image_data)} 字符")
        
        # 测试自动裁剪
        test_data = {
            "image_data": image_data,
            "crop_mode": "auto",
            "template": {
                "width": 413,
                "height": 626,
                "name": "标准一寸"
            }
        }
        
        print("发送裁剪请求...")
        response = requests.post(
            'http://localhost:5000/api/processing/unified-crop',
            json=test_data,
            timeout=30
        )
        
        print(f"响应状态: {response.status_code}")
        print(f"响应内容: {response.text[:500]}...")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_crop_api()