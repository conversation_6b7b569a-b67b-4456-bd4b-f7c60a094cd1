<template>
  <el-dialog v-model="visible" :title="template ? '编辑模板' : '新增模板'" width="600px" @close="close">
    <el-form :model="form" label-width="90px" :rules="rules" ref="formRef">
      <el-form-item label="名称" prop="name">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" rows="2" />
      </el-form-item>
      <el-form-item label="分类" prop="category">
        <el-select v-model="form.category" placeholder="请选择分类">
          <el-option label="商务" value="business" />
          <el-option label="极简" value="minimal" />
          <el-option label="创意" value="creative" />
          <el-option label="优雅" value="elegant" />
        </el-select>
      </el-form-item>
      <el-form-item label="预览图">
        <el-input v-model="form.preview_image" placeholder="图片URL或上传" />
      </el-form-item>
      <el-form-item label="默认配色">
        <el-input v-model="form.default_colors" placeholder='{"primary":"#409EFF"}' />
      </el-form-item>
      <el-form-item label="启用状态">
        <el-switch v-model="form.is_active" />
      </el-form-item>
      <el-form-item label="排序">
        <el-input-number v-model="form.sort_order" :min="0" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'
import api from '@/api'
const props = defineProps({
  modelValue: Boolean,
  template: Object,
  type: String
})
const emit = defineEmits(['update:modelValue', 'refresh'])
const visible = ref(false)
const form = ref({
  name: '',
  description: '',
  category: '',
  preview_image: '',
  default_colors: '',
  is_active: true,
  sort_order: 0
})
const rules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择分类', trigger: 'change' }]
}
const formRef = ref(null)
watch(() => props.modelValue, v => { visible.value = v })
watch(() => props.template, t => {
  if (t) Object.assign(form.value, t)
  else Object.assign(form.value, { name: '', description: '', category: '', preview_image: '', default_colors: '', is_active: true, sort_order: 0 })
})
const close = () => emit('update:modelValue', false)
const save = async () => {
  await formRef.value.validate()
  if (props.template && props.template.id) {
    await api.admin.updateTemplate(props.template.id, { ...form.value, type: props.type })
  } else {
    await api.admin.createTemplate({ ...form.value, type: props.type })
  }
  emit('refresh')
  close()
}
</script> 