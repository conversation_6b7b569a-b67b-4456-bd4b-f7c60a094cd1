import sqlite3
import os

def fix_database_schema():
    db_path = os.path.join('instance', 'photo_maker.db')
    
    if not os.path.exists('instance'):
        os.makedirs('instance')
        print("创建 instance 目录")

    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        print("检查 'business_cards' 表...")
        cursor.execute("PRAGMA table_info(business_cards)")
        columns = [row[1] for row in cursor.fetchall()]

        if 'style_config' not in columns:
            print("'style_config' 列不存在, 正在添加...")
            cursor.execute("ALTER TABLE business_cards ADD COLUMN style_config TEXT DEFAULT NULL")
            conn.commit()
            print("成功添加 'style_config' 列。")
        else:
            print("'style_config' 列已存在。")

    except sqlite3.OperationalError as e:
        if "no such table: business_cards" in str(e):
            print("'business_cards' 表不存在, 正在创建...")
            # Re-creating the table based on the model
            cursor.execute("""
                CREATE TABLE business_cards (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    template_id INTEGER,
                    title TEXT NOT NULL,
                    card_data TEXT,
                    style_config TEXT,
                    qr_code_data TEXT,
                    qr_code_image TEXT,
                    status TEXT DEFAULT 'draft',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                );
            """)
            conn.commit()
            print("成功创建 'business_cards' 表。")
        else:
            print(f"数据库错误: {e}")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        conn.close()
        print("数据库检查完成。")

if __name__ == '__main__':
    fix_database_schema() 