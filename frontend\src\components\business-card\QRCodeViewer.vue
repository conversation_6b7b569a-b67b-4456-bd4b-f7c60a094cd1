<template>
  <div class="qr-code-viewer">
    <div class="qr-content">
      <div v-if="!imgError && qrImage" class="qr-display">
        <img :src="qrImage" alt="名片二维码" class="qr-img" @error="onImgError" />
      </div>
      <div v-else class="qr-placeholder">
        <el-icon><Document /></el-icon>
        <p v-if="imgError" style="color: #f56c6c;">二维码加载失败，请重新生成</p>
        <p v-else>暂无二维码</p>
      </div>
      <div class="qr-info">
        <h4>{{ businessCard.title }}</h4>
        <p>扫描二维码查看名片详情</p>
      </div>
    </div>
    <div class="qr-actions">
      <el-button @click="generateQR" :loading="generating">
        <el-icon><Refresh /></el-icon>
        重新生成
      </el-button>
      <el-button @click="downloadQR" :disabled="!qrImage || imgError">
        <el-icon><Download /></el-icon>
        下载二维码
      </el-button>
      <el-button @click="shareQR" :disabled="!qrImage || imgError">
        <el-icon><Share /></el-icon>
        分享
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElButton, ElIcon, ElMessage } from 'element-plus'
import { Document, Refresh, Download, Share } from '@element-plus/icons-vue'
import api from '@/api'

export default {
  name: 'QRCodeViewer',
  
  components: {
    ElButton,
    ElIcon,
    Document,
    Refresh,
    Download,
    Share
  },
  
  props: {
    businessCard: {
      type: Object,
      required: true
    }
  },
  
  emits: ['close', 'refresh'],
  
  setup(props, { emit }) {
    const generating = ref(false)
    const qrImage = ref('')
    const imgError = ref(false)
    
    // 增强兜底逻辑：优先base64，其次绝对URL，再相对URL
    const qrImageSrc = computed(() => {
      const val = qrImage.value || props.businessCard.qr_code_image
      if (!val) return ''
      if (val.startsWith('data:')) return val
      if (/^https?:\/\//.test(val)) return val
      // 相对路径兜底
      return `${api.defaults.baseURL}${val}`
    })
    
    const generateQR = async () => {
      generating.value = true
      imgError.value = false
      try {
        const response = await api.businessCards.generateQR(props.businessCard.id, {
          type: 'url',
          style_config: {
            size: 400,
            fill_color: '#000000',
            back_color: '#ffffff',
            border: 4
          }
        })
        if (response.data.success) {
          // 优先base64
          qrImage.value = response.data.data.qr_code.base64_data || response.data.data.qr_code.file_url || ''
          // 通知父组件刷新 businessCard 数据
          emit('refresh')
          ElMessage.success('二维码生成成功')
        }
      } catch (error) {
        console.error('生成二维码失败:', error)
        ElMessage.error('生成二维码失败')
      } finally {
        generating.value = false
      }
    }
    
    const downloadQR = () => {
      const imgSrc = qrImageSrc.value
      if (!imgSrc) return
      const link = document.createElement('a')
      link.href = imgSrc
      link.download = `${props.businessCard.title || 'business-card'}_qr.png`
      link.click()
      ElMessage.success('下载成功')
    }
    
    const shareQR = async () => {
      const imgSrc = qrImageSrc.value
      if (!imgSrc) return
      try {
        if (navigator.share) {
          await navigator.share({
            title: `${props.businessCard.title} - 名片二维码`,
            text: '扫描二维码查看我的名片',
            url: window.location.href
          })
        } else {
          const viewUrl = `${window.location.origin}/business-card/view/${props.businessCard.id}`
          await navigator.clipboard.writeText(viewUrl)
          ElMessage.success('链接已复制到剪贴板')
        }
      } catch (error) {
        ElMessage.error('分享失败')
      }
    }

    const onImgError = () => {
      imgError.value = true
    }

    onMounted(() => {
      imgError.value = false
      if (!props.businessCard.qr_code_image) {
        generateQR()
      }
    })
    
    return {
      generating,
      qrImage: qrImageSrc,
      generateQR,
      downloadQR,
      shareQR,
      imgError,
      onImgError
    }
  }
}
</script>

<style scoped>
.qr-code-viewer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.qr-content {
  text-align: center;
  margin-bottom: 30px;
}

.qr-display {
  margin-bottom: 20px;
  width: 300px;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  /* 不要设置border-radius，保证二维码完整 */
  display: block;
}

.qr-placeholder {
  width: 300px;
  height: 300px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  margin-bottom: 20px;
}

.qr-placeholder .el-icon {
  font-size: 40px;
  margin-bottom: 10px;
}

.qr-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.qr-info p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.qr-actions {
  display: flex;
  gap: 10px;
}
</style> 