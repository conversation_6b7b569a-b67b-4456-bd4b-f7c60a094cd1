#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
支付订单模型
Payment Order Model
"""

from datetime import datetime
from app.models.base import db

class PaymentOrder(db.Model):
    __tablename__ = 'payment_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_no = db.Column(db.String(50), unique=True, nullable=False, comment='订单号')
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # 订单信息
    amount = db.Column(db.Float, nullable=False, comment='订单金额')
    points = db.Column(db.Integer, nullable=False, comment='购买积分数量')
    package_name = db.Column(db.String(100), comment='套餐名称')
    
    # 支付信息
    payment_method = db.Column(db.String(20), nullable=False, comment='支付方式(wechat/alipay)')
    payment_status = db.Column(db.String(20), default='pending', comment='支付状态(pending/paid/cancelled/failed)')
    payment_time = db.Column(db.DateTime, comment='支付时间')
    transaction_id = db.Column(db.String(100), comment='第三方交易ID')
    
    # 订单状态
    status = db.Column(db.String(20), default='pending', comment='订单状态(pending/completed/cancelled)')
    
    # 备注信息
    remark = db.Column(db.String(500), comment='订单备注')
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联用户
    user = db.relationship('User', backref='payment_orders')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_no': self.order_no,
            'user_id': self.user_id,
            'amount': self.amount,
            'points': self.points,
            'package_name': self.package_name,
            'payment_method': self.payment_method,
            'payment_status': self.payment_status,
            'payment_time': self.payment_time.isoformat() if self.payment_time else None,
            'transaction_id': self.transaction_id,
            'status': self.status,
            'remark': self.remark,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        } 