from datetime import datetime
from app import db

class PaymentOrder(db.Model):
    __tablename__ = 'payment_order'
    id = db.Column(db.Integer, primary_key=True)
    order_no = db.Column(db.String(64), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    amount = db.Column(db.Integer, nullable=False)  # 金额（分）
    points = db.Column(db.Integer, nullable=False)
    channel = db.Column(db.String(16), nullable=False)  # wechat/alipay
    status = db.Column(db.String(16), default='pending')  # pending/paid/failed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    paid_at = db.Column(db.DateTime)
    trade_no = db.Column(db.String(128))  # 支付平台流水号 