#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分日志模型
Points Log Model
"""

from datetime import datetime
from app.models.base import db

class PointsLog(db.Model):
    __tablename__ = 'points_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    
    # 积分变动
    points = db.Column(db.Integer, nullable=False, comment='变动积分数（正数增加，负数减少）')
    balance = db.Column(db.Integer, nullable=False, comment='变动后的余额')
    
    # 变动类型和描述
    type = db.Column(db.String(50), nullable=False, comment='变动类型')
    description = db.Column(db.String(500), comment='变动描述')
    related_id = db.Column(db.String(100), comment='关联ID（如订单ID等）')
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # 关联用户
    user = db.relationship('User', backref='points_logs')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'points': self.points,
            'balance': self.balance,
            'type': self.type,
            'description': self.description,
            'related_id': self.related_id,
            'created_at': self.created_at.isoformat() if self.created_at else None
        } 