from datetime import datetime
from app import db

class PointsLog(db.Model):
    __tablename__ = 'points_log'
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON><PERSON>('users.id'), nullable=False)
    change = db.Column(db.Integer, nullable=False)  # 正数为增加，负数为减少
    type = db.Column(db.String(32), nullable=False)  # 变动类型，如充值、消费、任务奖励等
    remark = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    balance = db.Column(db.Integer, default=0)  # 变动后余额 