import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models.system_config import SystemConfig

app = create_app()
with app.app_context():
    configs = [
        {'config_key': 'site_name', 'config_value': '智能证件照系统', 'config_type': 'string', 'description': '网站名称', 'is_system': True},
        {'config_key': 'version', 'config_value': '1.0.0', 'config_type': 'string', 'description': '系统版本', 'is_system': True},
        {'config_key': 'max_upload_size', 'config_value': '16', 'config_type': 'integer', 'description': '最大上传文件大小(MB)', 'is_system': True},
        {'config_key': 'allowed_image_types', 'config_value': 'jpg,png,gif,webp', 'config_type': 'string', 'description': '允许的图片格式', 'is_system': True},
        {'config_key': 'maintenance_mode', 'config_value': 'false', 'config_type': 'boolean', 'description': '系统维护模式', 'is_system': True},
        {'config_key': 'allow_register', 'config_value': 'true', 'config_type': 'boolean', 'description': '注册功能', 'is_system': True},
        {'config_key': 'announcement', 'config_value': '', 'config_type': 'string', 'description': '系统公告', 'is_system': True},
        {'config_key': 'customer_service_qq', 'config_value': '', 'config_type': 'string', 'description': '客服QQ', 'is_system': True},
        {'config_key': 'customer_service_wechat', 'config_value': '', 'config_type': 'string', 'description': '客服微信', 'is_system': True},
        {'config_key': 'copyright', 'config_value': '© 2024 智能证件照系统. All rights reserved.', 'config_type': 'string', 'description': '版权信息', 'is_system': True},
        
        # 广告配置
        {'config_key': 'baidu_appid', 'config_value': '', 'config_type': 'string', 'description': '百度广告AppID', 'is_system': False},
        {'config_key': 'baidu_appkey', 'config_value': '', 'config_type': 'string', 'description': '百度广告AppKey', 'is_system': False},
        {'config_key': 'baidu_ad_unit_id', 'config_value': '', 'config_type': 'string', 'description': '百度广告单元ID', 'is_system': False},
        {'config_key': 'baidu_enabled', 'config_value': 'false', 'config_type': 'boolean', 'description': '启用百度广告', 'is_system': False},
        
        {'config_key': 'toutiao_appid', 'config_value': '', 'config_type': 'string', 'description': '头条广告AppID', 'is_system': False},
        {'config_key': 'toutiao_appkey', 'config_value': '', 'config_type': 'string', 'description': '头条广告AppKey', 'is_system': False},
        {'config_key': 'toutiao_ad_unit_id', 'config_value': '', 'config_type': 'string', 'description': '头条广告单元ID', 'is_system': False},
        {'config_key': 'toutiao_enabled', 'config_value': 'false', 'config_type': 'boolean', 'description': '启用头条广告', 'is_system': False},
        
        {'config_key': 'admob_app_id', 'config_value': '', 'config_type': 'string', 'description': 'AdMob应用ID', 'is_system': False},
        {'config_key': 'admob_ad_unit_id', 'config_value': '', 'config_type': 'string', 'description': 'AdMob广告单元ID', 'is_system': False},
        {'config_key': 'admob_enabled', 'config_value': 'false', 'config_type': 'boolean', 'description': '启用AdMob广告', 'is_system': False},
    ]
    
    for config_data in configs:
        existing = SystemConfig.query.filter_by(config_key=config_data['config_key']).first()
        if not existing:
            config = SystemConfig(**config_data)
            db.session.add(config)
            print(f"添加配置: {config_data['config_key']}")
        else:
            print(f"配置已存在: {config_data['config_key']}")
    
    db.session.commit()
    print("系统配置初始化完成！") 