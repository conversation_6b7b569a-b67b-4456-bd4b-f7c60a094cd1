from app import create_app, db
from app.models.system_config import SystemConfig

app = create_app()
with app.app_context():
    configs = [
        {'config_key': 'site_name', 'config_value': '智能证件照系统', 'config_type': 'string', 'description': '网站名称', 'is_system': True},
        {'config_key': 'version', 'config_value': '1.0.0', 'config_type': 'string', 'description': '系统版本', 'is_system': True},
        {'config_key': 'max_upload_size', 'config_value': '16', 'config_type': 'integer', 'description': '最大上传文件大小(MB)', 'is_system': True},
        {'config_key': 'allowed_image_types', 'config_value': 'jpg,png,gif,webp', 'config_type': 'string', 'description': '允许的图片格式', 'is_system': True},
        {'config_key': 'maintenance_mode', 'config_value': 'false', 'config_type': 'boolean', 'description': '系统维护模式', 'is_system': True},
        {'config_key': 'allow_register', 'config_value': 'true', 'config_type': 'boolean', 'description': '注册功能', 'is_system': True},
        {'config_key': 'announcement', 'config_value': '', 'config_type': 'string', 'description': '系统公告', 'is_system': True},
        {'config_key': 'customer_service_qq', 'config_value': '', 'config_type': 'string', 'description': '客服QQ', 'is_system': True},
        {'config_key': 'customer_service_wechat', 'config_value': '', 'config_type': 'string', 'description': '客服微信', 'is_system': True},
        {'config_key': 'copyright', 'config_value': '© 2024 智能证件照系统. All rights reserved.', 'config_type': 'string', 'description': '版权信息', 'is_system': True},
        # 可继续补充支付、广告、邮件等配置项
    ]
    for c in configs:
        config = SystemConfig.query.filter_by(config_key=c['config_key']).first()
        if not config:
            config = SystemConfig(**c)
            db.session.add(config)
        else:
            config.config_value = c['config_value']
            config.config_type = c['config_type']
            config.description = c['description']
            config.is_system = True
    db.session.commit()
    print('系统配置项已补全') 