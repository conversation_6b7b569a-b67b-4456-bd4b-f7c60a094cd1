/* 现代双栏模板样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.resume-container {
    max-width: 210mm;
    margin: 0 auto;
    background: white;
    display: flex;
    min-height: 297mm;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.left-column {
    width: 35%;
    background: #2c3e50;
    color: white;
    padding: 2rem 1.5rem;
}

.right-column {
    width: 65%;
    padding: 2rem;
}



.left-column h3 {
    color: #ecf0f1;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.contact-section,
.skills-section {
    margin-bottom: 2rem;
}

.contact-section p {
    margin-bottom: 0.5rem;
}

.skill-item {
    margin-bottom: 1rem;
}

.skill-name {
    display: block;
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
}

.skill-bar {
    height: 8px;
    background: rgba(255,255,255,0.2);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: #3498db;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.header {
    margin-bottom: 2rem;
}

.header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.header h2 {
    font-size: 1.3rem;
    color: #3498db;
    font-weight: 300;
}

.section {
    margin-bottom: 2rem;
}

.section h3 {
    color: #2c3e50;
    font-size: 1.4rem;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.experience-item,
.education-item {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.exp-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.3rem;
}

.exp-header h4 {
    color: #2c3e50;
    font-size: 1.2rem;
}

.date {
    color: #7f8c8d;
    font-size: 0.9rem;
    font-style: italic;
}

.experience-item h5,
.education-item h4 {
    color: #3498db;
    margin-bottom: 0.5rem;
}

.experience-item p,
.education-item p {
    color: #555;
    line-height: 1.6;
}

@media print {
    .resume-container {
        box-shadow: none;
        margin: 0;
    }
}