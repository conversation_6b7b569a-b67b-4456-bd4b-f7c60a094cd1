"""清理数据库中的旧模板"""

import sqlite3
import os

def execute_sql_file(db_path, sql_file_path):
    print(f"正在执行SQL文件: {sql_file_path}")
    
    # 读取SQL文件内容
    with open(sql_file_path, 'r', encoding='utf-8') as f:
        sql_script = f.read()
    
    # 连接数据库
    print(f"连接数据库: {db_path}")
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 执行SQL脚本
        cursor.executescript(sql_script)
        conn.commit()
        print("SQL脚本执行成功")
        
        # 获取并打印验证结果
        cursor.execute("""
            SELECT id, name, description, category, industry, is_active, sort_order 
            FROM resume_templates 
            ORDER BY sort_order
        """)
        results = cursor.fetchall()
        print("\n当前模板列表:")
        print("ID | 名称 | 描述 | 分类 | 行业 | 是否激活 | 排序")
        print("-" * 80)
        for row in results:
            print(" | ".join(str(x) for x in row))
            
    except Exception as e:
        print(f"执行SQL时出错: {str(e)}")
        conn.rollback()
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    # 设置路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    db_path = os.path.join(current_dir, "instance", "photo_maker.db")
    sql_file_path = os.path.join(os.path.dirname(current_dir), "database", "clean_templates.sql")
    
    # 检查文件是否存在
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件不存在: {db_path}")
        exit(1)
    if not os.path.exists(sql_file_path):
        print(f"错误: SQL文件不存在: {sql_file_path}")
        exit(1)
    
    # 执行SQL文件
    execute_sql_file(db_path, sql_file_path) 