import BusinessClassic from './templates/BusinessClassic.vue';
import ModernMinimal from './templates/ModernMinimal.vue';
import CreativeDesign from './templates/CreativeDesign.vue';
import ElegantBusiness from './templates/ElegantBusiness.vue';
import AvatarCircle from './templates/AvatarCircle.vue';

/**
 * 模板名称到组件的映射。
 * key 应该是从API获取的模板的唯一标识符（例如，template.name 或 template.component_key）。
 * 确保这里的key与后端template表中的name字段或一个专门用于此目的的字段完全匹配。
 */
const templateMapping = {
  '商务经典': BusinessClassic,
  '现代简约': ModernMinimal,
  '创意设计': CreativeDesign,
  '优雅商务': ElegantBusiness,
  '头像圆形': AvatarCircle,
};

export default templateMapping; 