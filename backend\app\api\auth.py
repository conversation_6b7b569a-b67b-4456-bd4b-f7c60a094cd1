from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, create_access_token, create_refresh_token
from werkzeug.security import check_password_hash
import re
from datetime import datetime

from . import api_bp
from .errors import bad_request, unauthorized, forbidden, conflict, validation_error
from app import db
from app.models import User, UserLog
from app.models.points_log import PointsLog
from app.models.points_config import PointsConfig

@api_bp.route('/auth/register', methods=['POST'])
def register():
    """用户注册"""
    data = request.get_json()
    
    if not data:
        return bad_request('请求数据为空')
    
    # 验证必填字段
    required_fields = ['username', 'email', 'password']
    for field in required_fields:
        if not data.get(field):
            return bad_request(f'缺少必填字段: {field}')
    
    username = data['username'].strip()
    email = data['email'].strip().lower()
    password = data['password']
    
    # 验证用户名格式
    if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
        return validation_error('用户名格式不正确，只能包含字母、数字和下划线，长度3-20位')
    
    # 验证邮箱格式
    if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
        return validation_error('邮箱格式不正确')
    
    # 验证密码强度
    if len(password) < 6:
        return validation_error('密码长度不能少于6位')
    
    # 检查用户名是否已存在
    if User.query.filter_by(username=username).first():
        return conflict('用户名已存在')
    
    # 检查邮箱是否已存在
    if User.query.filter_by(email=email).first():
        return conflict('邮箱已被注册')
    
    try:
        # 创建新用户
        user = User(
            username=username,
            email=email,
            nickname=data.get('nickname', username),
            phone=data.get('phone', '').strip() if data.get('phone') else None
        )
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        # 邀请注册奖励
        invite_code = data.get('invite')
        if invite_code:
            inviter = User.query.filter_by(id=invite_code).first()
            if inviter:
                cfg = PointsConfig.query.filter_by(key='invite_reward').first()
                reward = int(cfg.value) if cfg else 10
                inviter.points += reward
                log = PointsLog(user_id=inviter.id, change=reward, type='invite', remark=f'邀请注册奖励，邀请用户ID:{user.id}', balance=inviter.points)
                db.session.add(log)
                db.session.commit()
        
        # 记录注册日志
        UserLog.log_action(
            user_id=user.id,
            action='user_register',
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        db.session.commit()
        
        # 生成令牌
        tokens = user.generate_tokens()
        
        return jsonify({
            'message': '注册成功',
            'user': user.to_dict(),
            'tokens': tokens
        }), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"注册失败: {e}")
        return validation_error('注册失败，请重试')

@api_bp.route('/auth/login', methods=['POST'])
def login():
    """用户登录"""
    data = request.get_json()
    
    if not data:
        return bad_request('请求数据为空')
    
    # 获取登录凭据（用户名或邮箱）
    credential = data.get('username') or data.get('email')
    password = data.get('password')
    
    if not credential or not password:
        return bad_request('请提供用户名/邮箱和密码')
    
    credential = credential.strip().lower()
    
    # 查找用户（支持用户名或邮箱登录）
    user = User.query.filter(
        db.or_(
            User.username == credential,
            User.email == credential
        )
    ).first()
    
    if not user or not user.check_password(password):
        # 记录登录失败
        if user:
            UserLog.log_action(
                user_id=user.id,
                action='login_failed',
                ip_address=request.remote_addr,
                user_agent=request.user_agent.string,
                response_status=401
            )
            db.session.commit()
        
        return unauthorized('用户名/邮箱或密码错误')
    
    # 检查用户状态
    if user.status != 1:
        return forbidden('用户账户已被禁用')
    
    try:
        # 更新登录信息
        user.update_login_info(request.remote_addr)
        
        # 记录登录成功
        UserLog.log_action(
            user_id=user.id,
            action='login_success',
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string,
            response_status=200
        )
        
        db.session.commit()
        
        # 生成令牌
        tokens = user.generate_tokens()
        
        return jsonify({
            'message': '登录成功',
            'user': user.to_dict(),
            'tokens': tokens
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"登录处理失败: {e}")
        return validation_error('登录失败，请重试')

@api_bp.route('/auth/refresh', methods=['POST'])
@jwt_required(refresh=True)
def refresh():
    """刷新访问令牌"""
    try:
        current_user_id = int(get_jwt_identity())
        user = User.query.get(current_user_id)
        
        if not user or user.status != 1:
            return unauthorized('用户不存在或已被禁用')
        
        # 生成新的访问令牌
        access_token = create_access_token(identity=str(user.id))
        
        return jsonify({
            'access_token': access_token
        })
        
    except Exception as e:
        current_app.logger.error(f"刷新令牌失败: {e}")
        return unauthorized('令牌刷新失败')

@api_bp.route('/auth/logout', methods=['POST'])
@jwt_required()
def logout():
    """用户登出"""
    try:
        current_user_id = int(get_jwt_identity())
        
        # 记录登出日志
        UserLog.log_action(
            user_id=current_user_id,
            action='logout',
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        db.session.commit()
        
        return jsonify({'message': '登出成功'})
        
    except Exception as e:
        current_app.logger.error(f"登出失败: {e}")
        return jsonify({'message': '登出成功'})  # 即使失败也返回成功

@api_bp.route('/auth/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """获取当前用户信息"""
    try:
        current_user_id = int(get_jwt_identity())
        user = User.query.get(current_user_id)
        
        if not user:
            return unauthorized('用户不存在')
        
        if user.status != 1:
            return forbidden('用户账户已被禁用')
        
        return jsonify({
            'user': user.to_dict(include_sensitive=True)
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户信息失败: {e}")
        return unauthorized('获取用户信息失败')

@api_bp.route('/auth/check-username', methods=['POST'])
def check_username():
    """检查用户名是否可用"""
    data = request.get_json()
    username = data.get('username', '').strip()
    
    if not username:
        return bad_request('用户名不能为空')
    
    if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
        return jsonify({
            'available': False,
            'message': '用户名格式不正确，只能包含字母、数字和下划线，长度3-20位'
        })
    
    user = User.query.filter_by(username=username).first()
    
    return jsonify({
        'available': user is None,
        'message': '用户名可用' if user is None else '用户名已被使用'
    })

@api_bp.route('/auth/check-email', methods=['POST'])
def check_email():
    """检查邮箱是否可用"""
    data = request.get_json()
    email = data.get('email', '').strip().lower()
    
    if not email:
        return bad_request('邮箱不能为空')
    
    if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
        return jsonify({
            'available': False,
            'message': '邮箱格式不正确'
        })
    
    user = User.query.filter_by(email=email).first()
    
    return jsonify({
        'available': user is None,
        'message': '邮箱可用' if user is None else '邮箱已被注册'
    })


@api_bp.route('/auth/reset-admin-password', methods=['POST'])
def reset_admin_password():
    """临时重置管理员密码的端点"""
    try:
        # 查找管理员用户
        admin_user = User.query.filter_by(username='admin').first()

        if not admin_user:
            return jsonify({
                'success': False,
                'message': '管理员用户不存在'
            })

        # 重新设置密码
        admin_user.set_password('admin123')
        admin_user.status = 1
        admin_user.is_active = True
        admin_user.is_admin = True

        db.session.commit()

        # 验证密码
        if admin_user.check_password('admin123'):
            return jsonify({
                'success': True,
                'message': '管理员密码重置成功，现在可以使用 admin/admin123 登录',
                'data': {
                    'username': admin_user.username,
                    'email': admin_user.email,
                    'status': admin_user.status,
                    'is_active': admin_user.is_active,
                    'is_admin': admin_user.is_admin
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '密码重置失败'
            })

    except Exception as e:
        current_app.logger.error(f"重置管理员密码失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'重置失败: {str(e)}'
        })