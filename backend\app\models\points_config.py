#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分配置模型
Points Configuration Model
"""

from datetime import datetime
from app.models.base import db

class PointsConfig(db.Model):
    __tablename__ = 'points_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 积分规则配置
    register_reward = db.Column(db.Integer, default=100, comment='注册奖励积分')
    invite_reward = db.Column(db.Integer, default=50, comment='邀请奖励积分')
    ad_reward = db.Column(db.Integer, default=5, comment='观看广告奖励积分')
    
    # 积分消费配置
    photo_process_cost = db.Column(db.Integer, default=10, comment='证件照处理消耗积分')
    resume_generate_cost = db.Column(db.Integer, default=20, comment='简历生成消耗积分')
    business_card_cost = db.Column(db.Integer, default=15, comment='名片制作消耗积分')
    document_compare_cost = db.Column(db.Integer, default=25, comment='文档对比消耗积分')
    
    # 充值配置
    recharge_ratio = db.Column(db.Float, default=1.0, comment='充值比例(1积分=1元)')
    
    # 支付配置
    wechat_enabled = db.Column(db.Boolean, default=False, comment='微信支付是否启用')
    wechat_account = db.Column(db.String(100), comment='微信收款账号')
    wechat_qr_code = db.Column(db.String(500), comment='微信收款二维码')
    wechat_receiver_name = db.Column(db.String(50), comment='微信收款人姓名')
    wechat_payment_note = db.Column(db.String(200), comment='微信支付说明')
    
    alipay_enabled = db.Column(db.Boolean, default=False, comment='支付宝是否启用')
    alipay_account = db.Column(db.String(100), comment='支付宝收款账号')
    alipay_qr_code = db.Column(db.String(500), comment='支付宝收款二维码')
    alipay_receiver_name = db.Column(db.String(50), comment='支付宝收款人姓名')
    alipay_payment_note = db.Column(db.String(200), comment='支付宝支付说明')
    
    # 套餐配置 (JSON格式存储)
    payment_packages = db.Column(db.Text, comment='充值套餐配置(JSON格式)')
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'register_reward': self.register_reward,
            'invite_reward': self.invite_reward,
            'ad_reward': self.ad_reward,
            'photo_process_cost': self.photo_process_cost,
            'resume_generate_cost': self.resume_generate_cost,
            'business_card_cost': self.business_card_cost,
            'document_compare_cost': self.document_compare_cost,
            'recharge_ratio': self.recharge_ratio,
            'wechat_enabled': self.wechat_enabled,
            'wechat_account': self.wechat_account,
            'wechat_qr_code': self.wechat_qr_code,
            'wechat_receiver_name': self.wechat_receiver_name,
            'wechat_payment_note': self.wechat_payment_note,
            'alipay_enabled': self.alipay_enabled,
            'alipay_account': self.alipay_account,
            'alipay_qr_code': self.alipay_qr_code,
            'alipay_receiver_name': self.alipay_receiver_name,
            'alipay_payment_note': self.alipay_payment_note,
            'payment_packages': self.payment_packages,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        } 