<template>
  <div class="credits-config">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">积分系统配置</h2>
        <p class="page-description">配置积分规则、支付方式和充值套餐</p>
      </div>
    </div>

    <!-- 配置选项卡 -->
    <el-tabs v-model="activeTab" type="border-card" class="config-tabs">
      <!-- 积分规则配置 -->
      <el-tab-pane label="积分规则" name="rules">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>积分获取和消费规则</span>
              <el-button type="primary" @click="saveRulesConfig" :loading="savingRules">
                保存配置
              </el-button>
            </div>
          </template>

          <el-form :model="rulesForm" label-width="200px" class="config-form">
            <el-divider content-position="left">积分获取规则</el-divider>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="注册奖励积分">
                  <el-input-number 
                    v-model="rulesForm.register_reward" 
                    :min="0" 
                    :max="1000"
                    placeholder="注册奖励积分"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邀请奖励积分">
                  <el-input-number 
                    v-model="rulesForm.invite_reward" 
                    :min="0" 
                    :max="500"
                    placeholder="邀请奖励积分"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="观看广告奖励积分">
                  <el-input-number 
                    v-model="rulesForm.ad_reward" 
                    :min="0" 
                    :max="50"
                    placeholder="观看广告奖励积分"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="充值比例">
                  <el-input-number 
                    v-model="rulesForm.recharge_ratio" 
                    :min="0.1" 
                    :max="10"
                    :precision="2"
                    placeholder="1积分=多少元"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-divider content-position="left">积分消费规则</el-divider>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="证件照处理消耗积分">
                  <el-input-number 
                    v-model="rulesForm.photo_process_cost" 
                    :min="0" 
                    :max="100"
                    placeholder="证件照处理消耗积分"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="简历生成消耗积分">
                  <el-input-number 
                    v-model="rulesForm.resume_generate_cost" 
                    :min="0" 
                    :max="100"
                    placeholder="简历生成消耗积分"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="名片制作消耗积分">
                  <el-input-number 
                    v-model="rulesForm.business_card_cost" 
                    :min="0" 
                    :max="100"
                    placeholder="名片制作消耗积分"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="文档对比消耗积分">
                  <el-input-number 
                    v-model="rulesForm.document_compare_cost" 
                    :min="0" 
                    :max="100"
                    placeholder="文档对比消耗积分"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 支付配置 -->
      <el-tab-pane label="支付配置" name="payment">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>支付收款配置</span>
              <el-button type="primary" @click="savePaymentConfig" :loading="savingPayment">
                保存配置
              </el-button>
            </div>
          </template>

          <div class="payment-config">
            <!-- 微信支付配置 -->
            <el-card class="payment-method-card">
              <template #header>
                <div class="payment-header">
                  <span>微信支付配置</span>
                  <el-tag :type="paymentForm.wechat.enabled ? 'success' : 'info'">
                    {{ paymentForm.wechat.enabled ? '已启用' : '未启用' }}
                  </el-tag>
                </div>
              </template>

              <el-form :model="paymentForm.wechat" label-width="120px" class="payment-form">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="启用状态">
                      <el-switch v-model="paymentForm.wechat.enabled" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="收款账号">
                      <el-input 
                        v-model="paymentForm.wechat.account" 
                        placeholder="微信收款账号"
                        :disabled="!paymentForm.wechat.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="收款二维码">
                      <div class="qr-upload-container">
                        <el-upload
                          class="qr-upload"
                          :action="null"
                          :auto-upload="false"
                          :show-file-list="false"
                          :on-change="(file) => handleQrCodeUpload(file, 'wechat')"
                          accept="image/*"
                          :disabled="!paymentForm.wechat.enabled"
                        >
                          <el-button type="primary" :disabled="!paymentForm.wechat.enabled">
                            上传二维码
                          </el-button>
                        </el-upload>
                        <div v-if="paymentForm.wechat.qr_code" class="qr-preview">
                          <img :src="paymentForm.wechat.qr_code" alt="微信收款二维码" />
                          <el-button 
                            type="danger" 
                            size="small" 
                            @click="removeQrCode('wechat')"
                            :disabled="!paymentForm.wechat.enabled"
                          >
                            删除
                          </el-button>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="收款人姓名">
                      <el-input 
                        v-model="paymentForm.wechat.receiver_name" 
                        placeholder="收款人姓名"
                        :disabled="!paymentForm.wechat.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="支付说明">
                      <el-input 
                        v-model="paymentForm.wechat.payment_note" 
                        type="textarea" 
                        :rows="3"
                        placeholder="微信支付说明，如：请备注订单号"
                        :disabled="!paymentForm.wechat.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>

            <!-- 支付宝配置 -->
            <el-card class="payment-method-card">
              <template #header>
                <div class="payment-header">
                  <span>支付宝配置</span>
                  <el-tag :type="paymentForm.alipay.enabled ? 'success' : 'info'">
                    {{ paymentForm.alipay.enabled ? '已启用' : '未启用' }}
                  </el-tag>
                </div>
              </template>

              <el-form :model="paymentForm.alipay" label-width="120px" class="payment-form">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="启用状态">
                      <el-switch v-model="paymentForm.alipay.enabled" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="收款账号">
                      <el-input 
                        v-model="paymentForm.alipay.account" 
                        placeholder="支付宝收款账号"
                        :disabled="!paymentForm.alipay.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="收款二维码">
                      <div class="qr-upload-container">
                        <el-upload
                          class="qr-upload"
                          :action="null"
                          :auto-upload="false"
                          :show-file-list="false"
                          :on-change="(file) => handleQrCodeUpload(file, 'alipay')"
                          accept="image/*"
                          :disabled="!paymentForm.alipay.enabled"
                        >
                          <el-button type="primary" :disabled="!paymentForm.alipay.enabled">
                            上传二维码
                          </el-button>
                        </el-upload>
                        <div v-if="paymentForm.alipay.qr_code" class="qr-preview">
                          <img :src="paymentForm.alipay.qr_code" alt="支付宝收款二维码" />
                          <el-button 
                            type="danger" 
                            size="small" 
                            @click="removeQrCode('alipay')"
                            :disabled="!paymentForm.alipay.enabled"
                          >
                            删除
                          </el-button>
                        </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="收款人姓名">
                      <el-input 
                        v-model="paymentForm.alipay.receiver_name" 
                        placeholder="收款人姓名"
                        :disabled="!paymentForm.alipay.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="支付说明">
                      <el-input 
                        v-model="paymentForm.alipay.payment_note" 
                        type="textarea" 
                        :rows="3"
                        placeholder="支付宝支付说明，如：请备注订单号"
                        :disabled="!paymentForm.alipay.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>

            <!-- 充值套餐配置 -->
            <el-card class="payment-method-card">
              <template #header>
                <div class="payment-header">
                  <span>充值套餐配置</span>
                  <el-button type="primary" size="small" @click="addPackage">
                    添加套餐
                  </el-button>
                </div>
              </template>

              <div class="package-config">
                <div class="package-list">
                  <div 
                    v-for="(pkg, index) in paymentForm.packages" 
                    :key="index"
                    class="package-item"
                  >
                    <el-row :gutter="20">
                      <el-col :span="6">
                        <el-form-item label="套餐名称">
                          <el-input 
                            v-model="pkg.name" 
                            placeholder="如：基础套餐"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="4">
                        <el-form-item label="积分数量">
                          <el-input-number 
                            v-model="pkg.points" 
                            :min="1"
                            placeholder="积分"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="4">
                        <el-form-item label="价格(元)">
                          <el-input-number 
                            v-model="pkg.price" 
                            :min="0.01"
                            :precision="2"
                            placeholder="价格"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="4">
                        <el-form-item label="原价(元)">
                          <el-input-number 
                            v-model="pkg.original_price" 
                            :min="0.01"
                            :precision="2"
                            placeholder="原价"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="3">
                        <el-form-item label="状态">
                          <el-switch v-model="pkg.enabled" />
                        </el-form-item>
                      </el-col>
                      <el-col :span="3">
                        <el-form-item>
                          <el-button 
                            type="danger" 
                            size="small" 
                            @click="removePackage(index)"
                            :disabled="paymentForm.packages.length <= 1"
                          >
                            删除
                          </el-button>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 积分管理 -->
      <el-tab-pane label="积分管理" name="management">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>积分调整和统计</span>
            </div>
          </template>

          <div class="management-content">
            <!-- 积分统计 -->
            <el-row :gutter="20" class="stats-row">
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.total_points }}</div>
                    <div class="stat-label">总积分</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.total_used }}</div>
                    <div class="stat-label">已使用积分</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.today_earned }}</div>
                    <div class="stat-label">今日获得积分</div>
                  </div>
                </el-card>
              </el-col>
              <el-col :span="6">
                <el-card class="stat-card">
                  <div class="stat-content">
                    <div class="stat-number">{{ stats.today_consumed }}</div>
                    <div class="stat-label">今日消费积分</div>
                  </div>
                </el-card>
              </el-col>
            </el-row>

            <!-- 积分调整 -->
            <el-card class="adjust-card">
              <template #header>
                <span>积分调整</span>
              </template>
              
              <el-form :model="adjustForm" label-width="120px" class="adjust-form">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="选择用户">
                      <el-select 
                        v-model="adjustForm.user_id" 
                        placeholder="选择用户"
                        filterable
                        remote
                        :remote-method="searchUsers"
                        :loading="searchingUsers"
                      >
                        <el-option
                          v-for="user in userOptions"
                          :key="user.id"
                          :label="`${user.username} (${user.points}积分)`"
                          :value="user.id"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="调整积分">
                      <el-input-number 
                        v-model="adjustForm.points" 
                        placeholder="正数增加，负数减少"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="调整原因">
                      <el-input 
                        v-model="adjustForm.description" 
                        placeholder="调整原因"
                      />
      </el-form-item>
                  </el-col>
                </el-row>
                
      <el-form-item>
                  <el-button type="primary" @click="adjustUserPoints" :loading="adjusting">
                    确认调整
                  </el-button>
      </el-form-item>
    </el-form>
            </el-card>

            <!-- 积分日志 -->
            <el-card class="logs-card">
              <template #header>
                <span>积分日志</span>
              </template>
              
              <el-table :data="logs" style="width: 100%">
                <el-table-column prop="username" label="用户" width="120" />
                <el-table-column prop="points" label="积分变动" width="100">
                  <template #default="scope">
                    <span :class="scope.row.points > 0 ? 'text-success' : 'text-danger'">
                      {{ scope.row.points > 0 ? '+' : '' }}{{ scope.row.points }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="balance" label="余额" width="100" />
                <el-table-column prop="type" label="类型" width="100">
                  <template #default="scope">
                    <el-tag :type="getTypeTag(scope.row.type)">
                      {{ getTypeText(scope.row.type) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" />
                <el-table-column prop="created_at" label="时间" width="180">
                  <template #default="scope">
                    {{ formatDate(scope.row.created_at) }}
                  </template>
                </el-table-column>
    </el-table>
              
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  :total="total"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { apiEndpoints } from '@/api'

// 响应式数据
const activeTab = ref('rules')
const savingRules = ref(false)
const savingPayment = ref(false)
const adjusting = ref(false)
const searchingUsers = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

// 积分规则表单
const rulesForm = reactive({
  register_reward: 100,
  invite_reward: 50,
  ad_reward: 5,
  photo_process_cost: 10,
  resume_generate_cost: 20,
  business_card_cost: 15,
  document_compare_cost: 25,
  recharge_ratio: 1.0
})

// 支付配置表单
const paymentForm = reactive({
  wechat: {
    enabled: false,
    account: '',
    qr_code: '',
    receiver_name: '',
    payment_note: ''
  },
  alipay: {
    enabled: false,
    account: '',
    qr_code: '',
    receiver_name: '',
    payment_note: ''
  },
  packages: []
})

// 积分调整表单
const adjustForm = reactive({
  user_id: null,
  points: 0,
  description: ''
})

// 统计数据
const stats = reactive({
  total_points: 0,
  total_used: 0,
  today_earned: 0,
  today_consumed: 0
})

// 用户选项
const userOptions = ref([])
const logs = ref([])

// 方法
const loadConfig = async () => {
  try {
    const response = await apiEndpoints.admin.credits.getConfig()
    if (response.data.success) {
      const data = response.data.data
      
      // 加载积分规则
      Object.keys(rulesForm).forEach(key => {
        if (data[key] !== undefined) {
          rulesForm[key] = data[key]
        }
      })
      
      // 加载支付配置
      paymentForm.wechat.enabled = data.wechat_enabled
      paymentForm.wechat.account = data.wechat_account || ''
      paymentForm.wechat.qr_code = data.wechat_qr_code ? `http://localhost:5000${data.wechat_qr_code}` : ''
      paymentForm.wechat.receiver_name = data.wechat_receiver_name || ''
      paymentForm.wechat.payment_note = data.wechat_payment_note || ''
      
      paymentForm.alipay.enabled = data.alipay_enabled
      paymentForm.alipay.account = data.alipay_account || ''
      paymentForm.alipay.qr_code = data.alipay_qr_code ? `http://localhost:5000${data.alipay_qr_code}` : ''
      paymentForm.alipay.receiver_name = data.alipay_receiver_name || ''
      paymentForm.alipay.payment_note = data.alipay_payment_note || ''
      
      paymentForm.packages = data.payment_packages || []
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败')
  }
}

const loadStats = async () => {
  try {
    const response = await apiEndpoints.admin.credits.getStats()
    if (response.data.success) {
      Object.assign(stats, response.data.data)
    }
  } catch (error) {
    console.error('加载统计失败:', error)
  }
}

const loadLogs = async () => {
  try {
    const response = await apiEndpoints.admin.credits.getLogs({
      page: currentPage.value,
      per_page: pageSize.value
    })
    if (response.data.success) {
      logs.value = response.data.data.logs
      total.value = response.data.data.total
    }
  } catch (error) {
    console.error('加载日志失败:', error)
  }
}

const saveRulesConfig = async () => {
  savingRules.value = true
  try {
    // 处理二维码URL，保存时去掉基础URL
    const processQrCodeUrl = (url) => {
      if (url && url.startsWith('http://localhost:5000')) {
        return url.replace('http://localhost:5000', '')
      }
      return url
    }
    
    const configData = {
      ...rulesForm,
      wechat_enabled: paymentForm.wechat.enabled,
      wechat_account: paymentForm.wechat.account,
      wechat_qr_code: processQrCodeUrl(paymentForm.wechat.qr_code),
      wechat_receiver_name: paymentForm.wechat.receiver_name,
      wechat_payment_note: paymentForm.wechat.payment_note,
      alipay_enabled: paymentForm.alipay.enabled,
      alipay_account: paymentForm.alipay.account,
      alipay_qr_code: processQrCodeUrl(paymentForm.alipay.qr_code),
      alipay_receiver_name: paymentForm.alipay.receiver_name,
      alipay_payment_note: paymentForm.alipay.payment_note,
      payment_packages: paymentForm.packages
    }
    
    const response = await apiEndpoints.admin.credits.saveConfig(configData)
    if (response.data.success) {
      ElMessage.success('积分规则配置保存成功')
    } else {
      ElMessage.error(response.data.error || '保存配置失败')
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    savingRules.value = false
  }
}

const savePaymentConfig = async () => {
  savingPayment.value = true
  try {
    console.log('开始保存支付配置')
    
    const configData = {
      ...rulesForm,
      wechat_enabled: paymentForm.wechat.enabled,
      wechat_account: paymentForm.wechat.account,
      wechat_qr_code: paymentForm.wechat.qr_code,
      wechat_receiver_name: paymentForm.wechat.receiver_name,
      wechat_payment_note: paymentForm.wechat.payment_note,
      alipay_enabled: paymentForm.alipay.enabled,
      alipay_account: paymentForm.alipay.account,
      alipay_qr_code: paymentForm.alipay.qr_code,
      alipay_receiver_name: paymentForm.alipay.receiver_name,
      alipay_payment_note: paymentForm.alipay.payment_note,
      payment_packages: paymentForm.packages
    }
    
    console.log('保存的配置数据:', configData)
    
    const response = await apiEndpoints.admin.credits.saveConfig(configData)
    console.log('保存响应:', response)
    
    if (response.data.success) {
      ElMessage.success('支付配置保存成功')
      console.log('配置保存成功')
    } else {
      console.error('保存失败响应:', response)
      ElMessage.error(response.data.error || '保存配置失败')
    }
  } catch (error) {
    console.error('保存配置异常:', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.error(`保存配置失败: ${error.message || '未知错误'}`)
  } finally {
    savingPayment.value = false
  }
}

const handleQrCodeUpload = async (file, type) => {
  try {
    console.log('开始上传二维码:', file, type)
    
    if (!file) {
      ElMessage.error('文件对象无效')
      return
    }
    
    const formData = new FormData()
    formData.append('file', file.raw || file)
    formData.append('payment_type', type)  // 添加支付类型参数
    
    console.log('FormData内容:', formData)
    console.log('文件信息:', {
      name: file.name,
      size: file.size,
      type: file.type,
      payment_type: type
    })
    
    const response = await apiEndpoints.admin.credits.uploadQr(formData)
    console.log('上传响应:', response)
    
    if (response.data.success) {
      // 构建完整的图片URL
      const baseUrl = 'http://localhost:5000'
      const fullImageUrl = `${baseUrl}${response.data.data.url}`
      paymentForm[type].qr_code = fullImageUrl
      ElMessage.success('二维码上传成功')
      console.log('二维码URL已设置:', fullImageUrl)
    } else {
      console.error('上传失败响应:', response)
      ElMessage.error(response.data.error || '二维码上传失败')
    }
  } catch (error) {
    console.error('上传异常:', error)
    console.error('错误详情:', error.response?.data)
    ElMessage.error(`二维码上传失败: ${error.message || '未知错误'}`)
  }
}

const removeQrCode = (type) => {
  paymentForm[type].qr_code = ''
}

const addPackage = () => {
  paymentForm.packages.push({
    id: `package_${Date.now()}`,
    name: '',
    points: 100,
    price: 10.0,
    original_price: 15.0,
    enabled: true
  })
}

const removePackage = (index) => {
  paymentForm.packages.splice(index, 1)
}

const searchUsers = async (query) => {
  if (query.length < 2) return
  
  searchingUsers.value = true
  try {
    const response = await apiEndpoints.admin.users.getUsers({
      keyword: query,
      per_page: 20
    })
    if (response.data.success) {
      userOptions.value = response.data.users
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
  } finally {
    searchingUsers.value = false
  }
}

const adjustUserPoints = async () => {
  if (!adjustForm.user_id) {
    ElMessage.warning('请选择用户')
    return
  }
  
  if (adjustForm.points === 0) {
    ElMessage.warning('请输入调整积分')
    return
  }
  
  adjusting.value = true
  try {
    const response = await apiEndpoints.admin.credits.adjustPoints(adjustForm)
    if (response.data.success) {
      ElMessage.success(response.data.data.message)
      adjustForm.user_id = null
      adjustForm.points = 0
      adjustForm.description = ''
      loadStats()
      loadLogs()
    }
  } catch (error) {
    console.error('调整积分失败:', error)
    ElMessage.error('调整积分失败')
  } finally {
    adjusting.value = false
  }
}

const handleSizeChange = (val) => {
  pageSize.value = val
  loadLogs()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadLogs()
}

const getTypeTag = (type) => {
  const typeMap = {
    'earn': 'success',
    'consume': 'danger',
    'register': 'primary',
    'invite': 'warning',
    'ad_reward': 'info',
    'admin_adjust': 'warning'
  }
  return typeMap[type] || 'info'
}

const getTypeText = (type) => {
  const typeMap = {
    'earn': '获得',
    'consume': '消费',
    'register': '注册',
    'invite': '邀请',
    'ad_reward': '广告',
    'admin_adjust': '调整'
  }
  return typeMap[type] || type
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

// 生命周期
onMounted(() => {
  loadConfig()
  loadStats()
  loadLogs()
})
</script>

<style scoped>
.credits-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.config-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.config-card {
  border: none;
  box-shadow: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.config-form {
  padding: 20px 0;
}

/* 支付配置样式 */
.payment-config {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.payment-method-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.payment-form {
  padding: 20px 0;
}

.qr-upload-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.qr-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.qr-preview img {
  max-width: 120px;
  max-height: 120px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.package-config {
  padding: 20px 0;
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.package-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

/* 管理页面样式 */
.management-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.adjust-card {
  margin-bottom: 20px;
}

.adjust-form {
  padding: 20px 0;
}

.logs-card {
  margin-top: 20px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.text-success {
  color: #67c23a;
}

.text-danger {
  color: #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .credits-config {
    padding: 10px;
  }
  
.config-form {
    padding: 10px 0;
  }
}
</style>