<template>
  <div class="credits-config">
    <h2>积分配置管理</h2>
    <el-form :model="configForm" label-width="180px" class="config-form">
      <el-form-item v-for="item in configForm" :key="item.key" :label="item.description || item.key">
        <el-input v-model="item.value" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </el-form-item>
    </el-form>
    <h3 style="margin-top:2em;">手动调整用户积分</h3>
    <el-form :inline="true" :model="adjustForm" class="adjust-form">
      <el-form-item label="用户ID"><el-input v-model="adjustForm.user_id" style="width:120px" /></el-form-item>
      <el-form-item label="变动积分"><el-input v-model="adjustForm.change" style="width:100px" /></el-form-item>
      <el-form-item label="备注"><el-input v-model="adjustForm.remark" style="width:180px" /></el-form-item>
      <el-form-item><el-button @click="adjustPoints">调整</el-button></el-form-item>
    </el-form>
    <h3 style="margin-top:2em;">积分变动日志</h3>
    <el-table :data="logs" style="width:100%" size="small">
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="user_id" label="用户ID" width="80" />
      <el-table-column prop="change" label="变动" width="80" />
      <el-table-column prop="type" label="类型" width="100" />
      <el-table-column prop="remark" label="备注" />
      <el-table-column prop="created_at" label="时间" width="180" />
      <el-table-column prop="balance" label="变动后余额" width="100" />
    </el-table>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
const configForm = ref([]);
const logs = ref([]);
const adjustForm = ref({ user_id: '', change: '', remark: '' });

async function fetchConfig() {
  const res = await axios.get('/api/admin/credits/config');
  configForm.value = res.data;
}
async function saveConfig() {
  await axios.post('/api/admin/credits/config', configForm.value);
  ElMessage.success('保存成功');
  fetchConfig();
}
async function fetchLogs() {
  const res = await axios.get('/api/admin/credits/logs');
  logs.value = res.data;
}
async function adjustPoints() {
  await axios.post('/api/admin/credits/adjust', adjustForm.value);
  ElMessage.success('积分已调整');
  fetchLogs();
}
onMounted(() => {
  fetchConfig();
  fetchLogs();
});
</script>
<style scoped>
.credits-config {
  max-width: 900px;
  margin: 2em auto;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 16px #3182ce22;
  padding: 2em 2.5em;
}
.config-form {
  margin-bottom: 2em;
}
</style> 