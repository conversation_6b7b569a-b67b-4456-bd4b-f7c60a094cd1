"""
安全工具函数
"""
import hashlib
import secrets
import string
from typing import <PERSON>ple

def generate_salt(length: int = 16) -> str:
    """
    生成随机盐值
    :param length: 盐值长度
    :return: 盐值字符串
    """
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def generate_password_hash(password: str, salt: str | None = None) -> Tuple[str, str]:
    """
    生成密码哈希
    :param password: 原始密码
    :param salt: 盐值（可选）
    :return: (密码哈希, 盐值)
    """
    if salt is None:
        salt = generate_salt()
    
    # 组合密码和盐值
    salted_password = password + salt
    
    # 使用SHA-256进行哈希
    hash_obj = hashlib.sha256(salted_password.encode())
    password_hash = hash_obj.hexdigest()
    
    return password_hash, salt

def verify_password(password: str, password_hash: str, salt: str) -> bool:
    """
    验证密码
    :param password: 待验证的密码
    :param password_hash: 存储的密码哈希
    :param salt: 存储的盐值
    :return: 是否验证通过
    """
    # 重新计算哈希
    computed_hash, _ = generate_password_hash(password, salt)
    return computed_hash == password_hash

def generate_random_password(length: int = 12) -> str:
    """
    生成随机密码
    :param length: 密码长度
    :return: 随机密码
    """
    # 确保密码包含所有类型的字符
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    symbols = '!@#$%^&*'
    
    # 确保至少包含一个特殊字符
    password = [
        secrets.choice(lowercase),
        secrets.choice(uppercase),
        secrets.choice(digits),
        secrets.choice(symbols)
    ]
    
    # 填充剩余长度
    remaining_length = length - len(password)
    all_characters = lowercase + uppercase + digits + symbols
    password.extend(secrets.choice(all_characters) for _ in range(remaining_length))
    
    # 打乱密码字符顺序
    password_list = list(password)
    secrets.SystemRandom().shuffle(password_list)
    
    return ''.join(password_list)

def hash_string(s: str) -> str:
    """
    对字符串进行简单哈希
    :param s: 输入字符串
    :return: 哈希值
    """
    return hashlib.sha256(s.encode()).hexdigest()

def generate_token(length: int = 32) -> str:
    """
    生成随机令牌
    :param length: 令牌长度
    :return: 随机令牌
    """
    return secrets.token_urlsafe(length)
