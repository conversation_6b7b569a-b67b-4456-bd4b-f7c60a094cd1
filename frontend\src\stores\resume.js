import { defineStore } from 'pinia'
import api from '@/api'
import { ElMessage } from 'element-plus'
import { getAllTemplates, getTemplateById } from '@/config/templateMapping'

// 转换简历数据结构以匹配模板期望的格式
const transformResumeData = (data) => {
  if (!data) return null

  // 确保基础信息存在
  const baseInfo = {
    id: data.id || 0,
    user_id: data.user_id || 0,
    title: data.title || '未命名简历',
    template_id: data.template_id || 'basic',
    status: data.status || 'draft',
    full_name: data.full_name || '姓名',
    gender: data.gender || '',
    age: data.age || '',
    phone: data.phone || '',
    email: data.email || '',
    address: data.address || '',
    objective: data.objective || '',
    summary: data.summary || '',
    photo_url: data.photo_url || '',
    created_at: data.created_at || new Date().toISOString(),
    updated_at: data.updated_at || new Date().toISOString()
  }

  // 转换教育经历
  const educations = (data.educations || []).map(edu => ({
    id: edu.id || 0,
    school_name: edu.school_name || '',
    degree: edu.degree || '',
    major: edu.major || '',
    start_date: edu.start_date || '',
    end_date: edu.end_date || '',
    gpa: edu.gpa || '',
    description: edu.description || '',
    sort_order: edu.sort_order || 0,
    period: (edu.start_date && edu.end_date) ? `${edu.start_date} - ${edu.end_date}` : ''
  }))

  // 转换工作经历
  const workExperiences = (data.work_experiences || []).map(exp => ({
    id: exp.id || 0,
    company_name: exp.company_name || '',
    position: exp.position || '',
    start_date: exp.start_date || '',
    end_date: exp.end_date || '',
    is_current: exp.is_current || false,
    description: exp.description || '',
    achievements: Array.isArray(exp.achievements) ? exp.achievements.filter(Boolean) : [],
    sort_order: exp.sort_order || 0,
    period: (exp.start_date && exp.end_date) ? `${exp.start_date} - ${exp.end_date}` : ''
  }))

  // 转换项目经验
  const projects = (data.projects || []).map(proj => ({
    id: proj.id || 0,
    name: proj.name || '',
    role: proj.role || '',
    start_date: proj.start_date || '',
    end_date: proj.end_date || '',
    description: proj.description || '',
    technologies: Array.isArray(proj.technologies) ? proj.technologies.filter(Boolean) : [],
    achievements: Array.isArray(proj.achievements) ? proj.achievements.filter(Boolean) : [],
    project_url: proj.project_url || '',
    sort_order: proj.sort_order || 0,
    period: (proj.start_date && proj.end_date) ? `${proj.start_date} - ${proj.end_date}` : ''
  }))

  // 转换技能
  const skills = (data.skills || []).map(skill => ({
    id: skill.id || 0,
    category: skill.category || '',
    name: skill.name || '',
    level: typeof skill.level === 'number' ? skill.level :
           skill.proficiency === '专家' ? 100 :
           skill.proficiency === '高级' ? 80 :
           skill.proficiency === '中级' ? 60 :
           skill.proficiency === '初级' ? 40 : 20,
    proficiency: skill.proficiency || '初级',
    years_experience: skill.years_experience || '',
    sort_order: skill.sort_order || 0
  }))

  // 转换证书
  const certifications = (data.certifications || []).map(cert => ({
    id: cert.id || 0,
    name: cert.name || '',
    issuer: cert.issuer || '',
    issue_date: cert.issue_date || '',
    expiry_date: cert.expiry_date || '',
    credential_id: cert.credential_id || '',
    credential_url: cert.credential_url || '',
    sort_order: cert.sort_order || 0,
    date: cert.issue_date || cert.date || ''
  }))

  const transformedData = {
    ...baseInfo,
    educations: educations.sort((a, b) => a.sort_order - b.sort_order),
    work_experiences: workExperiences.sort((a, b) => a.sort_order - b.sort_order),
    projects: projects.sort((a, b) => a.sort_order - b.sort_order),
    skills: skills.sort((a, b) => a.sort_order - b.sort_order),
    certifications: certifications.sort((a, b) => a.sort_order - b.sort_order)
  }

  // 调试输出：检查转换后的数据
  console.log('转换后的简历数据:', transformedData)

  return transformedData
}

export const useResumeStore = defineStore('resume', {
  state: () => ({
    currentResume: null,
    templates: [],
    loading: false,
    saving: false
  }),

  actions: {
    async loadResume(id) {
      try {
        this.loading = true
        const response = await api.resumes.getDetail(id)
        
        // 调试输出：检查API响应
        console.log('API响应:', response)
        
        if (!response?.data) {
          throw new Error('API响应格式错误')
        }

        if (!response.data.success) {
          throw new Error(response.data.message || '加载简历失败')
        }

          // 检查数据完整性
          const data = response.data.data
          if (!data) {
            throw new Error('API返回的数据为空')
          }

          // 检查必需字段
          const requiredFields = ['id', 'title', 'full_name']
          const missingFields = requiredFields.filter(field => !data[field])
          if (missingFields.length > 0) {
          console.warn('API返回的数据缺少必需字段:', missingFields)
          // 不抛出错误，而是使用默认值继续处理
          }

          // 检查数组字段
          const arrayFields = ['educations', 'work_experiences', 'projects', 'skills', 'certifications']
          arrayFields.forEach(field => {
            if (!Array.isArray(data[field])) {
              data[field] = []
              console.warn(`${field} 不是数组，已初始化为空数组`)
            }
          })

        // 转换数据
          const transformedData = transformResumeData(data)
        
        // 检查转换后的数据
        if (!transformedData) {
          throw new Error('数据转换失败')
        }

        // 检查转换后的数据完整性
        const transformedRequiredFields = ['id', 'title', 'full_name']
        const transformedMissingFields = transformedRequiredFields.filter(field => !transformedData[field])
        if (transformedMissingFields.length > 0) {
          throw new Error(`数据转换后缺失必要字段: ${transformedMissingFields.join(', ')}`)
        }

        // 检查转换后的数组字段
        const transformedArrayFields = ['educations', 'work_experiences', 'projects', 'skills', 'certifications']
        transformedArrayFields.forEach(field => {
          if (!Array.isArray(transformedData[field])) {
            throw new Error(`转换后的 ${field} 字段不是数组`)
          }
        })

        // 保存到store
        this.currentResume = transformedData
        
        // 调试输出：检查最终数据
        console.log('最终的简历数据:', this.currentResume)
        
        return transformedData
      } catch (error) {
        console.error('加载简历失败:', error)
        ElMessage.error(error.message || '加载简历失败')
        throw error
      } finally {
        this.loading = false
      }
    },

    async updateResume(data) {
      try {
        this.saving = true
        
        // 确保我们发送的是完整的简历数据
        const updateData = { ...this.currentResume, ...data }

        const response = await api.resumes.update(updateData.id, updateData)
        if (response.data.success) {
          const transformedData = transformResumeData(response.data.data)
          this.currentResume = transformedData
          ElMessage.success('保存成功')
          return transformedData
        }
        throw new Error(response.data.message || '保存简历失败')
      } catch (error) {
        console.error('保存简历失败:', error)
        ElMessage.error(error.message || '保存简历失败')
        throw error
      } finally {
        this.saving = false
      }
    },

    async loadTemplates() {
      try {
        // 首先获取本地配置的模板
        const localTemplates = getAllTemplates()
        
        // 尝试从API获取后端模板数据
        try {
          const response = await api.resumes.getTemplates()
          
          if (response.data.success) {
            // 将API数据与本地配置合并
            const apiTemplates = response.data.data.map(template => {
              const localTemplate = getTemplateById(template.id)
              return {
                id: template.id,
                name: template.name || localTemplate?.name || '未命名模板',
                description: template.description || localTemplate?.description || '',
                preview_image: template.preview_image || localTemplate?.preview_image || '/templates/default-preview.png',
                category: template.category || localTemplate?.category || 'other',
                industry: template.industry || localTemplate?.industry || 'other',
                backendId: localTemplate?.backendId || 'basic',
                component: localTemplate?.component,
                created_at: template.created_at,
                updated_at: template.updated_at
              }
            })
            
            // 添加API中没有但本地配置中有的模板
            const apiIds = new Set(apiTemplates.map(t => t.id))
            const missingTemplates = localTemplates.filter(t => !apiIds.has(t.id))
            
            this.templates = [...apiTemplates, ...missingTemplates]
            return this.templates
          } else {
            console.warn('从API加载模板列表失败:', response.data.message)
          }
        } catch (apiError) {
          console.error('API请求失败:', apiError)
        }

        // 如果API失败，使用本地配置的模板
        console.log('使用本地模板配置')
        this.templates = localTemplates
        return this.templates

      } catch (error) {
        console.error('加载模板列表失败:', error)
        ElMessage.error('加载模板列表失败')
        
        // 最后的兜底方案：使用本地模板配置
        this.templates = getAllTemplates()
        return this.templates
      }
    },

    async previewTemplate(resumeId, templateId) {
      // 这个函数现在只是一个占位符，因为预览逻辑已经全部移到前端处理
      // 切换模板时，我们只更新ID，并依赖现有的数据重新渲染组件
      console.log(`模板切换至: resumeId=${resumeId}, templateId=${templateId}`)
      return Promise.resolve({ success: true })
    },

    async exportResume(resumeId, format = 'pdf') {
      try {
        const response = await api.resumes.export(resumeId, format)
        
        // 创建下载链接
        const blob = new Blob([response.data], {
          type: format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.currentResume?.title || '我的简历'}.${format}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
        
        ElMessage.success('导出成功')
      } catch (error) {
        console.error('导出简历失败:', error)
        ElMessage.error(error.message || '导出简历失败')
        throw error
      }
    }
  }
}) 