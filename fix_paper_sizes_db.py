#!/usr/bin/env python3
"""
修复paper_sizes表的is_active字段问题
"""
import os
import sys
import sqlite3

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)

def fix_paper_sizes_table():
    """修复paper_sizes表，添加is_active字段"""
    db_path = os.path.join(backend_dir, 'app.db')

    print(f"🔍 检查数据库路径: {db_path}")
    print(f"🔍 backend_dir: {backend_dir}")
    print(f"🔍 数据库文件存在: {os.path.exists(db_path)}")

    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        # 尝试其他可能的路径
        alt_paths = [
            'backend/app.db',
            'app.db',
            'backend/instance/app.db',
            'instance/app.db'
        ]
        for alt_path in alt_paths:
            if os.path.exists(alt_path):
                print(f"✅ 找到数据库文件: {alt_path}")
                db_path = alt_path
                break
        else:
            return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(paper_sizes)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print("📋 当前paper_sizes表结构:")
        for col in columns:
            print(f"   - {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # 检查is_active字段是否存在
        if 'is_active' not in column_names:
            print("\n🔧 添加is_active字段...")
            
            # 添加is_active字段
            cursor.execute("ALTER TABLE paper_sizes ADD COLUMN is_active BOOLEAN DEFAULT 1")
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS ix_paper_sizes_is_active ON paper_sizes (is_active)")
            
            print("✅ is_active字段添加成功")
        else:
            print("\n✅ is_active字段已存在")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM paper_sizes")
        count = cursor.fetchone()[0]
        print(f"\n📊 当前相纸规格数量: {count}")
        
        if count == 0:
            print("\n🔧 初始化相纸规格数据...")
            
            # 插入初始数据
            paper_sizes_data = [
                ('4寸照片', 101.6, 152.4, 1200, 1800, 5.0, 1, 1, 1),
                ('5寸照片', 127.0, 178.0, 1500, 2100, 5.0, 1, 1, 2),
                ('6寸照片', 152.4, 203.2, 1800, 2400, 5.0, 1, 1, 3),
                ('A4纸', 210.0, 297.0, 2480, 3508, 10.0, 1, 1, 4),
            ]
            
            cursor.executemany("""
                INSERT INTO paper_sizes 
                (name, width_mm, height_mm, width_px, height_px, margin_mm, is_common, is_active, sort_order)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, paper_sizes_data)
            
            print(f"✅ 成功添加 {len(paper_sizes_data)} 个相纸规格")
        
        # 提交更改
        conn.commit()
        
        # 验证修复结果
        cursor.execute("SELECT name, width_mm, height_mm, is_active FROM paper_sizes ORDER BY sort_order")
        results = cursor.fetchall()
        
        print("\n📋 当前相纸规格:")
        for row in results:
            print(f"   - {row[0]}: {row[1]}x{row[2]}mm, is_active={row[3]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    print("🔧 开始修复paper_sizes表...")
    print(f"🔍 当前工作目录: {os.getcwd()}")
    print(f"🔍 脚本目录: {os.path.dirname(__file__)}")
    print(f"🔍 backend目录: {backend_dir}")

    try:
        success = fix_paper_sizes_table()

        if success:
            print("\n🎉 修复完成！现在可以正常使用相纸规格功能了。")
        else:
            print("\n❌ 修复失败，请检查错误信息。")
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")
        import traceback
        traceback.print_exc()
