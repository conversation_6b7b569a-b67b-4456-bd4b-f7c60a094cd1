#!/usr/bin/env python3
"""
清理数据库中指向不存在文件的图片记录
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.image import Image

def clean_invalid_images():
    """清理无效的图片记录"""
    app = create_app()
    
    with app.app_context():
        # 获取所有图片记录
        images = Image.query.all()
        
        print(f"检查 {len(images)} 张图片记录...")
        
        invalid_count = 0
        valid_count = 0
        
        for image in images:
            # 检查原图文件是否存在
            full_path = image.get_full_path()
            if not os.path.exists(full_path):
                print(f"删除无效记录: {image.original_filename} (文件不存在: {full_path})")
                db.session.delete(image)
                invalid_count += 1
                continue
            
            # 检查缩略图文件是否存在（如果有缩略图路径）
            if image.thumbnail_path:
                thumbnail_path = image.get_thumbnail_path()
                if not os.path.exists(thumbnail_path):
                    print(f"修复缩略图路径: {image.original_filename} (缩略图不存在，重新生成)")
                    # 删除缩略图记录，让系统重新生成
                    image.thumbnail_path = None
            
            valid_count += 1
        
        # 提交更改
        db.session.commit()
        
        print(f"清理完成: 删除了 {invalid_count} 个无效记录，保留了 {valid_count} 个有效记录")

if __name__ == '__main__':
    clean_invalid_images() 