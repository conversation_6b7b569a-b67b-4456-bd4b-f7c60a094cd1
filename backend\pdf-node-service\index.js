const express = require('express');
const multer = require('multer');
const fs = require('fs');
const { PDFDocument } = require('pdf-lib');
const jwt = require('jsonwebtoken');

const app = express();
const upload = multer({ dest: 'uploads/' });

// JWT密钥（建议与主站保持一致，或通过环境变量配置）
const JWT_SECRET = process.env.JWT_SECRET || 'your-flask-jwt-secret';

// 合并PDF接口
app.post('/api/pdf/merge', upload.array('pdfs'), async (req, res) => {
  // 1. 校验用户身份
  const token = req.headers.authorization?.split(' ')[1];
  try {
    jwt.verify(token, JWT_SECRET);
  } catch (e) {
    return res.status(401).json({ error: '未授权' });
  }
  // 2. 合并PDF
  try {
    const pdfDoc = await PDFDocument.create();
    for (const file of req.files) {
      const srcDoc = await PDFDocument.load(fs.readFileSync(file.path));
      const copiedPages = await pdfDoc.copyPages(srcDoc, srcDoc.getPageIndices());
      copiedPages.forEach(page => pdfDoc.addPage(page));
    }
    const mergedPdf = await pdfDoc.save();
    // 3. 返回合并后的PDF
    res.setHeader('Content-Type', 'application/pdf');
    res.send(Buffer.from(mergedPdf));
  } catch (err) {
    res.status(500).json({ error: 'PDF合并失败', detail: err.message });
  } finally {
    // 清理临时文件
    req.files.forEach(f => fs.unlink(f.path, () => {}));
  }
});

// PDF拆分API
app.post('/api/pdf/split', upload.single('pdf'), async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];
  try {
    jwt.verify(token, JWT_SECRET);
  } catch (e) {
    return res.status(401).json({ error: '未授权' });
  }
  try {
    const { pageIndices } = req.body; // 逗号分隔或数组
    const indices = typeof pageIndices === 'string' ? pageIndices.split(',').map(Number) : pageIndices;
    const srcDoc = await PDFDocument.load(fs.readFileSync(req.file.path));
    const outFiles = [];
    for (const idx of indices) {
      const pdfDoc = await PDFDocument.create();
      const [page] = await pdfDoc.copyPages(srcDoc, [idx]);
      pdfDoc.addPage(page);
      const pdfBytes = await pdfDoc.save();
      const outPath = `uploads/split_${Date.now()}_${idx}.pdf`;
      fs.writeFileSync(outPath, pdfBytes);
      outFiles.push(outPath);
    }
    // 返回文件路径或合并为zip（简化：返回第一个文件）
    res.setHeader('Content-Type', 'application/pdf');
    res.send(fs.readFileSync(outFiles[0]));
    // 清理
    fs.unlink(req.file.path, () => {});
    outFiles.forEach(f => fs.unlink(f, () => {}));
  } catch (err) {
    res.status(500).json({ error: 'PDF拆分失败', detail: err.message });
  }
});

// PDF页面长图生成API
const sharp = require('sharp');
app.post('/api/pdf/long-image', upload.array('images'), async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];
  try {
    jwt.verify(token, JWT_SECRET);
  } catch (e) {
    return res.status(401).json({ error: '未授权' });
  }
  try {
    // 前端需先将PDF页面渲染为图片上传
    const images = req.files.map(f => f.path);
    const imageBuffers = images.map(p => fs.readFileSync(p));
    const sharpImages = imageBuffers.map(b => sharp(b));
    const metadataArr = await Promise.all(sharpImages.map(img => img.metadata()));
    const totalHeight = metadataArr.reduce((sum, m) => sum + m.height, 0);
    const maxWidth = Math.max(...metadataArr.map(m => m.width));
    const compositeImages = [];
    let top = 0;
    for (let i = 0; i < sharpImages.length; i++) {
      compositeImages.push({ input: await sharpImages[i].toBuffer(), top, left: 0 });
      top += metadataArr[i].height;
    }
    const longImage = await sharp({ create: { width: maxWidth, height: totalHeight, channels: 3, background: 'white' } })
      .composite(compositeImages)
      .png()
      .toBuffer();
    res.setHeader('Content-Type', 'image/png');
    res.send(longImage);
    req.files.forEach(f => fs.unlink(f.path, () => {}));
  } catch (err) {
    res.status(500).json({ error: '长图生成失败', detail: err.message });
  }
});

// OCR识别API
const Tesseract = require('tesseract.js');
app.post('/api/pdf/ocr', upload.single('image'), async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];
  try {
    jwt.verify(token, JWT_SECRET);
  } catch (e) {
    return res.status(401).json({ error: '未授权' });
  }
  try {
    const imagePath = req.file.path;
    const { data: { text } } = await Tesseract.recognize(imagePath, 'chi_sim+eng');
    res.json({ text });
    fs.unlink(imagePath, () => {});
  } catch (err) {
    res.status(500).json({ error: 'OCR识别失败', detail: err.message });
  }
});

// PDF文本批注API（示例：添加文本到指定页面坐标）
app.post('/api/pdf/annotate', upload.single('pdf'), async (req, res) => {
  const token = req.headers.authorization?.split(' ')[1];
  try {
    jwt.verify(token, JWT_SECRET);
  } catch (e) {
    return res.status(401).json({ error: '未授权' });
  }
  try {
    const { annotations } = req.body; // [{pageIndex, text, x, y, size, color}]
    const srcDoc = await PDFDocument.load(fs.readFileSync(req.file.path));
    const annots = typeof annotations === 'string' ? JSON.parse(annotations) : annotations;
    for (const annot of annots) {
      const page = srcDoc.getPage(annot.pageIndex);
      page.drawText(annot.text, {
        x: annot.x,
        y: annot.y,
        size: annot.size || 12,
        color: annot.color ? srcDoc.constructor.rgb(...annot.color) : undefined
      });
    }
    const outBytes = await srcDoc.save();
    res.setHeader('Content-Type', 'application/pdf');
    res.send(Buffer.from(outBytes));
    fs.unlink(req.file.path, () => {});
  } catch (err) {
    res.status(500).json({ error: '批注失败', detail: err.message });
  }
});

// 健康检查
app.get('/api/pdf/health', (req, res) => {
  res.json({ status: 'ok' });
});

const PORT = process.env.PORT || 3100;
app.listen(PORT, () => {
  console.log(`PDF Node 服务已启动，端口: ${PORT}`);
}); 