"""
统一错误处理工具
"""
import logging
import traceback
from functools import wraps
from flask import jsonify, current_app
from werkzeug.exceptions import HTTPException

logger = logging.getLogger(__name__)

class APIError(Exception):
    """API错误基类"""
    def __init__(self, message, status_code=400, payload=None):
        super().__init__()
        self.message = message
        self.status_code = status_code
        self.payload = payload

    def to_dict(self):
        rv = dict(self.payload or {})
        rv['message'] = self.message
        rv['error'] = True
        return rv

def handle_api_error(error):
    """处理API错误"""
    response = jsonify(error.to_dict())
    response.status_code = error.status_code
    return response

def api_error_handler(f):
    """API错误处理装饰器"""
    @wraps(f)
    def wrapped(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except APIError as e:
            logger.warning(f"API错误: {e.message}")
            return handle_api_error(e)
        except HTTPException as e:
            logger.warning(f"HTTP错误: {e.description}")
            return jsonify({
                'error': True,
                'message': e.description
            }), e.code
        except Exception as e:
            logger.error(f"未处理的错误: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({
                'error': True,
                'message': '服务器内部错误'
            }), 500
    return wrapped

class ValidationError(APIError):
    """验证错误"""
    def __init__(self, message):
        super().__init__(message, status_code=422)

class AuthorizationError(APIError):
    """授权错误"""
    def __init__(self, message):
        super().__init__(message, status_code=401)

class ResourceNotFoundError(APIError):
    """资源未找到错误"""
    def __init__(self, message):
        super().__init__(message, status_code=404)

class ResourceConflictError(APIError):
    """资源冲突错误"""
    def __init__(self, message):
        super().__init__(message, status_code=409)
