import os
import sys
import logging

# 将项目根目录添加到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app, db
from app.models import User, BusinessCard, BusinessCardTemplate
from app.utils.security_utils import generate_password_hash

# 配置日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def initialize_database():
    """
    一个可靠的数据库初始化脚本，使用Flask应用上下文来创建所有表。
    包含错误处理和回滚机制。
    """
    app = create_app('development')
    with app.app_context():
        try:
            logger.info("正在删除所有现有的表...")
            db.drop_all()
            logger.info("表删除完成。")
            
            logger.info("正在根据当前模型创建所有表...")
            db.create_all()
            logger.info("表创建完成。")
            
            # 创建默认管理员用户
            if not User.query.filter_by(username='admin').first():
                logger.info("正在创建默认管理员用户...")
                password = 'admin123456'
                password_hash, salt = generate_password_hash(password)
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=password_hash,
                    salt=salt,
                    is_admin=True,
                    is_active=True,
                    points=1000  # 给管理员初始积分
                )
                db.session.add(admin)
                logger.info("管理员用户创建完成。")
            
            logger.info("正在添加默认名片模板...")
            add_default_templates()
            
            db.session.commit()
            logger.info("数据库初始化成功完成。")
            
        except Exception as e:
            logger.error(f"数据库初始化过程中发生错误: {str(e)}")
            db.session.rollback()
            raise

def add_default_templates():
    """添加默认的名片模板数据"""
    try:
        # 检查是否已存在模板
        if BusinessCardTemplate.query.count() > 0:
            logger.info("已存在名片模板，跳过添加默认模板。")
            return
            
        templates = [
            BusinessCardTemplate(
                name='商务经典',
                description='简洁大方，适合各行各业',
                category='business',
                template_html='''
                <div class="business-card">
                    <div class="header">
                        <h1 class="name">{{name}}</h1>
                        <p class="title">{{title}}</p>
                    </div>
                    <div class="company">
                        <h2>{{company}}</h2>
                    </div>
                    <div class="contact">
                        <p class="phone">{{phone}}</p>
                        <p class="email">{{email}}</p>
                        <p class="address">{{address}}</p>
                    </div>
                </div>
                ''',
                template_css='''
                <style>
                .business-card {
                    width: 90mm;
                    height: 54mm;
                    padding: 20px;
                    background: {{colors.secondary}};
                    color: {{colors.text}};
                    font-family: Arial, sans-serif;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .header {
                    margin-bottom: 15px;
                }
                .name {
                    color: {{colors.primary}};
                    font-size: 24px;
                    margin: 0;
                }
                .title {
                    color: {{colors.text}};
                    font-size: 16px;
                    margin: 5px 0;
                }
                .company h2 {
                    color: {{colors.primary}};
                    font-size: 20px;
                    margin: 10px 0;
                }
                .contact {
                    margin-top: 15px;
                }
                .contact p {
                    margin: 5px 0;
                    font-size: 14px;
                }
                </style>
                ''',
                default_colors={'primary': '#003366', 'secondary': '#f5f5f5', 'text': '#333333'}
            ),
            BusinessCardTemplate(
                name='现代简约',
                description='极简风格，适合设计和创意行业',
                category='modern',
                template_html='''
                <div class="business-card">
                    <div class="content">
                        <div class="logo-area">{{company}}</div>
                        <div class="details">
                            <h1>{{name}}</h1>
                            <p class="title">{{title}}</p>
                            <div class="contact">
                                <p>{{phone}}</p>
                                <p>{{email}}</p>
                            </div>
                            <p class="address">{{address}}</p>
                        </div>
                    </div>
                </div>
                ''',
                template_css='''
                <style>
                .business-card {
                    width: 90mm;
                    height: 54mm;
                    background: {{colors.secondary}};
                    color: {{colors.text}};
                    font-family: 'Helvetica Neue', sans-serif;
                    position: relative;
                    overflow: hidden;
                }
                .content {
                    padding: 25px;
                }
                .logo-area {
                    color: {{colors.primary}};
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 20px;
                }
                .details h1 {
                    color: {{colors.primary}};
                    font-size: 24px;
                    font-weight: 300;
                    margin: 0;
                }
                .title {
                    font-size: 16px;
                    margin: 5px 0 15px;
                    color: {{colors.text}};
                }
                .contact {
                    margin: 15px 0;
                    font-size: 14px;
                }
                .address {
                    font-size: 14px;
                    color: {{colors.text}};
                }
                </style>
                ''',
                default_colors={'primary': '#1a1a1a', 'secondary': '#eeeeee', 'text': '#2c3e50'}
            ),
            BusinessCardTemplate(
                name='创意多彩',
                description='充满活力的设计，适合创意和艺术行业',
                category='creative',
                template_html='''
                <div class="business-card">
                    <div class="color-bar"></div>
                    <div class="content">
                        <h1 class="name">{{name}}</h1>
                        <p class="title">{{title}}</p>
                        <div class="company">{{company}}</div>
                        <div class="contact-info">
                            <p>{{phone}}</p>
                            <p>{{email}}</p>
                            <p>{{address}}</p>
                        </div>
                    </div>
                </div>
                ''',
                template_css='''
                <style>
                .business-card {
                    width: 90mm;
                    height: 54mm;
                    background: {{colors.secondary}};
                    color: {{colors.text}};
                    font-family: 'Roboto', sans-serif;
                    position: relative;
                    overflow: hidden;
                }
                .color-bar {
                    height: 8px;
                    background: {{colors.primary}};
                }
                .content {
                    padding: 20px;
                }
                .name {
                    color: {{colors.primary}};
                    font-size: 26px;
                    margin: 0;
                    font-weight: 500;
                }
                .title {
                    font-size: 16px;
                    color: {{colors.text}};
                    margin: 5px 0;
                }
                .company {
                    color: {{colors.primary}};
                    font-size: 18px;
                    margin: 15px 0;
                    font-weight: 500;
                }
                .contact-info {
                    margin-top: 15px;
                }
                .contact-info p {
                    margin: 3px 0;
                    font-size: 14px;
                }
                </style>
                ''',
                default_colors={'primary': '#FF4B4B', 'secondary': '#ffffff', 'text': '#333333'}
            )
        ]
        
        db.session.bulk_save_objects(templates)
        logger.info(f"已成功添加 {len(templates)} 个默认模板。")
        
    except Exception as e:
        logger.error(f"添加默认模板时发生错误: {str(e)}")
        raise

if __name__ == '__main__':
    initialize_database() 