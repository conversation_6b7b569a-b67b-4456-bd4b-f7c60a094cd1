from datetime import datetime
from app import db

class PaperSize(db.Model):
    """纸张规格模型"""
    __tablename__ = 'paper_sizes'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)  # 纸张名称
    width_mm = db.Column(db.Numeric(6, 2), nullable=False)  # 宽度(毫米)
    height_mm = db.Column(db.Numeric(6, 2), nullable=False)  # 高度(毫米)
    width_px = db.Column(db.Integer, nullable=False)  # 宽度(像素,300DPI)
    height_px = db.Column(db.Integer, nullable=False)  # 高度(像素,300DPI)
    margin_mm = db.Column(db.Numeric(4, 2), default=5.00)  # 边距(毫米)
    is_common = db.Column(db.<PERSON><PERSON><PERSON>, default=False, index=True)  # 是否常用
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True, index=True)  # 是否启用
    sort_order = db.Column(db.Integer, default=0, index=True)  # 排序
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(PaperSize, self).__init__(**kwargs)
    
    @property
    def size_display(self):
        """尺寸显示"""
        return f"{self.width_mm}mm × {self.height_mm}mm"
    
    @property
    def printable_width_mm(self):
        """可打印宽度(毫米)"""
        return float(self.width_mm) - (2 * float(self.margin_mm))
    
    @property
    def printable_height_mm(self):
        """可打印高度(毫米)"""
        return float(self.height_mm) - (2 * float(self.margin_mm))
    
    @property
    def printable_width_px(self):
        """可打印宽度(像素)"""
        return int((self.printable_width_mm / 25.4) * 300)
    
    @property
    def printable_height_px(self):
        """可打印高度(像素)"""
        return int((self.printable_height_mm / 25.4) * 300)
    
    def calculate_layout(self, photo_width_mm, photo_height_mm, spacing_mm=2, orientation='portrait'):
        """
        计算照片排版，并根据纸张方向智能调整。
        orientation: 'portrait' (纵向) 或 'landscape' (横向)
        """
        # 确定纸张的物理长短边
        db_width = self.printable_width_mm
        db_height = self.printable_height_mm
        long_edge = max(db_width, db_height)
        short_edge = min(db_width, db_height)

        # 根据用户选择的纸张方向（物理概念）来确定计算用的宽高
        if orientation == 'landscape':
            # 横向：长边是宽度，短边是高度
            available_width = long_edge
            available_height = short_edge
        else:
            # 纵向：短边是宽度，长边是高度
            available_width = short_edge
            available_height = long_edge
        
        photo_width_mm = float(photo_width_mm)
        photo_height_mm = float(photo_height_mm)
        spacing_mm = float(spacing_mm)

        # 采用更精确的公式计算可排列的行列数
        # (W + s) / (w + s) 是计算在给定间距下能容纳多少个元素的最优方法
        cols = int((available_width + spacing_mm) / (photo_width_mm + spacing_mm)) if photo_width_mm + spacing_mm > 0 else 0
        rows = int((available_height + spacing_mm) / (photo_height_mm + spacing_mm)) if photo_height_mm + spacing_mm > 0 else 0
        
        # 总照片数
        total_photos = cols * rows
        
        # 实际使用的空间
        used_width = cols * photo_width_mm + (cols - 1) * spacing_mm if cols > 1 else cols * photo_width_mm
        used_height = rows * photo_height_mm + (rows - 1) * spacing_mm if rows > 1 else rows * photo_height_mm
        
        # 居中偏移
        offset_x = (available_width - used_width) / 2
        offset_y = (available_height - used_height) / 2
        
        return {
            'rows': rows,
            'cols': cols,
            'total_photos': total_photos,
            'used_width_mm': round(used_width, 2),
            'used_height_mm': round(used_height, 2),
            'offset_x_mm': round(offset_x, 2),
            'offset_y_mm': round(offset_y, 2),
            'spacing_mm': spacing_mm
        }
    
    @classmethod
    def get_common_sizes(cls):
        """获取常用纸张"""
        return cls.query.filter_by(is_common=True).order_by(cls.sort_order).all()
    
    @classmethod
    def get_all_sizes(cls):
        """获取所有纸张"""
        return cls.query.order_by(cls.sort_order).all()
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'width_mm': float(self.width_mm),
            'height_mm': float(self.height_mm),
            'width_px': self.width_px,
            'height_px': self.height_px,
            'margin_mm': float(self.margin_mm),
            'printable_width_mm': self.printable_width_mm,
            'printable_height_mm': self.printable_height_mm,
            'printable_width_px': self.printable_width_px,
            'printable_height_px': self.printable_height_px,
            'is_common': self.is_common,
            'size_display': self.size_display,
            'sort_order': self.sort_order
        }
    
    def __repr__(self):
        return f'<PaperSize {self.name}: {self.size_display}>' 