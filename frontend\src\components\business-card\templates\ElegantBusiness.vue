<template>
  <div class="card-wrapper elegant-business" :style="styleVariables">
    <div class="gold-line"></div>
    <div class="main-content">
      <div class="identity">
        <h2 class="name" :style="nameStyle">{{ cardData.name || '姓名' }}</h2>
        <p class="title" v-if="cardData.title" :style="titleStyle">{{ cardData.title }}</p>
      </div>
      <div class="divider"></div>
      <div class="company-block">
        <span class="company-name" :style="companyStyle">{{ cardData.company || '公司名称' }}</span>
      </div>
      <div class="contact-info">
        <div class="contact-item" v-if="cardData.phone" :style="contactStyle">
          <span class="icon">📞</span>
          <span>{{ cardData.phone }}</span>
        </div>
        <div class="contact-item" v-if="cardData.email" :style="contactStyle">
          <span class="icon">✉️</span>
          <span>{{ cardData.email }}</span>
        </div>
        <div class="contact-item" v-if="cardData.address" :style="contactStyle">
          <span class="icon">📍</span>
          <span>{{ cardData.address }}</span>
        </div>
        <div class="contact-item" v-if="cardData.website" :style="contactStyle">
          <span class="icon">🌐</span>
          <span>{{ cardData.website }}</span>
        </div>
      </div>
    </div>
    <div class="corner-decoration"></div>
    <div class="qr-section" v-if="qrCodeUrl">
      <img :src="qrCodeUrl" alt="QR Code" class="qr-img" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
const props = defineProps({
  cardData: { type: Object, required: true },
  styleConfig: { type: Object, default: () => ({}) },
  qrCodeUrl: { type: String, default: '' }
})
const styleVariables = computed(() => {
  const config = props.styleConfig || {};
  const multiplier = (config.fontSize || 100) / 100;
  // 动态生成背景色，优先用primary
  const primary = config.primary || '#d4af37';
  const secondary = config.secondary || '#1a1a1a';
  // 可选：主色和辅色混合，或直接用primary
  const backgroundColor = config.backgroundColor || secondary;
  return {
    '--font-family': config.fontFamily || 'Times New Roman, serif',
    '--background-color': backgroundColor,
    '--primary-color': primary,
    '--secondary-color': secondary,
    '--text-color': config.text || '#ffffff',
    '--light-text-color': config.light || '#cccccc',
    '--gold-color': config.goldColor || primary,
    '--border-color': config.borderColor || '#444',
    '--font-size-name': `${90 * multiplier}px`,
    '--font-size-title': `${45 * multiplier}px`,
    '--font-size-company': `${55 * multiplier}px`,
    '--font-size-contact': `${40 * multiplier}px`,
    '--name-color': config.nameColor || 'var(--gold-color)',
    '--company-color': config.companyColor || 'var(--text-color)',
    '--contact-color': config.contactColor || 'var(--light-text-color)',
    '--name-weight': config.nameFontStyle === 'bold' ? 'bold' : 'normal',
    '--company-weight': config.companyFontStyle === 'bold' ? 'bold' : 'normal',
    '--contact-weight': config.contactFontStyle === 'bold' ? 'bold' : 'normal',
    '--divider-color': config.dividerColor || 'rgba(255,255,255,0.12)',
  };
});
const nameStyle = computed(() => ({
  color: props.styleConfig.nameColor || 'var(--gold-color)',
  fontWeight: props.styleConfig.nameFontStyle === 'bold' ? 'bold' : 'normal',
  fontStyle: props.styleConfig.nameFontStyle === 'italic' ? 'italic' : 'normal',
}));
const companyStyle = computed(() => ({
  color: props.styleConfig.companyColor || 'var(--text-color)',
  fontWeight: props.styleConfig.companyFontStyle === 'bold' ? 'bold' : 'normal',
  fontStyle: props.styleConfig.companyFontStyle === 'italic' ? 'italic' : 'normal',
}));
const titleStyle = computed(() => ({
  color: props.styleConfig.text || 'var(--light-text-color)',
  fontWeight: props.styleConfig.titleFontStyle === 'bold' ? 'bold' : 'normal',
  fontStyle: props.styleConfig.titleFontStyle === 'italic' ? 'italic' : 'normal',
}));
const contactStyle = computed(() => ({
  color: props.styleConfig.light || 'var(--light-text-color)',
  fontWeight: props.styleConfig.contactFontStyle === 'bold' ? 'bold' : 'normal',
  fontStyle: props.styleConfig.contactFontStyle === 'italic' ? 'italic' : 'normal',
}));
</script>

<style scoped>
.card-wrapper.elegant-business {
  width: 100%;
  height: 100%;
  background: var(--background-color);
  color: var(--text-color);
  font-family: var(--font-family);
  box-sizing: border-box;
  position: relative;
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.gold-line {
  position: absolute;
  top: 0;
  left: 8mm;
  right: 8mm;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--gold-color), transparent);
}
.main-content {
  position: relative;
  z-index: 2;
  width: 90%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  margin-left: 32px;
}
.identity .name {
  font-size: var(--font-size-name);
  font-weight: var(--name-weight);
  color: var(--name-color);
  margin: 0 0 8px 0;
}
.identity .title {
  font-size: var(--font-size-title);
  color: var(--text-color);
  margin: 0 0 12px 0;
  opacity: 0.9;
}
.divider {
  width: 100%;
  height: 1px;
  background-color: var(--divider-color);
  margin: 16px 0;
  opacity: 0.7;
}
.company-block {
  margin-bottom: 12px;
}
.company-name {
  font-size: var(--font-size-company);
  color: var(--company-color);
  font-weight: var(--company-weight);
}
.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 32px;
}
.contact-item {
  display: flex;
  align-items: center;
  font-size: var(--font-size-contact);
  color: var(--contact-color);
  font-weight: var(--contact-weight);
}
.contact-item .icon {
  margin-right: 8px;
  font-size: 1.1em;
}
.corner-decoration {
  position: absolute;
  bottom: 16px;
  right: 24px;
  width: 32px;
  height: 32px;
  border: 1.5px solid var(--gold-color);
  border-radius: 50%;
}
.corner-decoration::after {
  content: "";
  position: absolute;
  top: 8px;
  left: 8px;
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, var(--gold-color) 30%, transparent 30%);
  border-radius: 50%;
}
.qr-section {
  position: absolute;
  bottom: 6mm;
  right: 6mm;
  width: 16mm;
  height: 16mm;
  background: white;
  padding: 1mm;
  border-radius: 2mm;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qr-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}
</style> 