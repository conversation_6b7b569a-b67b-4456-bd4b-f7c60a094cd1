from flask import jsonify, request
from werkzeug.http import HTTP_STATUS_CODES
from werkzeug.exceptions import HTTPException

def success_response(message="Success", data=None, status_code=200):
    """生成成功响应"""
    payload = {
        'success': True,
        'message': message
    }
    if data is not None:
        payload['data'] = data

    response = jsonify(payload)
    response.status_code = status_code
    return response

def error_response(status_code, message=None):
    """生成错误响应"""
    payload = {'error': HTTP_STATUS_CODES.get(status_code, 'Unknown error')}
    if message:
        payload['message'] = message
    
    response = jsonify(payload)
    response.status_code = status_code
    return response

def bad_request(message):
    """400错误处理"""
    return jsonify({'error': 'Bad Request', 'message': message}), 400

def unauthorized(message):
    """401错误处理"""
    return jsonify({'error': 'Unauthorized', 'message': message}), 401

def forbidden(message):
    """403错误处理"""
    return jsonify({'error': 'Forbidden', 'message': message}), 403

def not_found(message):
    """404错误处理"""
    return jsonify({'error': 'Not Found', 'message': message}), 404

def method_not_allowed(message="Method not allowed"):
    """405错误"""
    return error_response(405, message)

def conflict(message="Resource conflict"):
    """409错误"""
    return error_response(409, message)

def validation_error(message):
    """422错误处理"""
    return jsonify({'error': 'Validation Error', 'message': message}), 422

def internal_server_error(message):
    """500错误处理"""
    return jsonify({'error': 'Internal Server Error', 'message': message}), 500

def register_error_handlers(bp):
    """注册错误处理器"""
    
    @bp.errorhandler(400)
    def bad_request_handler(error):
        return bad_request("Bad request")
    
    @bp.errorhandler(401)
    def unauthorized_handler(error):
        return unauthorized()
    
    @bp.errorhandler(403)
    def forbidden_handler(error):
        return forbidden()
    
    @bp.errorhandler(404)
    def not_found_handler(error):
        return not_found()
    
    @bp.errorhandler(405)
    def method_not_allowed_handler(error):
        return method_not_allowed()
    
    @bp.errorhandler(409)
    def conflict_handler(error):
        return conflict()
    
    @bp.errorhandler(422)
    def validation_error_handler(error):
        return validation_error()
    
    @bp.errorhandler(500)
    def internal_error_handler(error):
        return internal_server_error()
    
    @bp.app_errorhandler(Exception)
    def handle_exception(error):
        """处理所有未捕获的异常"""
        from flask import current_app
        
        # 如果是HTTP异常（如404、403等），不要处理，让默认处理器处理
        if isinstance(error, HTTPException):
            return error
        
        # 记录错误日志
        current_app.logger.error(f"Unhandled exception: {error}")
        
        # 开发环境下返回详细错误信息
        if current_app.debug:
            return error_response(500, str(error))
        
        return internal_server_error() 