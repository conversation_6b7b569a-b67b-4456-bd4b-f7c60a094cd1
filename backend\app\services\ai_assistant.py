import os
import re
import json
import logging
import requests
from typing import List, Dict, Optional, Any
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class AIPromptTemplate:
    """AI提示词模板"""
    type: str
    template: str
    examples: List[str]

class AIAssistant:
    """
    AI辅助写作服务 - 按照me.md文档要求实现智能内容生成
    支持工作描述、项目描述、技能描述等多种内容的AI辅助生成
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 从环境变量获取AI服务配置
        self.api_key = os.getenv('AI_API_KEY', '')
        self.api_base_url = os.getenv('AI_API_BASE_URL', '')
        self.model_name = os.getenv('AI_MODEL_NAME', 'gpt-3.5-turbo')
        
        # 预定义的提示词模板
        self.prompt_templates = {
            'work_description': AIPromptTemplate(
                type='work_description',
                template="""请根据以下信息生成专业的工作描述，要求：
1. 使用动作词开头（如：负责、开发、设计、管理等）
2. 包含具体的工作内容和成果
3. 尽量量化成果（如：提升了X%、完成了X个项目等）
4. 符合简历格式，每条描述在30-50字之间
5. 生成3-5条工作描述

职位：{position}
公司：{company}
行业：{industry}
工作内容关键词：{keywords}

请生成专业的工作描述：""",
                examples=[
                    "负责公司核心产品的前端开发，使用Vue.js构建了5个业务模块，提升用户体验30%",
                    "设计并实现了用户管理系统，支持10万+用户并发访问，系统稳定性达99.9%",
                    "协调跨部门团队完成产品迭代，按时交付率100%，获得客户满意度95%以上"
                ]
            ),
            'project_description': AIPromptTemplate(
                type='project_description',
                template="""请根据以下项目信息生成专业的项目描述，要求：
1. 突出项目的技术难点和创新点
2. 说明你在项目中的具体贡献
3. 量化项目成果和影响
4. 符合简历格式，描述清晰简洁
5. 生成2-4条项目描述

项目名称：{project_name}
技术栈：{technologies}
你的角色：{role}
项目规模：{scale}
关键词：{keywords}

请生成专业的项目描述：""",
                examples=[
                    "基于Spring Boot构建的电商平台，日处理订单量10万+，实现了分布式缓存和消息队列",
                    "负责核心业务模块的架构设计，采用微服务架构，提升系统可扩展性200%",
                    "优化数据库查询性能，通过索引优化和SQL调优，查询速度提升80%"
                ]
            ),
            'skill_description': AIPromptTemplate(
                type='skill_description',
                template="""请根据以下技能信息生成专业的技能描述，要求：
1. 说明技能的熟练程度和应用场景
2. 突出相关的项目经验
3. 体现技能的深度和广度
4. 简洁明了，重点突出

技能名称：{skill_name}
使用年限：{years}
应用场景：{scenarios}
相关项目：{projects}

请生成专业的技能描述：""",
                examples=[
                    "精通Python开发，3年+实战经验，熟练使用Django/Flask框架开发Web应用",
                    "熟练掌握前端技术栈，包括Vue.js、React等，完成多个大型SPA项目开发",
                    "具备丰富的数据库设计经验，熟练使用MySQL、PostgreSQL，擅长性能优化"
                ]
            ),
            'summary_optimization': AIPromptTemplate(
                type='summary_optimization',
                template="""请优化以下个人总结，要求：
1. 突出核心竞争力和专业优势
2. 体现工作经验和技术能力
3. 展现职业规划和发展潜力
4. 语言简洁有力，逻辑清晰
5. 控制在100-200字之间

当前总结：{current_summary}
工作年限：{years_experience}
主要技能：{key_skills}
目标职位：{target_position}

请生成优化后的个人总结：""",
                examples=[
                    "具备5年+软件开发经验的全栈工程师，精通Java/Python后端开发和Vue.js前端技术，主导过多个大型项目的架构设计，具有丰富的团队管理经验，致力于技术创新和产品优化。",
                    "资深产品经理，8年互联网产品经验，成功推出3款用户量百万+的产品，擅长用户需求分析和产品策略制定，具备优秀的跨部门协作能力，专注于AI和大数据领域的产品创新。"
                ]
            )
        }
    
    def generate_work_description(self, position: str, company: str = '', 
                                industry: str = '', keywords: str = '') -> List[str]:
        """
        生成工作描述
        
        Args:
            position: 职位名称
            company: 公司名称
            industry: 行业
            keywords: 工作内容关键词
            
        Returns:
            List[str]: 生成的工作描述列表
        """
        try:
            template = self.prompt_templates['work_description']
            prompt = template.template.format(
                position=position,
                company=company,
                industry=industry,
                keywords=keywords
            )
            
            response = self._call_ai_api(prompt)
            descriptions = self._parse_ai_response(response, 'work_description')
            
            self.logger.info(f"生成工作描述完成，职位: {position}")
            return descriptions
            
        except Exception as e:
            self.logger.error(f"生成工作描述失败: {str(e)}")
            # 返回默认描述
            return self._get_default_work_descriptions(position)
    
    def generate_project_description(self, project_name: str, technologies: str = '',
                                   role: str = '', scale: str = '', keywords: str = '') -> List[str]:
        """
        生成项目描述
        
        Args:
            project_name: 项目名称
            technologies: 技术栈
            role: 担任角色
            scale: 项目规模
            keywords: 关键词
            
        Returns:
            List[str]: 生成的项目描述列表
        """
        try:
            template = self.prompt_templates['project_description']
            prompt = template.template.format(
                project_name=project_name,
                technologies=technologies,
                role=role,
                scale=scale,
                keywords=keywords
            )
            
            response = self._call_ai_api(prompt)
            descriptions = self._parse_ai_response(response, 'project_description')
            
            self.logger.info(f"生成项目描述完成，项目: {project_name}")
            return descriptions
            
        except Exception as e:
            self.logger.error(f"生成项目描述失败: {str(e)}")
            return self._get_default_project_descriptions(project_name)
    
    def generate_skill_description(self, skill_name: str, years: str = '',
                                 scenarios: str = '', projects: str = '') -> str:
        """
        生成技能描述
        
        Args:
            skill_name: 技能名称
            years: 使用年限
            scenarios: 应用场景
            projects: 相关项目
            
        Returns:
            str: 生成的技能描述
        """
        try:
            template = self.prompt_templates['skill_description']
            prompt = template.template.format(
                skill_name=skill_name,
                years=years,
                scenarios=scenarios,
                projects=projects
            )
            
            response = self._call_ai_api(prompt)
            description = self._parse_ai_response(response, 'skill_description')[0]
            
            self.logger.info(f"生成技能描述完成，技能: {skill_name}")
            return description
            
        except Exception as e:
            self.logger.error(f"生成技能描述失败: {str(e)}")
            return f"熟练掌握{skill_name}，具备丰富的实践经验"
    
    def generate_resume_summary(self, current_summary: str, years_experience: str = '',
                        key_skills: str = '', target_position: str = '') -> str:
        """
        优化个人总结
        
        Args:
            current_summary: 当前总结
            years_experience: 工作年限
            key_skills: 主要技能
            target_position: 目标职位
            
        Returns:
            str: 优化后的个人总结
        """
        try:
            template = self.prompt_templates['summary_optimization']
            prompt = template.template.format(
                current_summary=current_summary,
                years_experience=years_experience,
                key_skills=key_skills,
                target_position=target_position
            )
            
            response = self._call_ai_api(prompt)
            optimized_summary = self._parse_ai_response(response, 'summary_optimization')[0]
            
            self.logger.info("个人总结优化完成")
            return optimized_summary
            
        except Exception as e:
            self.logger.error(f"优化个人总结失败: {str(e)}")
            return current_summary if current_summary else "具备丰富的专业经验和技术能力，致力于在相关领域做出贡献。"
    
    def _call_ai_api(self, prompt: str) -> str:
        """
        调用AI API
        
        Args:
            prompt: 提示词
            
        Returns:
            str: AI响应内容
        """
        if not self.api_key or not self.api_base_url:
            raise ValueError("AI API配置不完整，请检查环境变量")
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'model': self.model_name,
            'messages': [
                {
                    'role': 'system',
                    'content': '你是一个专业的简历写作助手，专门帮助用户优化简历内容。请根据用户提供的信息生成专业、准确、有吸引力的简历内容。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'temperature': 0.7,
            'max_tokens': 1000
        }
        
        try:
            response = requests.post(
                f"{self.api_base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                self.logger.error(f"AI API调用失败: {response.status_code}, {response.text}")
                raise ValueError(f"AI API调用失败: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.logger.error(f"AI API请求异常: {str(e)}")
            raise ValueError(f"AI API请求失败: {str(e)}")
    
    def _parse_ai_response(self, response: str, response_type: str) -> List[str]:
        """
        解析AI响应内容
        
        Args:
            response: AI响应
            response_type: 响应类型
            
        Returns:
            List[str]: 解析后的内容列表
        """
        try:
            # 清理响应内容
            response = response.strip()
            
            # 按行分割
            lines = [line.strip() for line in response.split('\n') if line.strip()]
            
            # 过滤和清理内容
            cleaned_lines = []
            for line in lines:
                # 移除编号前缀
                line = re.sub(r'^\d+[\.\)]\s*', '', line)
                # 移除其他前缀符号
                line = re.sub(r'^[-•*]\s*', '', line)
                
                if line and len(line) > 10:  # 过滤太短的内容
                    cleaned_lines.append(line)
            
            return cleaned_lines if cleaned_lines else [response]
            
        except Exception as e:
            self.logger.error(f"解析AI响应失败: {str(e)}")
            return [response]
    
    def _get_default_work_descriptions(self, position: str) -> List[str]:
        """获取默认工作描述"""
        default_descriptions = {
            '软件工程师': [
                "负责公司核心产品的开发和维护，确保系统稳定运行",
                "参与系统架构设计，优化代码结构，提升开发效率",
                "协作完成需求分析和技术方案制定，按时交付高质量产品"
            ],
            '产品经理': [
                "负责产品需求分析和产品规划，制定产品发展策略",
                "协调各部门资源，推动产品按时上线，提升用户满意度",
                "分析用户反馈和市场数据，持续优化产品功能"
            ],
            '项目经理': [
                "负责项目整体规划和进度管理，确保项目按时交付",
                "协调跨部门团队协作，有效配置资源，控制项目风险",
                "建立项目管理体系，提升团队执行效率"
            ]
        }
        
        # 模糊匹配职位
        for key, descriptions in default_descriptions.items():
            if key in position or position in key:
                return descriptions
        
        # 通用默认描述
        return [
            f"负责{position}相关工作，具备丰富的专业经验",
            "参与团队协作，完成各项工作任务，获得良好评价",
            "持续学习新技术，不断提升专业能力和工作效率"
        ]
    
    def _get_default_project_descriptions(self, project_name: str) -> List[str]:
        """获取默认项目描述"""
        return [
            f"参与{project_name}项目的设计和开发工作",
            "负责项目核心模块的实现，确保功能正常运行",
            "与团队成员协作，按时完成项目交付"
        ]
    
    def get_industry_keywords(self, industry: str) -> List[str]:
        """
        获取行业相关关键词
        
        Args:
            industry: 行业名称
            
        Returns:
            List[str]: 行业关键词列表
        """
        industry_keywords = {
            'it': ['技术', '开发', '系统', '产品', '用户体验', '性能优化', '架构设计'],
            'finance': ['风险管控', '合规', '资产管理', '投资', '财务分析', '成本控制'],
            'education': ['教学', '课程设计', '学生管理', '教育质量', '教学创新'],
            'marketing': ['品牌推广', '市场分析', '用户增长', 'ROI', '转化率', '客户获取'],
            'sales': ['销售业绩', '客户关系', '市场开拓', '团队管理', '目标达成'],
            'design': ['用户体验', '视觉设计', '创意策划', '设计规范', '品牌形象']
        }
        
        return industry_keywords.get(industry.lower(), ['专业', '高效', '创新', '协作', '质量']) 