#!/usr/bin/env python3
"""
重置管理员密码
"""
import os
import sys

# 添加backend目录到Python路径
backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
sys.path.insert(0, backend_dir)

# 设置工作目录到backend
os.chdir(backend_dir)

try:
    from app import create_app, db
    from app.models.user import User
    
    def reset_admin_password():
        """重置管理员密码"""
        app = create_app('development')
        
        with app.app_context():
            # 查找管理员用户
            admin_user = User.query.filter_by(username='admin').first()
            
            if not admin_user:
                print("❌ 管理员用户不存在")
                return False
            
            print(f"✅ 找到管理员用户: {admin_user.username}")
            print(f"   - 邮箱: {admin_user.email}")
            print(f"   - 状态: {admin_user.status}")
            print(f"   - 是否激活: {admin_user.is_active}")
            print(f"   - 是否管理员: {admin_user.is_admin}")
            
            # 重新设置密码为admin123
            print("\n🔧 重新设置管理员密码为 'admin123'...")
            admin_user.set_password('admin123')
            
            # 确保用户状态正确
            admin_user.status = 1
            admin_user.is_active = True
            admin_user.is_admin = True
            
            db.session.commit()
            
            # 验证密码
            if admin_user.check_password('admin123'):
                print("✅ 密码重置成功！现在可以使用 admin/admin123 登录")
                return True
            else:
                print("❌ 密码重置失败")
                return False
    
    if __name__ == '__main__':
        reset_admin_password()
        
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
