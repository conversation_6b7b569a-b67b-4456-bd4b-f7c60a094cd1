"""
文件处理工具
"""
import os
import uuid
import logging
import mimetypes
from typing import Op<PERSON>, Tuple
from werkzeug.utils import secure_filename
from PIL import Image
from flask import current_app
from .error_handler import APIError

logger = logging.getLogger(__name__)

class FileManager:
    """文件管理器"""
    
    ALLOWED_IMAGE_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}
    ALLOWED_DOCUMENT_EXTENSIONS = {'pdf', 'doc', 'docx', 'txt'}
    MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB
    MAX_DOCUMENT_SIZE = 20 * 1024 * 1024  # 20MB

    @staticmethod
    def get_file_extension(filename: str) -> str:
        """获取文件扩展名"""
        return filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

    @staticmethod
    def is_allowed_image(filename: str) -> bool:
        """检查是否为允许的图片类型"""
        return FileManager.get_file_extension(filename) in FileManager.ALLOWED_IMAGE_EXTENSIONS

    @staticmethod
    def is_allowed_document(filename: str) -> bool:
        """检查是否为允许的文档类型"""
        return FileManager.get_file_extension(filename) in FileManager.ALLOWED_DOCUMENT_EXTENSIONS

    @staticmethod
    def save_file(file, upload_folder: str, allowed_extensions: set, max_size: int) -> Tuple[str, str]:
        """
        保存文件并返回文件路径和URL
        :param file: 文件对象
        :param upload_folder: 上传目录
        :param allowed_extensions: 允许的扩展名集合
        :param max_size: 最大文件大小
        :return: (文件路径, URL)
        """
        if not file:
            raise APIError('没有文件被上传')

        filename = secure_filename(file.filename)
        if not filename:
            raise APIError('无效的文件名')

        # 检查文件大小
        file.seek(0, os.SEEK_END)
        size = file.tell()
        file.seek(0)
        if size > max_size:
            raise APIError(f'文件大小超过限制 ({max_size/1024/1024:.1f}MB)')

        # 检查文件类型
        extension = FileManager.get_file_extension(filename)
        if extension not in allowed_extensions:
            raise APIError(f'不支持的文件类型 {extension}')

        # 生成唯一文件名
        unique_filename = f"{str(uuid.uuid4())}.{extension}"
        file_path = os.path.join(upload_folder, unique_filename)

        try:
            # 确保上传目录存在
            os.makedirs(upload_folder, exist_ok=True)
            file.save(file_path)
            return file_path, f"/uploads/{unique_filename}"
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            raise APIError('文件保存失败')

    @staticmethod
    def create_thumbnail(image_path: str, thumb_size: Tuple[int, int] = (200, 200)) -> Optional[str]:
        """
        为图片创建缩略图
        :param image_path: 原图路径
        :param thumb_size: 缩略图尺寸
        :return: 缩略图路径
        """
        try:
            # 生成缩略图路径
            directory = os.path.dirname(image_path)
            filename = os.path.basename(image_path)
            thumb_path = os.path.join(directory, f"thumb_{filename}")

            # 创建缩略图
            with Image.open(image_path) as img:
                img.thumbnail(thumb_size)
                img.save(thumb_path, quality=85, optimize=True)

            return thumb_path
        except Exception as e:
            logger.error(f"创建缩略图失败: {str(e)}")
            return None

    @staticmethod
    def safe_delete_file(file_path: str) -> bool:
        """
        安全删除文件
        :param file_path: 文件路径
        :return: 是否删除成功
        """
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                return True
        except Exception as e:
            logger.error(f"删除文件失败: {str(e)}")
        return False

    @staticmethod
    def safe_delete_files(file_paths: list) -> dict:
        """
        批量安全删除文件
        :param file_paths: 文件路径列表
        :return: 删除结果统计
        """
        results = {'success': 0, 'failed': 0}
        for path in file_paths:
            if FileManager.safe_delete_file(path):
                results['success'] += 1
            else:
                results['failed'] += 1
        return results
