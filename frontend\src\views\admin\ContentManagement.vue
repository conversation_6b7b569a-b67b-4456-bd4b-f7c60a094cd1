<template>
  <div class="content-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">内容管理</h2>
        <p class="page-description">管理系统中的名片、简历、文档、图片等用户生成的内容</p>
      </div>
    </div>

    <!-- 内容统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📇</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.business_cards?.total || 0 }}</div>
                <div class="stat-label">名片总数</div>
                <div class="stat-sub">今日: {{ stats.business_cards?.today || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📄</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.resumes?.total || 0 }}</div>
                <div class="stat-label">简历总数</div>
                <div class="stat-sub">今日: {{ stats.resumes?.today || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📁</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.documents?.total || 0 }}</div>
                <div class="stat-label">文档总数</div>
                <div class="stat-sub">今日: {{ stats.documents?.today || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">🖼️</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.images?.total || 0 }}</div>
                <div class="stat-label">图片总数</div>
                <div class="stat-sub">今日: {{ stats.images?.today || 0 }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px;">
        <el-col :span="12">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">💾</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.storage?.total_size_mb || 0 }} MB</div>
                <div class="stat-label">总存储空间</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-value">{{ totalContent }}</div>
                <div class="stat-label">内容总数</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 内容类型选项卡 -->
    <el-tabs v-model="activeTab" type="border-card" class="content-tabs">
      <!-- 名片管理 -->
      <el-tab-pane label="名片管理" name="business-cards">
        <div class="tab-content">
          <div class="content-header">
            <div class="header-left">
              <el-input
                v-model="businessCardFilters.keyword"
                placeholder="搜索名片标题或用户"
                style="width: 300px"
                clearable
                @input="loadBusinessCards"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="header-right">
              <el-button @click="exportBusinessCards">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <div class="batch-actions" v-if="selectedBusinessCards.length > 0" style="margin-bottom: 15px;">
            <el-button type="danger" @click="batchDeleteBusinessCards">
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedBusinessCards.length }})
            </el-button>
          </div>
          <el-table
            v-loading="businessCardsLoading"
            :data="businessCards"
            style="width: 100%"
            @selection-change="handleBusinessCardSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column label="用户信息" width="150">
              <template #default="{ row }">
                <div class="user-info">
                  <span class="username">{{ row.user?.username }}</span>
                  <span class="user-id">(ID: {{ row.user_id }})</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="名片标题" min-width="150" />
            <el-table-column prop="template_name" label="模板" width="120" />
            <el-table-column label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="previewBusinessCard(row)">预览</el-button>
                <el-button size="small" type="danger" @click="deleteBusinessCard(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="businessCardPagination.page"
              v-model:page-size="businessCardPagination.per_page"
              :page-sizes="[20, 50, 100]"
              :total="businessCardPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleBusinessCardSizeChange"
              @current-change="handleBusinessCardCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 简历管理 -->
      <el-tab-pane label="简历管理" name="resumes">
        <div class="tab-content">
          <div class="content-header">
            <div class="header-left">
              <el-input
                v-model="resumeFilters.keyword"
                placeholder="搜索简历标题或用户"
                style="width: 300px"
                clearable
                @input="loadResumes"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="header-right">
              <el-button @click="exportResumes">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <div class="batch-actions" v-if="selectedResumes.length > 0" style="margin-bottom: 15px;">
            <el-button type="danger" @click="batchDeleteResumes">
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedResumes.length }})
            </el-button>
          </div>
          <el-table
            v-loading="resumesLoading"
            :data="resumes"
            style="width: 100%"
            @selection-change="handleResumeSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column label="用户信息" width="150">
              <template #default="{ row }">
                <div class="user-info">
                  <span class="username">{{ row.user?.username }}</span>
                  <span class="user-id">(ID: {{ row.user_id }})</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="简历标题" min-width="150" />
            <el-table-column prop="full_name" label="姓名" width="120" />
            <el-table-column prop="template_name" label="模板" width="120" />
            <el-table-column label="创建时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="previewResume(row)">预览</el-button>
                <el-button size="small" type="danger" @click="deleteResume(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="resumePagination.page"
              v-model:page-size="resumePagination.per_page"
              :page-sizes="[20, 50, 100]"
              :total="resumePagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleResumeSizeChange"
              @current-change="handleResumeCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 文档管理 -->
      <el-tab-pane label="文档管理" name="documents">
        <div class="tab-content">
          <div class="content-header">
            <div class="header-left">
              <el-input
                v-model="documentFilters.keyword"
                placeholder="搜索文档名称或用户"
                style="width: 300px"
                clearable
                @input="loadDocuments"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="header-right">
              <el-button @click="exportDocuments">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <div class="batch-actions" v-if="selectedDocuments.length > 0" style="margin-bottom: 15px;">
            <el-button type="danger" @click="batchDeleteDocuments">
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedDocuments.length }})
            </el-button>
          </div>
          <el-table
            v-loading="documentsLoading"
            :data="documents"
            style="width: 100%"
            @selection-change="handleDocumentSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column label="用户信息" width="150">
              <template #default="{ row }">
                <div class="user-info">
                  <span class="username">{{ row.user?.username }}</span>
                  <span class="user-id">(ID: {{ row.user_id }})</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="filename" label="文件名" min-width="200" />
            <el-table-column prop="file_type" label="文件类型" width="100" />
            <el-table-column prop="file_size" label="文件大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.file_size) }}
              </template>
            </el-table-column>
            <el-table-column label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="downloadDocument(row)">下载</el-button>
                <el-button size="small" type="danger" @click="deleteDocument(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="documentPagination.page"
              v-model:page-size="documentPagination.per_page"
              :page-sizes="[20, 50, 100]"
              :total="documentPagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleDocumentSizeChange"
              @current-change="handleDocumentCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 图片管理 -->
      <el-tab-pane label="图片管理" name="images">
        <div class="tab-content">
          <div class="content-header">
            <div class="header-left">
              <el-input
                v-model="imageFilters.keyword"
                placeholder="搜索图片名称或用户"
                style="width: 300px"
                clearable
                @input="loadImages"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="header-right">
              <el-button @click="exportImages">
                <el-icon><Download /></el-icon>
                导出数据
              </el-button>
            </div>
          </div>

          <div class="batch-actions" v-if="selectedImages.length > 0" style="margin-bottom: 15px;">
            <el-button type="danger" @click="batchDeleteImages">
              <el-icon><Delete /></el-icon>
              批量删除 ({{ selectedImages.length }})
            </el-button>
          </div>
          <el-table
            v-loading="imagesLoading"
            :data="images"
            style="width: 100%"
            @selection-change="handleImageSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" />
            <el-table-column label="用户信息" width="150">
              <template #default="{ row }">
                <div class="user-info">
                  <span class="username">{{ row.user?.username }}</span>
                  <span class="user-id">(ID: {{ row.user_id }})</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="filename" label="文件名" min-width="200" />
            <el-table-column prop="file_type" label="文件类型" width="100" />
            <el-table-column prop="file_size" label="文件大小" width="100">
              <template #default="{ row }">
                {{ formatFileSize(row.file_size) }}
              </template>
            </el-table-column>
            <el-table-column label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="previewImage(row)">预览</el-button>
                <el-button size="small" @click="downloadImage(row)">下载</el-button>
                <el-button size="small" type="danger" @click="deleteImage(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="imagePagination.page"
              v-model:page-size="imagePagination.per_page"
              :page-sizes="[20, 50, 100]"
              :total="imagePagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleImageSizeChange"
              @current-change="handleImageCurrentChange"
            />
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 预览对话框 -->
    <el-dialog
      v-model="showPreview"
      title="内容预览"
      width="800px"
      :before-close="closePreview"
    >
      <div v-if="previewContent" class="preview-content">
        <!-- 根据内容类型显示不同的预览 -->
        <div v-if="previewType === 'business-card'" class="business-card-preview">
          <h3>{{ previewContent.title }}</h3>
          <div class="preview-details">
            <p><strong>用户:</strong> {{ previewContent.user?.username }}</p>
            <p><strong>模板:</strong> {{ previewContent.template_name }}</p>
            <p><strong>状态:</strong>
              <el-tag :type="previewContent.status === 'published' ? 'success' : 'warning'">
                {{ previewContent.status === 'published' ? '已发布' : '草稿' }}
              </el-tag>
            </p>
            <p><strong>创建时间:</strong> {{ formatDate(previewContent.created_at) }}</p>
            <p><strong>更新时间:</strong> {{ formatDate(previewContent.updated_at) }}</p>
          </div>
          <div v-if="previewContent.card_data">
            <h4>名片内容</h4>
            <pre>{{ JSON.stringify(previewContent.card_data, null, 2) }}</pre>
          </div>
          <div v-if="previewContent.style_config">
            <h4>样式配置</h4>
            <pre>{{ JSON.stringify(previewContent.style_config, null, 2) }}</pre>
          </div>
        </div>
        
        <div v-else-if="previewType === 'resume'" class="resume-preview">
          <h3>{{ previewContent.title }}</h3>
          <div class="preview-details">
            <p><strong>姓名:</strong> {{ previewContent.full_name }}</p>
            <p><strong>用户:</strong> {{ previewContent.user?.username }}</p>
            <p><strong>模板:</strong> {{ previewContent.template_name }}</p>
            <p><strong>状态:</strong>
              <el-tag :type="previewContent.status === 'published' ? 'success' : 'warning'">
                {{ previewContent.status === 'published' ? '已发布' : '草稿' }}
              </el-tag>
            </p>
            <p><strong>创建时间:</strong> {{ formatDate(previewContent.created_at) }}</p>
            <p><strong>更新时间:</strong> {{ formatDate(previewContent.updated_at) }}</p>
          </div>
          <div v-if="previewContent.summary">
            <h4>个人总结</h4>
            <p>{{ previewContent.summary }}</p>
          </div>
          <div v-if="previewContent.objective">
            <h4>求职意向</h4>
            <p>{{ previewContent.objective }}</p>
          </div>
        </div>
        
        <div v-else-if="previewType === 'image'" class="image-preview">
          <div class="preview-details">
            <p><strong>文件名:</strong> {{ previewContent.filename }}</p>
            <p><strong>文件大小:</strong> {{ formatFileSize(previewContent.file_size) }}</p>
            <p><strong>文件类型:</strong> {{ previewContent.file_type }}</p>
            <p><strong>图片尺寸:</strong> {{ previewContent.width }}x{{ previewContent.height }}</p>
            <p><strong>上传来源:</strong> {{ previewContent.upload_source }}</p>
            <p><strong>状态:</strong>
              <el-tag :type="previewContent.status === 1 ? 'success' : 'danger'">
                {{ previewContent.status === 1 ? '正常' : '已删除' }}
              </el-tag>
            </p>
            <p><strong>上传时间:</strong> {{ formatDate(previewContent.created_at) }}</p>
          </div>
          <div class="image-preview-container">
            <img v-if="previewContent.file_path" :src="previewContent.file_path.startsWith('http') ? previewContent.file_path : `http://localhost:5000/static/${previewContent.file_path.replace(/\\/g, '/')}`" alt="图片预览" style="max-width: 100%; height: auto; border-radius: 8px;"
              @error="imageLoadError = true" v-show="!imageLoadError" />
            <div v-if="imageLoadError" style="color: #e74c3c; padding: 20px; text-align: center;">图片无法显示，请检查文件是否存在或路径是否正确。</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Download } from '@element-plus/icons-vue'
import api from '@/api'

// 响应式数据
const activeTab = ref('business-cards')
const showPreview = ref(false)
const previewContent = ref(null)
const previewType = ref('')
const imageLoadError = ref(false)

// 统计数据
const stats = reactive({
  business_cards: { total: 0, today: 0 },
  resumes: { total: 0, today: 0 },
  documents: { total: 0, today: 0 },
  images: { total: 0, today: 0 },
  storage: { total_size_mb: 0 }
})

// 计算属性
const totalContent = computed(() => {
  return (stats.business_cards.total || 0) +
         (stats.resumes.total || 0) +
         (stats.documents.total || 0) +
         (stats.images.total || 0)
})

// 加载状态
const businessCardsLoading = ref(false)
const resumesLoading = ref(false)
const documentsLoading = ref(false)
const imagesLoading = ref(false)

// 数据列表
const businessCards = ref([])
const resumes = ref([])
const documents = ref([])
const images = ref([])

// 选中的数据
const selectedBusinessCards = ref([])
const selectedResumes = ref([])
const selectedDocuments = ref([])
const selectedImages = ref([])

// 筛选条件
const businessCardFilters = reactive({
  keyword: ''
})

const resumeFilters = reactive({
  keyword: ''
})

const documentFilters = reactive({
  keyword: ''
})

const imageFilters = reactive({
  keyword: ''
})

// 分页
const businessCardPagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

const resumePagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

const documentPagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

const imagePagination = reactive({
  page: 1,
  per_page: 20,
  total: 0
})

// 方法
const loadStats = async () => {
  try {
    const response = await api.admin.content.getContentStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载内容统计失败:', error)
  }
}

const loadBusinessCards = async () => {
  businessCardsLoading.value = true
  try {
    const params = {
      page: businessCardPagination.page,
      per_page: businessCardPagination.per_page,
      keyword: businessCardFilters.keyword
    }
    const response = await api.admin.content.getBusinessCards(params)
    businessCards.value = response.data.business_cards
    businessCardPagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载名片数据失败')
  } finally {
    businessCardsLoading.value = false
  }
}

const loadResumes = async () => {
  resumesLoading.value = true
  try {
    const params = {
      page: resumePagination.page,
      per_page: resumePagination.per_page,
      keyword: resumeFilters.keyword
    }
    const response = await api.admin.content.getResumes(params)
    resumes.value = response.data.resumes
    resumePagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载简历数据失败')
  } finally {
    resumesLoading.value = false
  }
}

const loadDocuments = async () => {
  documentsLoading.value = true
  try {
    const params = {
      page: documentPagination.page,
      per_page: documentPagination.per_page,
      keyword: documentFilters.keyword
    }
    const response = await api.admin.content.getDocuments(params)
    documents.value = response.data.documents
    documentPagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载文档数据失败')
  } finally {
    documentsLoading.value = false
  }
}

const loadImages = async () => {
  imagesLoading.value = true
  try {
    const params = {
      page: imagePagination.page,
      per_page: imagePagination.per_page,
      keyword: imageFilters.keyword
    }
    const response = await api.admin.content.getImages(params)
    images.value = response.data.images
    imagePagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载图片数据失败')
  } finally {
    imagesLoading.value = false
  }
}

// 分页处理
const handleBusinessCardSizeChange = (size) => {
  businessCardPagination.per_page = size
  loadBusinessCards()
}

const handleBusinessCardCurrentChange = (page) => {
  businessCardPagination.page = page
  loadBusinessCards()
}

const handleResumeSizeChange = (size) => {
  resumePagination.per_page = size
  loadResumes()
}

const handleResumeCurrentChange = (page) => {
  resumePagination.page = page
  loadResumes()
}

const handleDocumentSizeChange = (size) => {
  documentPagination.per_page = size
  loadDocuments()
}

const handleDocumentCurrentChange = (page) => {
  documentPagination.page = page
  loadDocuments()
}

const handleImageSizeChange = (size) => {
  imagePagination.per_page = size
  loadImages()
}

const handleImageCurrentChange = (page) => {
  imagePagination.page = page
  loadImages()
}

// 预览功能
const previewBusinessCard = (card) => {
  previewContent.value = card
  previewType.value = 'business-card'
  showPreview.value = true
}

const previewResume = (resume) => {
  previewContent.value = resume
  previewType.value = 'resume'
  showPreview.value = true
}

const previewImage = (image) => {
  previewContent.value = image
  previewType.value = 'image'
  showPreview.value = true
  imageLoadError.value = false
}

const closePreview = () => {
  showPreview.value = false
  previewContent.value = null
  previewType.value = ''
}

// 删除功能
const deleteBusinessCard = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这张名片吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.admin.content.deleteBusinessCard(id)
    ElMessage.success('删除成功')
    loadBusinessCards()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const deleteResume = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这份简历吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.admin.content.deleteResume(id)
    ElMessage.success('删除成功')
    loadResumes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const deleteDocument = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这个文档吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.admin.content.deleteDocument(id)
    ElMessage.success('删除成功')
    loadDocuments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const deleteImage = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这张图片吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.admin.content.deleteImage(id)
    ElMessage.success('删除成功')
    loadImages()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 下载功能
const downloadDocument = async (document) => {
  try {
    const response = await api.admin.downloadDocument(document.id)
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = document.filename
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const downloadImage = async (image) => {
  try {
    const response = await api.admin.downloadImage(image.id)
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = image.filename
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('下载成功')
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

// 导出功能
const exportBusinessCards = async () => {
  try {
    const response = await api.admin.content.exportBusinessCards(businessCardFilters)
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `名片数据_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const exportResumes = async () => {
  try {
    const response = await api.admin.content.exportResumes(resumeFilters)
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `简历数据_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const exportDocuments = async () => {
  try {
    const response = await api.admin.content.exportDocuments(documentFilters)
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `文档数据_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const exportImages = async () => {
  try {
    const response = await api.admin.content.exportImages(imageFilters)
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `图片数据_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 选择变化处理
const handleBusinessCardSelectionChange = (selection) => {
  selectedBusinessCards.value = selection
}

const handleResumeSelectionChange = (selection) => {
  selectedResumes.value = selection
}

const handleDocumentSelectionChange = (selection) => {
  selectedDocuments.value = selection
}

const handleImageSelectionChange = (selection) => {
  selectedImages.value = selection
}

// 批量删除方法
const batchDeleteBusinessCards = async () => {
  if (selectedBusinessCards.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedBusinessCards.value.length} 个名片吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedBusinessCards.value.map(item => item.id)
    await api.admin.content.batchDeleteBusinessCards({ ids })
    
    ElMessage.success(`成功删除 ${ids.length} 个名片`)
    selectedBusinessCards.value = []
    loadBusinessCards()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const batchDeleteResumes = async () => {
  if (selectedResumes.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedResumes.value.length} 个简历吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedResumes.value.map(item => item.id)
    await api.admin.content.batchDeleteResumes({ ids })
    
    ElMessage.success(`成功删除 ${ids.length} 个简历`)
    selectedResumes.value = []
    loadResumes()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const batchDeleteDocuments = async () => {
  if (selectedDocuments.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedDocuments.value.length} 个文档吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedDocuments.value.map(item => item.id)
    await api.admin.content.batchDeleteDocuments({ ids })
    
    ElMessage.success(`成功删除 ${ids.length} 个文档`)
    selectedDocuments.value = []
    loadDocuments()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const batchDeleteImages = async () => {
  if (selectedImages.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedImages.value.length} 个图片吗？此操作不可恢复！`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedImages.value.map(item => item.id)
    await api.admin.content.batchDeleteImages({ ids })
    
    ElMessage.success(`成功删除 ${ids.length} 个图片`)
    selectedImages.value = []
    loadImages()
    loadStats()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

// 工具函数
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听选项卡切换
watch(activeTab, (newTab) => {
  switch (newTab) {
    case 'business-cards':
      loadBusinessCards()
      break
    case 'resumes':
      loadResumes()
      break
    case 'documents':
      loadDocuments()
      break
    case 'images':
      loadImages()
      break
  }
})

onMounted(() => {
  loadStats()
  loadBusinessCards()
})
</script>

<style scoped>
.content-management {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  margin-bottom: 0;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

.stat-sub {
  font-size: 12px;
  color: #95a5a6;
  margin-top: 2px;
}

.content-tabs {
  margin-bottom: 24px;
}

.tab-content {
  padding: 20px 0;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 500;
  color: #2c3e50;
}

.user-id {
  font-size: 12px;
  color: #7f8c8d;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.preview-content {
  max-height: 500px;
  overflow-y: auto;
}

.business-card-preview,
.resume-preview,
.image-preview {
  padding: 16px;
}

.business-card-preview h3,
.resume-preview h3 {
  margin: 0 0 16px 0;
  color: #2c3e50;
}

.preview-details {
  margin-bottom: 20px;
}

.preview-details p {
  margin: 8px 0;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  gap: 8px;
}

.preview-details strong {
  color: #2c3e50;
  min-width: 80px;
}

.image-preview-container {
  text-align: center;
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.business-card-preview p,
.resume-preview p,
.image-preview p {
  margin: 8px 0;
  color: #7f8c8d;
}
</style>