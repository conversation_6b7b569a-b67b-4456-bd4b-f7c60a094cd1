<template>
  <div class="resume-content-wrapper basic-template">
    <!-- 头部信息 -->
    <header class="resume-header">
      <div class="header-main">
        <div v-if="displayPhotoUrl" class="photo-container">
          <img :src="displayPhotoUrl" alt="个人照片" class="photo">
        </div>
        <div class="personal-info">
          <h1 class="full-name">{{ formattedResumeData.full_name }}</h1>
          <div class="basic-info">
            <span v-if="formattedResumeData.gender" class="info-item">
              {{ formattedResumeData.gender }}
            </span>
            <span v-if="formattedResumeData.age" class="info-item">
              {{ formattedResumeData.age }}岁
            </span>
          </div>
          <div class="contact-info">
            <span v-if="formattedResumeData.phone" class="contact-item">
              {{ formattedResumeData.phone }}
            </span>
            <span v-if="formattedResumeData.email" class="contact-item">
              {{ formattedResumeData.email }}
            </span>
          </div>
          <div class="address-info" v-if="formattedResumeData.address">
            {{ formattedResumeData.address }}
        </div>
        </div>
      </div>
    </header>

    <!-- 求职意向 -->
    <section v-if="formattedResumeData.objective" class="resume-section">
      <h3 class="section-title">求职意向</h3>
      <div class="section-content">
        <p class="objective-text">{{ formattedResumeData.objective }}</p>
      </div>
    </section>

    <!-- 个人总结 -->
    <section v-if="formattedResumeData.summary" class="resume-section">
      <h3 class="section-title">个人总结</h3>
      <div class="section-content">
        <p class="summary-text">{{ formattedResumeData.summary }}</p>
      </div>
    </section>

    <!-- 工作经历 -->
    <section v-if="formattedResumeData.work_experiences?.length" class="resume-section">
      <h3 class="section-title">工作经历</h3>
      <div class="section-content">
        <div v-for="exp in formattedResumeData.work_experiences" :key="exp.company_name" class="experience-item">
          <div class="experience-header">
            <h4 class="company-name">{{ exp.company_name }}</h4>
            <span class="period">{{ exp.period }}</span>
          </div>
          <div class="position">{{ exp.position }}</div>
          <p v-if="exp.description" class="description">{{ exp.description }}</p>
          <ul v-if="exp.achievements?.length" class="achievements">
            <li v-for="(achievement, index) in exp.achievements" :key="index">
              {{ achievement }}
            </li>
          </ul>
        </div>
      </div>
    </section>

    <!-- 教育背景 -->
    <section v-if="formattedResumeData.educations?.length" class="resume-section">
      <h3 class="section-title">教育背景</h3>
      <div class="section-content">
        <div v-for="edu in formattedResumeData.educations" :key="edu.school_name" class="education-item">
          <div class="education-header">
            <h4 class="school-name">{{ edu.school_name }}</h4>
            <span class="period">{{ edu.period }}</span>
          </div>
          <div class="major">{{ edu.major }} · {{ edu.degree }}</div>
          <p v-if="edu.description" class="description">{{ edu.description }}</p>
        </div>
      </div>
    </section>

    <!-- 项目经验 -->
    <section v-if="formattedResumeData.projects?.length" class="resume-section">
      <h3 class="section-title">项目经验</h3>
      <div class="section-content">
        <div v-for="project in formattedResumeData.projects" :key="project.name" class="project-item">
          <div class="project-header">
            <h4 class="project-name">{{ project.name }}</h4>
            <span class="period">{{ project.period }}</span>
          </div>
          <p v-if="project.description" class="description">{{ project.description }}</p>
          <div v-if="project.technologies?.length" class="technologies">
            <span v-for="tech in project.technologies" :key="tech" class="tech-tag">
              {{ tech }}
            </span>
          </div>
        </div>
      </div>
    </section>

    <!-- 技能特长 -->
    <section v-if="formattedResumeData.skills?.length" class="resume-section">
      <h3 class="section-title">技能特长</h3>
      <div class="section-content">
        <div class="skills-grid">
          <div v-for="skill in formattedResumeData.skills" :key="skill.name" class="skill-item">
            <span class="skill-name">{{ skill.name }}</span>
            <div class="skill-level">
              <div class="skill-bar" :style="{ width: skill.level + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 证书资质 -->
    <section v-if="formattedResumeData.certifications?.length" class="resume-section">
      <h3 class="section-title">证书资质</h3>
      <div class="section-content">
        <ul class="certifications-list">
          <li v-for="cert in formattedResumeData.certifications" :key="cert.name" class="certification-item">
            <span class="cert-name">{{ cert.name }}</span>
            <span v-if="cert.date" class="cert-date">{{ cert.date }}</span>
          </li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script>
import { computed, watch } from 'vue'

export default {
  name: 'BasicTemplate',
  
  props: {
    resumeData: {
      type: Object,
      required: true,
      validator(value) {
        // 基本必需字段验证
        const requiredFields = ['id', 'title', 'full_name']
        const hasRequiredFields = requiredFields.every(field => 
          value[field] !== undefined && value[field] !== null
        )
        
        // 数组字段验证
        const arrayFields = ['work_experiences', 'educations', 'projects', 'skills', 'certifications']
        const hasValidArrays = arrayFields.every(field => 
          !value[field] || Array.isArray(value[field])
        )
        
        return hasRequiredFields && hasValidArrays
      }
    }
  },

  emits: ['error'],

  setup(props, { emit }) {
    // 验证并格式化数据
    const formattedResumeData = computed(() => {
      try {
        const data = props.resumeData

        // 调试输出：检查传入的数据
        console.log('模板组件接收到的数据:', data)

        // 格式化工作经历
        const work_experiences = (data.work_experiences || []).map(exp => {
          if (!exp.company_name || !exp.position) {
            console.warn('工作经历数据不完整:', exp)
          }
          return {
            ...exp,
            period: exp.start_date && exp.end_date ? `${exp.start_date} - ${exp.end_date}` : '',
            achievements: Array.isArray(exp.achievements) ? exp.achievements : []
          }
        })

        // 格式化教育经历
        const educations = (data.educations || []).map(edu => {
          if (!edu.school_name || !edu.major) {
            console.warn('教育经历数据不完整:', edu)
          }
          return {
            ...edu,
            period: edu.start_date && edu.end_date ? `${edu.start_date} - ${edu.end_date}` : ''
          }
        })

        // 格式化项目经验
        const projects = (data.projects || []).map(proj => {
          if (!proj.name) {
            console.warn('项目经验数据不完整:', proj)
          }
          return {
            ...proj,
            period: proj.start_date && proj.end_date ? `${proj.start_date} - ${proj.end_date}` : '',
            technologies: Array.isArray(proj.technologies) ? proj.technologies : [],
            achievements: Array.isArray(proj.achievements) ? proj.achievements : []
          }
        })

        // 格式化技能
        const skills = (data.skills || []).map(skill => {
          if (!skill.name) {
            console.warn('技能数据不完整:', skill)
          }
          return {
            ...skill,
            level: typeof skill.level === 'number' ? skill.level : 
                   skill.proficiency === '专家' ? 100 :
                   skill.proficiency === '高级' ? 80 :
                   skill.proficiency === '中级' ? 60 :
                   skill.proficiency === '初级' ? 40 : 20
          }
        })

        // 格式化证书
        const certifications = (data.certifications || []).map(cert => {
          if (!cert.name) {
            console.warn('证书数据不完整:', cert)
          }
          return {
            ...cert,
            date: cert.issue_date || cert.date || ''
          }
        })

        const formattedData = {
          ...data,
          work_experiences,
          educations,
          projects,
          skills,
          certifications
        }

        // 调试输出：检查格式化后的数据
        console.log('模板格式化后的数据:', formattedData)

        return formattedData
      } catch (err) {
        console.error('简历数据格式化失败:', err)
        emit('error', '简历数据格式化失败')
        return props.resumeData
      }
    })

    const displayPhotoUrl = computed(() => {
      const url = props.resumeData.thumbnail_url || props.resumeData.photo_url
      if (!url) return ''
      if (url.startsWith('http')) return url
      // 兼容相对路径
      return (window.API_BASE_URL || 'http://localhost:5000') + url
    })

    // 监听数据变化
    watch(() => props.resumeData, (newData) => {
      if (!newData) {
        console.warn('模板接收到空数据')
        return
      }

      // 检查数据完整性
      const sections = ['educations', 'work_experiences', 'projects', 'skills', 'certifications']
      sections.forEach(section => {
        if (!newData[section] || !Array.isArray(newData[section])) {
          console.warn(`${section} 数据不完整或格式错误`)
        }
      })
    }, { immediate: true })

    return {
      formattedResumeData,
      displayPhotoUrl
    }
  }
}
</script>

<style scoped>
.basic-template {
  font-family: 'Microsoft YaHei', sans-serif;
  color: #2c3e50;
  font-size: 14px;
  line-height: 1.4;
  padding: 2.54cm 1.97cm;
  background: white;
  /* box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1); */
  border-radius: 8px;
}

/* 头部样式 */
.resume-header {
  margin-bottom: 0.7rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #eee;
}

.header-main {
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.photo-container {
  width: 120px;
  height: 160px;
  overflow: hidden;
  border-radius: 4px;
  /* 保证图片高度 */
  box-shadow: none;
  flex-shrink: 0;
}

.photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.personal-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  height: 160px;
}

.full-name {
  font-size: 1.4rem;
  font-weight: 600;
  margin: 0 0 1.4rem 0;
  color: #333;
}

.basic-info {
  display: flex;
  gap: 1.2rem;
  margin-bottom: 0.6rem;
  color: #666;
  font-size: 1.2rem;
}

.info-item {
  display: inline-block;
}

.contact-info {
  display: flex;
  gap: 2.4rem;
  margin-bottom: 0.6rem;
  color: #666;
  font-size: 0.95rem;
}

.contact-item {
  display: inline-block;
}

.address-info {
  color: #666;
  margin-bottom: 0.6rem;
  font-size: 0.95rem;
}

/* 章节样式 */
.resume-section {
  margin-bottom: 0.5rem;
  break-inside: avoid;
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.2rem;
  margin-bottom: 0.5rem;
}

.section-content {
  padding: 0 0.6rem;
}

/* 经验项目通用样式 */
.experience-item,
.education-item,
.project-item {
  margin-bottom: 0.1rem;
  padding: 0.2rem;
  border-radius: 6px;
  break-inside: avoid;
}

.experience-header,
.education-header,
.project-header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 1.0rem;
}

.company-name,
.school-name,
.project-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.period {
  color: #666;
  font-size: 0.9rem;
}

.position,
.major {
  font-weight: 500;
  color: #3498db;
  margin-bottom: 0.5rem;
}

.description {
  color: #666;
  margin: 0.5rem 0;
  line-height: 1.6;
  white-space: pre-line;
}

/* 技能样式 */
.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.skill-item {
  padding: 0.3rem;
  border-radius: 6px;
}

.skill-name {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.skill-level {
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.skill-bar {
  height: 100%;
  background: #3498db;
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 证书列表样式 */
.certifications-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.certification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.3rem;
  border-radius: 6px;
  margin-bottom: 0.3rem;
}

.cert-name {
  font-weight: 500;
}

.cert-date {
  color: #666;
  font-size: 0.9rem;
}

/* 成就列表样式 */
.achievements {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0;
}

.achievements li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
  color: #666;
}

.achievements li::before {
  content: "•";
  position: absolute;
  left: 0.5rem;
  color: #3498db;
}

/* 技术标签样式 */
.technologies {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.tech-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.9rem;
}

/* 打印样式优化 */
@media print {
  .basic-template {
    box-shadow: none;
    padding: 0;
  }

  .resume-section {
    page-break-inside: avoid;
  }

  .experience-item,
  .education-item,
  .project-item,
  .skill-item,
  .certification-item {
    background: none;
    padding: 0;
  }
}

.summary-text {
  color: #666;
  line-height: 1.6;
  margin: 0;
  white-space: pre-line;
}

.objective-text {
  color: #666;
  line-height: 1.6;
  margin: 0;
  white-space: pre-line;
}
</style> 