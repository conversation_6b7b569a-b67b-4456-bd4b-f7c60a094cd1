<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ resume.full_name }} - 简历</title>
    <!-- 样式将通过CSS文件加载 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="resume-container">
        <!-- 头部信息 -->
        <header class="resume-header">
            <div class="header-content">
                <div class="profile-section">
                    <div class="profile-info">
                        <h1 class="name">{{ resume.full_name }}</h1>
                        <h2 class="title">{{ resume.objective or '求职者' }}</h2>
                    </div>
                </div>
                
                <div class="contact-section">
                    {% if resume.phone %}
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>{{ resume.phone }}</span>
                    </div>
                    {% endif %}
                    {% if resume.email %}
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span>{{ resume.email }}</span>
                    </div>
                    {% endif %}
                    {% if resume.address %}
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>{{ resume.address }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </header>

        <!-- 主要内容 -->
        <main class="resume-main">
            <!-- 个人总结 -->
            {% if resume.summary %}
            <section class="resume-section">
                <h3 class="section-title">
                    <i class="fas fa-user"></i>
                    <span>个人总结</span>
                </h3>
                <div class="section-content">
                    <p class="summary-text">{{ resume.summary }}</p>
                </div>
            </section>
            {% endif %}

            <!-- 工作经历 -->
            {% if resume.work_experiences %}
            <section class="resume-section">
                <h3 class="section-title">
                    <i class="fas fa-briefcase"></i>
                    <span>工作经历</span>
                </h3>
                <div class="section-content">
                    {% for exp in resume.work_experiences %}
                    <div class="experience-item">
                        <div class="experience-header">
                            <div class="experience-title">
                                <h4>{{ exp.position }}</h4>
                                <h5>{{ exp.company_name }}</h5>
                            </div>
                            <div class="experience-date">
                                <i class="fas fa-calendar-alt"></i>
                                <span>{{ exp.start_date }} - {{ exp.end_date if exp.end_date else '至今' }}</span>
                            </div>
                        </div>
                        {% if exp.description %}
                        <div class="experience-description">
                            <p>{{ exp.description }}</p>
                        </div>
                        {% endif %}
                        {% if exp.achievements %}
                        <div class="experience-achievements">
                            <ul>
                                {% for achievement in exp.achievements %}
                                <li><i class="fas fa-check-circle"></i> {{ achievement }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- 教育背景 -->
            {% if resume.educations %}
            <section class="resume-section">
                <h3 class="section-title">
                    <i class="fas fa-graduation-cap"></i>
                    <span>教育背景</span>
                </h3>
                <div class="section-content">
                    {% for edu in resume.educations %}
                    <div class="education-item">
                        <div class="education-header">
                            <div class="education-title">
                                <h4>{{ edu.school_name }}</h4>
                                <h5>{{ edu.degree }}{% if edu.major %} · {{ edu.major }}{% endif %}</h5>
                            </div>
                            <div class="education-date">
                                <i class="fas fa-calendar-alt"></i>
                                <span>{{ edu.start_date }} - {{ edu.end_date }}</span>
                            </div>
                        </div>
                        {% if edu.gpa %}
                        <div class="education-gpa">
                            <i class="fas fa-star"></i>
                            <span>GPA: {{ edu.gpa }}</span>
                        </div>
                        {% endif %}
                        {% if edu.description %}
                        <div class="education-description">
                            <p>{{ edu.description }}</p>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- 项目经验 -->
            {% if resume.projects %}
            <section class="resume-section">
                <h3 class="section-title">
                    <i class="fas fa-code"></i>
                    <span>项目经验</span>
                </h3>
                <div class="section-content">
                    {% for project in resume.projects %}
                    <div class="project-item">
                        <div class="project-header">
                            <div class="project-title">
                                <h4>{{ project.name }}</h4>
                                {% if project.role %}
                                <h5>{{ project.role }}</h5>
                                {% endif %}
                            </div>
                            <div class="project-date">
                                <i class="fas fa-calendar-alt"></i>
                                <span>{{ project.start_date }} - {{ project.end_date }}</span>
                            </div>
                        </div>
                        {% if project.description %}
                        <div class="project-description">
                            <p>{{ project.description }}</p>
                        </div>
                        {% endif %}
                        {% if project.technologies %}
                        <div class="project-technologies">
                            <i class="fas fa-tools"></i>
                            <div class="tech-tags">
                                {% for tech in project.technologies %}
                                <span class="tech-tag">{{ tech }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                        {% if project.achievements %}
                        <div class="project-achievements">
                            <ul>
                                {% for achievement in project.achievements %}
                                <li><i class="fas fa-trophy"></i> {{ achievement }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- 技能特长 -->
            {% if resume.skills %}
            <section class="resume-section">
                <h3 class="section-title">
                    <i class="fas fa-cogs"></i>
                    <span>技能特长</span>
                </h3>
                <div class="section-content">
                    <div class="skills-grid">
                        {% for skill in resume.skills %}
                        <div class="skill-item">
                            <div class="skill-header">
                                <span class="skill-name">{{ skill.name }}</span>
                                <span class="skill-level">{{ skill.proficiency }}</span>
                            </div>
                            <div class="skill-progress">
                                <div class="skill-bar" style="width: {% if skill.proficiency == '专家' %}100{% elif skill.proficiency == '高级' %}80{% elif skill.proficiency == '中级' %}60{% else %}40{% endif %}%"></div>

                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </section>
            {% endif %}

            <!-- 证书资质 -->
            {% if resume.certifications %}
            <section class="resume-section">
                <h3 class="section-title">
                    <i class="fas fa-certificate"></i>
                    <span>证书资质</span>
                </h3>
                <div class="section-content">
                    {% for cert in resume.certifications %}
                    <div class="certification-item">
                        <div class="certification-header">
                            <div class="certification-title">
                                <h4>{{ cert.name }}</h4>
                                {% if cert.issuer %}
                                <h5>{{ cert.issuer }}</h5>
                                {% endif %}
                            </div>
                            <div class="certification-date">
                                <i class="fas fa-calendar-check"></i>
                                <span>{{ cert.issue_date }}</span>
                            </div>
                        </div>
                        {% if cert.credential_id %}
                        <div class="certification-id">
                            <i class="fas fa-id-card"></i>
                            <span>证书编号: {{ cert.credential_id }}</span>
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}
        </main>
    </div>
</body>
</html>