<template>
  <div class="template-selector">
    <div class="template-toolbar">
      <el-input v-model="search" placeholder="搜索模板" clearable @input="filterTemplates" style="width:180px;margin-right:12px" />
      <el-select v-model="category" placeholder="全部分类" clearable @change="filterTemplates" style="width:120px">
        <el-option label="全部" value="" />
        <el-option label="商务" value="business" />
        <el-option label="极简" value="minimal" />
        <el-option label="创意" value="creative" />
        <el-option label="优雅" value="elegant" />
      </el-select>
    </div>
    <div class="template-grid">
      <div 
        v-for="template in pagedTemplates" 
        :key="template.id"
        class="template-option"
        :class="{ active: modelValue === template.id }"
        @click="selectTemplate(template.id)"
      >
        <div class="template-info">
          <h4>{{ template.name }}</h4>
          <p>{{ template.description }}</p>
        </div>
      </div>
    </div>
    <el-pagination
      v-if="filteredTemplates.length > pageSize"
      v-model:current-page="page"
      :page-size="pageSize"
      :total="filteredTemplates.length"
      layout="total, prev, pager, next"
      @current-change="onPageChange"
      style="margin-top:16px; text-align:center;"
    />
  </div>
</template>
<script setup>
import { ref, computed, watch } from 'vue'
const props = defineProps({
  modelValue: [String, Number],
  templates: { type: Array, default: () => [] }
})
const emit = defineEmits(['update:modelValue', 'change'])
const search = ref('')
const category = ref('')
const page = ref(1)
const pageSize = 6
const filteredTemplates = ref([])
const filterTemplates = () => {
  let arr = props.templates
  if (search.value) arr = arr.filter(t => t.name.includes(search.value) || t.description.includes(search.value))
  if (category.value) arr = arr.filter(t => t.category === category.value)
  filteredTemplates.value = arr
  page.value = 1
}
const pagedTemplates = computed(() => {
  const start = (page.value - 1) * pageSize
  return filteredTemplates.value.slice(start, start + pageSize)
})
const selectTemplate = (templateId) => {
  emit('update:modelValue', templateId)
  emit('change', templateId)
}
const onPageChange = (p) => { page.value = p }
watch(() => props.templates, filterTemplates, { immediate: true })
</script>
<style scoped>
.template-selector {
  padding: 10px 0;
}
.template-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.template-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}
.template-option {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fff;
}
.template-option:hover {
  border-color: #c0c4cc;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}
.template-option.active {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.12);
}
.template-info {
  padding: 18px 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.template-info h4 {
  margin: 0 0 6px 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}
.template-info p {
  margin: 0;
  font-size: 13px;
  color: #909399;
  line-height: 1.5;
}
</style> 