<template>
  <div class="business-card-list-preview">
    <div 
      class="card-container"
      ref="cardContainer"
    >
      <div class="card-scaler" :style="scalerStyle">
        <component
          v-if="currentComponent"
          :is="currentComponent"
          :card-data="businessCard.card_data"
          :style-config="businessCard.style_config"
          :qr-code-url="businessCard.qr_code_image"
        />
        <div v-else class="template-placeholder">
          <p v-if="!template" class="error-text">模板数据为空</p>
          <p v-else class="error-text">模板组件 '{{ template.name }}' 加载失败</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed, ref, onMounted } from 'vue';
import templateMapping from './templateMapping.js';

export default {
  name: 'BusinessCardListPreview',
  
  props: {
    businessCard: {
      type: Object,
      required: true
    },
    template: {
      type: Object,
      default: null
    }
  },
  
  setup(props) {
    onMounted(() => {
        console.log('[BusinessCardListPreview] Props for Card ID', props.businessCard.id, { 
            template: props.template 
        });
    });

    const cardContainer = ref(null);

    const currentComponent = computed(() => {
      if (!props.template || !props.template.name) {
        console.warn(`[BusinessCardListPreview] Card ID ${props.businessCard.id}: Template data or template name is missing.`);
        return null;
      }
      const component = templateMapping[props.template.name] || null;
      if (!component) {
        console.error(`[BusinessCardListPreview] Card ID ${props.businessCard.id}: Component for template name "${props.template.name}" not found in templateMapping.js.`);
      }
      return component;
    });

    const scalerStyle = computed(() => {
      const baseWidth = 900; 
      const baseHeight = 540;
      
      return {
        width: `${baseWidth}px`,
        height: `${baseHeight}px`,
        transform: `scale(var(--card-scale, 0.25))`, // 使用CSS变量，由父级控制
        transformOrigin: 'top left',
      };
    });
    
    return {
      cardContainer,
      currentComponent,
      scalerStyle,
    };
  }
}
</script>

<style scoped>
.business-card-list-preview {
  width: 100%;
  height: 100%;
}

.card-container {
  width: 100%;
  padding-top: 60%; /* 宽高比 540 / 900 = 0.6 */
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  background-color: #f0f2f5;
}

.card-scaler {
  position: absolute;
  top: 0;
  left: 0;
}

.template-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  border: 2px dashed #ccc;
  border-radius: 4px;
  color: #999;
  font-size: 12px;
  text-align: center;
}

.error-text {
  color: #f56c6c;
  padding: 5px;
}
</style> 