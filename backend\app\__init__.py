import os
from flask import Flask, send_from_directory, current_app, request
from flask_cors import CORS
from flask_jwt_extended import J<PERSON>TManager
from celery import Celery
import redis
from config import config
from sqlalchemy import event
from sqlalchemy.engine import Engine
import sqlite3

from .database import db, migrate

# 初始化扩展
jwt = JWTManager()
celery = Celery(__name__)



# SQLite优化配置
@event.listens_for(Engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """设置SQLite优化参数"""
    if 'sqlite' in str(dbapi_connection):
        cursor = dbapi_connection.cursor()
        # 设置WAL模式以支持并发
        cursor.execute("PRAGMA journal_mode=WAL")
        # 设置同步模式
        cursor.execute("PRAGMA synchronous=NORMAL")
        # 设置缓存大小
        cursor.execute("PRAGMA cache_size=10000")
        # 设置超时
        cursor.execute("PRAGMA busy_timeout=30000")
        # 设置锁定超时
        cursor.execute("PRAGMA lock_timeout=30000")
        cursor.close()

# 数据库事务装饰器
def with_db_transaction(func):
    """数据库事务装饰器，自动处理事务和错误"""
    from functools import wraps
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            result = func(*args, **kwargs)
            db.session.commit()
            return result
        except Exception as e:
            db.session.rollback()
            if hasattr(current_app, 'logger'):
                current_app.logger.error(f"数据库事务失败: {e}")
            raise
        finally:
            db.session.close()
    return wrapper

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # 设置静态文件目录
    static_folder = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static')
    
    app = Flask(__name__, static_folder=None, static_url_path=None)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)

    # 数据库连接正常，移除调试代码

    # 初始化JWT
    jwt.init_app(app)
    
    # 配置CORS - 临时允许所有来源进行调试
    CORS(app,
         origins="*",
         methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
         allow_headers=["Content-Type", "Authorization", "X-Requested-With"])
    print("CORS已启用")

    # 添加请求日志中间件
    @app.before_request
    def log_request_info():
        current_app.logger.info(f"=== 收到请求 ===")
        current_app.logger.info(f"方法: {request.method}")
        current_app.logger.info(f"路径: {request.path}")
        current_app.logger.info(f"来源: {request.remote_addr}")
        current_app.logger.info(f"User-Agent: {request.headers.get('User-Agent', 'Unknown')}")
        if request.headers.get('Origin'):
            current_app.logger.info(f"Origin: {request.headers.get('Origin')}")

    @app.after_request
    def log_response_info(response):
        current_app.logger.info(f"=== 响应发送 ===")
        current_app.logger.info(f"状态码: {response.status_code}")
        current_app.logger.info(f"Content-Type: {response.headers.get('Content-Type', 'Unknown')}")
        return response

    # 配置Celery
    celery.conf.update(app.config)
    
    class ContextTask(celery.Task):
        """Make celery tasks work with Flask app context."""
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    
    # 注册蓝图
    from app.api import api_bp
    app.register_blueprint(api_bp, url_prefix='/api')

    # 注册管理员相关蓝图
    from app.api.admin_users import admin_users_bp
    from app.api.admin_credits import admin_credits_bp, admin_credits_bp_credits
    from app.api.admin_content import admin_content_bp
    from app.api.admin_system import admin_system_bp
    from app.api.admin_statistics import admin_statistics_bp
    app.register_blueprint(admin_users_bp)
    app.register_blueprint(admin_credits_bp)
    app.register_blueprint(admin_credits_bp_credits)
    app.register_blueprint(admin_content_bp)
    app.register_blueprint(admin_system_bp)
    app.register_blueprint(admin_statistics_bp)
    
    # 创建上传目录
    upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
    os.makedirs(upload_folder, exist_ok=True)
    os.makedirs(os.path.join(upload_folder, 'thumbnails'), exist_ok=True)
    os.makedirs(os.path.join(upload_folder, 'processed'), exist_ok=True)
    
    # 创建静态文件目录
    static_folder = app.config.get('STATIC_FOLDER')
    if static_folder:
        os.makedirs(static_folder, exist_ok=True)
        os.makedirs(os.path.join(static_folder, 'processed'), exist_ok=True)
    
    # 配置静态文件路由
    @app.route('/static/<path:filename>')
    def serve_static(filename):
        """提供静态文件服务"""
        app.logger.info(f'请求静态文件: {filename}')
        
        # 获取配置的目录
        upload_folder = app.config.get('UPLOAD_FOLDER')
        static_folder = app.config.get('STATIC_FOLDER')
        
        # 确保是绝对路径
        if upload_folder and not os.path.isabs(upload_folder):
            upload_folder = os.path.abspath(upload_folder)
        if static_folder and not os.path.isabs(static_folder):
            static_folder = os.path.abspath(static_folder)
        
        # 优先从uploads目录提供缩略图、图片、processed
        if filename.startswith(('thumbnails/', 'images/', 'processed/', 'documents/')) and upload_folder:
            full_path = os.path.join(upload_folder, filename)
            app.logger.info(f'检查uploads文件: {full_path}')
            if os.path.exists(full_path):
                app.logger.info(f'从uploads目录提供文件: {filename}')
                return send_from_directory(upload_folder, filename)
        
        # 其次从static目录提供
        if static_folder:
            full_path = os.path.join(static_folder, filename)
            app.logger.info(f'检查static文件: {full_path}')
            if os.path.exists(full_path):
                app.logger.info(f'从static目录提供文件: {filename}')
                return send_from_directory(static_folder, filename)
        
        # 如果都失败了，返回404
        app.logger.error(f'文件未找到: {filename}')
        from flask import abort
        abort(404)
    
    # 健康检查路由
    @app.route('/health')
    @app.route('/api/health')
    def health_check():
        """健康检查接口"""
        return {'status': 'running', 'message': 'Photo Maker API Server', 'version': '1.0.0'}
    
    @app.route('/')
    def index():
        """首页重定向到健康检查"""
        return health_check()
    
    return app

def create_celery_app(app=None):
    """创建Celery应用"""
    app = app or create_app()
    celery.conf.update(app.config)
    
    class ContextTask(celery.Task):
        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)
    
    celery.Task = ContextTask
    return celery 