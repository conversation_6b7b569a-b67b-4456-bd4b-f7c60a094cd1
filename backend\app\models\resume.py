"""
简历模型
"""
"""
简历主模型
"""
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, cast
from sqlalchemy import Column, Integer, String, Text, Boolean, JSON, ForeignKey, DateTime, Float, Enum as SQLEnum
from sqlalchemy.orm import relationship
from ..database import db
from .base import BaseModel
from .resume_tables import ResumeStatus, TemplateCategory, IndustryType, resume_projects, resume_skills, resume_certifications
from .resume_template import ResumeTemplate

class Resume(BaseModel):
    """简历模型"""
    __tablename__ = 'resumes'
    
    # 基本信息
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    template_id = Column(Integer, ForeignKey('resume_templates.id'))
    title = Column(String(100), nullable=False)
    
    # 个人信息
    name = Column(String(50), nullable=False)
    email = Column(String(100))
    phone = Column(String(20))
    location = Column(String(100))
    photo_url = Column(String(500))
    
    # 个人简介
    summary = Column(Text)
    
    # 教育经历 [{school, degree, field, start_date, end_date, description}]
    education = Column(JSON, default=list)
    
    # 工作经历 [{company, position, start_date, end_date, description}]
    experience = Column(JSON, default=list)
    
    # 其他信息
    languages = Column(JSON, default=list)  # [{language, proficiency}]
    interests = Column(JSON, default=list)  # [interest1, interest2, ...]
    
    # 简历状态
    status = Column(SQLEnum(ResumeStatus), default=ResumeStatus.DRAFT)
    is_public = Column(Boolean, default=False)
    view_count = Column(Integer, default=0)
    
    # 关联
    template = relationship('ResumeTemplate', backref='resumes')
    user = relationship('User', backref='resumes')
    projects = relationship('ResumeProject', secondary=resume_projects, lazy='dynamic',
                          cascade='all, delete-orphan', single_parent=True)
    skills = relationship('ResumeSkill', secondary=resume_skills, lazy='dynamic',
                        cascade='all, delete-orphan', single_parent=True)
    certifications = relationship('ResumeCertification', secondary=resume_certifications, 
                                lazy='dynamic', cascade='all, delete-orphan', single_parent=True)

    def __init__(self, **kwargs):
        """
        初始化简历对象
        """
        super().__init__(**kwargs)
        self.status = ResumeStatus.DRAFT
        self.view_count = 0
        self.is_public = False
        
        # 初始化JSON字段
        for field in ['education', 'experience', 'languages', 'interests']:
            if getattr(self, field, None) is None:
                setattr(self, field, [])

    def increment_views(self) -> None:
        """增加浏览次数"""
        if hasattr(self, 'view_count'):
            self.view_count += 1
            self.save()
    
    def update_status(self, status: ResumeStatus) -> None:
        """
        更新简历状态
        :param status: 新状态
        """
        if status != self.status:
            self.status = status
            self.save()

    def to_dict(self, include_relationships: bool = False) -> Dict[str, Any]:
        """
        转换为字典格式
        :param include_relationships: 是否包含关联对象
        :return: 字典形式的简历数据
        """
        base_dict = super().to_dict()
        
        # 处理JSON字段默认值
        for field in ['education', 'experience', 'languages', 'interests']:
            base_dict[field] = base_dict.get(field) or []
        
        # 处理枚举值
        if 'status' in base_dict and isinstance(base_dict['status'], ResumeStatus):
            base_dict['status'] = base_dict['status'].value
        
        if include_relationships:
            # 添加关联对象
            base_dict.update({
                'template': self.template.to_dict() if self.template else None,
                'projects': [p.to_dict() for p in self.projects.all()],
                'skills': [s.to_dict() for s in self.skills.all()],
                'certifications': [c.to_dict() for c in self.certifications.all()]
            })
        
        return base_dict
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    title = Column(String(200), nullable=False, comment='简历标题')
    template_id = Column(Integer, ForeignKey('resume_templates.id'), default=1, comment='模板ID')
    status = Column(String(20), default=ResumeStatus.DRAFT.value, comment='状态')
    
    # 个人基本信息
    full_name = Column(String(100), nullable=False, comment='姓名')
    gender = Column(String(10), comment='性别')
    age = Column(Integer, comment='年龄')
    phone = Column(String(20), comment='电话')
    email = Column(String(100), comment='邮箱')
    address = Column(String(200), comment='地址')
    objective = Column(Text, comment='求职意向')
    summary = Column(Text, comment='个人总结')
    
    # 照片信息
    photo_url = Column(String(500), comment='照片URL')
    photo_image_id = Column(Integer, ForeignKey('images.id'), nullable=True, comment='引用系统中的图片ID')
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    user = relationship("User", backref="resumes")
    template = relationship("ResumeTemplate", backref="resumes")
    photo_image = relationship("Image", backref="resumes")
    educations = relationship("Education", backref="resume", lazy=True, cascade="all, delete-orphan")
    work_experiences = relationship("WorkExperience", backref="resume", lazy=True, cascade="all, delete-orphan")
    projects = relationship("Project", backref="resume", lazy=True, cascade="all, delete-orphan")
    skills = relationship("Skill", backref="resume", lazy=True, cascade="all, delete-orphan")
    certifications = relationship("Certification", backref="resume", lazy=True, cascade="all, delete-orphan")
    
    def to_dict(self, include_relations=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'user_id': self.user_id,
            'title': self.title,
            'template_id': self.template_id,
            'status': self.status,
            'full_name': self.full_name,
            'gender': self.gender,
            'age': self.age,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'objective': self.objective,
            'summary': self.summary,
            'photo_url': self.photo_url,
            'photo_image_id': self.photo_image_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'template': self.template.to_dict() if self.template else None
        }
        
        if include_relations:
            data.update({
                'educations': [edu.to_dict() for edu in self.educations],
                'work_experiences': [exp.to_dict() for exp in self.work_experiences],
                'projects': [proj.to_dict() for proj in self.projects],
                'skills': [skill.to_dict() for skill in self.skills],
                'certifications': [cert.to_dict() for cert in self.certifications]
            })
            
        return data

class Education(db.Model):
    """教育背景表"""
    __tablename__ = 'resume_educations'
    
    id = Column(Integer, primary_key=True)
    resume_id = Column(Integer, ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    school_name = Column(String(200), nullable=False, comment='学校名称')
    degree = Column(String(100), comment='学历学位')
    major = Column(String(100), comment='专业')
    start_date = Column(String(20), comment='开始时间')
    end_date = Column(String(20), comment='结束时间')
    gpa = Column(Float, comment='绩点')
    description = Column(Text, comment='描述')
    sort_order = Column(Integer, default=0, comment='排序')
    
    def to_dict(self):
        return {
            'id': self.id,
            'resume_id': self.resume_id,
            'school_name': self.school_name,
            'degree': self.degree,
            'major': self.major,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'gpa': self.gpa,
            'description': self.description,
            'sort_order': self.sort_order
        }

class WorkExperience(db.Model):
    """工作经历表"""
    __tablename__ = 'resume_work_experiences'
    
    id = Column(Integer, primary_key=True)
    resume_id = Column(Integer, ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    company_name = Column(String(200), nullable=False, comment='公司名称')
    position = Column(String(100), nullable=False, comment='职位')
    start_date = Column(String(20), comment='开始时间')
    end_date = Column(String(20), comment='结束时间')
    is_current = Column(Boolean, default=False, comment='是否当前工作')
    description = Column(Text, comment='工作描述')
    achievements = Column(JSON, comment='工作成就(JSON数组)')
    sort_order = Column(Integer, default=0, comment='排序')
    
    def to_dict(self):
        return {
            'id': self.id,
            'resume_id': self.resume_id,
            'company_name': self.company_name,
            'position': self.position,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'is_current': self.is_current,
            'description': self.description,
            'achievements': self.achievements or [],
            'sort_order': self.sort_order
        }

class Project(db.Model):
    """项目经验表"""
    __tablename__ = 'resume_projects'
    
    id = Column(Integer, primary_key=True)
    resume_id = Column(Integer, ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    name = Column(String(200), nullable=False, comment='项目名称')
    role = Column(String(100), comment='担任角色')
    start_date = Column(String(20), comment='开始时间')
    end_date = Column(String(20), comment='结束时间')
    description = Column(Text, comment='项目描述')
    technologies = Column(JSON, comment='使用技术(JSON数组)')
    achievements = Column(JSON, comment='项目成果(JSON数组)')
    project_url = Column(String(500), comment='项目链接')
    sort_order = Column(Integer, default=0, comment='排序')
    
    def to_dict(self):
        return {
            'id': self.id,
            'resume_id': self.resume_id,
            'name': self.name,
            'role': self.role,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'description': self.description,
            'technologies': self.technologies or [],
            'achievements': self.achievements or [],
            'project_url': self.project_url,
            'sort_order': self.sort_order
        }

class Skill(db.Model):
    """技能表"""
    __tablename__ = 'resume_skills'
    
    id = Column(Integer, primary_key=True)
    resume_id = Column(Integer, ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    category = Column(String(100), comment='技能分类')
    name = Column(String(100), nullable=False, comment='技能名称')
    proficiency = Column(String(20), comment='熟练度(初级/中级/高级/专家)')
    years_experience = Column(Float, comment='使用年限')
    sort_order = Column(Integer, default=0, comment='排序')
    
    def to_dict(self):
        return {
            'id': self.id,
            'resume_id': self.resume_id,
            'category': self.category,
            'name': self.name,
            'proficiency': self.proficiency,
            'years_experience': self.years_experience,
            'sort_order': self.sort_order
        }

class Certification(db.Model):
    """证书表"""
    __tablename__ = 'resume_certifications'
    
    id = Column(Integer, primary_key=True)
    resume_id = Column(Integer, ForeignKey('resumes.id', ondelete='CASCADE'), nullable=False)
    name = Column(String(200), nullable=False, comment='证书名称')
    issuer = Column(String(200), comment='颁发机构')
    issue_date = Column(String(20), comment='获得时间')
    expiry_date = Column(String(20), comment='过期时间')
    credential_id = Column(String(100), comment='证书编号')
    credential_url = Column(String(500), comment='证书链接')
    sort_order = Column(Integer, default=0, comment='排序')
    
    def to_dict(self):
        return {
            'id': self.id,
            'resume_id': self.resume_id,
            'name': self.name,
            'issuer': self.issuer,
            'issue_date': self.issue_date,
            'expiry_date': self.expiry_date,
            'credential_id': self.credential_id,
            'credential_url': self.credential_url,
            'sort_order': self.sort_order
        } 