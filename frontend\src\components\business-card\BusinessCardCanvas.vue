<template>
  <div class="canvas-wrapper">
    <div class="canvas-toolbar">
      <ElementToolbox @addElement="addElement" />
      <button @click="undo" :disabled="!undoStack.length">撤销</button>
      <button @click="redo" :disabled="!redoStack.length">重做</button>
      <button @click="alignSelected('left')" :disabled="selectedIds.length<2">左对齐</button>
      <button @click="alignSelected('right')" :disabled="selectedIds.length<2">右对齐</button>
      <button @click="alignSelected('top')" :disabled="selectedIds.length<2">上对齐</button>
      <button @click="alignSelected('bottom')" :disabled="selectedIds.length<2">下对齐</button>
      <button @click="alignSelected('hcenter')" :disabled="selectedIds.length<2">水平居中</button>
      <button @click="alignSelected('vcenter')" :disabled="selectedIds.length<2">垂直居中</button>
      <button @click="groupSelected" :disabled="selectedIds.length<2">分组</button>
      <button @click="ungroupSelected" :disabled="!selectedIds.some(id=>elements.find(e=>e.id===id&&e.type==='group'))">解组</button>
      <button @click="deleteSelected" :disabled="!selectedIds.length">批量删除</button>
      <button @click="copySelected" :disabled="!selectedIds.length">批量复制</button>
      <button @click="lockSelected" :disabled="!selectedIds.length">批量锁定</button>
      <button @click="unlockSelected" :disabled="!selectedIds.length">批量解锁</button>
      <button @click="hideSelected" :disabled="!selectedIds.length">批量隐藏</button>
      <button @click="showSelected" :disabled="!selectedIds.length">批量显示</button>
      <button @click="bringToFront" :disabled="!selectedIds.length">置顶</button>
      <button @click="sendToBack" :disabled="!selectedIds.length">置底</button>
      <button @click="moveUp" :disabled="!selectedIds.length">上移</button>
      <button @click="moveDown" :disabled="!selectedIds.length">下移</button>
      <button v-if="groupEditId" @click="exitGroupEdit">退出组内编辑</button>
      <button @click="() => historyPanelVisible = true">历史</button>
      <button @click="exportCanvas">导出JSON</button>
      <input type="file" ref="importInput" style="display:none" @change="importCanvas" />
      <button @click="() => importInput.click()">导入JSON</button>
    </div>
    <LayerPanel :elements="elements" @update:elements="val => elements = val" />
    <v-stage :config="stageConfig" @mousedown="onStageMouseDown" @mousemove="onStageMouseMove" @mouseup="onStageMouseUp" @wheel="onStageWheel">
      <v-layer>
        <!-- 画布背景 -->
        <v-rect :x="0" :y="0" :width="canvasWidth" :height="canvasHeight" :fill="canvasBg" />
        <!-- 元素渲染 -->
        <template v-for="el in visibleElements">
          <v-rect v-if="el.type==='group' && groupEditId===el.id" :x="el.x" :y="el.y" :width="el.width" :height="el.height" stroke="#2196f3" :strokeWidth="2" dash="[8,4]" />
          <v-text v-if="el.type==='text'" :key="'text-' + el.id" v-bind="el" :draggable="!el.locked" @click="evt => selectElement(el.id, evt)" @dblclick="() => onElementDblClick(el)" @dragmove="evt => onDragMove(el, evt)" @dragend="evt => onDragEnd(el, evt)" />
          <v-image v-else-if="el.type==='image'" :key="'img-' + el.id" :x="el.x" :y="el.y" :width="el.width" :height="el.height" :image="el.konvaImage" :draggable="!el.locked" @click="evt => selectElement(el.id, evt)" @dblclick="() => onElementDblClick(el)" @dragmove="evt => onDragMove(el, evt)" @dragend="evt => onDragEnd(el, evt)" />
          <v-image v-else-if="el.type==='qrcode'" :key="'qr-' + el.id" :x="el.x" :y="el.y" :width="el.width" :height="el.height" :image="el.konvaImage" :draggable="!el.locked" @click="evt => selectElement(el.id, evt)" @dblclick="() => onElementDblClick(el)" @dragmove="evt => onDragMove(el, evt)" @dragend="evt => onDragEnd(el, evt)" />
          <v-rect v-if="selectedIds.includes(el.id)" :x="el.x-2" :y="el.y-2" :width="el.width+4" :height="el.height+4" stroke="blue" :strokeWidth="2" dash="[4,2]" />
        </template>
        <!-- 智能对齐线 -->
        <template v-for="guide in alignmentGuides">
          <v-line v-if="guide.orientation==='vertical'" :points="[guide.x, 0, guide.x, canvasHeight]" stroke="red" :strokeWidth="1" dash="[4,4]" />
          <v-line v-if="guide.orientation==='horizontal'" :points="[0, guide.y, canvasWidth, guide.y]" stroke="red" :strokeWidth="1" dash="[4,4]" />
        </template>
        <!-- 框选矩形 -->
        <v-rect v-if="selectionRect" :x="selectionRect.x" :y="selectionRect.y" :width="selectionRect.width" :height="selectionRect.height" stroke="#2196f3" :strokeWidth="1" dash="[4,2]" fill="rgba(33,150,243,0.1)" />
      </v-layer>
    </v-stage>
    <ElementPropertyPanel :selectedElement="selectedElement" :selectedIds="selectedIds" :updateSelectedProps="updateSelectedProps" />
    <div v-if="historyPanelVisible" class="history-panel">
      <h4>操作历史</h4>
      <ul>
        <li v-for="(h, idx) in historyList" :key="idx">
          <span>{{ h.time }} {{ h.desc }}</span>
          <button @click="() => jumpToHistory(idx)">跳转</button>
        </li>
      </ul>
      <button @click="() => historyPanelVisible = false">关闭</button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { Stage as VStage, Layer as VLayer, Text as VText, Image as VImage, Rect as VRect, Line as VLine } from 'vue-konva';
import { v4 as uuidv4 } from 'uuid';
import { generateQRCode } from '@/utils/qrcode';
import ElementToolbox from './ElementToolbox.vue';
import ElementPropertyPanel from './ElementPropertyPanel.vue';
import LayerPanel from './LayerPanel.vue';

// 画布基础参数
const canvasWidth = 600;
const canvasHeight = 350;
const canvasBg = ref('#fff');
const stageConfig = { width: canvasWidth, height: canvasHeight, style: 'background:#f8f8f8;border-radius:18px;' };

// 元素与状态
const elements = ref([]);
const selectedIds = ref([]);
const selectedId = ref(null);
const groupEditId = ref(null);
const selectionRect = ref(null);
const alignmentGuides = ref([]);
const undoStack = ref([]);
const redoStack = ref([]);
const historyList = ref([]);
const historyPanelVisible = ref(false);
const importInput = ref(null);

// 画布缩放
const scale = ref(1);
function onStageWheel(e) {
  e.evt.preventDefault();
  const delta = e.evt.deltaY > 0 ? -0.1 : 0.1;
  scale.value = Math.max(0.2, Math.min(2, scale.value + delta));
}

// 可见元素（组内编辑）
const visibleElements = computed(() => {
  if (!groupEditId.value) return elements.value;
  const group = elements.value.find(e => e.id === groupEditId.value);
  if (!group) return [];
  return [group, ...elements.value.filter(e => e.groupId === group.id)];
});

// 选中元素
const selectedElement = computed(() => elements.value.find(el => selectedIds.value[0] === el.id));

// 添加元素
function addElement(el) {
  elements.value.push(el);
  pushHistory('添加元素');
}

// 选中/多选
function selectElement(id, evt) {
  if (evt && evt.shiftKey) {
    if (selectedIds.value.includes(id)) {
      selectedIds.value = selectedIds.value.filter(eid => eid !== id);
    } else {
      selectedIds.value.push(id);
    }
  } else {
    selectedIds.value = [id];
  }
}

// 框选（伪代码，需完善）
function onStageMouseDown(evt) {
  if (evt.target === evt.target.getStage()) {
    selectedIds.value = [];
    // 记录起点，显示selectionRect
  }
}
function onStageMouseMove(evt) {
  // 若正在框选，更新selectionRect
}
function onStageMouseUp(evt) {
  // 计算selectionRect范围内的元素，加入selectedIds
  // 隐藏selectionRect
}

// 拖拽/对齐线
function onDragMove(el, evt) {
  el.x = evt.target.x();
  el.y = evt.target.y();
  checkAlignment(el);
}
function onDragEnd(el, evt) {
  el.x = evt.target.x();
  el.y = evt.target.y();
  alignmentGuides.value = [];
  onElementChange();
}
function checkAlignment(movingEl) {
  const guides = [];
  const threshold = 3;
  elements.value.forEach(el => {
    if (el.id !== movingEl.id) {
      if (Math.abs(movingEl.y - el.y) < threshold) {
        guides.push({ y: el.y, orientation: 'horizontal' });
        movingEl.y = el.y;
      }
      if (Math.abs(movingEl.x - el.x) < threshold) {
        guides.push({ x: el.x, orientation: 'vertical' });
        movingEl.x = el.x;
      }
      const movingCenterX = movingEl.x + movingEl.width / 2;
      const elCenterX = el.x + el.width / 2;
      if (Math.abs(movingCenterX - elCenterX) < threshold) {
        guides.push({ x: elCenterX, orientation: 'vertical' });
        movingEl.x = elCenterX - movingEl.width / 2;
      }
      const movingCenterY = movingEl.y + movingEl.height / 2;
      const elCenterY = el.y + el.height / 2;
      if (Math.abs(movingCenterY - elCenterY) < threshold) {
        guides.push({ y: elCenterY, orientation: 'horizontal' });
        movingEl.y = elCenterY - movingEl.height / 2;
      }
    }
  });
  alignmentGuides.value = guides;
}

// 撤销重做/历史
function pushHistory(desc = '') {
  undoStack.value.push(JSON.parse(JSON.stringify(elements.value)));
  historyList.value.push({
    elements: JSON.parse(JSON.stringify(elements.value)),
    desc,
    time: new Date().toLocaleTimeString(),
  });
  redoStack.value = [];
}
function undo() {
  if (undoStack.value.length) {
    redoStack.value.push(JSON.parse(JSON.stringify(elements.value)));
    elements.value = undoStack.value.pop();
  }
}
function redo() {
  if (redoStack.value.length) {
    undoStack.value.push(JSON.parse(JSON.stringify(elements.value)));
    elements.value = redoStack.value.pop();
  }
}
function jumpToHistory(idx) {
  if (historyList.value[idx]) {
    elements.value = JSON.parse(JSON.stringify(historyList.value[idx].elements));
    selectedIds.value = [];
    groupEditId.value = null;
  }
  historyPanelVisible.value = false;
}
function onElementChange() {
  pushHistory('元素变更');
}

// 多选批量操作
function deleteSelected() {
  elements.value = elements.value.filter(el => !selectedIds.value.includes(el.id));
  selectedIds.value = [];
  onElementChange();
}
function copySelected() {
  const newEls = elements.value.filter(el => selectedIds.value.includes(el.id)).map(el => {
    const copy = { ...el, id: uuidv4(), x: el.x + 20, y: el.y + 20 };
    if (el.type === 'group') copy.children = [...el.children];
    return copy;
  });
  elements.value.push(...newEls);
  selectedIds.value = newEls.map(el => el.id);
  onElementChange();
}
function lockSelected() {
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) el.locked = true;
  });
  onElementChange();
}
function unlockSelected() {
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) el.locked = false;
  });
  onElementChange();
}
function hideSelected() {
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) el.visible = false;
  });
  onElementChange();
}
function showSelected() {
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) el.visible = true;
  });
  onElementChange();
}

// 批量对齐
function alignSelected(direction) {
  if (selectedIds.value.length < 2) return;
  const selectedEls = elements.value.filter(el => selectedIds.value.includes(el.id));
  if (!selectedEls.length) return;
  let refVal;
  switch (direction) {
    case 'left':
      refVal = Math.min(...selectedEls.map(el => el.x));
      selectedEls.forEach(el => { el.x = refVal; });
      break;
    case 'right':
      refVal = Math.max(...selectedEls.map(el => el.x + el.width));
      selectedEls.forEach(el => { el.x = refVal - el.width; });
      break;
    case 'top':
      refVal = Math.min(...selectedEls.map(el => el.y));
      selectedEls.forEach(el => { el.y = refVal; });
      break;
    case 'bottom':
      refVal = Math.max(...selectedEls.map(el => el.y + el.height));
      selectedEls.forEach(el => { el.y = refVal - el.height; });
      break;
    case 'hcenter':
      refVal = (Math.min(...selectedEls.map(el => el.x)) + Math.max(...selectedEls.map(el => el.x + el.width))) / 2;
      selectedEls.forEach(el => { el.x = refVal - el.width / 2; });
      break;
    case 'vcenter':
      refVal = (Math.min(...selectedEls.map(el => el.y)) + Math.max(...selectedEls.map(el => el.y + el.height))) / 2;
      selectedEls.forEach(el => { el.y = refVal - el.height / 2; });
      break;
  }
  onElementChange();
}

// 分组/解组
function groupSelected() {
  if (selectedIds.value.length < 2) return;
  const groupId = uuidv4();
  const groupEls = elements.value.filter(el => selectedIds.value.includes(el.id));
  const minX = Math.min(...groupEls.map(el => el.x));
  const minY = Math.min(...groupEls.map(el => el.y));
  const maxX = Math.max(...groupEls.map(el => el.x + el.width));
  const maxY = Math.max(...groupEls.map(el => el.y + el.height));
  const group = {
    id: groupId,
    type: 'group',
    x: minX,
    y: minY,
    width: maxX - minX,
    height: maxY - minY,
    children: groupEls.map(el => el.id),
    zIndex: Math.max(...groupEls.map(el => el.zIndex)),
    locked: false,
    visible: true,
  };
  groupEls.forEach(el => {
    el.x = el.x - minX;
    el.y = el.y - minY;
    el.groupId = groupId;
  });
  elements.value.push(group);
  selectedIds.value = [groupId];
  onElementChange();
}
function ungroupSelected() {
  const groupEls = elements.value.filter(el => selectedIds.value.includes(el.id) && el.type === 'group');
  groupEls.forEach(group => {
    const minX = group.x, minY = group.y;
    elements.value.forEach(el => {
      if (el.groupId === group.id) {
        el.x = el.x + minX;
        el.y = el.y + minY;
        delete el.groupId;
      }
    });
    elements.value = elements.value.filter(e => e.id !== group.id);
  });
  selectedIds.value = [];
  onElementChange();
}
function onElementDblClick(el) {
  if (el.type === 'group') {
    groupEditId.value = el.id;
    selectedIds.value = [];
  }
}
function exitGroupEdit() {
  groupEditId.value = null;
  selectedIds.value = [];
}

// 层级调整
function bringToFront() {
  let maxZ = Math.max(...elements.value.map(el => el.zIndex || 0), 0);
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) {
      el.zIndex = ++maxZ;
    }
  });
  onElementChange();
}
function sendToBack() {
  let minZ = Math.min(...elements.value.map(el => el.zIndex || 0), 0);
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) {
      el.zIndex = --minZ;
    }
  });
  onElementChange();
}
function moveUp() {
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) {
      el.zIndex++;
    }
  });
  onElementChange();
}
function moveDown() {
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) {
      el.zIndex--;
    }
  });
  onElementChange();
}

// 属性同步
function updateSelectedProps(props) {
  elements.value.forEach(el => {
    if (selectedIds.value.includes(el.id)) {
      Object.assign(el, props);
    }
  });
  onElementChange();
}

// 导入导出
function exportCanvas() {
  const data = JSON.stringify(elements.value, null, 2);
  const blob = new Blob([data], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'business_card_canvas.json';
  a.click();
  URL.revokeObjectURL(url);
}
function importCanvas(e) {
  const file = e.target.files[0];
  if (!file) return;
  const reader = new FileReader();
  reader.onload = (evt) => {
    try {
      const data = JSON.parse(evt.target.result);
      elements.value = data;
      selectedIds.value = [];
      groupEditId.value = null;
      onElementChange();
    } catch (err) {
      alert('导入失败：文件格式错误');
    }
  };
  reader.readAsText(file);
}

// 二维码渲染
watch(elements, async (newEls) => {
  for (const el of newEls) {
    if (el.type === 'qrcode') {
      el.image = await generateQRCode(el.content, { width: el.width });
      const img = new window.Image();
      img.src = el.image;
      el.konvaImage = img;
    }
  }
}, { deep: true });

// 快捷键
onMounted(() => {
  window.addEventListener('keydown', (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'z') {
      undo();
    } else if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.shiftKey && e.key === 'z'))) {
      redo();
    }
    if ((e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'a') {
      e.preventDefault();
      selectedIds.value = elements.value.map(el => el.id);
    }
  });
});
</script>

<style scoped>
.canvas-wrapper { position: relative; width: 100%; max-width: 900px; margin: 0 auto; }
.canvas-toolbar { display: flex; flex-wrap: wrap; gap: 8px; margin-bottom: 8px; }
.history-panel {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 260px;
  background: #fff;
  border: 1px solid #ddd;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  z-index: 100;
  padding: 12px;
}
.history-panel ul { max-height: 300px; overflow: auto; margin: 0; padding: 0; list-style: none; }
.history-panel li { display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px; }
@media (max-width: 700px) {
  .canvas-wrapper { max-width: 100vw; }
  .canvas-toolbar { flex-direction: column; gap: 4px; }
}
</style> 