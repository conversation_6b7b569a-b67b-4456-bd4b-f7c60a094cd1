from datetime import datetime
from typing import Dict, Any, List
import html

class ReportGenerator:
    """报告生成器，用于生成各种格式的对比报告"""
    
    def generate_html_report(self, comparison_result: Dict[str, Any], 
                           file1_name: str, file2_name: str, 
                           task_name: str = "", detailed: bool = True) -> str:
        """生成HTML格式的详细对比报告"""
        
        # 获取差异数据
        differences = comparison_result.get('differences', [])
        summary = comparison_result.get('summary', {})
        
        # 统计信息
        total_diffs = len(differences)
        additions = sum(1 for d in differences if d.get('type') == 1)  # INSERTION
        deletions = sum(1 for d in differences if d.get('type') == -1)  # DELETION
        replacements = sum(1 for d in differences if d.get('type') == 2)  # REPLACEMENT
        similarity = summary.get('similarity_percentage', 0)
        
        # 生成报告HTML
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>文档智能对比报告 - {html.escape(task_name or '文档对比')}</title>
            <style>
                {self._get_enhanced_report_css()}
            </style>
        </head>
        <body>
            <div class="container">
                <header class="report-header">
                    <div class="header-background"></div>
                    <div class="header-content">
                        <h1>📄 文档智能对比报告</h1>
                    <div class="task-info">
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">任务名称</span>
                                    <span class="info-value">{html.escape(task_name or '文档对比')}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">生成时间</span>
                                    <span class="info-value">{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">标准文档</span>
                                    <span class="info-value">{html.escape(file1_name)}</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">修改文档</span>
                                    <span class="info-value">{html.escape(file2_name)}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </header>
                
                <section class="summary-section">
                    <h2>📊 对比摘要</h2>
                    <div class="summary-grid">
                        <div class="summary-card total">
                            <div class="card-icon">📝</div>
                            <div class="card-content">
                                <div class="summary-number">{total_diffs}</div>
                            <div class="summary-label">总差异数</div>
                            </div>
                        </div>
                        <div class="summary-card additions">
                            <div class="card-icon">➕</div>
                            <div class="card-content">
                                <div class="summary-number">{additions}</div>
                            <div class="summary-label">新增内容</div>
                            </div>
                        </div>
                        <div class="summary-card deletions">
                            <div class="card-icon">➖</div>
                            <div class="card-content">
                                <div class="summary-number">{deletions}</div>
                            <div class="summary-label">删除内容</div>
                            </div>
                        </div>
                        <div class="summary-card replacements">
                            <div class="card-icon">🔄</div>
                            <div class="card-content">
                                <div class="summary-number">{replacements}</div>
                                <div class="summary-label">替换内容</div>
                            </div>
                        </div>
                        <div class="summary-card similarity">
                            <div class="card-icon">🎯</div>
                            <div class="card-content">
                                <div class="summary-number">{similarity:.1f}%</div>
                            <div class="summary-label">相似度</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="similarity-bar">
                        <div class="similarity-label">文档相似度</div>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: {similarity}%"></div>
                            </div>
                            <span class="progress-text">{similarity:.1f}%</span>
                        </div>
                    </div>
                </section>
                
                {self._generate_differences_section(differences, detailed)}
                
                <footer class="report-footer">
                    <div class="footer-content">
                        <p>📋 本报告由智能文档对比系统自动生成</p>
                        <p>🕒 生成时间：{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
                        <div class="footer-stats">
                            <span>共分析 {total_diffs} 处差异</span>
                            <span>•</span>
                            <span>相似度 {similarity:.1f}%</span>
                        </div>
                    </div>
                </footer>
            </div>
            
            <script>
                {self._get_report_javascript()}
            </script>
        </body>
        </html>
        """
        
        return html_content.strip()
    
    def _generate_differences_section(self, differences: List[Dict], detailed: bool = True) -> str:
        """生成差异详情部分"""
        if not differences:
            return """
            <section class="differences-section">
                <h2>✅ 差异详情</h2>
                <div class="no-differences">
                    <div class="no-diff-icon">🎉</div>
                    <h3>恭喜！未发现差异</h3>
                    <p>两个文档内容完全一致，没有发现任何差异。</p>
                </div>
            </section>
            """
        
        differences_html = """
        <section class="differences-section">
            <h2>📝 差异详情</h2>
            <div class="differences-container">
        """
        
        for i, diff in enumerate(differences, 1):
            diff_type = diff.get('type', 0)
            diff_html = self._format_difference_item(diff, i, detailed)
            differences_html += diff_html
        
        differences_html += """
            </div>
        </section>
        """
        
        return differences_html
    
    def _format_difference_item(self, diff: Dict, index: int, detailed: bool = True) -> str:
        """格式化单个差异项"""
        diff_type = diff.get('type', 0)
        
        # 确定差异类型和样式
        type_info = self._get_diff_type_info(diff_type)
        
        old_content = diff.get('old_content', '')
        new_content = diff.get('new_content', '')
        position = diff.get('position', 0)
        
        # 处理内容显示
        old_display = self._format_content_for_display(old_content)
        new_display = self._format_content_for_display(new_content)
        
        diff_html = f"""
        <div class="diff-item {type_info['class']}">
            <div class="diff-header">
                <div class="diff-number">#{index}</div>
                <div class="diff-type">
                    <span class="type-icon">{type_info['icon']}</span>
                    <span class="type-label">{type_info['label']}</span>
                </div>
                <div class="diff-position">位置: {position + 1}</div>
            </div>
            <div class="diff-content">
        """
        
        if diff_type == -1:  # 删除
            diff_html += f"""
                <div class="content-block deleted">
                    <div class="content-label">删除的内容</div>
                    <div class="content-text">{old_display}</div>
                </div>
            """
        elif diff_type == 1:  # 插入
            diff_html += f"""
                <div class="content-block added">
                    <div class="content-label">新增的内容</div>
                    <div class="content-text">{new_display}</div>
                </div>
            """
        elif diff_type == 2:  # 替换
            diff_html += f"""
                <div class="content-comparison">
                    <div class="content-block deleted">
                        <div class="content-label">原内容</div>
                        <div class="content-text">{old_display}</div>
                    </div>
                    <div class="comparison-arrow">→</div>
                    <div class="content-block added">
                        <div class="content-label">新内容</div>
                        <div class="content-text">{new_display}</div>
                    </div>
                </div>
            """
        
        diff_html += """
            </div>
        </div>
        """
        
        return diff_html
    
    def _get_diff_type_info(self, diff_type: int) -> Dict[str, str]:
        """获取差异类型信息"""
        type_map = {
            -1: {'class': 'deletion', 'icon': '➖', 'label': '删除'},
            0: {'class': 'equal', 'icon': '✅', 'label': '相同'},
            1: {'class': 'addition', 'icon': '➕', 'label': '新增'},
            2: {'class': 'replacement', 'icon': '🔄', 'label': '替换'}
        }
        return type_map.get(diff_type, {'class': 'unknown', 'icon': '❓', 'label': '未知'})
    
    def _format_content_for_display(self, content: Any) -> str:
        """格式化内容用于显示"""
        if isinstance(content, dict):
            # 如果是结构化内容
            content_type = content.get('type', 'text')
            text = content.get('content', '') or content.get('text', str(content))
            # 清理标记符和特殊字符
            text = self._clean_text_for_display(text)
            return f'<span class="content-type">[{content_type}]</span> {html.escape(text)}'
        elif isinstance(content, str):
            # 如果是纯文本，清理标记符
            cleaned_text = self._clean_text_for_display(content)
            return html.escape(cleaned_text)
        else:
            # 其他类型
            cleaned_text = self._clean_text_for_display(str(content))
            return html.escape(cleaned_text)
    
    def _clean_text_for_display(self, text: str) -> str:
        """清理文本中的标记符和特殊字符"""
        if not isinstance(text, str):
            text = str(text)
        
        # 移除常见的标记符
        import re
        
        # 移除HTML标签
        text = re.sub(r'<[^>]+>', '', text)
        
        # 移除Markdown标记
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # 粗体
        text = re.sub(r'\*(.*?)\*', r'\1', text)      # 斜体
        text = re.sub(r'`(.*?)`', r'\1', text)        # 代码
        text = re.sub(r'#{1,6}\s*', '', text)         # 标题
        
        # 移除特殊控制字符
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        text = text.strip()
        
        # 移除文档格式标记
        text = re.sub(r'\[.*?\]', '', text)  # 移除方括号标记
        text = re.sub(r'\{.*?\}', '', text)  # 移除花括号标记
        
        return text
    
    def _get_enhanced_report_css(self) -> str:
        """获取增强的报告CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            overflow: hidden;
            margin-top: 2rem;
            margin-bottom: 2rem;
        }
        
        /* 报告头部 */
        .report-header {
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
            overflow: hidden;
        }
        
        .header-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
            background-size: 30px 30px;
            animation: float 20s linear infinite;
        }
        
        @keyframes float {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-30px); }
        }
        
        .header-content {
            position: relative;
            z-index: 1;
        }
        
        .report-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 2rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        .info-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .info-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 0.5rem;
        }
        
        .info-value {
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        /* 摘要部分 */
        .summary-section {
            padding: 3rem 2rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        }
        
        .summary-section h2 {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2rem;
            color: #2d3748;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }
        
        .summary-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .summary-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .card-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        }
        
        .card-content {
            flex: 1;
        }
        
        .summary-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .summary-label {
            font-size: 0.9rem;
            color: #718096;
            font-weight: 500;
        }
        
        .summary-card.total .summary-number { color: #4a5568; }
        .summary-card.additions .summary-number { color: #38a169; }
        .summary-card.deletions .summary-number { color: #e53e3e; }
        .summary-card.replacements .summary-number { color: #3182ce; }
        .summary-card.similarity .summary-number { color: #805ad5; }
        
        /* 相似度进度条 */
        .similarity-bar {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .similarity-label {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .progress-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .progress-bar {
            flex: 1;
            height: 20px;
            background: #edf2f7;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #38a169 0%, #68d391 100%);
            border-radius: 10px;
            transition: width 0.5s ease;
        }
        
        .progress-text {
            font-weight: 600;
            font-size: 1.1rem;
            color: #2d3748;
        }
        
        /* 差异详情部分 */
        .differences-section {
            padding: 3rem 2rem;
        }
        
        .differences-section h2 {
            font-size: 2rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 2rem;
            color: #2d3748;
        }
        
        .no-differences {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
            border-radius: 12px;
            border: 2px solid #38a169;
        }
        
        .no-diff-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .no-differences h3 {
            font-size: 1.5rem;
            color: #38a169;
            margin-bottom: 1rem;
        }
        
        .no-differences p {
            color: #2f855a;
            font-size: 1.1rem;
        }
        
        /* 差异项样式 */
        .diff-item {
            background: white;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border-left: 4px solid;
        }
        
        .diff-item.addition { border-left-color: #38a169; }
        .diff-item.deletion { border-left-color: #e53e3e; }
        .diff-item.replacement { border-left-color: #3182ce; }
        
        .diff-header {
            background: #f7fafc;
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .diff-number {
            background: #4a5568;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .diff-type {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
        }
        
        .type-icon {
            font-size: 1.2rem;
        }
        
        .type-label {
            font-weight: 600;
            font-size: 1rem;
        }
        
        .diff-position {
            font-size: 0.9rem;
            color: #718096;
            background: #edf2f7;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
        }
        
        .diff-content {
            padding: 1.5rem;
        }
        
        .content-block {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .content-block:last-child {
            margin-bottom: 0;
        }
        
        .content-block.added {
            background: #f0fff4;
            border: 1px solid #9ae6b4;
        }
        
        .content-block.deleted {
            background: #fed7d7;
            border: 1px solid #feb2b2;
        }
        
        .content-label {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #4a5568;
        }
        
        .content-text {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            white-space: pre-wrap;
            word-break: break-word;
        }
        
        .content-comparison {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 1rem;
            align-items: center;
        }
        
        .comparison-arrow {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3182ce;
            text-align: center;
        }
        
        .content-type {
            background: #edf2f7;
            color: #4a5568;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-right: 0.5rem;
        }
        
        /* 报告底部 */
        .report-footer {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .footer-content p {
            margin-bottom: 0.5rem;
            opacity: 0.9;
        }
        
        .footer-stats {
            margin-top: 1rem;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                border-radius: 8px;
            }
            
            .report-header {
                padding: 2rem 1rem;
            }
            
            .report-header h1 {
                font-size: 2rem;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .summary-section,
            .differences-section {
                padding: 2rem 1rem;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .content-comparison {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
            
            .comparison-arrow {
                transform: rotate(90deg);
            }
        }
        """
    
    def _get_report_javascript(self) -> str:
        """获取报告JavaScript代码"""
        return """
        // 页面加载完成后的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 数字动画
            const numbers = document.querySelectorAll('.summary-number');
            numbers.forEach(function(number) {
                const finalValue = parseInt(number.textContent) || parseFloat(number.textContent) || 0;
                let currentValue = 0;
                const increment = finalValue / 50;
                const timer = setInterval(function() {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        currentValue = finalValue;
                        clearInterval(timer);
                    }
                    if (Number.isInteger(finalValue)) {
                        number.textContent = Math.floor(currentValue);
                    } else {
                        number.textContent = currentValue.toFixed(1) + '%';
                    }
                }, 20);
            });
            
            // 差异项淡入动画
            const diffItems = document.querySelectorAll('.diff-item');
            diffItems.forEach(function(item, index) {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                setTimeout(function() {
                    item.style.transition = 'all 0.5s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
        
        // 打印功能
        function printReport() {
            window.print();
        }
        """ 