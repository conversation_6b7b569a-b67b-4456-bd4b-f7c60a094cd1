from datetime import datetime
import json
import uuid
from app import db

class ProcessingTask(db.Model):
    """处理任务模型"""
    __tablename__ = 'processing_tasks'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.String(255), unique=True, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.Foreign<PERSON>ey('users.id'), nullable=False, index=True)
    image_id = db.Column(db.Integer, db.ForeignKey('images.id'), nullable=False, index=True)
    task_type = db.Column(db.String(50), nullable=False)  # remove_bg, beautify, id_photo等
    operations = db.Column(db.JSON, nullable=False)  # 处理操作配置
    status = db.Column(db.Enum('pending', 'processing', 'completed', 'failed', 'cancelled'), 
                      default='pending', index=True)
    progress = db.Column(db.SmallInteger, default=0)  # 处理进度(0-100)
    result_data = db.Column(db.JSON)  # 处理结果数据
    result_files = db.Column(db.JSON)  # 结果文件路径列表
    error_message = db.Column(db.Text)  # 错误信息
    processing_time = db.Column(db.Integer)  # 处理耗时(秒)
    credits_cost = db.Column(db.Integer, default=0)  # 消耗积分
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    started_at = db.Column(db.DateTime)  # 开始处理时间
    completed_at = db.Column(db.DateTime)  # 完成时间
    
    # 处理类型
    PROCESSING_TYPES = [
        ('background_replace', '背景替换'),
        ('id_photo', '证件照制作'),
        ('beauty_enhance', '美颜增强'),
        ('smart_crop', '智能裁剪'),
        ('other', '其他')
    ]
    
    def __init__(self, **kwargs):
        super(ProcessingTask, self).__init__(**kwargs)
        if not self.task_id:
            self.task_id = self.generate_task_id()
    
    @staticmethod
    def generate_task_id():
        """生成唯一任务ID"""
        return str(uuid.uuid4()).replace('-', '')
    
    def start_processing(self):
        """开始处理"""
        self.status = 'processing'
        self.started_at = datetime.utcnow()
        self.progress = 0
    
    def update_progress(self, progress, message=None):
        """更新进度"""
        self.progress = min(100, max(0, progress))
        if message:
            if not self.result_data:
                self.result_data = {}
            self.result_data['progress_message'] = message
    
    def complete_task(self, result_data=None, result_files=None):
        """完成任务"""
        self.status = 'completed'
        self.completed_at = datetime.utcnow()
        self.progress = 100
        
        if result_data:
            self.result_data = result_data
        
        if result_files:
            self.result_files = result_files
        
        # 计算处理时间
        if self.started_at:
            delta = self.completed_at - self.started_at
            self.processing_time = int(delta.total_seconds())
    
    def fail_task(self, error_message):
        """任务失败"""
        self.status = 'failed'
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        
        # 计算处理时间
        if self.started_at:
            delta = self.completed_at - self.started_at
            self.processing_time = int(delta.total_seconds())
    
    def cancel_task(self):
        """取消任务"""
        self.status = 'cancelled'
        self.completed_at = datetime.utcnow()
    
    def get_operations_dict(self):
        """获取操作配置字典"""
        if isinstance(self.operations, str):
            try:
                return json.loads(self.operations)
            except json.JSONDecodeError:
                return {}
        return self.operations or {}
    
    def set_operations(self, operations):
        """设置操作配置"""
        if isinstance(operations, dict):
            self.operations = operations
        else:
            self.operations = json.loads(operations) if isinstance(operations, str) else {}
    
    def get_result_data_dict(self):
        """获取结果数据字典"""
        if isinstance(self.result_data, str):
            try:
                return json.loads(self.result_data)
            except json.JSONDecodeError:
                return {}
        return self.result_data or {}
    
    def get_result_files_list(self):
        """获取结果文件列表"""
        if isinstance(self.result_files, str):
            try:
                return json.loads(self.result_files)
            except json.JSONDecodeError:
                return []
        return self.result_files or []
    
    def get_processing_duration(self):
        """获取处理时长(秒)"""
        if self.processing_time:
            return self.processing_time
        
        if self.started_at and self.completed_at:
            delta = self.completed_at - self.started_at
            return int(delta.total_seconds())
        
        if self.started_at and self.status == 'processing':
            delta = datetime.utcnow() - self.started_at
            return int(delta.total_seconds())
        
        return 0
    
    def is_finished(self):
        """检查任务是否已完成"""
        return self.status in ['completed', 'failed', 'cancelled']
    
    def is_success(self):
        """检查任务是否成功"""
        return self.status == 'completed'
    
    def get_credits_cost(self):
        """获取积分消耗 - 已取消积分限制"""
        # 积分限制已取消，所有操作都不消耗积分
        return 0  # 不消耗任何积分
    
    def to_dict(self, include_details=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'task_id': self.task_id,
            'task_type': self.task_type,
            'status': self.status,
            'progress': self.progress,
            'credits_cost': self.credits_cost,
            'created_at': self.created_at.isoformat(),
            'processing_duration': self.get_processing_duration()
        }
        
        if self.started_at:
            data['started_at'] = self.started_at.isoformat()
        
        if self.completed_at:
            data['completed_at'] = self.completed_at.isoformat()
        
        if self.error_message:
            data['error_message'] = self.error_message
        
        if include_details:
            data.update({
                'operations': self.get_operations_dict(),
                'result_data': self.get_result_data_dict(),
                'result_files': self.get_result_files_list(),
                'image_id': self.image_id,
                'user_id': self.user_id
            })
        
        return data
    
    def __repr__(self):
        return f'<ProcessingTask {self.task_id}: {self.task_type} ({self.status})>' 