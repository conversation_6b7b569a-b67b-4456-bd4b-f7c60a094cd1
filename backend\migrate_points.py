#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
积分字段数据库迁移脚本
"""

import sqlite3
import os
import sys

def execute_sql_file(db_path, sql_file):
    """执行SQL文件"""
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"正在执行SQL文件: {sql_file}")
        
        # 读取SQL文件内容
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 分割SQL语句（按分号分割）
        sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
        
        # 执行每个SQL语句
        for i, statement in enumerate(sql_statements, 1):
            if statement:
                try:
                    print(f"执行第 {i} 条SQL语句...")
                    cursor.execute(statement)
                    print(f"✓ 第 {i} 条SQL语句执行成功")
                except sqlite3.OperationalError as e:
                    if "duplicate column name" in str(e) or "already exists" in str(e):
                        print(f"⚠ 第 {i} 条SQL语句跳过（字段/表已存在）")
                    else:
                        print(f"✗ 第 {i} 条SQL语句执行失败: {e}")
                        raise
                except Exception as e:
                    print(f"✗ 第 {i} 条SQL语句执行失败: {e}")
                    raise
        
        # 提交事务
        conn.commit()
        print("✓ 数据库迁移完成")
        
    except Exception as e:
        print(f"✗ 数据库迁移失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def check_database_exists(db_path):
    """检查数据库文件是否存在"""
    if not os.path.exists(db_path):
        print(f"✗ 数据库文件不存在: {db_path}")
        return False
    return True

def main():
    """主函数"""
    # 数据库文件路径
    db_path = "photo_maker.db"
    sql_file = "../database/add_points_fields.sql"
    
    print("=== 积分字段数据库迁移脚本 ===")
    
    # 检查数据库文件
    if not check_database_exists(db_path):
        print("请确保数据库文件存在，或者先运行 init_database.py")
        sys.exit(1)
    
    # 检查SQL文件
    if not os.path.exists(sql_file):
        print(f"✗ SQL文件不存在: {sql_file}")
        sys.exit(1)
    
    try:
        # 执行迁移
        execute_sql_file(db_path, sql_file)
        print("\n=== 迁移成功完成 ===")
        print("现在可以启动后端服务了")
        
    except Exception as e:
        print(f"\n=== 迁移失败 ===")
        print(f"错误信息: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 