<template>
  <div class="business-card-manager">
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">名片管理</h1>
        <p class="page-description">智能名片管理系统，支持多模板设计、批量管理与高效搜索</p>
      </div>
      <div class="header-actions">
        <div class="filters">
          <el-row :gutter="12">
            <el-col :span="10">
              <el-select v-model="statusFilter" placeholder="按状态筛选" clearable>
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
                <el-option label="已归档" value="archived" />
              </el-select>
            </el-col>
            <el-col :span="14">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索名片标题"
                clearable
                @input="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
          </el-row>
        </div>
        <button class="create-btn primary" @click="createNewCard" :disabled="creating" style="margin-left: 16px;">
          <span class="btn-icon">✨</span>
          <span class="btn-text">创建名片</span>
        </button>
      </div>
    </div>

    <!-- 名片列表 -->
    <div class="content-card" v-loading="loading">
      <div class="section-header compact">
        <div class="section-title-left">我的名片</div>
        <div class="section-title-right">
          <label class="select-all-label">
            <input type="checkbox" :checked="isAllSelected" @change="toggleSelectAll" class="select-all-checkbox">
            <span class="checkbox-text">全选</span>
          </label>
          <button v-if="selectedCards.length > 0" class="batch-btn danger" @click="batchDelete">
            <span class="btn-icon"><svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M6 7v10a2 2 0 002 2h8a2 2 0 002-2V7"/><path d="M9 7V4a1 1 0 011-1h4a1 1 0 011 1v3"/><path d="M4 7h16"/></svg></span>
            <span class="btn-text">批量删除</span>
          </button>
        </div>
      </div>
      <div class="card-list">
        <el-row :gutter="20">
          <el-col 
            :span="6" 
            v-for="card in businessCards" 
            :key="card.id"
            style="margin-bottom: 20px;"
          >
            <div class="business-card-item" :data-card-id="card.id">
              <!-- 添加复选框 -->
              <div class="card-checkbox">
                <input 
                  type="checkbox" 
                  :checked="selectedCards.includes(card.id)"
                  @click.stop
                  @change="toggleCardSelection(card.id)"
                  class="card-checkbox-input"
                >
              </div>
              <div class="card-preview">
                <BusinessCardListPreview 
                  :business-card="card"
                  :template="card.template"
                  :key="`preview-${card.id}`"
                />
              </div>
              
              <div class="card-info">
                <h3>{{ card.title }}</h3>
                <p class="card-meta">
                  <el-tag :type="getStatusType(card.status)" size="small">
                    {{ getStatusText(card.status) }}
                  </el-tag>
                  <span class="update-time">
                    {{ formatDate(card.updated_at) }}
                  </span>
                </p>
                
                <div class="card-actions">
                  <el-button size="small" @click="editCard(card)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button size="small" @click="previewCard(card)">
                    <el-icon><ViewIcon /></el-icon>
                    预览
                  </el-button>
                  <el-dropdown @command="handleCardAction">
                    <el-button size="small">
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{action: 'qr', card}">
                          <el-icon><Document /></el-icon>
                          二维码
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'download', card}">
                          <el-icon><Download /></el-icon>
                          下载
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'print', card}">
                          <el-icon><Printer /></el-icon>
                          打印
                        </el-dropdown-item>
                        <el-dropdown-item 
                          :command="{action: 'publish', card}"
                          v-if="card.status === 'draft'"
                        >
                          <el-icon><Share /></el-icon>
                          发布
                        </el-dropdown-item>
                        <el-dropdown-item 
                          :command="{action: 'delete', card}"
                          divided
                          style="color: #f56c6c;"
                        >
                          <el-icon><Delete /></el-icon>
                          删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <div v-if="!loading && businessCards.length === 0" class="empty-state">
          <el-empty description="还没有名片，创建第一张名片吧！">
            <button class="create-btn primary large" @click="createNewCard" :disabled="creating">
              <span class="btn-icon">✨</span>
              <span class="btn-text">创建名片</span>
            </button>
          </el-empty>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination" v-if="pagination.total > 0">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.per_page"
        :page-sizes="[12, 24, 36, 48]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 二维码对话框 -->
    <el-dialog
      v-model="showQrDialog"
      title="名片二维码"
      width="400px"
    >
      <QRCodeViewer
        v-if="selectedCard"
        :business-card="selectedCard"
        @close="showQrDialog = false"
        @refresh="handleQrRefresh"
      />
    </el-dialog>

    <!-- 打印设置对话框 -->
    <el-dialog
      v-model="showPrintDialog"
      title="打印设置"
      width="800px"
    >
      <PrintLayoutSettings
        v-if="selectedCard"
        :business-card="selectedCard"
        @close="showPrintDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Edit, View as ViewIcon, Delete, Download, Document, 
  Printer, Share, Search, MoreFilled 
} from '@element-plus/icons-vue'
import api from '@/api'
import { handleApiError } from '@/utils/api'
import QRCodeViewer from '@/components/business-card/QRCodeViewer.vue'
import PrintLayoutSettings from '@/components/business-card/PrintLayoutSettings.vue'
import BusinessCardListPreview from '@/components/business-card/BusinessCardListPreview.vue'

export default {
  name: 'BusinessCardManager',
  
  components: {
    Edit, ViewIcon, Delete, Download, Document, 
    Printer, Share, Search, MoreFilled,
    QRCodeViewer, PrintLayoutSettings,
    BusinessCardListPreview
  },

  setup() {
    const router = useRouter()
    
    // 响应式数据
    const loading = ref(false)
    const businessCards = ref([])
    const statusFilter = ref('')
    const searchKeyword = ref('')
    const showQrDialog = ref(false)
    const showPrintDialog = ref(false)
    const selectedCard = ref(null)
    const templates = ref([])
    const creating = ref(false)
    const selectedCards = ref([])
    const isAllSelected = computed(() => businessCards.value.length > 0 && selectedCards.value.length === businessCards.value.length)
    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedCards.value = []
      } else {
        selectedCards.value = businessCards.value.map(card => card.id)
      }
    }
    const batchDelete = async () => {
      if (selectedCards.value.length === 0) {
        ElMessage.warning('请选择要删除的名片')
        return
      }
      
      try {
        await ElMessageBox.confirm(
          `确定要删除选中的 ${selectedCards.value.length} 个名片吗？此操作不可恢复。`,
          '确认批量删除',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )
        
        const response = await api.businessCards.batchDelete({
          ids: selectedCards.value
        })
        
        if (response.data.success) {
          ElMessage.success(response.data.message || `成功删除 ${selectedCards.value.length} 个名片`)
          selectedCards.value = []
          await loadBusinessCards()
        }
      } catch (error) {
        if (error !== 'cancel') {
          handleApiError(error, '批量删除失败')
        }
      }
    }
    
    // 切换单个名片选择状态
    const toggleCardSelection = (cardId) => {
      const index = selectedCards.value.indexOf(cardId)
      if (index > -1) {
        selectedCards.value.splice(index, 1)
      } else {
        selectedCards.value.push(cardId)
      }
    }
    
    const pagination = reactive({
      page: 1,
      per_page: 12,
      total: 0,
      pages: 1
    })

    // 加载模板列表
    const loadTemplates = async () => {
      const response = await api.templates.getBusinessCardTemplates()
      if (response.data.success) {
        templates.value = response.data.data.templates
      }
    }

    // 加载名片列表
    const loadBusinessCards = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          per_page: pagination.per_page
        }
        if (statusFilter.value) {
          params.status = statusFilter.value
        }
        if (searchKeyword.value) {
          params.search = searchKeyword.value
        }
        const response = await api.businessCards.getList(params)
        if (response.data.success) {
          // 补全每个card的template字段，确保预览能正确渲染
          const cards = response.data.data.business_cards
          cards.forEach(card => {
            // 强化检查：如果 template 不存在，或是一个没有ID的空对象，则尝试通过 template_id 从列表补全
            if ((!card.template || !card.template.id) && card.template_id && templates.value.length > 0) {
              card.template = templates.value.find(t => t.id === card.template_id)
            }
          })
          businessCards.value = cards
          Object.assign(pagination, response.data.data.pagination)
        }
      } catch (error) {
        handleApiError(error, '加载名片列表失败')
      } finally {
        loading.value = false
      }
    }

    // 获取预览样式
    const getPreviewStyle = (card) => {
      const template = card.template
      if (!template || !template.default_colors) return {}
      
      const colors = template.default_colors
      return {
        backgroundColor: colors.primary || '#f5f5f5',
        color: colors.text || '#333'
      }
    }

    // 获取状态类型
    const getStatusType = (status) => {
      const types = {
        draft: 'info',
        published: 'success',
        archived: 'warning'
      }
      return types[status] || 'info'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const texts = {
        draft: '草稿',
        published: '已发布',
        archived: '已归档'
      }
      return texts[status] || status
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    // 编辑名片
    const editCard = (card) => {
      router.push(`/business-card/edit/${card.id}`)
    }

    // 预览名片
    const previewCard = (card) => {
      router.push(`/business-card/preview/${card.id}`)
    }

    // 处理名片操作
    const handleCardAction = async ({ action, card }) => {
      selectedCard.value = card
      
      switch (action) {
        case 'qr':
          showQrDialog.value = true
          break
          
        case 'download':
          await downloadCard(card)
          break
          
        case 'print':
          showPrintDialog.value = true
          break
          
        case 'publish':
          await publishCard(card)
          break
          
        case 'delete':
          await deleteCard(card)
          break
      }
    }

    // 发布名片
    const publishCard = async (card) => {
      try {
        await api.businessCards.update(card.id, {
          status: 'published'
        })
        
        ElMessage.success('名片已发布')
        loadBusinessCards()
      } catch (error) {
        handleApiError(error, '发布失败')
      }
    }

    // 删除名片
    const deleteCard = async (card) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除名片"${card.title}"吗？此操作不可恢复。`,
          '确认删除',
          {
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )
        
        await api.businessCards.delete(card.id)
        ElMessage.success('删除成功')
        loadBusinessCards()
      } catch (error) {
        if (error !== 'cancel') {
          handleApiError(error, '删除失败')
        }
      }
    }

    // 下载名片
    const downloadCard = async (card) => {
      try {
        // 获取名片预览元素
        const cardPreviewElement = document.querySelector(`[data-card-id="${card.id}"] .business-card-list-preview`)
        if (!cardPreviewElement) {
          throw new Error('名片预览元素未找到')
        }

        // 导入工具函数
        const { elementToImage, downloadImage } = await import('@/utils/htmlToImage.js')
        
        // 转换为图片
        const blob = await elementToImage(cardPreviewElement, {
          scale: 3, // 高清晰度
          backgroundColor: '#ffffff',
          width: 900,
          height: 540
        })
        
        // 下载
        const filename = `${card.title}.png`
        downloadImage(blob, filename)
        
        ElMessage.success('下载成功')
      } catch (error) {
        console.error('下载失败:', error)
        ElMessage.error('下载失败，请重试')
      }
    }

    // 处理搜索
    const handleSearch = () => {
      // 这里可以实现搜索逻辑
      // 目前简化处理，在前端过滤
      pagination.page = 1
      loadBusinessCards()
    }

    // 分页处理
    const handleSizeChange = (size) => {
      pagination.per_page = size
      pagination.page = 1
      loadBusinessCards()
    }

    const handleCurrentChange = (page) => {
      pagination.page = page
      loadBusinessCards()
    }

    // 创建新名片
    const createNewCard = async () => {
      creating.value = true
      try {
        if (templates.value.length === 0) {
          await loadTemplates()
        }
        if (templates.value.length === 0) {
          ElMessage.error('没有可用的名片模板，无法创建。')
          return
        }

        const defaultTemplate = templates.value[0]
        const response = await api.businessCards.create({
          title: '未命名名片',
          template_id: defaultTemplate.id,
          card_data: {},
          style_config: defaultTemplate.default_colors || {}
        })

        if (response.data.success) {
          const newCard = response.data.data.business_card
          ElMessage.success('创建成功，正在跳转到编辑器...')
          router.push(`/business-card/edit/${newCard.id}`)
        }
      } catch (error) {
        handleApiError(error, '创建名片失败')
      } finally {
        creating.value = false
      }
    }

    // 监听状态筛选变化
    watch([statusFilter, searchKeyword], () => {
      pagination.page = 1
      loadBusinessCards()
    })

    // 生命周期
    onMounted(async () => {
      await loadTemplates()
      await loadBusinessCards()
    })

    const handleQrRefresh = async () => {
      await loadBusinessCards();
      // 可选：刷新selectedCard为最新数据
      if (selectedCard.value && selectedCard.value.id) {
        const res = await api.businessCards.getDetail(selectedCard.value.id);
        if (res.data && res.data.data && res.data.data.business_card) {
          Object.assign(selectedCard.value, res.data.data.business_card);
        }
      }
    };

    return {
      loading,
      businessCards,
      statusFilter,
      searchKeyword,
      showQrDialog,
      showPrintDialog,
      selectedCard,
      pagination,
      loadBusinessCards,
      getPreviewStyle,
      getStatusType,
      getStatusText,
      formatDate,
      editCard,
      previewCard,
      handleCardAction,
      publishCard,
      deleteCard,
      downloadCard,
      handleSearch,
      handleSizeChange,
      handleCurrentChange,
      creating,
      createNewCard,
      handleQrRefresh,
      selectedCards,
      isAllSelected,
      toggleSelectAll,
      batchDelete,
      toggleCardSelection,
    }
  }
}
</script>

<style scoped>
.business-card-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 0 48px 0;
  background: #f8fafc;
  min-height: 100vh;
}

.page-header {
  background: linear-gradient(90deg, #4A90E2 0%, #D6E4F0 100%);
  color: #fff;
  border-radius: 0 0 24px 24px;
  padding: 36px 32px 28px 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 24px #4A90E222;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-description {
  font-size: 1.1rem;
  color: rgba(255,255,255,0.9);
  margin: 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filters {
  min-width: 300px;
}

.business-card-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.business-card-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-preview {
  margin-top: 8px;
}

.card-content {
  width: 120px;
  height: 72px;
  border-radius: 4px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 10px;
  line-height: 1.2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-name {
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 2px;
}

.card-title {
  font-size: 9px;
  opacity: 0.8;
  margin-bottom: 2px;
}

.card-company {
  font-size: 8px;
  opacity: 0.7;
}

.card-info {
  padding: 15px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-info h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 12px;
  color: #909399;
}

.update-time {
  font-size: 11px;
}

.card-actions {
  display: flex;
  gap: 8px;
  margin-top: auto;
}

.card-actions .el-button {
  flex: 1;
}

.pagination {
  margin-top: 30px;
  text-align: center;
}

.content-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px #4A90E211;
  padding: 32px;
  margin-bottom: 24px;
}

.card-list {
  /* 保持原有布局 */
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
  background: #f4f8ff;
  border-radius: 32px;
  box-shadow: 0 8px 40px #4A90E222;
  margin-bottom: 32px;
}

.section-header.compact {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin: 0 0 12px 0;
  padding: 0;
  text-align: left;
}
.section-title-left {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2d3748;
}
.section-title-right {
  display: flex;
  align-items: center;
  gap: 12px;
}
.select-all-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 1rem;
  color: #2d3748;
}
.select-all-checkbox {
  width: 16px;
  height: 16px;
  margin-right: 2px;
}
.batch-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}
.batch-btn.danger {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
}
.batch-btn.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(245, 101, 101, 0.4);
}
.batch-btn .btn-icon {
  display: flex;
  align-items: center;
}
.batch-btn .btn-text {
  margin-left: 2px;
}

.card-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
}

.card-checkbox-input {
  width: 18px;
  height: 18px;
  accent-color: #409eff;
  cursor: pointer;
}

/* 按钮样式 - 参考简历管理页面 */
.create-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.create-btn.primary {
  background: linear-gradient(90deg, #F5C242 0%, #FFD700 100%);
  color: #333;
  box-shadow: 0 2px 12px #4A90E222;
}

.create-btn.large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

.create-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #FFD700 0%, #F5C242 100%);
  color: #222;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px #4A90E244;
}

.create-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.create-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.create-btn:hover:not(:disabled)::before {
  transform: translateX(0);
}

.btn-icon {
  font-size: 1.1em;
}

.btn-text {
  font-weight: 600;
}

@media (max-width: 768px) {
  .business-card-manager {
    padding: 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .filters .el-row .el-col {
    margin-bottom: 10px;
  }
}
</style>