import requests
import json
import base64
import traceback
import sys
import os

# 添加项目路径
sys.path.insert(0, 'e:\\photoNew\\backend')

def test_crop_with_logging():
    """详细测试裁剪API并捕获日志"""
    
    print("=== 详细裁剪API调试 ===")
    
    try:
        # 测试服务器连接
        print("1. 测试服务器连接...")
        health_response = requests.get('http://localhost:5000/api/common/health', timeout=5)
        print(f"   健康检查: {health_response.status_code}")
        
        # 创建真实测试图片
        print("2. 创建测试图片...")
        from PIL import Image
        import numpy as np
        import io
        
        # 创建更真实的测试图片（人脸模拟）
        img = Image.new('RGB', (600, 800), color='lightgray')
        
        # 添加人脸区域（椭圆形）
        import cv2
        img_cv = np.array(img)
        center = (300, 400)
        axes = (80, 100)
        cv2.ellipse(img_cv, center, axes, 0, 0, 360, (200, 150, 130), -1)
        
        # 添加眼睛
        cv2.circle(img_cv, (280, 380), 8, (0, 0, 0), -1)
        cv2.circle(img_cv, (320, 380), 8, (0, 0, 0), -1)
        
        # 转换为PIL
        img = Image.fromarray(img_cv)
        
        # 转换为base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        buffer.seek(0)
        img_base64 = base64.b64encode(buffer.read()).decode()
        image_data = f"data:image/png;base64,{img_base64}"
        
        print(f"   图片大小: {len(image_data)} 字符")
        
        # 测试请求数据
        test_data = {
            "image_data": image_data,
            "crop_mode": "auto",
            "template": {
                "width": 413,
                "height": 626,
                "name": "标准一寸"
            },
            "auto_settings": {
                "face_ratio": 0.55,
                "center_face": True
            }
        }
        
        print("3. 发送裁剪请求...")
        print("   请求数据预览:")
        print(f"   - crop_mode: {test_data['crop_mode']}")
        print(f"   - template: {test_data['template']['name']} ({test_data['template']['width']}x{test_data['template']['height']})")
        
        try:
            response = requests.post(
                'http://localhost:5000/api/processing/unified-crop',
                json=test_data,
                timeout=30
            )
            
            print(f"   响应状态: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            
            if response.status_code == 500:
                print("   ❌ 服务器错误详情:")
                print(f"   响应内容: {response.text}")
                
                # 尝试获取更详细的错误
                if 'application/json' in response.headers.get('content-type', ''):
                    try:
                        error_data = response.json()
                        print(f"   错误详情: {error_data}")
                    except:
                        print(f"   原始响应: {response.text}")
                        
            elif response.status_code == 200:
                print("   ✅ 裁剪成功!")
                result = response.json()
                print(f"   裁剪信息: {result.get('crop_info', {})}")
            else:
                print(f"   ⚠️ 意外状态码: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except requests.exceptions.Timeout:
            print("   ⏰ 请求超时")
        except requests.exceptions.ConnectionError as e:
            print(f"   ❌ 连接错误: {e}")
            
    except ImportError as e:
        print(f"   缺少依赖: {e}")
        print("   尝试基础测试...")
        
        # 基础base64测试
        img = Image.new('RGB', (100, 100), color='red')
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.read()).decode()
        
        test_data = {
            "image_data": f"data:image/png;base64,{img_base64}",
            "crop_mode": "auto",
            "template": {"width": 413, "height": 626}
        }
        
        try:
            response = requests.post(
                'http://localhost:5000/api/processing/unified-crop',
                json=test_data,
                timeout=10
            )
            print(f"   基础测试响应: {response.status_code}")
            
        except Exception as e2:
            print(f"   基础测试也失败: {e2}")
            
    except Exception as e:
        print(f"   测试失败: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    test_crop_with_logging()