@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    智能证件照制作系统启动脚本
echo ========================================
echo.

echo 🚀 正在启动系统...
echo.

echo 📋 系统信息:
echo    - 管理员账号: admin / admin123
echo    - 测试账号: test / test123
echo    - 前端地址: http://localhost:8102
echo    - 后端地址: http://localhost:5000
echo.

echo 🔧 启动后端服务...
cd backend
start "后端服务" cmd /k "python run.py"
cd ..

echo ⏳ 等待后端服务启动...
timeout /t 3 /nobreak >nul

echo 🎨 启动前端服务...
cd frontend
start "前端服务" cmd /k "npm run serve"
cd ..

echo.
echo ✅ 系统启动完成!
echo.
echo 📖 使用说明:
echo    1. 等待前端编译完成（约1-2分钟）
echo    2. 浏览器访问: http://localhost:8102
echo    3. 使用管理员账号登录: admin / admin123
echo    4. 开始使用系统功能
echo.
echo 🛑 关闭系统: 关闭两个命令行窗口即可
echo.
pause
