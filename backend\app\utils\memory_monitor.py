#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存监控工具 - 用于跟踪和优化内存使用
"""

import psutil
import os
import time
import logging
import gc
from typing import Dict, Any, Optional
from functools import wraps

logger = logging.getLogger(__name__)

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self):
        self.process = psutil.Process(os.getpid())
        self.start_time = time.time()
        self.memory_history = []
        self.max_memory_usage = 0
        self.warning_threshold = 0.8  # 80%内存使用率警告
    
    def get_memory_info(self) -> Dict[str, Any]:
        """获取当前内存信息"""
        try:
            # 系统内存信息
            system_memory = psutil.virtual_memory()
            
            # 进程内存信息
            process_memory = self.process.memory_info()
            
            # Python对象信息
            python_objects = len(gc.get_objects())
            
            memory_info = {
                'timestamp': time.time(),
                'system': {
                    'total': system_memory.total,
                    'available': system_memory.available,
                    'used': system_memory.used,
                    'percent': system_memory.percent
                },
                'process': {
                    'rss': process_memory.rss,  # 物理内存
                    'vms': process_memory.vms,  # 虚拟内存
                    'percent': self.process.memory_percent()
                },
                'python': {
                    'objects': python_objects,
                    'garbage': len(gc.garbage)
                }
            }
            
            # 更新最大内存使用
            current_usage = process_memory.rss
            if current_usage > self.max_memory_usage:
                self.max_memory_usage = current_usage
            
            # 记录历史
            self.memory_history.append(memory_info)
            
            # 保持历史记录在合理范围内
            if len(self.memory_history) > 100:
                self.memory_history = self.memory_history[-50:]
            
            return memory_info
            
        except Exception as e:
            logger.error(f"获取内存信息失败: {e}")
            return {}
    
    def log_memory_usage(self, context: str = ""):
        """记录内存使用情况"""
        memory_info = self.get_memory_info()
        if not memory_info:
            return
        
        system_percent = memory_info['system']['percent']
        process_rss_mb = memory_info['process']['rss'] / 1024 / 1024
        python_objects = memory_info['python']['objects']
        
        log_message = f"内存使用 [{context}]: 系统 {system_percent:.1f}%, 进程 {process_rss_mb:.1f}MB, Python对象 {python_objects}"
        
        # 根据使用率选择日志级别
        if system_percent > 90:
            logger.critical(log_message)
        elif system_percent > 80:
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        # 检查是否需要强制垃圾回收
        if system_percent > self.warning_threshold:
            self.force_cleanup()
    
    def force_cleanup(self):
        """强制清理内存"""
        try:
            # 强制垃圾回收
            collected = gc.collect()
            
            # 清理Python对象
            gc.collect(2)  # 深度清理
            
            logger.info(f"强制内存清理完成，回收对象: {collected}")
            
        except Exception as e:
            logger.error(f"强制清理失败: {e}")
    
    def monitor_function(self, context: str = ""):
        """函数装饰器，用于监控函数执行前后的内存使用"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # 执行前记录内存
                self.log_memory_usage(f"{context} 开始")
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    logger.error(f"函数执行失败: {e}")
                    raise
                finally:
                    # 执行后记录内存
                    self.log_memory_usage(f"{context} 结束")
                    # 执行后清理
                    self.force_cleanup()
            
            return wrapper
        return decorator
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """获取内存使用摘要"""
        memory_info = self.get_memory_info()
        if not memory_info:
            return {}
        
        return {
            'current_usage_mb': memory_info['process']['rss'] / 1024 / 1024,
            'max_usage_mb': self.max_memory_usage / 1024 / 1024,
            'system_percent': memory_info['system']['percent'],
            'python_objects': memory_info['python']['objects'],
            'uptime_seconds': time.time() - self.start_time
        }
    
    def check_memory_health(self) -> Dict[str, Any]:
        """检查内存健康状态"""
        memory_info = self.get_memory_info()
        if not memory_info:
            return {'status': 'unknown', 'message': '无法获取内存信息'}
        
        system_percent = memory_info['system']['percent']
        process_rss_mb = memory_info['process']['rss'] / 1024 / 1024
        
        health_status = {
            'status': 'healthy',
            'system_percent': system_percent,
            'process_mb': process_rss_mb,
            'warnings': []
        }
        
        # 检查系统内存
        if system_percent > 90:
            health_status['status'] = 'critical'
            health_status['warnings'].append(f"系统内存使用率过高: {system_percent:.1f}%")
        elif system_percent > 80:
            health_status['status'] = 'warning'
            health_status['warnings'].append(f"系统内存使用率较高: {system_percent:.1f}%")
        
        # 检查进程内存
        if process_rss_mb > 1000:  # 超过1GB
            health_status['status'] = 'warning'
            health_status['warnings'].append(f"进程内存使用较高: {process_rss_mb:.1f}MB")
        
        return health_status

# 全局内存监控器实例
memory_monitor = MemoryMonitor()

def log_memory_usage(context: str = ""):
    """便捷函数：记录内存使用"""
    memory_monitor.log_memory_usage(context)

def monitor_memory(context: str = ""):
    """便捷装饰器：监控函数内存使用"""
    return memory_monitor.monitor_function(context)

def get_memory_summary() -> Dict[str, Any]:
    """便捷函数：获取内存摘要"""
    return memory_monitor.get_memory_summary()

def check_memory_health() -> Dict[str, Any]:
    """便捷函数：检查内存健康"""
    return memory_monitor.check_memory_health()

def force_memory_cleanup():
    """便捷函数：强制内存清理"""
    memory_monitor.force_cleanup() 