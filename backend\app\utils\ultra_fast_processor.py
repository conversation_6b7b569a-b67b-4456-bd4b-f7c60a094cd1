#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超快速图片处理算法 - 专门解决超时问题
"""

import cv2
import numpy as np
from PIL import Image
import time
import threading
import queue

def ultra_fast_background_replace_v2(img_cv, bg_color):
    """超快速背景替换算法 - 2秒内完成"""
    height, width = img_cv.shape[:2]
    
    # 快速人脸检测（降低精度换取速度）
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
    
    # 使用更宽松的参数进行快速检测
    faces = face_cascade.detectMultiScale(gray, scaleFactor=1.2, minNeighbors=2, minSize=(50, 50))
    
    if len(faces) > 0:
        # 选择最大人脸
        face = max(faces, key=lambda f: f[2] * f[3])
        fx, fy, fw, fh = face
        
        # 创建简单蒙版（矩形区域）
        mask = np.zeros((height, width), dtype=np.uint8)
        
        # 估算人体区域（简化计算）
        body_width = int(fw * 2.5)
        body_height = int(fh * 5.5)
        
        center_x = fx + fw // 2
        center_y = fy + fh // 2
        
        x1 = max(0, center_x - body_width // 2)
        y1 = max(0, fy - int(fh * 0.3))
        x2 = min(width, center_x + body_width // 2)
        y2 = min(height, y1 + body_height)
        
        # 创建椭圆形蒙版（更自然）
        center = (center_x, (y1 + y2) // 2)
        axes = (body_width // 2, body_height // 2)
        cv2.ellipse(mask, center, axes, 0, 0, 360, 255, -1)
        
        # 快速边缘平滑
        mask = cv2.GaussianBlur(mask, (5, 5), 2)
        mask = mask.astype(np.float32) / 255.0
        
    else:
        # 无人脸时使用中心区域
        mask = np.zeros((height, width), dtype=np.float32)
        center_region = (height // 4, width // 4, 3 * height // 4, 3 * width // 4)
        mask[center_region[0]:center_region[2], center_region[1]:center_region[3]] = 1.0
        mask = cv2.GaussianBlur(mask, (21, 21), 10)
    
    # 快速合成
    bg_img = np.full_like(img_cv, bg_color[::-1])  # BGR格式
    
    # 三通道蒙版
    mask_3d = np.stack([mask] * 3, axis=2)
    result = (img_cv * mask_3d + bg_img * (1 - mask_3d)).astype(np.uint8)
    
    # 转换回PIL格式
    result_rgb = cv2.cvtColor(result, cv2.COLOR_BGR2RGB)
    return Image.fromarray(result_rgb)

def timeout_protected_process(img, operations, max_time=10):
    """带超时保护的图片处理"""
    result_queue = queue.Queue()
    
    def process_worker():
        try:
            result = process_image_ultra_fast(img, operations)
            result_queue.put(('success', result))
        except Exception as e:
            result_queue.put(('error', str(e)))
    
    # 启动处理线程
    thread = threading.Thread(target=process_worker)
    thread.daemon = True
    thread.start()
    
    # 等待结果
    thread.join(timeout=max_time)
    
    if thread.is_alive():
        # 超时，返回简单处理结果
        return simple_fallback_process(img, operations)
    
    try:
        status, result = result_queue.get_nowait()
        if status == 'success':
            return result
        else:
            return simple_fallback_process(img, operations)
    except:
        return simple_fallback_process(img, operations)

def process_image_ultra_fast(img, operations):
    """超快速图片处理"""
    for operation in operations:
        op_type = operation.get('type')
        params = operation.get('params', {})
        
        if op_type in ['replace_background', 'background_replace']:
            # 转换为OpenCV格式
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            
            # 获取背景色
            color = params.get('color', 'white')
            color_map = {
                'white': (255, 255, 255),
                'blue': (70, 130, 180),
                'red': (220, 20, 60),
                'gray': (128, 128, 128),
                'lightblue': (173, 216, 230),
                'lightgray': (211, 211, 211),
                'pink': (255, 192, 203),
                'yellow': (255, 255, 224),
                'green': (144, 238, 144),
                'darkblue': (44, 62, 80)
            }
            bg_color = color_map.get(color, (255, 255, 255))
            
            # 使用超快速算法
            img = ultra_fast_background_replace_v2(img_cv, bg_color)
        
        elif op_type == 'crop':
            # 快速裁剪
            width, height = img.size
            target_ratio = params.get('ratio', '1:1')
            
            if target_ratio == '1:1':
                size = min(width, height)
                left = (width - size) // 2
                top = (height - size) // 2
                img = img.crop((left, top, left + size, top + size))
            
        elif op_type == 'beautify':
            # 简单美化（跳过复杂处理）
            pass
    
    return img

def simple_fallback_process(img, operations):
    """简单的备用处理方法"""
    # 如果所有方法都失败，至少返回一个处理过的图片
    for operation in operations:
        if operation.get('type') in ['replace_background', 'background_replace']:
            # 创建简单的白色背景
            width, height = img.size
            white_bg = Image.new('RGB', (width, height), 'white')
            
            # 简单的中心区域保留
            mask = Image.new('L', (width, height), 0)
            from PIL import ImageDraw
            draw = ImageDraw.Draw(mask)
            margin = min(width, height) // 8
            draw.ellipse([margin, margin, width-margin, height-margin], fill=255)
            
            # 合成
            img = Image.composite(img, white_bg, mask)
    
    return img
