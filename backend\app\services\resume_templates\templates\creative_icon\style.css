/* 创意图标模板样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.resume-container {
    max-width: 210mm;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    min-height: 297mm;
}

/* 头部样式 */
.resume-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.resume-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="white" opacity="0.1"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0); }
    100% { transform: translateY(-100px); }
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
}

.profile-section {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}



.profile-info .name {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
}

.profile-info .title {
    font-size: 1.3rem;
    font-weight: 300;
    opacity: 0.9;
}

.contact-section {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 1rem;
}

.contact-item i {
    width: 20px;
    text-align: center;
    color: rgba(255,255,255,0.8);
}

/* 主要内容样式 */
.resume-main {
    padding: 2rem;
}

.resume-section {
    margin-bottom: 2.5rem;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 1.4rem;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #667eea;
    position: relative;
}

.section-title i {
    font-size: 1.2rem;
    color: #667eea;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: #764ba2;
}

.section-content {
    padding-left: 2rem;
}

/* 个人总结样式 */
.summary-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    text-align: justify;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

/* 工作经历样式 */
.experience-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
    position: relative;
}

.experience-item::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 1.5rem;
    width: 8px;
    height: 8px;
    background: #667eea;
    border-radius: 50%;
}

.experience-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.experience-title h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.3rem;
}

.experience-title h5 {
    font-size: 1.1rem;
    color: #667eea;
    font-weight: 500;
}

.experience-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.experience-date i {
    color: #667eea;
}

.experience-description {
    margin-bottom: 1rem;
}

.experience-description p {
    color: #555;
    line-height: 1.7;
}

.experience-achievements ul {
    list-style: none;
}

.experience-achievements li {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    margin-bottom: 0.5rem;
    color: #555;
}

.experience-achievements li i {
    color: #28a745;
    margin-top: 0.2rem;
}

/* 教育背景样式 */
.education-item {
    margin-bottom: 1.5rem;
    padding: 1.2rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #764ba2;
}

.education-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.8rem;
}

.education-title h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.3rem;
}

.education-title h5 {
    font-size: 1rem;
    color: #764ba2;
    font-weight: 500;
}

.education-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.education-date i {
    color: #764ba2;
}

.education-gpa {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #f39c12;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.education-description p {
    color: #555;
    line-height: 1.6;
}

/* 项目经验样式 */
.project-item {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #e74c3c;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.project-title h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.3rem;
}

.project-title h5 {
    font-size: 1rem;
    color: #e74c3c;
    font-weight: 500;
}

.project-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

.project-date i {
    color: #e74c3c;
}

.project-description {
    margin-bottom: 1rem;
}

.project-description p {
    color: #555;
    line-height: 1.7;
}

.project-technologies {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.project-technologies i {
    color: #17a2b8;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tag {
    background: #17a2b8;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.project-achievements ul {
    list-style: none;
}

.project-achievements li {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    margin-bottom: 0.5rem;
    color: #555;
}

.project-achievements li i {
    color: #f39c12;
    margin-top: 0.2rem;
}

/* 技能特长样式 */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.skill-item {
    background: white;
    padding: 1.2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid #28a745;
}

.skill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.8rem;
}

.skill-name {
    font-weight: 600;
    color: #333;
    font-size: 1.1rem;
}

.skill-level {
    background: #28a745;
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.skill-progress {
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.skill-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 证书资质样式 */
.certification-item {
    margin-bottom: 1.5rem;
    padding: 1.2rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #6f42c1;
}

.certification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.8rem;
}

.certification-title h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.3rem;
}

.certification-title h5 {
    font-size: 1rem;
    color: #6f42c1;
    font-weight: 500;
}

.certification-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    font-size: 0.9rem;
}

.certification-date i {
    color: #6f42c1;
}

.certification-id {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #555;
    font-size: 0.9rem;
}

.certification-id i {
    color: #6f42c1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }
    
    .profile-section {
        flex-direction: column;
        text-align: center;
    }
    
    .contact-section {
        align-items: center;
    }
    
    .experience-header,
    .education-header,
    .project-header,
    .certification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.8rem;
    }
    
    .skills-grid {
        grid-template-columns: 1fr;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
    }
    
    .resume-container {
        box-shadow: none;
        margin: 0;
    }
    
    .resume-header::before {
        display: none;
    }
    
    .experience-item,
    .project-item,
    .skill-item {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
    
    .resume-section {
        break-inside: avoid;
    }
}