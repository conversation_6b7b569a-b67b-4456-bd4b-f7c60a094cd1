<template>
  <div class="card-wrapper creative-design" :style="styleVariables">
    <div class="background-pattern"></div>
    <div class="main-content">
      <div class="identity">
        <h2 class="name" :style="nameStyle">{{ cardData.name || '姓名' }}</h2>
        <p class="title" v-if="cardData.title" :style="titleStyle">{{ cardData.title }}</p>
      </div>
      <div class="divider"></div>
      <div class="contact-info">
        <div class="contact-item" v-if="cardData.phone" :style="contactStyle">
          <span class="icon">📞</span>
          <span>{{ cardData.phone }}</span>
        </div>
        <div class="contact-item" v-if="cardData.email" :style="contactStyle">
          <span class="icon">✉️</span>
          <span>{{ cardData.email }}</span>
        </div>
      </div>
      <div class="company-block">
        <span class="company-name" :style="companyStyle">{{ cardData.company || '公司名称' }}</span>
      </div>
    </div>
    <div class="qr-section" v-if="qrCodeUrl">
      <img :src="qrCodeUrl" alt="QR Code" class="qr-img" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
const props = defineProps({
  cardData: { type: Object, required: true },
  styleConfig: { type: Object, default: () => ({}) },
  qrCodeUrl: { type: String, default: '' }
})
const styleVariables = computed(() => {
  const config = props.styleConfig || {};
  const multiplier = (config.fontSize || 100) / 100;
  // 动态生成渐变背景
  const primary = config.primary || '#667eea';
  const secondary = config.secondary || '#764ba2';
  const backgroundGradient = `linear-gradient(135deg, ${primary} 0%, ${secondary} 100%)`;
  return {
    '--font-family': config.fontFamily || 'PingFang SC, Microsoft YaHei, sans-serif',
    '--background-gradient': backgroundGradient,
    '--primary-color': primary,
    '--secondary-color': secondary,
    '--text-color': config.text || '#ffffff',
    '--light-text-color': config.light || 'rgba(255,255,255,0.7)',
    '--font-size-name': `${90 * multiplier}px`,
    '--font-size-title': `${45 * multiplier}px`,
    '--font-size-company': `${55 * multiplier}px`,
    '--font-size-contact': `${40 * multiplier}px`,
    '--name-color': config.nameColor || 'var(--primary-color)',
    '--company-color': config.companyColor || 'var(--secondary-color)',
    '--contact-color': config.contactColor || 'var(--light-text-color)',
    '--name-weight': config.nameFontStyle === 'bold' ? 'bold' : 'normal',
    '--company-weight': config.companyFontStyle === 'bold' ? 'bold' : 'normal',
    '--contact-weight': config.contactFontStyle === 'bold' ? 'bold' : 'normal',
    '--divider-color': config.dividerColor || 'rgba(255,255,255,0.15)',
  };
});
const nameStyle = computed(() => ({
  color: props.styleConfig.nameColor || 'var(--primary-color)',
  fontWeight: props.styleConfig.nameFontStyle === 'bold' ? 'bold' : 'normal',
  fontStyle: props.styleConfig.nameFontStyle === 'italic' ? 'italic' : 'normal',
}));
const companyStyle = computed(() => ({
  color: props.styleConfig.companyColor || 'var(--secondary-color)',
  fontWeight: props.styleConfig.companyFontStyle === 'bold' ? 'bold' : 'normal',
  fontStyle: props.styleConfig.companyFontStyle === 'italic' ? 'italic' : 'normal',
}));
const titleStyle = computed(() => ({
  color: props.styleConfig.text || 'var(--text-color)',
  fontWeight: props.styleConfig.textFontStyle === 'bold' ? 'bold' : 'normal',
  fontStyle: props.styleConfig.textFontStyle === 'italic' ? 'italic' : 'normal',
}));
const contactStyle = computed(() => ({
  color: props.styleConfig.light || 'var(--light-text-color)',
  fontWeight: props.styleConfig.contactFontStyle === 'bold' ? 'bold' : 'normal',
  fontStyle: props.styleConfig.contactFontStyle === 'italic' ? 'italic' : 'normal',
}));
</script>

<style scoped>
.card-wrapper.creative-design {
  width: 100%;
  height: 100%;
  background: var(--background-gradient);
  color: var(--text-color);
  font-family: var(--font-family);
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
.background-pattern {
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='20' cy='20' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.2;
  pointer-events: none;
}
.main-content {
  position: relative;
  z-index: 2;
  width: 90%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.identity .name {
  font-size: var(--font-size-name);
  font-weight: var(--name-weight);
  color: var(--name-color);
  margin: 0 0 8px 0;
}
.identity .title {
  font-size: var(--font-size-title);
  color: var(--text-color);
  margin: 0 0 12px 0;
  opacity: 0.9;
}
.divider {
  width: 100%;
  height: 1px;
  background-color: var(--divider-color);
  margin: 16px 0;
  opacity: 0.7;
}
.contact-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 32px;
  margin-bottom: 12px;
}
.contact-item {
  display: flex;
  align-items: center;
  font-size: var(--font-size-contact);
  color: var(--contact-color);
  font-weight: var(--contact-weight);
}
.contact-item .icon {
  margin-right: 8px;
  font-size: 1.1em;
}
.company-block {
  margin-top: 12px;
}
.company-name {
  font-size: var(--font-size-company);
  color: var(--company-color);
  font-weight: var(--company-weight);
}
.qr-section {
  position: absolute;
  bottom: 6mm;
  right: 6mm;
  width: 16mm;
  height: 16mm;
  background: white;
  padding: 1mm;
  border-radius: 2mm;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qr-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}
</style> 