<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ resume.title }}</title>
    <link rel="stylesheet" href="style.css">
    {% if page_settings %}
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }
        @media print {
            html, body {
                width: 210mm;
                height: 297mm;
                margin: 0;
                padding: 0;
            }
            .resume-container {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 20mm;
            }
        }
    </style>
    {% endif %}
</head>
<body>
    <div class="resume-container">
        <!-- 头部信息 -->
        <header class="resume-header">
            <div class="header-main">
                <h1 class="full-name">{{ resume.full_name }}</h1>
                <h2 class="title">{{ resume.title }}</h2>
            </div>
            <div class="contact-info">
                {% if resume.email %}
                <div class="contact-item">
                    <i class="icon-email"></i>
                    <span>{{ resume.email }}</span>
                </div>
                {% endif %}
                {% if resume.phone %}
                <div class="contact-item">
                    <i class="icon-phone"></i>
                    <span>{{ resume.phone }}</span>
                </div>
                {% endif %}
                {% if resume.address %}
                <div class="contact-item">
                    <i class="icon-location"></i>
                    <span>{{ resume.address }}</span>
                </div>
                {% endif %}
            </div>
        </header>

        <main class="resume-body">
            <!-- 个人总结 -->
            {% if resume.summary %}
            <section class="resume-section summary-section">
                <h3 class="section-title">个人总结</h3>
                <div class="section-content">
                    <p class="summary-text">{{ resume.summary }}</p>
                </div>
            </section>
            {% endif %}

            <!-- 工作经历 -->
            {% if resume.work_experiences %}
            <section class="resume-section work-section">
                <h3 class="section-title">工作经历</h3>
                <div class="section-content">
                    {% for work in resume.work_experiences %}
                    <div class="work-item">
                        <div class="work-header">
                            <div class="work-title">
                                <h4 class="company-name">{{ work.company_name }}</h4>
                                <span class="position">{{ work.position }}</span>
                            </div>
                            <div class="work-period">
                                {{ work.start_date }} - {{ work.end_date }}
                            </div>
                        </div>
                        <div class="work-content">
                            <p class="work-description">{{ work.description }}</p>
                            {% if work.achievements %}
                            <ul class="achievements-list">
                                {% for achievement in work.achievements %}
                                <li>{{ achievement }}</li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- 教育背景 -->
            {% if resume.educations %}
            <section class="resume-section education-section">
                <h3 class="section-title">教育背景</h3>
                <div class="section-content">
                    {% for edu in resume.educations %}
                    <div class="education-item">
                        <div class="education-header">
                            <div class="education-title">
                                <h4 class="school-name">{{ edu.school_name }}</h4>
                                <span class="major">{{ edu.major }}</span>
                            </div>
                            <div class="education-period">
                                {{ edu.start_date }} - {{ edu.end_date }}
                            </div>
                        </div>
                        <div class="education-content">
                            <span class="degree">{{ edu.degree }}</span>
                            {% if edu.description %}
                            <p class="education-description">{{ edu.description }}</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- 项目经验 -->
            {% if resume.projects %}
            <section class="resume-section project-section">
                <h3 class="section-title">项目经验</h3>
                <div class="section-content">
                    {% for project in resume.projects %}
                    <div class="project-item">
                        <div class="project-header">
                            <h4 class="project-name">{{ project.name }}</h4>
                            {% if project.period %}
                            <span class="project-period">{{ project.period }}</span>
                            {% endif %}
                        </div>
                        <div class="project-content">
                            <p class="project-description">{{ project.description }}</p>
                            {% if project.technologies %}
                            <div class="tech-stack">
                                {% for tech in project.technologies %}
                                <span class="tech-tag">{{ tech }}</span>
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if project.achievements %}
                            <ul class="project-achievements">
                                {% for achievement in project.achievements %}
                                <li>{{ achievement }}</li>
                                {% endfor %}
                            </ul>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
            {% endif %}

            <!-- 技能特长 -->
            {% if resume.skills %}
            <section class="resume-section skills-section">
                <h3 class="section-title">技能特长</h3>
                <div class="section-content">
                    <div class="skills-container">
                        {% for skill in resume.skills %}
                        <div class="skill-item">
                            <span class="skill-name">{{ skill.name }}</span>
                            <div class="skill-level">
                                <div class="skill-bar" style="width: {{ skill.level }}%"></div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </section>
            {% endif %}

            <!-- 证书资质 -->
            {% if resume.certifications %}
            <section class="resume-section certifications-section">
                <h3 class="section-title">证书资质</h3>
                <div class="section-content">
                    <ul class="certifications-list">
                        {% for cert in resume.certifications %}
                        <li class="certification-item">
                            <span class="cert-name">{{ cert.name }}</span>
                            {% if cert.date %}
                            <span class="cert-date">{{ cert.date }}</span>
                            {% endif %}
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </section>
            {% endif %}
        </main>
    </div>
</body>
</html> 