#!/usr/bin/env python3
import sqlite3
import os

# 切换到backend目录
os.chdir('backend')

# 连接数据库
conn = sqlite3.connect('photo_maker.db')
cursor = conn.cursor()

# 检查所有表
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print('所有表:', tables)

# 检查paper_sizes表是否存在
if ('paper_sizes',) in tables:
    print('\npaper_sizes表存在')
    
    # 检查表结构
    cursor.execute('PRAGMA table_info(paper_sizes)')
    columns = cursor.fetchall()
    print('表结构:', columns)
    
    # 检查数据
    cursor.execute('SELECT * FROM paper_sizes')
    rows = cursor.fetchall()
    print(f'数据行数: {len(rows)}')
    
    if rows:
        print('数据内容:')
        for row in rows:
            print(row)
    else:
        print('表中没有数据')
else:
    print('paper_sizes表不存在')

conn.close()
