### 智能简历生成器 - 技术需求文档 (TRD)

版本: 1.0
日期: 2025年6月30日
负责人: 技术分析部

---

### 1. 项目概述

#### 1.1 项目目标
开发一款现代化的智能简历生成软件，旨在为求职者提供两种高效、便捷的简历创建模式：
1.  DOCX 简历一键解析：允许用户上传现有简历，系统自动提取、格式化信息，快速生成新版简历。
2.  引导式在线创建：通过分步向导和AI写作辅助，帮助用户从零开始构建一份专业、有竞争力的简历。

最终目标是降低简历制作门槛，提升简历专业度，从而提高用户的求职成功率。

#### 1.2 项目范围
为了确保项目V1.0能够聚焦核心价值并按时交付，我们明确定义本次开发的边界。

| 包含 (In-Scope)                                          | 不包含 (Out-of-Scope)                                        |
| :------------------------------------------------------- | :----------------------------------------------------------- |
| ✅ 用户注册、登录与密码管理                                  | ❌ 第三方账号社交登录（如微信、LinkedIn）                       |
| ✅ `.docx` 格式简历文件的上传与智能解析                       | ❌ 其他格式（如 `.pdf`, `.txt`, `.pages`）的解析                |
| ✅ 引导式、分模块的简历内容在线填写                           | ❌ 与招聘平台（如BOSS直聘、拉勾）的数据直连与一键投递           |
| ✅ 针对核心描述（如工作经历）的AI写作辅助                     | ❌ 完整的AI面试模拟或职业规划建议                              |
| ✅ 提供多种专业模板并支持实时预览                            | ❌ 用户自定义设计模板或上传个人模板                            |
| ✅ 生成并导出为 `PDF` 和 `DOCX` 格式                          | ❌ 导出为图片（JPEG/PNG）或生成在线分享链接                      |
| ✅ 个人中心，用于管理已创建的简历（增删改查）                  | ❌ 团队协作或简历分享给他人审阅的功能                          |

#### 1.3 目标用户
本产品主要服务于以下三类用户群体：

| 用户画像               | 核心需求                                                                 | 痛点                                                                 |
| :------------------- | :----------------------------------------------------------------------- | :------------------------------------------------------------------- |
| 初入职场的应届毕业生 | 快速创建第一份专业简历，突出教育背景和实习经历，缺乏撰写经验，需要指导。           | 不懂简历规范，内容空洞，无法有效展示潜力。                             |
| 寻求职业发展的专业人士 | 更新和优化现有简历，针对不同职位快速调整内容，使其更具吸引力和针对性。           | 现有简历格式陈旧，手动调整费时费力，难以突出核心竞争力。               |
| 跨行业或职业转型者   | 重构简历，将过往经验与新目标职位进行关联，突出可迁移技能。                     | 不知如何包装过往经历，简历与目标岗位匹配度低，难以通过初筛。           |

---

### 2. 功能需求 (Functional Requirements)

#### 2.1 用户认证模块
- FR101 - 用户注册：用户需使用有效的电子邮箱和密码进行注册。系统需验证邮箱格式的正确性，并对密码强度提出基本要求（如长度、包含字母和数字）。
- FR102 - 用户登录：用户使用注册邮箱和密码进行登录。系统需提供“记住我”功能，并处理登录失败的情况（如密码错误、账户不存在）。
- FR103 - 密码找回：用户可通过注册邮箱重置密码。系统需发送一个有时效性的密码重置链接至用户邮箱。

#### 2.2 简历解析模块 (核心)
此模块是实现快速简历优化的关键路径。

- FR201 - 文件上传：
    - 用户可从本地上传一个 `.docx` 格式的简历文件。
    - 系统需进行文件格式校验，拒绝非 `.docx` 文件。
    - 设置文件大小上限（建议为 `5MB`），以防止滥用。
- FR202 - 智能信息提取：
    - 后端服务接收到文件后，使用 `python-docx` 库解析其内容。
    - 通过预设的规则和关键词（如“教育背景”、“工作经历”），自动识别并提取以下关键字段：

| 字段分类       | 提取内容                                  | 备注                                         |
| :------------- | :---------------------------------------- | :------------------------------------------- |
| 个人信息   | 姓名、电话、邮箱、求职意向                |                                              |
| 教育背景   | 学校名称、专业、学历、在校时间            | 可支持多段教育经历                           |
| 工作经历   | 公司名称、职位、在职时间、工作内容描述    | 工作内容描述按点（bullet points）进行结构化  |
| 项目经验   | 项目名称、担任角色、项目周期、项目描述    | 项目描述按点进行结构化                       |
| 技能证书   | 技能分类（如编程语言、设计软件）、技能名称、熟练度、证书名称 |                                              |
| 个人总结   | 自我评价或职业摘要                        |                                              |

- FR203 - 数据校对与修正：
    - 解析完成后，前端需提供一个清晰的表单界面，将提取出的数据填充到对应的输入框中。
    - 用户可以在此界面上方便地审查、修改、补充或删除被错误识别的信息。
    - 用户确认无误后，数据将被保存至数据库，进入引导式创建流程的后续步骤。

#### 2.3 引导式创建模块 (核心)
此模块为用户提供从零创建简历的结构化路径。

- FR301 - 分步信息录入：
    - 设计一个多步骤的向导（Wizard）界面，引导用户依次填写简历的各个模块（个人信息 -> 教育背景 -> 工作经历 -> ...）。
    - 每个步骤聚焦于一个模块，提供清晰的输入字段和填写提示。
    - 用户可以随时返回上一步修改，或跳过非必填模块。
- FR302 - AI 辅助写作：
    - 在“工作经历”和“项目经验”的描述填写区域，提供AI辅助功能。
    - 用户输入职位名称或项目关键词后，点击“AI生成建议”按钮。
    - 后端AI服务根据输入，生成3-5条符合行业标准、量化且有影响力的描述性语句供用户选择、参考或修改。

#### 2.4 模板引擎与简历生成模块
- FR401 - 专业模板库：
    - 系统内置不少于20款专业简历模板。
    - 模板需支持分类筛选，例如按 风格 (*简约、现代、创意*) 和 行业 (*互联网、金融、教育*)。
    - 所有模板设计必须遵循ATS（Applicant Tracking System）友好原则，确保结构清晰，易于机器解析。
- FR402 - 实时预览：
    - 简历编辑界面采用双栏布局：左侧为数据填写表单，右侧为简历实时预览区。
    - 用户在左侧修改任何信息或在顶部切换模板时，右侧的预览效果必须立即同步更新，所见即所得。
- FR403 - 简历生成：
    - 用户完成编辑后，后端服务根据用户最终确认的数据和选择的模板ID，动态生成最终的简历文件。

#### 2.5 导出功能
- FR501 - 导出为 PDF：用户可将最终生成的简历下载为 `PDF` 文件。`PDF` 格式需保证在任何设备上查看或打印时，布局和字体都保持不变。
- FR502 - 导出为 DOCX：用户可将简历下载为 `DOCX` 文件，以便在本地使用Microsoft Word进行离线编辑或微调。

#### 2.6 用户中心
- FR601 - 简历管理：提供一个仪表盘（Dashboard）页面，以卡片或列表形式展示用户所有已创建/上传的简历。
- FR602 - 简历操作：在用户中心，用户可以对每份简历进行编辑、复制（作为新简历的基础）、重命名和删除操作。

---

### 3. 非功能需求 (Non-Functional Requirements)

| 类别        | 需求描述                                                                                                                               | 度量标准/要求                                                                                                             |
| :---------- | :------------------------------------------------------------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------ |
| 性能    | 系统响应速度快，操作流畅，尤其在文件处理和生成等关键环节。                                                                               | - DOCX解析：对于<2MB的文件，从上传完成到数据显示在校对界面，响应时间 < 5秒。<br>- 简历生成：PDF/DOCX文件生成与下载响应时间 < 3秒。<br>- 页面加载：主要页面（编辑页/用户中心）首次加载时间 < 2秒。 |
| 安全性  | 用户数据，特别是个人身份信息（PII）必须得到妥善保护，防止未授权访问和数据泄露。                                                              | - 数据传输：全站启用 HTTPS 加密传输。<br>- 密码存储：用户密码必须使用 `bcrypt` 或类似强度的哈希算法加盐存储。<br>- API安全：所有API需通过Token（如JWT）进行认证和授权。<br>- 数据存储：数据库中存储的敏感个人信息（如身份证、电话）应加密。 |
| 可用性  | 界面设计直观、简洁，用户无需培训即可快速上手。引导流程清晰，错误提示友好。                                                                 | - 响应式设计：系统在主流桌面浏览器（Chrome, Firefox, Edge）上表现一致，并能适应移动端浏览器访问。<br>- 可访问性：遵循WCAG 2.1 A级标准，确保关键功能对残障人士可用。<br>- 操作路径：从注册到导出第一份简历的核心路径，点击次数应尽可能少。 |
| 可扩展性| 系统架构设计应具备良好的模块化和扩展性，便于未来迭代。                                                                                   | - 模块化：前后端业务逻辑按功能模块（认证、解析、模板等）解耦。<br>- 模板系统：新增简历模板无需修改后端代码，只需添加新的模板配置文件和样式文件。<br>- AI服务：AI辅助写作功能应作为独立微服务，便于未来升级或替换模型。 |

---

### 4. 系统架构与技术选型

#### 4.1 总体架构
系统采用前后端分离的架构模式。
- 前端 (Client)：基于 Vue.js 的单页应用 (SPA)，负责UI展示和用户交互。
- 后端 (Server)：基于 Python 的API服务，负责处理业务逻辑、数据持久化、文件解析和AI服务调用。
- 数据库 (Database)：关系型数据库，用于存储用户和简历数据。
- 对象存储 (Object Storage)：用于存储用户上传的DOCX文件和生成的简历文件（可选，也可存放在服务器文件系统）。

#### 4.2 技术选型

| 层面     | 技术/框架         | 推荐/要求                                                              | 理由                                                                                       |
| :------- | :---------------- | :--------------------------------------------------------------------- | :----------------------------------------------------------------------------------------- |
| 前端 | 框架          | `Vue.js 3`                                                             | 性能优异，生态成熟，组件化开发模式非常适合构建复杂的UI界面。                                 |
|          | UI库          | `Element Plus` 或 `Ant Design Vue`                                     | 提供高质量、丰富的组件库，能极大加速前端界面的开发。                                       |
|          | 状态管理      | `Pinia`                                                                | Vue 3 官方推荐的状态管理库，轻量、直观且类型友好。                                           |
| 后端 | 框架          | `Django` (推荐) 或 `Flask`                                             | Django "Batteries-included" 的特性（ORM, Admin, Auth）能快速搭建项目骨架。Flask 则更轻量灵活。 |
|          | 文件解析库    | `python-docx`                                                          | 强大的、专门用于处理 `.docx` 文件的Python库，完全满足需求。                                |
|          | API通信       | `Django REST Framework` (若用Django)                                   | 构建RESTful API的标准选择，功能强大且文档完善。                                            |
|          | AI模型/服务   | (待定) 可集成第三方大语言模型API（如OpenAI GPT, Kimi）或自研模型           | 快速实现AI写作辅助功能，具体选型需进行成本和效果评估。                                       |
| 数据库 | 类型          | `PostgreSQL` (推荐) 或 `MySQL`                                         | 两者都是成熟可靠的关系型数据库，PostgreSQL在处理复杂查询和数据类型方面略有优势。           |
| 部署 | 容器化        | `Docker` & `Docker Compose`                                            | 实现开发、测试、生产环境的一致性，简化部署和运维流程。                                       |
|          | Web服务器     | `Nginx`                                                                | 作为反向代理和静态文件服务器，性能卓越。                                                   |
|          | 应用服务器    | `Gunicorn`                                                             | 成熟的Python WSGI服务器，用于运行Django/Flask应用。                                          |


### 智能简历生成器 - 项目实现路径与开发路线图

文档版本: 1.0
状态: 待执行
关联文档: 《智能简历生成器 - 技术需求文档 (TRD) v1.0》

---

### 1. 核心洞察与战略导向

此路线图旨在将技术需求（*What we need to build*）转化为一个清晰、可执行的开发计划（*How we will build it*）。我们的核心战略是：
- 分阶段交付价值：通过迭代式开发，确保每个阶段结束时都有可演示的成果，尽早验证核心功能。
- 质量内建：将测试、代码审查和自动化流程融入日常开发，而非事后补救。
- 架构先行：在项目初期搭建稳固的基础设施，为后续高效、可扩展的开发铺平道路。

### 2. 总体技术选型确认

基于TRD的分析，我们在此确认并锁定项目V1.0的技术栈。此选型旨在平衡开发效率、社区支持、性能与未来可扩展性。

| 层面 | 技术/框架 | 核心库/工具 | 理由 |
| :--- | :--- | :--- | :--- |
| 前端 | Vue.js 3 | `Pinia`, `Vue Router`, `Axios` | 响应式数据绑定与组件化架构，非常适合构建动态、复杂的单页应用(SPA)。 |
| | UI 库 | `Element Plus` | 提供丰富、高质量且符合ATS友好原则的UI组件，极大加速界面开发。 |
| 后端 | Python 3.10+ / Django 4+ | `Django REST Framework`, `python-docx` | Django的全家桶特性（ORM, Auth, Admin）可快速启动项目；DRF是构建健壮API的首选。 |
| | AI 服务 | 第三方大语言模型 API (如 Kimi, GPT) | 通过API集成，快速实现高质量的AI写作辅助功能，避免自研模型的复杂性。 |
| 数据库 | PostgreSQL 15+ | - | 强大的关系型数据库，性能优越，支持复杂查询和JSONB字段，为未来功能扩展提供便利。 |
| 部署与运维 | Docker / Docker Compose | `Nginx`, `Gunicorn` | 实现开发、测试、生产环境的一致性，简化部署流程，实现服务隔离与水平扩展。 |

### 3. 项目阶段划分与开发路线图

我们将整个项目分解为四个逻辑清晰的阶段，每个阶段都有明确的目标、任务清单和里程碑。



---

#### 第一阶段：环境搭建与基础架构 (Foundation & Infrastructure)
- 目标: 搭建项目骨架和开发环境，确保协作流程顺畅。此阶段不产出面向用户的完整功能，但为后续所有开发工作奠定基础。
- 预估周期: 1.5 周

| 类别 | 详细任务 | 关键产出 |
| :--- | :--- | :--- |
| 项目管理 | - 初始化 Git 仓库，配置分支保护规则 (main, develop)。<br>- 建立团队任务板 (如 Jira, Trello)。 | Git 仓库、任务板 |
| DevOps | - 编写 `docker-compose.yml` 文件，定义前端、后端、数据库服务。<br>- 配置基础的 CI 流程 (GitHub Actions/GitLab CI)，实现代码提交后自动运行代码检查 (Linting) 和单元测试。 | `docker-compose.yml`、CI 配置文件 |
| 后端 | - 初始化 Django 项目，配置数据库连接。<br>- 设计并实现核心数据库模型 (User, Resume, Education, WorkExperience 等)。<br>- 搭建基础 API 结构，实现健康检查接口 (`/api/health`)。<br>- 集成 JWT (JSON Web Token) 进行用户认证的基础配置。 | 数据库 ER 图、Django Models、基础 API 路由 |
| 前端 | - 初始化 Vue 3 项目 (Vite)。<br>- 配置路由 (`vue-router`)、状态管理 (`pinia`) 和 API 请求客户端 (`axios`)。<br>- 搭建基础页面布局 (如 Header, Footer, Main Content Area)。<br>- 实现与后端健康检查接口的联调。 | Vue 项目结构、基础布局组件 |

里程碑 1: 开发环境就绪。开发人员可以在本地通过一条 `docker-compose up` 命令完整运行前后端应用，并且基础CI流程已建立。

---

#### 第二阶段：核心功能开发 (Core Feature Implementation)
- 目标: 实现产品的核心价值主张——用户认证、简历解析与引导式创建。此阶段结束后，产品应具备最基础的可用性。
- 预估周期: 3 - 4 周

| 类别 | 详细任务 | 关联TRD |
| :--- | :--- | :--- |
| 后端 | - 用户认证: 开发用户注册、登录、密码找回的完整 API 逻辑。<br>- DOCX 解析: 创建文件上传接口，使用 `python-docx` 解析简历内容，并将其结构化为 JSON 格式。<br>- 简历 CRUD: 开发创建、读取、更新、删除简历数据的核心 API。 | FR101-103<br>FR201-202<br>FR601-602 |
| 前端 | - 认证页面: 开发注册、登录、密码找回页面，并与后端 API 对接。<br>- 引导式创建: 开发分步向导 (Wizard) 界面，用于引导用户填写简历各模块信息。<br>- DOCX 解析流程: 实现文件上传组件，并将后端解析的数据回填至引导式表单中，供用户校对。 | FR101-103<br>FR301<br>FR203 |
| 通用 | - 实时预览 (V1): 实现双栏布局，左侧为表单，右侧使用一个基础模板实时渲染用户输入的数据。<br>- PDF 生成 (V1): 实现将渲染后的简历导出为 PDF 的基础功能。 | FR402<br>FR501 |

里程碑 2: 核心 MVP 功能完成。用户可以完成从 注册 -> 上传DOCX解析/手动填写 -> 实时预览 -> 导出PDF 的完整流程。

---

#### 第三阶段：高级功能与 AI 集成 (Advanced Features & AI Integration)
- 目标: 丰富产品功能，引入智能化元素和个性化选项，提升产品竞争力和用户体验。
- 预估周期: 3 - 4 周

| 类别 | 详细任务 | 关联TRD |
| :--- | :--- | :--- |
| 后端 | - AI 辅助写作: 集成第三方大语言模型 API，开发接口根据用户输入（如职位）生成工作描述建议。<br>- 模板引擎: 设计并实现一个灵活的后端模板渲染机制，支持根据模板 ID 和用户数据生成不同风格的简历。<br>- DOCX 导出: 开发将简历数据填充到 `.docx` 模板中并导出的功能。 | FR302<br>FR401, FR403<br>FR502 |
| 前端 | - AI 功能集成: 在工作经历、项目经验等描述区域，添加 "AI 生成" 按钮，并展示返回的建议。<br>- 模板选择器: 开发一个可视化的模板库页面，允许用户预览并选择不同的简历模板。<br>- 用户中心: 实现完整的用户中心仪表盘，用于管理（复制、重命名、删除）用户的多份简历。 | FR302<br>FR401<br>FR601-602 |
| 设计/UX| - 模板设计: 设计并实现TRD中要求的所有简历模板 (HTML/CSS)。<br>- UI 优化: 全面优化界面交互和视觉效果。 | FR401 |

里程碑 3: 完整功能就绪。产品具备TRD中定义的所有核心及高级功能，AI辅助和多模板选择功能上线，用户体验显著提升。

---

#### 第四阶段：测试、部署与优化 (Testing, Deployment & Optimization)
- 目标: 确保产品的稳定性、安全性和性能，并将其正式部署到生产环境，面向真实用户。
- 预估周期: 2 - 3 周

| 类别 | 详细任务 | 关联NFR |
| :--- | :--- | :--- |
| 测试 | - 单元测试: 提高前后端代码的单元测试覆盖率，尤其是核心业务逻辑。<br>- 集成测试: 测试前后端 API 交互的正确性。<br>- 端到端(E2E)测试: 编写自动化脚本，模拟用户完整操作路径（注册->创建->导出）。<br>- 性能测试: 对文件解析、简历生成等关键接口进行压力测试。 | 性能 |
| DevOps | - 生产环境搭建: 在云服务器上配置生产级的 Nginx, Gunicorn, PostgreSQL 环境。<br>- 配置 CD 流程: 实现自动化部署，当 `main` 分支更新时，自动部署到生产环境。<br>- 数据备份与监控: 配置数据库定期备份策略和应用性能监控 (APM) 工具。 | 安全性, 可扩展性 |
| 安全 | - 安全审计: 全面检查是否存在安全漏洞（如 SQL 注入、XSS、CSRF），并修复。<br>- 依赖库扫描: 扫描项目所有依赖，确保没有已知的安全漏洞。 | 安全性 |
| 优化 | - 前端性能: 优化静态资源加载速度（代码分割、压缩、CDN）。<br>- 后端性能: 优化慢查询 SQL，对高频访问的 API 增加缓存。 | 性能, 可用性 |

里程碑 4: 产品正式上线 (Go-Live)。应用成功部署到生产环境，对外提供稳定服务，并建立了完善的监控和维护流程。

---

### 4. 开发流程与协作规范

为保证开发效率和代码质量，团队需遵循以下工作流程：

#### 4.1 Git 分支管理策略 (Git Flow)

我们将采用 `Git Flow` 模型来管理代码分支，确保开发流程的清晰和稳定。

- `main`: 主分支，始终保持与生产环境一致的、最稳定的代码。只接受来自 `release` 分支或 `hotfix` 分支的合并。
- `develop`: 开发主分支，汇集所有已完成的功能开发。是新功能分支的起点。
- `feature/<feature-name>`: 功能分支，用于开发新功能。从 `develop` 分支创建，完成后合并回 `develop` 分支。例如 `feature/user-auth`。
- `release/<version>`: 发布分支，用于准备发布新版本。从 `develop` 分支创建，在此分支上进行最后的测试和修复。完成后，同时合并到 `main` 和 `develop` 分支。
- `hotfix/<issue-name>`: 热修复分支，用于紧急修复生产环境的 Bug。从 `main` 分支创建，完成后同时合并到 `main` 和 `develop` 分支。

#### 4.2 代码审查 (Code Review)

> 所有向 `develop` 或 `main` 分支的合并请求 (Pull Request / Merge Request) 都必须经过至少一位其他团队成员的审查。

- 审查重点:
    - 功能实现: 是否符合需求。
    - 代码质量: 是否遵循编码规范、是否存在潜在 Bug、是否可读和可维护。
    - 测试覆盖: 是否包含必要的单元测试或集成测试。
- 目的: 提高代码质量，促进知识共享，分散风险。

#### 4.3 持续集成/持续部署 (CI/CD)

- 持续集成 (CI): 每次向 `feature` 或 `develop` 分支推送代码时，CI 服务器将自动执行以下任务：
    1. 拉取最新代码。
    2. 安装依赖。
    3. 运行代码风格检查 (Linting)。
    4. 运行所有自动化测试。
    5. 构建应用。
    *只有所有步骤都通过，合并请求才被允许合并。*
- 持续部署 (CD): 当代码成功合并到 `main` 分支后，CD 流程将自动将应用部署到生产环境。这确保了快速、可靠和可重复的发布过程。

### 智能简历生成器 - 项目实现路径与开发路线图

文档版本: 1.0
状态: 待执行
关联文档: 《智能简历生成器 - 技术需求文档 (TRD) v1.0》

---

### 1. 核心洞察与战略导向

此路线图旨在将技术需求（*What we need to build*）转化为一个清晰、可执行的开发计划（*How we will build it*）。我们的核心战略是：
- 分阶段交付价值：通过迭代式开发，确保每个阶段结束时都有可演示的成果，尽早验证核心功能。
- 质量内建：将测试、代码审查和自动化流程融入日常开发，而非事后补救。
- 架构先行：在项目初期搭建稳固的基础设施，为后续高效、可扩展的开发铺平道路。

### 2. 总体技术选型确认

基于TRD的分析，我们在此确认并锁定项目V1.0的技术栈。此选型旨在平衡开发效率、社区支持、性能与未来可扩展性。

| 层面 | 技术/框架 | 核心库/工具 | 理由 |
| :--- | :--- | :--- | :--- |
| 前端 | Vue.js 3 | `Pinia`, `Vue Router`, `Axios` | 响应式数据绑定与组件化架构，非常适合构建动态、复杂的单页应用(SPA)。 |
| | UI 库 | `Element Plus` | 提供丰富、高质量且符合ATS友好原则的UI组件，极大加速界面开发。 |
| 后端 | Python 3.10+ / Django 4+ | `Django REST Framework`, `python-docx` | Django的全家桶特性（ORM, Auth, Admin）可快速启动项目；DRF是构建健壮API的首选。 |
| | AI 服务 | 第三方大语言模型 API (如 Kimi, GPT) | 通过API集成，快速实现高质量的AI写作辅助功能，避免自研模型的复杂性。 |
| 数据库 | PostgreSQL 15+ | - | 强大的关系型数据库，性能优越，支持复杂查询和JSONB字段，为未来功能扩展提供便利。 |
| 部署与运维 | Docker / Docker Compose | `Nginx`, `Gunicorn` | 实现开发、测试、生产环境的一致性，简化部署流程，实现服务隔离与水平扩展。 |

### 3. 项目阶段划分与开发路线图

我们将整个项目分解为四个逻辑清晰的阶段，每个阶段都有明确的目标、任务清单和里程碑。



---

#### 第一阶段：环境搭建与基础架构 (Foundation & Infrastructure)
- 目标: 搭建项目骨架和开发环境，确保协作流程顺畅。此阶段不产出面向用户的完整功能，但为后续所有开发工作奠定基础。
- 预估周期: 1.5 周

| 类别 | 详细任务 | 关键产出 |
| :--- | :--- | :--- |
| 项目管理 | - 初始化 Git 仓库，配置分支保护规则 (main, develop)。<br>- 建立团队任务板 (如 Jira, Trello)。 | Git 仓库、任务板 |
| DevOps | - 编写 `docker-compose.yml` 文件，定义前端、后端、数据库服务。<br>- 配置基础的 CI 流程 (GitHub Actions/GitLab CI)，实现代码提交后自动运行代码检查 (Linting) 和单元测试。 | `docker-compose.yml`、CI 配置文件 |
| 后端 | - 初始化 Django 项目，配置数据库连接。<br>- 设计并实现核心数据库模型 (User, Resume, Education, WorkExperience 等)。<br>- 搭建基础 API 结构，实现健康检查接口 (`/api/health`)。<br>- 集成 JWT (JSON Web Token) 进行用户认证的基础配置。 | 数据库 ER 图、Django Models、基础 API 路由 |
| 前端 | - 初始化 Vue 3 项目 (Vite)。<br>- 配置路由 (`vue-router`)、状态管理 (`pinia`) 和 API 请求客户端 (`axios`)。<br>- 搭建基础页面布局 (如 Header, Footer, Main Content Area)。<br>- 实现与后端健康检查接口的联调。 | Vue 项目结构、基础布局组件 |

里程碑 1: 开发环境就绪。开发人员可以在本地通过一条 `docker-compose up` 命令完整运行前后端应用，并且基础CI流程已建立。

---

#### 第二阶段：核心功能开发 (Core Feature Implementation)
- 目标: 实现产品的核心价值主张——用户认证、简历解析与引导式创建。此阶段结束后，产品应具备最基础的可用性。
- 预估周期: 3 - 4 周

| 类别 | 详细任务 | 关联TRD |
| :--- | :--- | :--- |
| 后端 | - 用户认证: 开发用户注册、登录、密码找回的完整 API 逻辑。<br>- DOCX 解析: 创建文件上传接口，使用 `python-docx` 解析简历内容，并将其结构化为 JSON 格式。<br>- 简历 CRUD: 开发创建、读取、更新、删除简历数据的核心 API。 | FR101-103<br>FR201-202<br>FR601-602 |
| 前端 | - 认证页面: 开发注册、登录、密码找回页面，并与后端 API 对接。<br>- 引导式创建: 开发分步向导 (Wizard) 界面，用于引导用户填写简历各模块信息。<br>- DOCX 解析流程: 实现文件上传组件，并将后端解析的数据回填至引导式表单中，供用户校对。 | FR101-103<br>FR301<br>FR203 |
| 通用 | - 实时预览 (V1): 实现双栏布局，左侧为表单，右侧使用一个基础模板实时渲染用户输入的数据。<br>- PDF 生成 (V1): 实现将渲染后的简历导出为 PDF 的基础功能。 | FR402<br>FR501 |

里程碑 2: 核心 MVP 功能完成。用户可以完成从 注册 -> 上传DOCX解析/手动填写 -> 实时预览 -> 导出PDF 的完整流程。

---

#### 第三阶段：高级功能与 AI 集成 (Advanced Features & AI Integration)
- 目标: 丰富产品功能，引入智能化元素和个性化选项，提升产品竞争力和用户体验。
- 预估周期: 3 - 4 周

| 类别 | 详细任务 | 关联TRD |
| :--- | :--- | :--- |
| 后端 | - AI 辅助写作: 集成第三方大语言模型 API，开发接口根据用户输入（如职位）生成工作描述建议。<br>- 模板引擎: 设计并实现一个灵活的后端模板渲染机制，支持根据模板 ID 和用户数据生成不同风格的简历。<br>- DOCX 导出: 开发将简历数据填充到 `.docx` 模板中并导出的功能。 | FR302<br>FR401, FR403<br>FR502 |
| 前端 | - AI 功能集成: 在工作经历、项目经验等描述区域，添加 "AI 生成" 按钮，并展示返回的建议。<br>- 模板选择器: 开发一个可视化的模板库页面，允许用户预览并选择不同的简历模板。<br>- 用户中心: 实现完整的用户中心仪表盘，用于管理（复制、重命名、删除）用户的多份简历。 | FR302<br>FR401<br>FR601-602 |
| 设计/UX| - 模板设计: 设计并实现TRD中要求的所有简历模板 (HTML/CSS)。<br>- UI 优化: 全面优化界面交互和视觉效果。 | FR401 |

里程碑 3: 完整功能就绪。产品具备TRD中定义的所有核心及高级功能，AI辅助和多模板选择功能上线，用户体验显著提升。

---

#### 第四阶段：测试、部署与优化 (Testing, Deployment & Optimization)
- 目标: 确保产品的稳定性、安全性和性能，并将其正式部署到生产环境，面向真实用户。
- 预估周期: 2 - 3 周

| 类别 | 详细任务 | 关联NFR |
| :--- | :--- | :--- |
| 测试 | - 单元测试: 提高前后端代码的单元测试覆盖率，尤其是核心业务逻辑。<br>- 集成测试: 测试前后端 API 交互的正确性。<br>- 端到端(E2E)测试: 编写自动化脚本，模拟用户完整操作路径（注册->创建->导出）。<br>- 性能测试: 对文件解析、简历生成等关键接口进行压力测试。 | 性能 |
| DevOps | - 生产环境搭建: 在云服务器上配置生产级的 Nginx, Gunicorn, PostgreSQL 环境。<br>- 配置 CD 流程: 实现自动化部署，当 `main` 分支更新时，自动部署到生产环境。<br>- 数据备份与监控: 配置数据库定期备份策略和应用性能监控 (APM) 工具。 | 安全性, 可扩展性 |
| 安全 | - 安全审计: 全面检查是否存在安全漏洞（如 SQL 注入、XSS、CSRF），并修复。<br>- 依赖库扫描: 扫描项目所有依赖，确保没有已知的安全漏洞。 | 安全性 |
| 优化 | - 前端性能: 优化静态资源加载速度（代码分割、压缩、CDN）。<br>- 后端性能: 优化慢查询 SQL，对高频访问的 API 增加缓存。 | 性能, 可用性 |

里程碑 4: 产品正式上线 (Go-Live)。应用成功部署到生产环境，对外提供稳定服务，并建立了完善的监控和维护流程。

---