<template>
  <div class="user-logs">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">用户日志管理</h2>
        <p class="page-description">查看和管理用户操作日志，监控用户行为，分析系统使用情况</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="exportLogs">
          <el-icon><Download /></el-icon>
          导出日志
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.today_logs }}</div>
                <div class="stat-label">今日日志</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">📈</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.total_logs }}</div>
                <div class="stat-label">总日志数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">👥</div>
              <div class="stat-info">
                <div class="stat-value">{{ stats.active_users }}</div>
                <div class="stat-label">活跃用户</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon">⚡</div>
              <div class="stat-info">
                <div class="stat-value">{{ logs.length }}</div>
                <div class="stat-label">当前显示</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filters-section">
      <el-card class="filter-card">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-input
              v-model="filters.user_id"
              placeholder="用户ID"
              clearable
              @input="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.action" placeholder="操作类型" clearable @change="handleSearch">
              <el-option label="登录" value="login" />
              <el-option label="登出" value="logout" />
              <el-option label="注册" value="register" />
              <el-option label="上传图片" value="image_upload" />
              <el-option label="删除图片" value="image_delete" />
              <el-option label="更新资料" value="profile_update" />
              <el-option label="修改密码" value="password_change" />
              <el-option label="设置头像" value="avatar_set" />
              <el-option label="生成简历" value="resume_generate" />
              <el-option label="生成名片" value="business_card_generate" />
              <el-option label="文档对比" value="document_compare" />
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select v-model="filters.resource_type" placeholder="资源类型" clearable @change="handleSearch">
              <el-option label="图片" value="image" />
              <el-option label="简历" value="resume" />
              <el-option label="名片" value="business_card" />
              <el-option label="文档" value="document" />
              <el-option label="用户" value="user" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleSearch"
            />
          </el-col>
          <el-col :span="4">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetFilters">重置</el-button>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 日志列表 -->
    <el-card class="logs-card">
      <template #header>
        <div class="card-header">
          <span>用户操作日志 (共 {{ pagination.total }} 条)</span>
          <div class="header-actions">
            <el-button @click="refreshLogs">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="logs"
        style="width: 100%"
        max-height="600"
      >
        <el-table-column prop="id" label="ID" width="80" />
        
        <el-table-column label="用户信息" width="150">
          <template #default="{ row }">
            <div class="user-info">
              <div class="username">{{ row.username }}</div>
              <div class="user-id">ID: {{ row.user_id }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="action" label="操作类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getActionTag(row.action)">
              {{ getActionLabel(row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="资源信息" width="150">
          <template #default="{ row }">
            <div v-if="row.resource_type" class="resource-info">
              <div class="resource-type">{{ getResourceTypeLabel(row.resource_type) }}</div>
              <div class="resource-id" v-if="row.resource_id">ID: {{ row.resource_id }}</div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="ip_address" label="IP地址" width="120" />
        
        <el-table-column prop="response_status" label="状态" width="80">
          <template #default="{ row }">
            <el-tag 
              v-if="row.response_status" 
              :type="row.response_status >= 400 ? 'danger' : row.response_status >= 300 ? 'warning' : 'success'"
              size="small"
            >
              {{ row.response_status }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="操作时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewLogDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.per_page"
          :page-sizes="[50, 100, 200, 500]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="showLogDetail"
      title="日志详情"
      width="800px"
    >
      <div v-if="selectedLog" class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日志ID">{{ selectedLog.id }}</el-descriptions-item>
          <el-descriptions-item label="用户ID">{{ selectedLog.user_id }}</el-descriptions-item>
          <el-descriptions-item label="用户名">{{ selectedLog.username }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getActionTag(selectedLog.action)">
              {{ getActionLabel(selectedLog.action) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="资源类型">{{ selectedLog.resource_type || '-' }}</el-descriptions-item>
          <el-descriptions-item label="资源ID">{{ selectedLog.resource_id || '-' }}</el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ selectedLog.ip_address || '-' }}</el-descriptions-item>
          <el-descriptions-item label="响应状态">
            <el-tag 
              v-if="selectedLog.response_status" 
              :type="selectedLog.response_status >= 400 ? 'danger' : selectedLog.response_status >= 300 ? 'warning' : 'success'"
            >
              {{ selectedLog.response_status }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="操作时间" :span="2">{{ formatDate(selectedLog.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="用户代理" :span="2">
            <div class="user-agent">{{ selectedLog.user_agent || '-' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="请求数据" :span="2" v-if="selectedLog.request_data">
            <pre class="request-data">{{ JSON.stringify(selectedLog.request_data, null, 2) }}</pre>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Refresh } from '@element-plus/icons-vue'
import api from '@/api'

// 响应式数据
const loading = ref(false)
const logs = ref([])
const showLogDetail = ref(false)
const selectedLog = ref(null)

const stats = reactive({
  today_logs: 0,
  total_logs: 0,
  active_users: 0
})

const filters = reactive({
  user_id: '',
  action: '',
  resource_type: '',
  dateRange: []
})

const pagination = reactive({
  page: 1,
  per_page: 50,
  total: 0
})

// 方法
const loadStats = async () => {
  try {
    const response = await api.admin.getUserLogsStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载日志统计失败:', error)
  }
}

const loadLogs = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      per_page: pagination.per_page,
      ...filters
    }
    
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0]
      params.end_date = filters.dateRange[1]
    }
    
    const response = await api.admin.getUserLogs(params)
    logs.value = response.data.logs
    pagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载用户日志失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadLogs()
}

const resetFilters = () => {
  Object.assign(filters, {
    user_id: '',
    action: '',
    resource_type: '',
    dateRange: []
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.per_page = size
  loadLogs()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadLogs()
}

const refreshLogs = () => {
  loadStats()
  loadLogs()
}

const viewLogDetail = (log) => {
  selectedLog.value = log
  showLogDetail.value = true
}

const exportLogs = async () => {
  try {
    const params = { ...filters }
    if (filters.dateRange && filters.dateRange.length === 2) {
      params.start_date = filters.dateRange[0]
      params.end_date = filters.dateRange[1]
    }
    
    const response = await api.admin.exportUserLogs(params)
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `用户操作日志_${new Date().toISOString().split('T')[0]}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const getActionLabel = (action) => {
  const actionMap = {
    'login': '登录',
    'logout': '登出',
    'register': '注册',
    'image_upload': '上传图片',
    'image_delete': '删除图片',
    'profile_update': '更新资料',
    'password_change': '修改密码',
    'avatar_set': '设置头像',
    'resume_generate': '生成简历',
    'business_card_generate': '生成名片',
    'document_compare': '文档对比'
  }
  return actionMap[action] || action
}

const getActionTag = (action) => {
  const tagMap = {
    'login': 'success',
    'logout': 'info',
    'register': 'primary',
    'image_upload': 'success',
    'image_delete': 'danger',
    'profile_update': 'warning',
    'password_change': 'warning',
    'avatar_set': 'info',
    'resume_generate': 'success',
    'business_card_generate': 'success',
    'document_compare': 'info'
  }
  return tagMap[action] || 'info'
}

const getResourceTypeLabel = (resourceType) => {
  const typeMap = {
    'image': '图片',
    'resume': '简历',
    'business_card': '名片',
    'document': '文档',
    'user': '用户'
  }
  return typeMap[resourceType] || resourceType
}

onMounted(() => {
  loadStats()
  loadLogs()
})
</script>

<style scoped>
.user-logs {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  margin-bottom: 0;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 4px;
}

.filters-section {
  margin-bottom: 24px;
}

.filter-card {
  margin-bottom: 0;
}

.logs-card {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-weight: 500;
  color: #2c3e50;
}

.user-id {
  font-size: 12px;
  color: #7f8c8d;
}

.resource-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.resource-type {
  font-weight: 500;
  color: #2c3e50;
}

.resource-id {
  font-size: 12px;
  color: #7f8c8d;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

.log-detail {
  max-height: 600px;
  overflow-y: auto;
}

.user-agent {
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  max-height: 100px;
  overflow-y: auto;
}

.request-data {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
}
</style> 