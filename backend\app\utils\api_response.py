"""
API错误处理工具
"""
from functools import wraps
from typing import Any, Callable, Dict, Optional, Type, Union
from flask import jsonify, current_app
import traceback
import logging

logger = logging.getLogger(__name__)

class APIError(Exception):
    """API错误基类"""
    
    def __init__(self, message: str, status_code: int = 400, 
                 payload: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.payload = payload or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典形式"""
        response = {
            'error': True,
            'message': self.message,
            'code': self.status_code
        }
        response.update(self.payload)
        return response

class ValidationError(APIError):
    """验证错误"""
    def __init__(self, message: str, payload: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=422, payload=payload)

class AuthenticationError(APIError):
    """认证错误"""
    def __init__(self, message: str, payload: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=401, payload=payload)

class AuthorizationError(APIError):
    """授权错误"""
    def __init__(self, message: str, payload: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=403, payload=payload)

class NotFoundError(APIError):
    """资源未找到错误"""
    def __init__(self, message: str, payload: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=404, payload=payload)

class ConflictError(APIError):
    """资源冲突错误"""
    def __init__(self, message: str, payload: Optional[Dict[str, Any]] = None):
        super().__init__(message, status_code=409, payload=payload)

def handle_api_error(error: Union[APIError, Exception]) -> tuple:
    """处理API错误"""
    if isinstance(error, APIError):
        response = jsonify(error.to_dict())
        return response, error.status_code
    
    # 处理其他异常
    logger.error(f"未处理的异常: {str(error)}")
    logger.error(traceback.format_exc())
    
    if current_app.config['DEBUG']:
        error_detail = {
            'error': True,
            'message': str(error),
            'traceback': traceback.format_exc()
        }
    else:
        error_detail = {
            'error': True,
            'message': '服务器内部错误'
        }
    
    return jsonify(error_detail), 500

def api_error_handler(func: Callable) -> Callable:
    """API错误处理装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            return handle_api_error(e)
    return wrapper

def validate_input(*required_fields: str, 
                  custom_validator: Optional[Callable] = None) -> Callable:
    """
    输入验证装饰器
    :param required_fields: 必需的字段
    :param custom_validator: 自定义验证函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            from flask import request
            
            # 获取输入数据
            if request.is_json:
                data = request.get_json()
            else:
                data = request.form.to_dict()
            
            # 检查必需字段
            missing_fields = [
                field for field in required_fields
                if field not in data
            ]
            if missing_fields:
                raise ValidationError(
                    f"缺少必需字段: {', '.join(missing_fields)}"
                )
            
            # 执行自定义验证
            if custom_validator:
                try:
                    custom_validator(data)
                except Exception as e:
                    raise ValidationError(str(e))
            
            return func(*args, **kwargs)
        return wrapper
    return decorator

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(APIError)
    def handle_api_exception(error):
        return handle_api_error(error)
    
    @app.errorhandler(404)
    def handle_404(error):
        return handle_api_error(NotFoundError('请求的资源不存在'))
    
    @app.errorhandler(405)
    def handle_405(error):
        return handle_api_error(APIError('不支持的请求方法', status_code=405))
    
    @app.errorhandler(500)
    def handle_500(error):
        return handle_api_error(error)

def success_response(data: Optional[Dict[str, Any]] = None, 
                    message: str = '操作成功') -> tuple:
    """
    生成成功响应
    :param data: 响应数据
    :param message: 成功消息
    :return: 响应元组 (response, status_code)
    """
    response = {
        'success': True,
        'message': message
    }
    if data is not None:
        response['data'] = data
    return jsonify(response), 200
