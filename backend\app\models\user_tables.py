"""
用户模型相关的数据库表定义
"""
from sqlalchemy import Table, Column, Integer, String, DateTime, ForeignKey, Boolean
from .base import db

# 用户与角色关联表
user_roles = Table(
    'user_roles',
    db.metadata,
    Column('user_id', Integer, ForeignKey('users.id', ondelete='CASCADE'), primary_key=True),
    Column('role_id', Integer, ForeignKey('roles.id', ondelete='CASCADE'), primary_key=True)
)

# 用户收藏简历模板表
user_favorite_templates = Table(
    'user_favorite_templates',
    db.metadata,
    Column('user_id', Integer, ForeignKey('users.id', ondelete='CASCADE'), primary_key=True),
    Column('template_id', Integer, ForeignKey('resume_templates.id', ondelete='CASCADE'), primary_key=True),
    Column('created_at', DateTime)
)

# 用户订阅关系表
user_subscriptions = Table(
    'user_subscriptions',
    db.metadata,
    Column('user_id', Integer, Foreign<PERSON>ey('users.id', ondelete='CASCADE'), primary_key=True),
    Column('plan_id', Integer, ForeignKey('subscription_plans.id', ondelete='CASCADE'), primary_key=True),
    Column('start_date', DateTime, nullable=False),
    Column('end_date', DateTime),
    Column('is_active', Boolean, default=True),
    Column('auto_renew', Boolean, default=False),
    Column('payment_id', String(100))
)

# 用户积分变动记录表
user_points_logs = Table(
    'user_points_logs',
    db.metadata,
    Column('id', Integer, primary_key=True),
    Column('user_id', Integer, ForeignKey('users.id', ondelete='CASCADE')),
    Column('points', Integer, nullable=False),  # 变动积分数（正数增加，负数减少）
    Column('balance', Integer, nullable=False),  # 变动后的余额
    Column('type', String(50), nullable=False),  # 变动类型（例如：注册奖励、消费等）
    Column('description', String(500)),  # 变动描述
    Column('related_id', String(100)),  # 关联ID（如订单ID等）
    Column('created_at', DateTime, nullable=False)
)

# 用户操作日志表
user_operation_logs = Table(
    'user_operation_logs',
    db.metadata,
    Column('id', Integer, primary_key=True),
    Column('user_id', Integer, ForeignKey('users.id', ondelete='CASCADE')),
    Column('action', String(50), nullable=False),  # 操作类型
    Column('resource_type', String(50)),  # 资源类型
    Column('resource_id', String(100)),  # 资源ID
    Column('details', String(1000)),  # 操作详情
    Column('ip_address', String(50)),  # IP地址
    Column('user_agent', String(200)),  # 用户代理
    Column('created_at', DateTime, nullable=False)
)
