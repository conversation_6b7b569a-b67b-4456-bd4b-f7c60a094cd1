{"name": "photo-maker-frontend", "version": "1.0.0", "description": "智能证件照制作工具前端", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@vueuse/core": "^10.5.0", "axios": "^1.5.0", "cropperjs": "^1.6.1", "echarts": "^5.6.0", "element-plus": "^2.4.0", "fabric": "^5.5.2", "file-saver": "^2.0.5", "html-docx-js": "^0.3.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "jszip": "^3.10.1", "mammoth": "^1.9.1", "moment": "^2.30.1", "pdfjs-dist": "^5.3.93", "pinia": "^3.0.3", "vue": "^3.3.0", "vue-router": "^4.2.0", "vuedraggable": "^4.1.0", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/eslint-parser": "^7.22.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-unit-jest": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/test-utils": "^2.4.0", "eslint": "^8.50.0", "eslint-plugin-vue": "^9.17.0", "jest": "^29.7.0", "sass": "^1.69.0", "sass-loader": "^13.3.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}