"""
简历模型的派生类
"""
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy import Column, Integer, String, Text, DateTime, Float, ForeignKey, JSON
from sqlalchemy.orm import relationship
from .base import BaseModel
from .resume_tables import resume_projects, resume_skills, resume_certifications

class ResumeProject(BaseModel):
    """简历项目经历"""
    __table__ = resume_projects
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        start_date = getattr(self, 'start_date', None)
        end_date = getattr(self, 'end_date', None)
        
        return {
            'id': self.id,
            'resume_id': self.resume_id,
            'name': self.name,
            'role': self.role,
            'description': self.description,
            'technologies': self.technologies,
            'start_date': start_date.isoformat() if start_date else None,
            'end_date': end_date.isoformat() if end_date else None,
            'order': self.order
        }

class ResumeSkill(BaseModel):
    """简历技能"""
    __table__ = resume_skills
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'resume_id': self.resume_id,
            'name': self.name,
            'level': self.level,
            'description': self.description,
            'category': self.category,
            'years_experience': self.years_experience,
            'order': self.order
        }

class ResumeCertification(BaseModel):
    """简历证书"""
    __table__ = resume_certifications
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        issue_date = getattr(self, 'issue_date', None)
        expiry_date = getattr(self, 'expiry_date', None)
        
        return {
            'id': self.id,
            'resume_id': self.resume_id,
            'name': self.name,
            'issuer': self.issuer,
            'issue_date': issue_date.isoformat() if issue_date else None,
            'expiry_date': expiry_date.isoformat() if expiry_date else None,
            'credential_id': self.credential_id,
            'credential_url': self.credential_url,
            'description': self.description,
            'gpa': self.gpa,
            'order': self.order
        }
