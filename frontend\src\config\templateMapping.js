// 前后端模板映射配置
// 将前端模板ID映射到后端模板ID和Vue组件

import BasicTemplate from '@/components/resume/templates/BasicTemplate.vue'
import ClassicTemplate from '@/components/resume/templates/ClassicTemplate.vue'
import CreativeTemplate from '@/components/resume/templates/CreativeTemplate.vue'
import MinimalistTemplate from '@/components/resume/templates/MinimalistTemplate.vue'
import ModernTwoColumnTemplate from '@/components/resume/templates/ModernTwoColumnTemplate.vue'

// 简历模板映射配置
export const templateMapping = {
  basic: {
    id: 'basic',
    name: '基础简历模板',
    description: '清晰简洁的基础简历模板，适合大多数求职场景',
    category: 'simple',
    industry: 'other',
    tags: ['简约', '通用'],
    component: BasicTemplate,
    preview_image: '/templates/basic/preview.png',
    features: ['清晰布局', '重点突出', '通用适配', '专业大方']
  },
  classic: {
    id: 'classic',
    name: '经典简历模板',
    description: '传统经典的单栏布局，适合保守行业和传统企业',
    category: 'traditional',
    industry: 'other',
    tags: ['经典', '传统'],
    component: ClassicTemplate,
    preview_image: '/templates/classic/preview.png',
    features: ['传统布局', '正式风格', '适合保守行业', '易于阅读']
  },
  creative: {
    id: 'creative',
    name: '创意简历模板',
    description: '现代创意设计，适合创意行业和年轻求职者',
    category: 'creative',
    industry: 'creative',
    tags: ['创意', '现代'],
    component: CreativeTemplate,
    preview_image: '/templates/creative/preview.png',
    features: ['创意设计', '视觉冲击', '适合创意行业', '个性突出']
  },
  minimalist: {
    id: 'minimalist',
    name: '极简简历模板',
    description: '极简主义设计，突出内容本身，适合技术岗位',
    category: 'minimal',
    industry: 'tech',
    tags: ['极简', '技术'],
    component: MinimalistTemplate,
    preview_image: '/templates/minimalist/preview.png',
    features: ['极简设计', '内容为王', '适合技术岗', '清爽简洁']
  },
  modern_two_column: {
    id: 'modern_two_column',
    name: '现代双栏模板',
    description: '现代双栏布局，信息层次清晰，适合经验丰富的求职者',
    category: 'modern',
    industry: 'other',
    tags: ['现代', '双栏'],
    component: ModernTwoColumnTemplate,
    preview_image: '/templates/modern_two_column/preview.png',
    features: ['双栏布局', '信息丰富', '层次清晰', '专业现代']
  }
}

// 获取所有模板列表
export function getAllTemplates() {
  return Object.values(templateMapping)
}

// 根据ID获取模板信息
export function getTemplateById(id) {
  return templateMapping[id] || templateMapping['basic'] // 默认返回基础模板
}

// 根据分类获取模板
export function getTemplatesByCategory(category) {
  return Object.values(templateMapping).filter(template => template.category === category)
}

// 根据行业获取模板
export function getTemplatesByIndustry(industry) {
  return Object.values(templateMapping).filter(template => template.industry === industry)
}

// 获取模板组件
export function getTemplateComponent(id) {
  const template = templateMapping[id]
  return template ? template.component : BasicTemplate
}

// 根据后端ID获取模板配置
export function getTemplateByBackendId(backendId) {
  return Object.values(templateMapping).find(template => template.id === backendId) || templateMapping['basic']
} 