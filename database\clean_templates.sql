-- 清理不需要的简历模板
-- 执行时间：2024年

-- 1. 保留基础模板和现代专业模板的ID
CREATE TEMPORARY TABLE keep_templates (id INTEGER);
INSERT INTO keep_templates (id) VALUES (1), (2);  -- 基础模板和现代专业模板的ID

-- 2. 删除其他所有模板
DELETE FROM resume_templates 
WHERE id NOT IN (SELECT id FROM keep_templates);

-- 3. 更新保留的模板信息
UPDATE resume_templates
SET name = '基础简历模板',
    description = '清晰简洁的基础简历模板，适合大多数求职场景',
    category = 'simple',
    industry = 'other',
    is_active = true,
    sort_order = 1
WHERE id = 1;

UPDATE resume_templates
SET name = '现代专业模板',
    description = '富有现代感的专业格式模板，突出重点信息',
    category = 'modern',
    industry = 'it',
    is_active = true,
    sort_order = 2
WHERE id = 2;

-- 4. 删除临时表
DROP TABLE keep_templates;

-- 5. 验证结果
SELECT id, name, description, category, industry, is_active, sort_order 
FROM resume_templates 
ORDER BY sort_order;

-- 重置自增ID
DELETE FROM sqlite_sequence WHERE name='resume_templates'; 