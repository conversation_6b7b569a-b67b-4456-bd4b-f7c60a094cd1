<template>
  <div class="business-card-editor">
    <div class="editor-header">
      <div class="header-left">
        <el-button @click="goBack" type="text">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <h1>{{ isEditing ? '编辑名片' : '创建名片' }}</h1>
      </div>
      
      <div class="header-actions">
        <el-button @click="saveAsDraft" :loading="saving">
          <el-icon><Document /></el-icon>
          保存草稿
        </el-button>
        <el-button type="primary" @click="saveAndPublish" :loading="saving">
          <el-icon><Share /></el-icon>
          保存并发布
        </el-button>
      </div>
    </div>

    <div class="editor-content">
      <!-- 左侧编辑面板 -->
      <div class="editor-panel">
        <el-scrollbar height="calc(100vh - 120px)">
          <div class="panel-content">
            <el-collapse v-model="activePanel" accordion @change="handlePanelChange">
              <!-- 基本信息 -->
              <el-collapse-item title="1. 基本信息" name="basic">
                <div class="form-section">
                  <el-form :model="businessCard" label-width="80px">
                    <el-form-item label="名片标题">
                      <el-input 
                        v-model="businessCard.title" 
                        placeholder="为这张名片起个名字"
                      />
                    </el-form-item>
                    
                    <el-form-item label="姓名">
                      <el-input 
                        v-model="businessCard.card_data.name" 
                        placeholder="请输入姓名"
                      />
                    </el-form-item>
                    
                    <el-form-item label="职位">
                      <el-input 
                        v-model="businessCard.card_data.title" 
                        placeholder="请输入职位"
                      />
                    </el-form-item>
                    
                    <el-form-item label="公司">
                      <el-input 
                        v-model="businessCard.card_data.company" 
                        placeholder="请输入公司名称"
                      />
                    </el-form-item>
                    
                    <el-form-item label="电话">
                      <el-input 
                        v-model="businessCard.card_data.phone" 
                        placeholder="请输入电话号码"
                      />
                    </el-form-item>
                    
                    <el-form-item label="邮箱">
                      <el-input 
                        v-model="businessCard.card_data.email" 
                        placeholder="请输入邮箱地址"
                      />
                    </el-form-item>
                    
                    <el-form-item label="地址">
                      <el-input 
                        v-model="businessCard.card_data.address" 
                        type="textarea" 
                        :rows="2"
                        placeholder="请输入联系地址"
                      />
                    </el-form-item>
                    
                    <el-form-item label="网站">
                      <el-input 
                        v-model="businessCard.card_data.website" 
                        placeholder="请输入网站地址"
                      />
                    </el-form-item>
                  </el-form>
                  <div class="panel-footer">
                    <el-button type="primary" @click="navigateToPanel('template')">下一步</el-button>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 模板选择 -->
              <el-collapse-item title="2. 模板选择" name="template">
                <div class="form-section">
                  <TemplateSelector 
                    v-model="businessCard.template_id"
                    :templates="templates"
                    @change="handleTemplateChange"
                  />
                  <div class="panel-footer">
                    <el-button @click="navigateToPanel('style')">下一步</el-button>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 样式配置 -->
              <el-collapse-item title="3. 样式配置" name="style">
                <div class="form-section">
                  <StyleConfigurator 
                    v-model="businessCard.style_config"
                    :template="currentTemplate"
                    @change="handleStyleChange"
                  />
                  <div class="panel-footer">
                    <el-button @click="navigateToPanel('qrcode')">下一步</el-button>
                  </div>
                </div>
              </el-collapse-item>

              <!-- 二维码设置 -->
              <el-collapse-item title="4. 二维码设置" name="qrcode">
                <div class="form-section">
                  <QRCodeSettings 
                    :business-card="businessCard"
                    @generate="generateQRCode"
                  />
                  <!-- 生成后直接显示真实二维码和下载按钮 -->
                  <div v-if="qrImageSrc" class="qr-result">
                    <img :src="qrImageSrc" alt="名片二维码" class="qr-img" @error="imgError = true" />
                    <el-button @click="downloadQRCode" type="primary" style="margin-top: 10px;">下载二维码</el-button>
                    <div v-if="imgError" style="color: #f56c6c;">二维码加载失败，请重新生成</div>
                  </div>
                  <div class="panel-footer">
                     <el-button type="success" @click="saveAsDraft">完成</el-button>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </el-scrollbar>
      </div>

      <!-- 右侧预览面板 -->
      <div class="preview-panel">
        <div class="preview-header">
          <h3>实时预览</h3>
          <div class="preview-actions">
            <el-button size="small" @click="togglePreviewSize">
              <el-icon><FullScreen /></el-icon>
              {{ previewSize === 'normal' ? '放大' : '缩小' }}
            </el-button>
            <el-button size="small" @click="downloadPreview">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
          </div>
        </div>
        
        <div class="preview-content">
          <div
            class="business-card-preview-wrapper"
            :class="{ 
              'is-large': previewSize === 'large',
              'dragging': dragging 
            }"
            :style="{
              transform: `scale(${previewSize === 'large' ? 1.5 : 1}) translate(${offset.x}px, ${offset.y}px)`
            }"
            ref="previewWrapper"
            @mousedown="onMouseDown"
          >
            <BusinessCardPreview 
              :business-card="businessCard"
              :template="currentTemplate"
              :key="previewKey"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-icon class="is-loading"><Loading /></el-icon>
      <p>加载中...</p>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElCollapse, ElCollapseItem } from 'element-plus'
import { 
  ArrowLeft, Document, Share, FullScreen, Download, Loading 
} from '@element-plus/icons-vue'
import api, { handleApiError } from '@/api'
import TemplateSelector from '@/components/business-card/TemplateSelector.vue'
import StyleConfigurator from '@/components/business-card/StyleConfigurator.vue'
import QRCodeSettings from '@/components/business-card/QRCodeSettings.vue'
import BusinessCardPreview from '@/components/business-card/BusinessCardPreview.vue'

export default {
  name: 'BusinessCardEditor',
  
  components: {
    ArrowLeft, Document, Share, FullScreen, Download, Loading,
    TemplateSelector, StyleConfigurator, QRCodeSettings, BusinessCardPreview,
    ElCollapse,
    ElCollapseItem,
  },

  setup() {
    const route = useRoute()
    const router = useRouter()
    
    // 响应式数据
    const loading = ref(false)
    const saving = ref(false)
    const templates = ref([])
    const previewKey = ref(0)
    const previewSize = ref('normal')
    // 拖拽相关
    const dragging = ref(false)
    const offset = ref({ x: 0, y: 0 })
    const lastPos = ref({ x: 0, y: 0 })
    const previewWrapper = ref(null)
    const lastQrResult = ref({ base64: '' })
    const qrImage = ref('')
    const imgError = ref(false)
    
    const businessCard = reactive({
      id: null,
      title: '',
      template_id: null,
      card_data: {
        name: '',
        title: '',
        company: '',
        phone: '',
        email: '',
        address: '',
        website: ''
      },
      style_config: {},
      status: 'draft'
    })

    // 计算属性
    const isEditing = computed(() => !!route.params.id)
    
    const currentTemplate = computed(() => {
      return templates.value.find(t => t.id === businessCard.template_id)
    })

    const activePanel = ref('basic') // 默认展开第一个

    const qrImageSrc = computed(() => {
      // 优先base64，其次qr_code_image，再相对路径兜底
      const val = qrImage.value || lastQrResult.value.base64 || businessCard.qr_code_image
      if (!val) return ''
      if (val.startsWith('data:')) return val
      if (/^https?:\/\//.test(val)) return val
      // 相对路径兜底
      return `${api.defaults.baseURL}${val}`
    })

    // 加载模板列表
    const loadTemplates = async () => {
      try {
        const response = await api.businessCards.getTemplates()
        if (response.data.success) {
          templates.value = response.data.data.templates
          
          // 如果是创建模式且没有选择模板，选择第一个
          if (!isEditing.value && !businessCard.template_id && templates.value.length > 0) {
            businessCard.template_id = templates.value[0].id
          }
        }
      } catch (error) {
        handleApiError(error, '加载模板失败')
      }
    }

    // 加载名片数据
    const loadBusinessCard = async () => {
      if (!isEditing.value) return
      
      loading.value = true
      try {
        const response = await api.businessCards.getDetail(route.params.id)
        if (response.data.success) {
          const data = response.data.data.business_card
          
          businessCard.id = data.id
          businessCard.title = data.title
          businessCard.template_id = data.template_id
          businessCard.status = data.status
          
          // 通过逐个属性赋值来保持 card_data 和 style_config 的响应性
          if (data.card_data) {
            Object.assign(businessCard.card_data, data.card_data)
          }
          if (data.style_config) {
            Object.assign(businessCard.style_config, data.style_config)
          }

          // 强制刷新预览
          previewKey.value++
        }
      } catch (error) {
        handleApiError(error, '加载名片数据失败')
        router.push('/business-cards')
      } finally {
        loading.value = false
      }
    }

    // 保存名片
    const saveBusinessCard = async (status = 'draft') => {
      if (!businessCard.title.trim()) {
        ElMessage.error('请输入名片标题')
        return false
      }
      
      if (!businessCard.template_id) {
        ElMessage.error('请选择名片模板')
        return false
      }

      saving.value = true
      try {
        const data = {
          title: businessCard.title,
          template_id: businessCard.template_id,
          card_data: businessCard.card_data,
          style_config: businessCard.style_config,
          status: status
        }

        let response
        if (isEditing.value) {
          response = await api.businessCards.update(businessCard.id, data)
        } else {
          response = await api.businessCards.create(data)
        }

        if (response.data.success) {
          const savedCard = response.data.data.business_card
          
          if (!isEditing.value) {
            // 创建成功后更新路由和数据
            businessCard.id = savedCard.id
            router.replace(`/business-card/edit/${savedCard.id}`)
          }
          
          businessCard.status = savedCard.status
          
          ElMessage.success(status === 'published' ? '发布成功' : '保存成功')
          return true
        }
      } catch (error) {
        handleApiError(error, '保存失败')
        return false
      } finally {
        saving.value = false
      }
    }

    // 保存草稿
    const saveAsDraft = () => {
      saveBusinessCard('draft')
    }

    // 保存并发布
    const saveAndPublish = async () => {
      const success = await saveBusinessCard('published')
      if (success) {
        // 可以跳转到预览页面或名片管理页面
        router.push('/business-cards')
      }
    }

    // 模板变化处理
    const handleTemplateChange = (templateId) => {
      businessCard.template_id = templateId
      
      // 重置样式配置为模板默认值
      const template = templates.value.find(t => t.id === templateId)
      if (template && template.default_colors) {
        businessCard.style_config = { ...template.default_colors }
      }
      
      // 触发预览更新
      previewKey.value++
    }

    // 样式变化处理
    const handleStyleChange = (styleConfig) => {
      businessCard.style_config = { ...styleConfig }
      previewKey.value++
    }

    // 生成二维码
    const generateQRCode = async (qrConfig) => {
      if (!businessCard.id) {
        ElMessage.warning('请先保存名片再生成二维码')
        return
      }

      try {
        console.log('生成二维码配置:', qrConfig)
        const response = await api.businessCards.generateQR(businessCard.id, qrConfig)
        if (response.data.success) {
          ElMessage.success('二维码生成成功')
          // 优先base64_data
          qrImage.value = response.data.data.qr_code?.base64_data || response.data.data.qr_code?.base64 || response.data.data.qr_code?.file_url || ''
          // 更新名片数据，确保二维码信息同步
          if (response.data.data.business_card) {
            Object.assign(businessCard, response.data.data.business_card)
          }
          // base64兜底
          lastQrResult.value.base64 = response.data.data.qr_code?.base64_data || response.data.data.qr_code?.base64 || ''
          previewKey.value++
        }
      } catch (error) {
        console.error('生成二维码失败:', error)
        handleApiError(error, '生成二维码失败')
      }
    }

    // 切换预览大小
    const togglePreviewSize = () => {
      previewSize.value = previewSize.value === 'normal' ? 'large' : 'normal';
      // 如果是缩小操作，则重置拖动偏移量
      if (previewSize.value === 'normal') {
        offset.value = { x: 0, y: 0 };
      }
    }

    // 下载预览
    const downloadPreview = async () => {
      if (!businessCard.template_id) {
        ElMessage.warning('请先选择一个模板')
        return
      }

      try {
        // 获取预览组件实例
        const previewComponent = document.querySelector('.business-card-preview-wrapper .business-card-preview')
        if (!previewComponent) {
          throw new Error('预览组件未找到')
        }

        // 导入工具函数
        const { elementToImage, downloadImage } = await import('@/utils/htmlToImage.js')
        
        // 转换为图片
        const blob = await elementToImage(previewComponent, {
          scale: 3, // 高清晰度
          backgroundColor: '#ffffff',
          width: 900,
          height: 540
        })
        
        // 下载
        const filename = `${businessCard.title || 'business-card'}.png`
        downloadImage(blob, filename)
        
        ElMessage.success('名片已开始下载')
      } catch (error) {
        console.error('下载失败:', error)
        ElMessage.error('下载失败，请重试')
      }
    }

    // 下载二维码支持base64
    const downloadQRCode = () => {
      const url = qrImageSrc.value
      if (!url) return
      const link = document.createElement('a')
      link.href = url
      link.download = `${businessCard.title || 'business-card'}_qr.png`
      link.click()
    }

    // 返回
    const goBack = async () => {
      // 检查是否有未保存的更改
      const hasChanges = businessCard.title || 
                        businessCard.card_data.name || 
                        businessCard.card_data.company
      
      if (hasChanges && !businessCard.id) {
        try {
          await ElMessageBox.confirm(
            '您有未保存的更改，确定要离开吗？',
            '确认离开',
            {
              confirmButtonText: '离开',
              cancelButtonText: '取消',
              type: 'warning'
            }
          )
        } catch {
          return
        }
      }
      
      router.push('/business-cards')
    }

    // 自动保存
    let autoSaveTimer = null
    const startAutoSave = () => {
      clearTimeout(autoSaveTimer)
      autoSaveTimer = setTimeout(() => {
        if (businessCard.id && businessCard.title.trim()) {
          saveBusinessCard('draft')
        }
      }, 30000) // 30秒自动保存
    }

    // 监听数据变化，触发自动保存
    watch(() => [businessCard.title, businessCard.card_data], () => {
      if (businessCard.id) {
        startAutoSave()
      }
    }, { deep: true })

    function onMouseDown(e) {
      if (previewSize.value === 'normal') return;
      dragging.value = true;
      lastPos.value = { x: e.clientX, y: e.clientY };
      document.body.style.cursor = 'grabbing';
    }
    function onMouseMove(e) {
      if (!dragging.value) return;
      offset.value.x += e.clientX - lastPos.value.x;
      offset.value.y += e.clientY - lastPos.value.y;
      lastPos.value = { x: e.clientX, y: e.clientY };
    }
    function onMouseUp() {
      dragging.value = false;
      document.body.style.cursor = '';
    }

    const navigateToPanel = (panelName) => {
      activePanel.value = panelName;
    };

    const handlePanelChange = () => {
      // 可选：当用户手动切换面板时执行的逻辑
    };

    // 动态渲染名片表单区，根据当前模板的字段配置（fieldsConfig），只显示visible=true的字段，按order排序，必填字段加*。
    const fieldsConfig = ref([
      { key: 'avatar', label: '头像', required: false, visible: true, order: 1 },
      { key: 'name', label: '姓名', required: true, visible: true, order: 2 },
      { key: 'title', label: '职位', required: false, visible: true, order: 3 },
      { key: 'company', label: '公司', required: false, visible: true, order: 4 },
      { key: 'phone', label: '电话', required: false, visible: true, order: 5 },
      { key: 'email', label: '邮箱', required: false, visible: true, order: 6 },
      { key: 'address', label: '地址', required: false, visible: true, order: 7 },
      { key: 'website', label: '网站', required: false, visible: true, order: 8 }
    ])
    const sortedFields = computed(() => fieldsConfig.value.filter(f => f.visible).sort((a, b) => a.order - b.order))

    // 生命周期
    onMounted(async () => {
      await loadTemplates()
      await loadBusinessCard()

      // 确保首次加载时 style_config 有默认值
      if (!businessCard.style_config || Object.keys(businessCard.style_config).length === 0) {
        businessCard.style_config = {
          fontSize: 100, // 默认字体大小
          // 在此可以设置其他默认样式
        };
      } else if (!businessCard.style_config.fontSize) {
        businessCard.style_config.fontSize = 100;
      }

      window.addEventListener('mousemove', onMouseMove);
      window.addEventListener('mouseup', onMouseUp);
    })
    onBeforeUnmount(() => {
      window.removeEventListener('mousemove', onMouseMove);
      window.removeEventListener('mouseup', onMouseUp);
    });

    return {
      loading,
      saving,
      templates,
      businessCard,
      previewKey,
      previewSize,
      isEditing,
      currentTemplate,
      saveAsDraft,
      saveAndPublish,
      handleTemplateChange,
      handleStyleChange,
      generateQRCode,
      togglePreviewSize,
      downloadPreview,
      goBack,
      dragging,
      offset,
      previewWrapper,
      onMouseDown,
      activePanel,
      navigateToPanel,
      handlePanelChange,
      downloadQRCode,
      lastQrResult,
      qrImage,
      qrImageSrc,
      imgError,
      sortedFields,
    }
  }
}
</script>

<style scoped>
.business-card-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.header-left h1 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.editor-content {
  flex: 1;
  display: flex;
  min-height: 0;
}

.editor-panel {
  width: 400px;
  background: white;
  border-right: 1px solid #e4e7ed;
}

.panel-content {
  padding: 0 10px;
}

.form-section {
  padding: 10px;
}

.panel-footer {
  margin-top: 15px;
  text-align: right;
}

.preview-panel {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.preview-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  flex: 1;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  overflow: hidden; /* 隐藏溢出，通过拖动查看 */
  user-select: none; /* 防止拖动时选中文本 */
}

.business-card-preview-wrapper {
  transition: transform 0.2s ease-out;
  transform-origin: center;
  /* 默认由父级 flex 布局居中 */
}

.business-card-preview-wrapper.is-large {
  cursor: grab;
}

.business-card-preview-wrapper.is-large.dragging {
  cursor: grabbing;
  transition: none; /* 拖动时移除动画，使其更跟手 */
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-overlay .el-icon {
  font-size: 40px;
  color: #409eff;
  margin-bottom: 15px;
}

.loading-overlay p {
  color: #606266;
  font-size: 14px;
}

.qr-result {
  margin-top: 20px;
  text-align: center;
}
.qr-img {
  width: 300px;
  height: 300px;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }
  
  .editor-panel {
    width: 100%;
    height: 50vh;
  }
  
  .preview-panel {
    height: 50vh;
  }
  
  .header-left h1 {
    font-size: 16px;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 5px;
  }
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 10px;
}
</style> 