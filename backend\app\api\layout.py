# -*- coding: utf-8 -*-
"""
排版和布局相关API
"""

from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from app.api import api_bp
from app.models.paper_size import PaperSize
from app.models.photo_template import PhotoTemplate
from app.api.errors import bad_request, not_found, error_response, success_response
from app import db
import math


@api_bp.route('/layout/paper-sizes', methods=['GET'])
def get_paper_sizes():
    """获取所有相纸规格"""
    try:
        current_app.logger.info("=== 开始获取相纸规格 ===")
        # 暂时移除is_active过滤，因为数据库中还没有这个字段
        paper_sizes = PaperSize.query.order_by(PaperSize.sort_order).all()
        current_app.logger.info(f"查询到 {len(paper_sizes)} 个相纸规格")

        # 如果没有数据，创建默认数据
        if not paper_sizes:
            current_app.logger.info("没有找到相纸规格数据，正在创建默认数据...")

            # 简化的相纸规格数据
            default_paper_sizes = [
                {'name': '4寸照片', 'width_mm': 101.6, 'height_mm': 152.4, 'width_px': 1200, 'height_px': 1800, 'sort_order': 1},
                {'name': '5寸照片', 'width_mm': 127.0, 'height_mm': 178.0, 'width_px': 1500, 'height_px': 2100, 'sort_order': 2},
                {'name': '6寸照片', 'width_mm': 152.4, 'height_mm': 203.2, 'width_px': 1800, 'height_px': 2400, 'sort_order': 3},
                {'name': 'A4纸', 'width_mm': 210.0, 'height_mm': 297.0, 'width_px': 2480, 'height_px': 3508, 'sort_order': 4}
            ]

            # 创建相纸规格记录
            for data in default_paper_sizes:
                paper_size = PaperSize(**data)
                db.session.add(paper_size)

            db.session.commit()
            current_app.logger.info(f"成功创建 {len(default_paper_sizes)} 个相纸规格")

            # 重新查询
            paper_sizes = PaperSize.query.order_by(PaperSize.sort_order).all()

        paper_sizes_data = []
        for paper in paper_sizes:
            paper_data = {
                'id': paper.id,
                'name': paper.name,
                'width_mm': paper.width_mm,
                'height_mm': paper.height_mm,
                'width_px': paper.width_px,
                'height_px': paper.height_px,
                'printable_width_mm': getattr(paper, 'printable_width_mm', paper.width_mm - 20),
                'printable_height_mm': getattr(paper, 'printable_height_mm', paper.height_mm - 20),
                'margin_mm': getattr(paper, 'margin_mm', 10),
                'is_common': paper.is_common,
                'sort_order': paper.sort_order,
                'size_display': f"{paper.width_mm}×{paper.height_mm}mm"
            }
            paper_sizes_data.append(paper_data)
        
        return success_response(
            message='相纸规格加载成功',
            data={'paper_sizes': paper_sizes_data}
        )

    except Exception as e:
        current_app.logger.error(f"获取相纸规格失败: {str(e)}")
        return error_response('加载相纸规格失败')


@api_bp.route('/layout/test-paper-sizes', methods=['GET'])
def test_paper_sizes():
    """测试相纸规格API响应格式"""
    try:
        current_app.logger.info("=== 测试相纸规格API ===")

        # 模拟数据
        test_data = [
            {
                'id': 1,
                'name': 'A4纸',
                'width_mm': 210.0,
                'height_mm': 297.0,
                'width_px': 2480,
                'height_px': 3508,
                'printable_width_mm': 190.0,
                'printable_height_mm': 277.0,
                'margin_mm': 10.0,
                'is_common': True,
                'sort_order': 1,
                'size_display': '210.0×297.0mm'
            }
        ]

        response_data = {
            'paper_sizes': test_data
        }

        current_app.logger.info(f"测试响应数据: {response_data}")

        result = success_response(
            message='测试相纸规格数据',
            data=response_data
        )

        current_app.logger.info(f"最终测试响应: {result}")
        return result

    except Exception as e:
        current_app.logger.error(f"测试相纸规格失败: {str(e)}")
        return error_response('测试失败')


@api_bp.route('/layout/init-paper-sizes', methods=['POST'])
def init_paper_sizes():
    """初始化相纸规格数据（临时端点）"""
    try:
        # 检查是否已有数据
        existing_count = PaperSize.query.count()
        if existing_count > 0:
            return success_response(
                message=f'已存在 {existing_count} 个相纸规格',
                data={'count': existing_count}
            )

        # 常用相纸规格数据（暂时不包含is_active字段）
        paper_sizes_data = [
            {
                'name': '4寸照片',
                'width_mm': 101.6,
                'height_mm': 152.4,
                'width_px': 1200,
                'height_px': 1800,
                'margin_mm': 5.0,
                'is_common': True,
                'sort_order': 1
            },
            {
                'name': '5寸照片',
                'width_mm': 127.0,
                'height_mm': 178.0,
                'width_px': 1500,
                'height_px': 2100,
                'margin_mm': 5.0,
                'is_common': True,
                'sort_order': 2
            },
            {
                'name': '6寸照片',
                'width_mm': 152.4,
                'height_mm': 203.2,
                'width_px': 1800,
                'height_px': 2400,
                'margin_mm': 5.0,
                'is_common': True,
                'sort_order': 3
            },
            {
                'name': 'A4纸',
                'width_mm': 210.0,
                'height_mm': 297.0,
                'width_px': 2480,
                'height_px': 3508,
                'margin_mm': 10.0,
                'is_common': True,
                'sort_order': 4
            }
        ]

        # 创建相纸规格记录
        for data in paper_sizes_data:
            paper_size = PaperSize(**data)
            db.session.add(paper_size)

        db.session.commit()

        return success_response(
            message=f'成功创建 {len(paper_sizes_data)} 个相纸规格',
            data={'count': len(paper_sizes_data)}
        )

    except Exception as e:
        current_app.logger.error(f"初始化相纸规格失败: {str(e)}")
        db.session.rollback()
        return error_response('初始化相纸规格失败')


@api_bp.route('/layout/calculate/<int:paper_id>', methods=['POST'])
def calculate_layout(paper_id):
    """计算排版布局"""
    try:
        data = request.get_json() or {}
        
        # 获取相纸规格
        paper_size = PaperSize.query.get(paper_id)
        if not paper_size:
            return not_found('相纸规格不存在')
        
        # 获取照片尺寸参数
        photo_width_mm = data.get('photo_width_mm')
        photo_height_mm = data.get('photo_height_mm')
        spacing_mm = data.get('spacing_mm', 2)  # 默认间距2mm
        orientation = data.get('orientation', 'portrait')  # 纸张方向
        
        if not photo_width_mm or not photo_height_mm:
            return bad_request('缺少照片尺寸参数')
        
        # 根据纸张方向调整纸张尺寸
        if orientation == 'landscape':
            paper_width = float(paper_size.height_mm)
            paper_height = float(paper_size.width_mm)
        else:
            paper_width = float(paper_size.width_mm)
            paper_height = float(paper_size.height_mm)
        
        # 使用系统默认边距，移除手动边距设置
        default_margin = float(getattr(paper_size, 'margin_mm', 10))
        top_margin = default_margin
        bottom_margin = default_margin
        left_margin = default_margin
        right_margin = default_margin
            
        # 计算可用打印区域（减去边距）
        printable_width = float(paper_width) - (float(left_margin) + float(right_margin))
        printable_height = float(paper_height) - (float(top_margin) + float(bottom_margin))
        
        # 计算可以排列的行数和列数
        cols = int(float(printable_width) // (float(photo_width_mm) + float(spacing_mm)))
        rows = int(float(printable_height) // (float(photo_height_mm) + float(spacing_mm)))
        
        # 确保至少能放下一张照片
        if cols < 1:
            cols = 1
        if rows < 1:
            rows = 1
        
        # 计算总照片数量
        total_photos = rows * cols
        
        # 计算实际使用的尺寸
        used_width = cols * float(photo_width_mm) + (cols - 1) * float(spacing_mm)
        used_height = rows * float(photo_height_mm) + (rows - 1) * float(spacing_mm)
        
        # 计算居中偏移
        offset_x = (float(printable_width) - float(used_width)) / 2 + float(left_margin)
        offset_y = (float(printable_height) - float(used_height)) / 2 + float(top_margin)
        
        # 计算每张照片的位置
        photo_positions = []
        for row in range(rows):
            for col in range(cols):
                x = offset_x + col * (photo_width_mm + spacing_mm)
                y = offset_y + row * (photo_height_mm + spacing_mm)
                photo_positions.append({
                    'x': x,
                    'y': y,
                    'width': photo_width_mm,
                    'height': photo_height_mm
                })
        
        layout_data = {
            'paper_size': {
                'id': paper_size.id,
                'name': paper_size.name,
                'width_mm': paper_width,
                'height_mm': paper_height,
                'orientation': orientation
            },
            'layout': {
                'rows': rows,
                'cols': cols,
                'total_photos': total_photos,
                'spacing_mm': spacing_mm,
                'top_margin_mm': top_margin,
                'bottom_margin_mm': bottom_margin,
                'left_margin_mm': left_margin,
                'right_margin_mm': right_margin
            },
            'dimensions': {
                'printable_width': printable_width,
                'printable_height': printable_height,
                'used_width': used_width,
                'used_height': used_height,
                'offset_x': offset_x,
                'offset_y': offset_y
            },
            'photo_positions': photo_positions
        }
        
        return success_response(
            message='排版计算成功',
            data=layout_data
        )
        
    except Exception as e:
        current_app.logger.error(f"排版计算失败: {str(e)}")
        return error_response('排版计算失败')


@api_bp.route('/layout/preview', methods=['POST'])
def preview_layout():
    """生成排版预览"""
    try:
        data = request.get_json() or {}
        
        paper_id = data.get('paper_id')
        photo_data = data.get('photo_data')  # base64图片数据
        layout_config = data.get('layout_config', {})
        
        if not paper_id or not photo_data:
            return bad_request('缺少必要参数')
        
        # 获取相纸规格
        paper_size = PaperSize.query.get(paper_id)
        if not paper_size:
            return not_found('相纸规格不存在')
        
        # 这里可以添加生成排版预览图的逻辑
        # 暂时返回成功响应
        
        return success_response(
            message='排版预览生成成功',
            data={'preview_url': '/api/layout/preview-image'}
        )
        
    except Exception as e:
        current_app.logger.error(f"生成排版预览失败: {str(e)}")
        return error_response('生成排版预览失败')


@api_bp.route('/layout/templates', methods=['GET'])
def get_layout_templates():
    """获取排版模板"""
    try:
        templates = PhotoTemplate.query.filter_by(is_active=True).order_by(PhotoTemplate.sort_order).all()
        
        templates_data = []
        for template in templates:
            template_data = {
                'id': template.id,
                'name': template.name,
                'category': template.category,
                'width': template.width,
                'height': template.height,
                'width_mm': template.width_mm,
                'height_mm': template.height_mm,
                'description': template.description,
                'sort_order': template.sort_order
            }
            templates_data.append(template_data)
        
        return success_response(
            message='排版模板加载成功',
            data={'templates': templates_data}
        )
        
    except Exception as e:
        current_app.logger.error(f"获取排版模板失败: {str(e)}")
        return error_response('加载排版模板失败')


@api_bp.route('/layout/optimize', methods=['POST'])
def optimize_layout():
    """优化排版布局"""
    try:
        data = request.get_json() or {}
        
        paper_id = data.get('paper_id')
        photo_width_mm = data.get('photo_width_mm')
        photo_height_mm = data.get('photo_height_mm')
        min_spacing_mm = data.get('min_spacing_mm', 1)
        max_spacing_mm = data.get('max_spacing_mm', 5)
        
        if not all([paper_id, photo_width_mm, photo_height_mm]):
            return bad_request('缺少必要参数')
        
        # 获取相纸规格
        paper_size = PaperSize.query.get(paper_id)
        if not paper_size:
            return not_found('相纸规格不存在')
        
        # 尝试不同的间距值，找到最优布局
        best_layout = None
        max_photos = 0
        
        for spacing in range(int(min_spacing_mm), int(max_spacing_mm) + 1):
            # 计算当前间距下的布局
            margin_mm = getattr(paper_size, 'margin_mm', 10)
            printable_width = paper_size.width_mm - (2 * margin_mm)
            printable_height = paper_size.height_mm - (2 * margin_mm)
            
            cols = int(printable_width // (photo_width_mm + spacing))
            rows = int(printable_height // (photo_height_mm + spacing))
            
            if cols < 1:
                cols = 1
            if rows < 1:
                rows = 1
            
            total_photos = rows * cols
            
            if total_photos > max_photos:
                max_photos = total_photos
                best_layout = {
                    'spacing_mm': spacing,
                    'rows': rows,
                    'cols': cols,
                    'total_photos': total_photos
                }
        
        if not best_layout:
            return error_response('无法找到合适的排版布局')
        
        return success_response(
            message='排版优化完成',
            data={'optimized_layout': best_layout}
        )
        
    except Exception as e:
        current_app.logger.error(f"排版优化失败: {str(e)}")
        return error_response('排版优化失败')
