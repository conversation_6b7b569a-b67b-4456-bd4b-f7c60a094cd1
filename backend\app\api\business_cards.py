import os
import uuid
import traceback
from datetime import datetime
from flask import request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
import io
import base64

from . import api_bp
from .errors import bad_request, unauthorized, not_found, validation_error
from app import db
from app.models import User
from app.models.business_card import BusinessCard, BusinessCardTemplate
from app.services.qr_service import QRCodeService
from app.services.print_layout_service import PrintLayoutService
from app.services.business_card_generator import BusinessCardGenerator
from app.utils.response import success_response, error_response

@api_bp.route('/business-card-templates', methods=['GET'])
def get_business_card_templates():
    """获取所有名片模板"""
    try:
        category = request.args.get('category')
        industry = request.args.get('industry')
        
        query = BusinessCardTemplate.query.filter_by(is_active=True)
        
        if category:
            query = query.filter_by(category=category)
        if industry:
            query = query.filter_by(industry=industry)
        
        templates = query.order_by(BusinessCardTemplate.sort_order).all()
        
        return success_response(data={
            'templates': [template.to_dict() for template in templates]
        })
        
    except Exception as e:
        current_app.logger.error(f"获取名片模板失败: {str(e)}")
        return error_response('获取模板失败')

@api_bp.route('/business-card-templates/<int:template_id>', methods=['GET'])
def get_business_card_template(template_id):
    """获取单个名片模板详情"""
    try:
        template = BusinessCardTemplate.query.filter_by(
            id=template_id, 
            is_active=True
        ).first()
        
        if not template:
            return not_found('模板不存在')
        
        return success_response(data={'template': template.to_dict()})
        
    except Exception as e:
        current_app.logger.error(f"获取名片模板详情失败: {str(e)}")
        return error_response('获取模板详情失败')

@api_bp.route('/business-cards', methods=['GET'])
@jwt_required()
def get_user_business_cards():
    """获取用户的名片列表"""
    try:
        current_user_id = int(get_jwt_identity())
        
        # 分页参数
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status')
        
        query = BusinessCard.query.filter_by(user_id=current_user_id)
        
        if status:
            query = query.filter_by(status=status)
        
        pagination = query.order_by(BusinessCard.updated_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        cards = [card.to_dict(include_template=True) for card in pagination.items]
        
        return success_response(data={
            'business_cards': cards,
            'pagination': {
                'page': pagination.page,
                'pages': pagination.pages,
                'per_page': pagination.per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取用户名片列表失败: {str(e)}")
        return error_response('获取名片列表失败')

@api_bp.route('/business-cards', methods=['POST'])
@jwt_required()
def create_business_card():
    """创建新名片"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()
        
        # 验证必需字段
        if not data or 'template_id' not in data or 'title' not in data:
            return bad_request('缺少必需字段')
        
        template_id = data.get('template_id')
        title = data.get('title', '').strip()
        card_data = data.get('card_data', {})
        style_config = data.get('style_config', {})
        
        if not title:
            return bad_request('名片标题不能为空')
        
        # 验证模板是否存在
        template = BusinessCardTemplate.query.filter_by(
            id=template_id, 
            is_active=True
        ).first()
        
        if not template:
            return bad_request('选择的模板不存在')
        
        # 创建名片
        business_card = BusinessCard(
            user_id=current_user_id,
            template_id=template_id,
            title=title,
            card_data=card_data,
            style_config=style_config,
            status='draft'
        )
        
        db.session.add(business_card)
        db.session.commit()
        
        # 增加模板使用次数
        template.increment_usage()
        db.session.commit()
        
        return success_response(
            message='名片创建成功',
            data={'business_card': business_card.to_dict(include_template=True)}
        ), 201
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建名片失败: {str(e)}")
        return error_response('创建名片失败')

@api_bp.route('/business-cards/<int:card_id>', methods=['GET'])
@jwt_required()
def get_business_card(card_id):
    """获取名片详情"""
    try:
        current_user_id = int(get_jwt_identity())
        
        business_card = BusinessCard.query.filter_by(
            id=card_id,
            user_id=current_user_id
        ).first()
        
        if not business_card:
            return not_found('名片不存在')
        
        return success_response(data={
            'business_card': business_card.to_dict(include_template=True)
        })
        
    except Exception as e:
        current_app.logger.error(f"获取名片详情失败: {str(e)}")
        return error_response('获取名片详情失败')

@api_bp.route('/business-cards/<int:card_id>', methods=['PUT'])
@jwt_required()
def update_business_card(card_id):
    """更新名片"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()
        
        business_card = BusinessCard.query.filter_by(
            id=card_id,
            user_id=current_user_id
        ).first()
        
        if not business_card:
            return not_found('名片不存在')
        
        # 更新字段
        if 'title' in data:
            title = data['title'].strip()
            if not title:
                return bad_request('名片标题不能为空')
            business_card.title = title
        
        if 'card_data' in data:
            business_card.update_card_data(data['card_data'])
        
        if 'style_config' in data:
            business_card.update_style_config(data['style_config'])
        
        if 'status' in data:
            if data['status'] in ['draft', 'published', 'archived']:
                business_card.status = data['status']
        
        business_card.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            message='名片更新成功',
            data={'business_card': business_card.to_dict(include_template=True)}
        )
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新名片失败: {str(e)}")
        return error_response('更新名片失败')

@api_bp.route('/business-cards/<int:card_id>', methods=['DELETE'])
@jwt_required()
def delete_business_card(card_id):
    """删除名片"""
    try:
        current_user_id = int(get_jwt_identity())
        
        business_card = BusinessCard.query.filter_by(
            id=card_id,
            user_id=current_user_id
        ).first()
        
        if not business_card:
            return not_found('名片不存在')
        
        # 删除相关文件（二维码图片等）
        if business_card.qr_code_image:
            try:
                qr_file_path = os.path.join(current_app.config['STATIC_FOLDER'], business_card.qr_code_image.lstrip('/static/'))
                if os.path.exists(qr_file_path):
                    os.remove(qr_file_path)
            except Exception as e:
                current_app.logger.warning(f"删除二维码文件失败: {str(e)}")
        
        db.session.delete(business_card)
        db.session.commit()
        
        return success_response(message='名片删除成功')
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除名片失败: {str(e)}")
        return error_response('删除名片失败')

@api_bp.route('/business-cards/batch-delete', methods=['POST'])
@jwt_required()
def batch_delete_business_cards():
    """批量删除名片"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json()
        ids = data.get('ids', [])
        if not ids or not isinstance(ids, list):
            return bad_request('参数错误，缺少名片ID列表')
        cards = BusinessCard.query.filter(BusinessCard.id.in_(ids), BusinessCard.user_id == current_user_id).all()
        if not cards:
            return not_found('未找到可删除的名片')
        for card in cards:
            # 删除相关文件（如二维码图片）
            if card.qr_code_image:
                try:
                    qr_file_path = os.path.join(current_app.config['STATIC_FOLDER'], card.qr_code_image.lstrip('/static/'))
                    if os.path.exists(qr_file_path):
                        os.remove(qr_file_path)
                except Exception as e:
                    current_app.logger.warning(f"删除二维码文件失败: {str(e)}")
            db.session.delete(card)
        db.session.commit()
        return success_response(message=f'成功删除{len(cards)}个名片')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量删除名片失败: {str(e)}")
        return error_response('批量删除名片失败')

@api_bp.route('/business-cards/render-preview', methods=['POST'])
@jwt_required()
def render_business_card_preview():
    """实时渲染名片预览图"""
    try:
        data = request.get_json()
        if not data:
            return bad_request('缺少请求数据')

        template_id = data.get('template_id')
        card_data = data.get('card_data', {})
        style_config = data.get('style_config', {})

        if not template_id:
            return bad_request('缺少模板ID')

        template = BusinessCardTemplate.query.get(template_id)
        if not template:
            return not_found('模板不存在')
        
        # 使用新的名片生成器
        generator = BusinessCardGenerator()
        
        # 准备预览数据
        preview_data = {
            'name': card_data.get('name', ''),
            'title': card_data.get('title', ''),
            'company': card_data.get('company', ''),
            'phone': card_data.get('phone', ''),
            'email': card_data.get('email', ''),
            'address': card_data.get('address', ''),
            'website': card_data.get('website', ''),
            'style_config': style_config
        }
        
        # 根据数据库模板ID映射到后端模板ID
        template_mapping = {
            1: 'business_classic',
            2: 'modern_minimal', 
            3: 'creative_design',
            4: 'elegant_business'
        }
        backend_template_id = template_mapping.get(template_id, 'business_classic')
        
        # 生成预览HTML
        html_content = generator.preview_from_data(preview_data, backend_template_id)
        
        return success_response(data={'html_content': html_content})

    except Exception as e:
        current_app.logger.error(f"渲染名片预览失败: {str(e)}\n{traceback.format_exc()}")
        return error_response('渲染预览图失败')

def _render_card_dynamically(template, card_data, style_config):
    """
    一个简化的动态名片渲染函数。
    理想情况下，这应该是一个更健壮的、基于模板HTML/CSS的渲染服务的一部分。
    """
    from PIL import Image, ImageDraw, ImageFont
    import platform

    def get_system_font(style='regular'):
        system = platform.system()
        # 简单的字体选择
        if system == "Windows":
            return "C:/Windows/Fonts/msyh.ttc"  # 微软雅黑
        elif system == "Darwin":
            return "/System/Library/Fonts/PingFang.ttc"
        else: # Linux (需要确保字体存在)
            font_path = "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
            if os.path.exists(font_path):
                return font_path
            # 提供一个备用路径
            font_path_alt = "/usr/share/fonts/TTF/DejaVuSans.ttf"
            if os.path.exists(font_path_alt):
                return font_path_alt
        return None # 返回None，让Pillow使用默认字体

    # 从毫米到像素的转换 (基于300 DPI)
    card_width_px = int(90 * 300 / 25.4)
    card_height_px = int(54 * 300 / 25.4)
    
    # 从样式配置中获取颜色，或使用默认值
    bg_color = style_config.get('backgroundColor', '#FFFFFF')
    text_color = style_config.get('textColor', '#000000')
    name_color = style_config.get('nameColor', '#000000')
    company_color = style_config.get('companyColor', '#333333')

    image = Image.new('RGB', (card_width_px, card_height_px), color=bg_color)
    draw = ImageDraw.Draw(image)

    # 加载字体，如果找不到则使用Pillow的默认字体
    try:
        font_name = ImageFont.truetype(get_system_font('bold'), 60)
        font_title = ImageFont.truetype(get_system_font(), 40)
        font_company = ImageFont.truetype(get_system_font(), 42)
        font_contact = ImageFont.truetype(get_system_font(), 32)
    except (IOError, TypeError):
        font_name = ImageFont.load_default()
        font_title = ImageFont.load_default()
        font_company = ImageFont.load_default()
        font_contact = ImageFont.load_default()

    # 简化的布局逻辑 (可以根据模板设计进行改进)
    # 这里使用简单的Y坐标定位，实际应用中可以从模板元数据中读取布局
    y_pos = 40
    padding = 10

    # 姓名
    if card_data.get('name'):
        draw.text((50, y_pos), card_data['name'], font=font_name, fill=name_color)
        y_pos += font_name.getbbox(card_data['name'])[3] + padding
    
    # 职位
    if card_data.get('title'):
        y_pos += padding
        draw.text((50, y_pos), card_data['title'], font=font_title, fill=text_color)
        y_pos += font_title.getbbox(card_data['title'])[3] + padding

    # 公司
    if card_data.get('company'):
        y_pos += padding
        draw.text((50, y_pos), card_data['company'], font=font_company, fill=company_color)
        y_pos += font_company.getbbox(card_data['company'])[3] + padding * 3

    # 联系信息
    contact_info = [
        f"电话: {card_data.get('phone', '')}",
        f"邮箱: {card_data.get('email', '')}",
        f"地址: {card_data.get('address', '')}",
        f"网站: {card_data.get('website', '')}"
    ]
    for line in contact_info:
        # 只绘制有数据的字段
        if ': ' in line and line.split(': ')[1]:
            draw.text((50, y_pos), line, font=font_contact, fill=text_color)
            y_pos += font_contact.getbbox(line)[3] + padding // 2

    return image

@api_bp.route('/business-cards/<int:card_id>/qr-code', methods=['POST'])
@jwt_required()
def generate_qr_code(card_id):
    """生成名片二维码"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json() or {}
        
        business_card = BusinessCard.query.filter_by(
            id=card_id,
            user_id=current_user_id
        ).first()
        
        if not business_card:
            return not_found('名片不存在')
        
        qr_service = QRCodeService()
        
        # 获取二维码类型和样式配置
        qr_type = data.get('type', 'url')  # url/vcard/json
        style_config = data.get('style_config', {})
        
        current_app.logger.info(f"生成二维码 - 名片ID: {card_id}, 类型: {qr_type}, 样式配置: {style_config}")
        
        if qr_type == 'vcard':
            # 生成vCard格式二维码
            result = qr_service.generate_vcard_qr(business_card.card_data, style_config)
        elif qr_type == 'json':
            # 生成JSON格式二维码
            result = qr_service.generate_business_card_qr(business_card, style_config)
        else:
            # 默认生成URL二维码
            result = qr_service.generate_url_qr(business_card.id, style_config)
        
        # 更新数据库记录
        if result.get('file_url'):
            business_card.qr_code_image = result['file_url']
            business_card.qr_code_data = result.get('qr_data', result.get('url', result.get('vcard_data', '')))
            db.session.commit()
            current_app.logger.info(f"二维码已保存到数据库 - 路径: {result['file_url']}")
        else:
            # 对于URL类型，只保存base64数据到qr_code_data
            business_card.qr_code_data = result.get('url', result.get('vcard_data', ''))
            db.session.commit()
            current_app.logger.info(f"二维码数据已保存到数据库")
        
        return success_response(
            message='二维码生成成功',
            data={
                'qr_code': result,
                'business_card': business_card.to_dict()
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"生成二维码失败: {str(e)}")
        current_app.logger.error(f"错误详情: {traceback.format_exc()}")
        return error_response('生成二维码失败')

@api_bp.route('/business-cards/<int:card_id>/download', methods=['GET'])
@jwt_required()
def download_business_card(card_id):
    """下载名片图片"""
    try:
        current_user_id = int(get_jwt_identity())
        
        business_card = BusinessCard.query.filter_by(
            id=card_id,
            user_id=current_user_id
        ).first()
        
        if not business_card:
            return not_found('名片不存在')
        
        # 获取下载格式
        format_type = request.args.get('format', 'png').lower()
        
        if format_type not in ['png', 'jpg', 'pdf']:
            return bad_request('不支持的下载格式')
        
        # 使用新的名片生成器
        generator = BusinessCardGenerator()
        
        # 生成名片图片
        if format_type == 'jpg':
            img_data = generator.generate_jpg(business_card, quality=95)
            mimetype = 'image/jpeg'
        elif format_type == 'pdf':
            # PDF暂未实现，返回PNG
            img_data = generator.generate_png(business_card)
            mimetype = 'image/png'
            format_type = 'png'
        else:
            img_data = generator.generate_png(business_card)
            mimetype = 'image/png'
        
        # 保存到内存
        buffer = io.BytesIO(img_data)
        buffer.seek(0)
        
        # 生成文件名
        safe_title = secure_filename(business_card.title) or f"business_card_{card_id}"
        filename = f"{safe_title}.{format_type}"
        
        return send_file(
            buffer,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
        
    except Exception as e:
        current_app.logger.error(f"下载名片失败: {str(e)}")
        return error_response('下载失败')

@api_bp.route('/business-cards/<int:card_id>/print-layout', methods=['POST'])
@jwt_required()
def create_print_layout(card_id):
    """创建打印排版"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json() or {}
        
        business_card = BusinessCard.query.filter_by(
            id=card_id,
            user_id=current_user_id
        ).first()
        
        if not business_card:
            return not_found('名片不存在')
        
        # 获取排版配置
        layout_config = {
            'paper_size': data.get('paper_size', 'A4'),
            'margin_mm': data.get('margin_mm', 10),
            'show_cut_lines': data.get('show_cut_lines', True)
        }
        
        card_count = data.get('card_count')  # None表示填满整页
        
        print_service = PrintLayoutService()
        
        # 获取模板HTML和CSS
        template = business_card.template
        if not template:
            return bad_request('名片模板不存在')
        
        # 创建打印排版
        layout_img = print_service.create_print_layout(
            template.template_html,
            template.template_css,
            layout_config,
            card_count
        )
        
        # 转换为base64
        buffer = io.BytesIO()
        layout_img.save(buffer, format='PNG', dpi=(300, 300))
        buffer.seek(0)
        
        layout_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        buffer.close()
        
        # 获取布局信息
        layout_info = print_service.calculate_layout(
            layout_config.get('paper_size', 'A4'),
            layout_config.get('margin_mm', 10)
        )
        
        return success_response(
            message='打印排版创建成功',
            data={
                'layout_image': f"data:image/png;base64,{layout_base64}",
                'layout_info': layout_info
            }
        )
        
    except Exception as e:
        current_app.logger.error(f"创建打印排版失败: {str(e)}")
        return error_response('创建打印排版失败')

@api_bp.route('/business-cards/<int:card_id>/print-layout/download', methods=['POST'])
@jwt_required()
def download_print_layout(card_id):
    """下载打印排版文件"""
    try:
        current_user_id = int(get_jwt_identity())
        data = request.get_json() or {}
        
        business_card = BusinessCard.query.filter_by(
            id=card_id,
            user_id=current_user_id
        ).first()
        
        if not business_card:
            return not_found('名片不存在')
        
        # 获取配置
        layout_config = {
            'paper_size': data.get('paper_size', 'A4'),
            'margin_mm': data.get('margin_mm', 10),
            'show_cut_lines': data.get('show_cut_lines', True)
        }
        
        card_count = data.get('card_count')
        format_type = data.get('format', 'pdf').lower()
        
        if format_type not in ['pdf', 'png', 'jpg']:
            return bad_request('不支持的文件格式')
        
        print_service = PrintLayoutService()
        template = business_card.template
        
        if not template:
            return bad_request('名片模板不存在')
        
        if format_type == 'pdf':
            # 生成PDF
            pdf_bytes = print_service.create_pdf_layout(
                template.template_html,
                template.template_css,
                layout_config,
                card_count
            )
            
            buffer = io.BytesIO(pdf_bytes)
            mimetype = 'application/pdf'
            file_ext = 'pdf'
        else:
            # 生成图片
            layout_img = print_service.create_print_layout(
                template.template_html,
                template.template_css,
                layout_config,
                card_count
            )
            
            buffer = io.BytesIO()
            
            if format_type == 'jpg':
                layout_img.save(buffer, format='JPEG', quality=95, dpi=(300, 300))
                mimetype = 'image/jpeg'
                file_ext = 'jpg'
            else:
                layout_img.save(buffer, format='PNG', dpi=(300, 300))
                mimetype = 'image/png'
                file_ext = 'png'
        
        buffer.seek(0)
        
        # 生成文件名
        safe_title = secure_filename(business_card.title) or f"business_card_{card_id}"
        filename = f"{safe_title}_print_layout.{file_ext}"
        
        return send_file(
            buffer,
            as_attachment=True,
            download_name=filename,
            mimetype=mimetype
        )
        
    except Exception as e:
        current_app.logger.error(f"下载打印排版失败: {str(e)}")
        return error_response('下载失败')

@api_bp.route('/business-cards/<int:card_id>/view', methods=['GET'])
def view_business_card_public(card_id):
    """公开查看名片（通过二维码扫描访问）"""
    try:
        business_card = BusinessCard.query.filter_by(
            id=card_id,
            status='published'
        ).first()
        
        if not business_card:
            return not_found('名片不存在或未公开')
        
        return success_response(data={
            'business_card': {
                'id': business_card.id,
                'title': business_card.title,
                'card_data': business_card.card_data,
                'template': business_card.template.to_dict() if business_card.template else None,
                'created_at': business_card.created_at.isoformat()
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"查看公开名片失败: {str(e)}")
        return error_response('查看名片失败')

@api_bp.route('/business-cards/layout-preview', methods=['POST'])
@jwt_required()
def get_layout_preview():
    """获取排版预览"""
    try:
        data = request.get_json() or {}
        
        paper_size = data.get('paper_size', 'A4')
        margin_mm = data.get('margin_mm', 10)
        show_guidelines = data.get('show_guidelines', True)
        
        print_service = PrintLayoutService()
        preview_data = print_service.get_layout_preview(
            paper_size=paper_size,
            margin_mm=margin_mm,
            show_guidelines=show_guidelines
        )
        
        return success_response(data=preview_data)
        
    except Exception as e:
        current_app.logger.error(f"获取排版预览失败: {str(e)}")
        return error_response('获取预览失败') 