<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="现代简约风格名片">
    <title>{{ card.name|default('名片', true) }}</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ style.font_family|default('Arial, sans-serif', true) }};
            background-color: {{ style.background_color|default('#ffffff', true) }};
            line-height: 1.4;
            color: {{ style.text_color|default('#333333', true) }};
        }
        
        /* 名片容器 */
        .business-card {
            width: 90mm;
            height: 54mm;
            padding: 6mm 8mm;
            background: {{ style.background_color|default('#ffffff', true) }};
            border: none;
            font-family: {{ style.font_family|default('Arial, sans-serif', true) }};
            position: relative;
            overflow: hidden;
        }
        
        /* 内容布局 */
        .modern-minimal .content {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        /* 姓名样式 */
        .modern-minimal .name {
            font-size: {{ style.name_size|default('18px', true) }};
            font-weight: {{ style.name_weight|default('bold', true) }};
            color: {{ style.primary_color|default('#2c3e50', true) }};
            margin: 0;
            letter-spacing: 1px;
            line-height: 1.2;
        }
        
        /* 分割线 */
        .modern-minimal .divider {
            width: 30mm;
            height: 1px;
            background: {{ style.accent_color|default('#e74c3c', true) }};
            margin: 3mm 0;
        }
        
        /* 职位标题 */
        .modern-minimal .title {
            font-size: {{ style.title_size|default('12px', true) }};
            color: {{ style.light_color|default('#7f8c8d', true) }};
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: normal;
        }
        
        /* 公司名称 */
        .modern-minimal .company {
            font-size: 14px;
            color: {{ style.text_color|default('#333333', true) }};
            margin: 0;
            font-weight: 500;
        }
        
        /* 联系信息 */
        .modern-minimal .contact-line {
            font-size: 11px;
            color: {{ style.light_color|default('#7f8c8d', true) }};
            margin: 0 0 1mm 0;
            word-break: break-all;
        }
        
        /* 装饰条 */
        .modern-minimal .accent-bar {
            position: absolute;
            right: 0;
            top: 0;
            width: 3mm;
            height: 100%;
            background: linear-gradient(180deg, {{ style.accent_color|default('#e74c3c', true) }} 0%, #c0392b 100%);
        }
        
        /* 响应式设计 */
        @media print {
            body {
                background: white;
            }
            .business-card {
                border: none;
                box-shadow: none;
            }
        }
        
        /* 可访问性增强 */
        .modern-minimal .name:focus,
        .modern-minimal .title:focus,
        .modern-minimal .company:focus {
            outline: 2px solid {{ style.primary_color|default('#2c3e50', true) }};
            outline-offset: 1px;
        }
    </style>
</head>
<body>
    <div class="business-card modern-minimal" role="article" aria-label="现代简约名片">
        <div class="content">
            <header class="name-section">
                <h1 class="name" id="card-name">{{ card.name|default('姓名', true) }}</h1>
                <div class="divider" aria-hidden="true"></div>
                <p class="title" id="card-title">{{ card.title|default('职位', true) }}</p>
            </header>
            
            <section class="company-section" aria-labelledby="card-company">
                <p class="company" id="card-company">{{ card.company|default('公司名称', true) }}</p>
            </section>
            
            <section class="contact-section" aria-label="联系信息">
                {% if card.phone %}
                <p class="contact-line" id="phone-info">{{ card.phone }}</p>
                {% endif %}
                {% if card.email %}
                <p class="contact-line" id="email-info">{{ card.email }}</p>
                {% endif %}
            </section>
        </div>
        <div class="accent-bar" aria-hidden="true"></div>
    </div>
</body>
</html> 