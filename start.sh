#!/bin/bash

# 智能简历生成器 - 开发环境启动脚本
echo "🚀 启动智能简历生成器..."

# 检查是否安装了必要的依赖
if ! command -v node &> /dev/null; then
    echo "❌ 错误: Node.js 未安装，请先安装 Node.js 16+"
    exit 1
fi

if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: Python3 未安装，请先安装 Python 3.8+"
    exit 1
fi

# 检查数据库是否运行
if ! command -v psql &> /dev/null; then
    echo "⚠️  警告: PostgreSQL 客户端未找到，请确保 PostgreSQL 已安装并运行"
fi

# 启动后端服务
echo "📡 启动后端服务..."
cd backend
if [ ! -d "venv" ]; then
    echo "🔧 创建Python虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt 2>/dev/null || echo "⚠️ 依赖安装可能存在问题，请检查requirements.txt"

echo "🏃 启动Django服务器..."
python run.py &
BACKEND_PID=$!
cd ..

# 等待后端启动
sleep 3

# 启动前端服务
echo "🎨 启动前端服务..."
cd frontend
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

echo "🏃 启动Vue开发服务器..."
npm run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo "✅ 服务启动完成！"
echo "🌐 前端地址: http://localhost:3000"
echo "🔗 后端API: http://localhost:5000"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
wait

# 清理进程
echo "🛑 正在停止服务..."
kill $BACKEND_PID 2>/dev/null
kill $FRONTEND_PID 2>/dev/null
echo "✅ 服务已停止" 