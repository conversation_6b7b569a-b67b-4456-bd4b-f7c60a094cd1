### 名片生成功能技术实施方案

版本: 1.0  
日期: 2025年7月6日  
负责人: 技术架构组

---

### 1. 核心洞察与战略考量

本方案旨在为新“名片生成”功能提供一个技术上可行、可扩展且高效的实施路径。核心策略是充分利用项目现有技术栈（Flask, SQLAlchemy, Celery, Pillow），确保新功能与现有系统无缝集成，从而最小化技术引入成本并加快开发周期。

关键决策点:
- 前后端分离: 采用此架构可实现开发任务解耦，提升前端用户体验的灵活性和后端服务的可复用性。
- 异步处理: 图像和PDF生成是计算密集型任务。强制使用 `Celery` 进行异步处理是保障主应用响应能力和稳定性的关键。
- 模板化与数据驱动: 将名片样式（模板）与用户数据分离，允许未来轻松扩展新模板而无需修改核心代码。

---

### 2. 总体架构设计

我们将采用标准的前后端分离架构，通过 RESTful API 进行通信。这种模式已被现有系统的 `Flask-CORS` 和 `Flask-JWT-Extended` 库所支持。



-   前端 (Client-Side)
    -   技术选型: 推荐使用 Vue.js 3 或 React。这些现代框架擅长构建响应式、组件化的用户界面，非常适合实现名片信息的实时预览功能。
    -   核心职责:
        -   渲染用户界面（信息输入表单、模板选择廊、实时预览区）。
        -   处理用户输入，并将其打包成JSON格式。
        -   调用后端API，获取模板数据、提交名片信息、请求生成文件。
        -   利用JS模板引擎（如 Vue 的模板语法或 Handlebars.js）在浏览器端实现数据到模板的实时渲染。

-   后端 (Server-Side)
    -   技术选型: 基于现有的 Flask 应用进行扩展。
    -   核心职责:
        -   提供安全的 RESTful API 接口。
        -   处理业务逻辑：用户身份验证、数据校验与持久化。
        -   管理数据库模型（用户、名片模板、已存名片）。
        -   将计算密集型任务（JPG生成、PDF排版）推送到 `Celery` 任务队列。
        -   与 `qrcode`, `Pillow`, `reportlab` 等库交互，执行核心生成逻辑。

---

### 3. 数据库模型设计 (SQLAlchemy)

为支持名片功能，我们需要在数据库中引入以下模型。模型将使用 `Flask-SQLAlchemy` 进行定义。

```python
from your_app.database import db # 假设 db 实例在 app 中定义
import datetime

# BusinessCardTemplate 模型: 存储名片模板
class BusinessCardTemplate(db.Model):
    __tablename__ = 'business_card_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True) # 模板名称, e.g., "简约商务"
    preview_image_url = db.Column(db.String(255), nullable=False) # 预览图URL
    html_structure = db.Column(db.Text, nullable=False) # 模板的HTML结构
    css_styles = db.Column(db.Text, nullable=False) # 模板的CSS样式
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'preview_image_url': self.preview_image_url
        }

# BusinessCard 模型: 存储用户生成的名片实例
class BusinessCard(db.Model):
    __tablename__ = 'business_cards'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True) # 关联到现有 User 模型
    template_id = db.Column(db.Integer, db.ForeignKey('business_card_templates.id'), nullable=False)
    
    # 使用 JSON/JSONB 类型存储用户输入的动态数据，具备高灵活性
    card_data = db.Column(db.JSON, nullable=False) 
    
    # 关联关系
    user = db.relationship('User', backref=db.backref('business_cards', lazy=True))
    template = db.relationship('BusinessCardTemplate', backref=db.backref('cards', lazy=True))
    
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)

    def to_dict(self):
        return {
            'id': self.id,
            'user_id': self.user_id,
            'template_id': self.template_id,
            'card_data': self.card_data,
            'created_at': self.created_at.isoformat()
        }
```
*注：现有 `User` 模型需要确保已定义，或按需扩展以建立 `business_cards` 的反向关系。*

---

### 4. 核心功能实现路径

#### 4.1. 信息输入与提取
1.  API 设计: 创建 `POST /api/cards` 接口，接收前端提交的包含 `template_id` 和 `card_data` (JSON对象) 的请求体。
2.  数据校验: 在后端使用 `Werkzeug` 或类似库对 `card_data` 中的字段（如姓名、电话、邮箱）进行严格的格式校验。
3.  信息自动填充:
    -   在前端，当用户登录后，可以通过调用现有的用户profile接口 (`GET /api/me`) 获取用户信息。
    -   前端逻辑将获取到的用户信息（姓名、邮箱、公司等）自动填充到名片输入表单中，提升用户体验。

#### 4.2. 模板库与切换
1.  模板列表API: 实现 `GET /api/templates` 接口，查询 `BusinessCardTemplate` 表，返回所有可用模板的列表（包含 `id`, `name`, `preview_image_url`）。
2.  模板详情API: 实现 `GET /api/templates/<template_id>` 接口，返回单个模板的完整信息，包括 `html_structure` 和 `css_styles`。
3.  前端实现:
    -   前端页面使用模板列表API返回的数据，渲染一个模板选择廊。
    -   当用户点击某个模板时，前端调用模板详情API获取其HTML/CSS，并加载到预览区域。

#### 4.3. 实时预览
此功能完全在前端实现，以保证零延迟的交互体验。
1.  数据绑定: 使用 Vue/React 的双向数据绑定特性，将输入表单中的数据与一个JavaScript对象（`card_data`）关联。
2.  动态渲染: 当 `card_data` 或所选模板发生变化时，前端的模板引擎会：
    -   获取当前模板的HTML结构。
    -   将 `card_data` 对象中的值（如`{ "name": "张三", "title": "软件工程师" }`）注入到HTML模板的占位符中（如 `{{ name }}`）。
    -   将模板的CSS应用到渲染后的HTML上。
    -   整个过程在浏览器中实时发生，无需与后端通信。

#### 4.4. JPG 图片生成与下载 (异步)
这是一个典型的耗时操作，必须通过 `Celery` 异步处理。
1.  触发API: 前端提供一个“生成图片”按钮，点击后将当前预览区域的完整HTML内容（已填充数据）和CSS样式通过 `POST /api/cards/<id>/generate_image` 发送到后端。
2.  后端处理流程:
    -   API接收到请求后，立即将任务分发给 Celery Worker，并返回一个任务ID或一个轮询状态的URL。
        ```python
        from tasks import generate_card_image_task
        
        @app.route('/api/cards/<id>/generate_image', methods=['POST'])
        @jwt_required()
        def trigger_image_generation(id):
            # ... 权限和数据校验 ...
            html_content = request.json.get('html')
            css_content = request.json.get('css')
            
            # 关键：将任务交给Celery
            task = generate_card_image_task.delay(id, html_content, css_content)
            
            return jsonify({'task_id': task.id, 'status': 'pending'}), 202
        ```
    -   Celery Worker 执行:
        -   安全考量: 在渲染之前，必须对接收的 `html_content` 进行严格的清理，防止XSS或服务器端请求伪造（SSRF）攻击。可使用 `bleach` 等库。
        -   HTML渲染: 使用一个无头浏览器库（Playwright 或 Puppeteer 是现代首选，Selenium也可）加载HTML和CSS，并截图生成PNG格式的内存图像。
        -   图像处理: 使用 `Pillow` 库打开此PNG图像，进行必要的裁剪（去除多余白边）、尺寸调整，并将其转换为JPG格式。
        -   存储与更新: 将生成的JPG文件保存到文件存储（如S3或本地），并将文件的URL更新到 `BusinessCard` 模型的相应字段中。
3.  前端轮询与下载: 前端可使用任务ID轮询任务状态接口，当任务完成后，获取图片URL并提供给用户下载。

#### 4.5. 二维码生成
利用已有的 `qrcode` 库。
1.  vCard格式: 定义一个辅助函数，将名片数据 (`card_data`) 转换为标准的 vCard 字符串格式。
2.  二维码API: 创建 `GET /api/cards/<id>/qrcode` 接口。
    -   该接口根据 `id` 查找到名片数据。
    -   调用vCard转换函数。
    -   使用 `qrcode` 库将vCard字符串生成为二维码图片（PNG格式）。
    -   将生成的图片作为二进制流直接返回，`Content-Type` 设置为 `image/png`。前端可以直接在 `<img>` 标签的 `src` 属性中使用此API地址。

#### 4.6. 排版与打印 (异步)
同样是耗时操作，也应由 `Celery` 异步处理。
1.  触发API: `POST /api/cards/print_layout`。请求体中包含要排版的名片ID列表和纸张尺寸（如 `A4`）。
2.  Celery Worker 执行:
    -   Worker接收到任务后，根据ID列表从数据库中查询所有名片数据。
    -   使用 `reportlab` 库创建一个新的PDF文档，并设置纸张尺寸和边距。
    -   循环处理每张名片：
        -   使用无头浏览器（与JPG生成逻辑类似）将每张名片渲染成独立的图像。
        -   在PDF画布上计算每张名片的位置（考虑行列、间距），然后将图像绘制到指定坐标。
    -   生成最终的PDF文件，保存并更新任务状态。
3.  下载: 实现方式与JPG下载类似，前端通过轮询获取最终的PDF文件下载链接。

---

### 5. API接口定义 (RESTful)

| 方法   | 端点 (Endpoint)                      | 描述                                                               | 关键请求/响应数据                                                                                              |
| :----- | :----------------------------------- | :----------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------- |
| `GET`  | `/api/templates`                     | 获取所有可用的名片模板列表。                                       | 响应: `[{"id": 1, "name": "简约商务", "preview_image_url": "..."}, ...]`                                    |
| `GET`  | `/api/templates/<template_id>`       | 获取单个模板的详细信息（HTML/CSS）。                               | 响应: `{"id": 1, "html_structure": "<div>...", "css_styles": "div { ... }"}`                                 |
| `POST` | `/api/cards`                         | 创建一个新的名片实例并保存。                                       | 请求体: `{"template_id": 1, "card_data": {"name": "张三", ...}}` <br/> 响应: `{"id": 123, ...}`          |
| `PUT`  | `/api/cards/<card_id>`               | 更新一个已存在的名片信息。                                         | 请求体: `{"card_data": {"name": "李四", ...}}` <br/> 响应: `{"id": 123, ...}`                               |
| `GET`  | `/api/cards/<card_id>/qrcode`        | 实时生成并返回指定名片的vCard二维码。                              | 响应: `image/png` 二进制流                                                                                 |
| `POST` | `/api/cards/<card_id>/generate_image`| (异步) 触发服务器端生成JPG图片。                               | 请求体: `{"html": "...", "css": "..."}` <br/> 响应: `{"task_id": "...", "status": "pending"}` |
| `GET`  | `/api/tasks/<task_id>`               | 查询异步任务的状态。                                               | 响应: `{"status": "SUCCESS", "result_url": "..."}` 或 `{"status": "PENDING"}`                               |
| `POST` | `/api/cards/print_layout`            | (异步) 触发生成用于打印的多名片PDF排版文件。                   | 请求体: `{"card_ids": [123, 124, 125], "paper_size": "A4"}` <br/> 响应: `{"task_id": "..."}`       |

*注：所有需要修改或创建资源的接口 (`POST`, `PUT`, `DELETE`) 均应使用 `Flask-JWT-Extended` 的 `@jwt_required()` 装饰器进行保护。*
### 名片生成器功能开发交接报告

版本: 1.0  
日期: 2025年7月6日  
目标读者: 开发团队、项目经理

---

### 1. 项目背景与目标

项目名称: 名片生成器功能模块开发

核心目标: 为现有系统增加一个功能强大且用户友好的名片生成器。此功能旨在让用户能够通过一个直观的界面，轻松创建、定制并导出个人名片。

关键功能需求:
-   信息输入: 用户可以输入或自动填充姓名、职位、公司、联系方式等个人信息。
-   模板选择: 提供多种预设的设计模板，用户可一键切换。
-   实时预览: 用户输入的任何信息或选择的模板，都应即时在预览区域反映出来。
-   文件导出:
    -   下载单张名片为 JPG 图片。
    -   生成适合打印的 A4 纸张排版 PDF 文件。
    -   生成包含联系信息的 vCard 二维码。

---

### 2. 现有系统技术栈分析

新功能将构建在当前稳定且功能强大的技术栈之上，确保无缝集成和性能。

核心技术栈概览:

| 分类 | 技术 / 库 | 在本项目中的作用 |
| :--- | :--- | :--- |
| Web框架 | Flask | 构建后端 RESTful API 服务。 |
| 应用服务器 | Gunicorn | 生产环境的 WSGI 服务器，确保服务稳定。 |
| 数据库 | MySQL (推断) | 持久化存储用户数据、名片数据及模板。 |
| ORM | Flask-SQLAlchemy | 用于定义数据库模型和执行数据操作。 |
| 异步任务 | Celery / Redis | 关键组件：处理计算密集型任务（JPG/PDF生成），避免阻塞主应用。 |
| 认证 | Flask-JWT-Extended | 保护所有需要用户登录的 API 接口。 |
| 图像处理 | Pillow, OpenCV | 用于图像的后期处理，如格式转换、裁剪等。 |

> 战略整合: 本项目的设计充分利用了现有架构，特别是 `Celery` 异步任务队列。所有耗时的文件生成操作都将作为后台任务执行，这是保障主应用响应能力和用户体验的关键。我们将扩展现有的 Flask 应用，而不是引入新的微服务，以降低复杂度和维护成本。

---

### 3. 详细技术实施方案

#### 3.1 系统架构

采用前后端分离的架构模式，通过 RESTful API 进行通信。

*   前端: 采用现代JavaScript框架（如 Vue.js 或 React），负责渲染UI、处理用户交互和实时预览。
*   后端 (Flask): 负责业务逻辑、数据持久化、API接口提供以及将耗时任务分派给 Celery。

![System Architecture Diagram](https://r2.flowith.net/files/o/1751774351052-image.png)

#### 3.2 数据库模型 (SQLAlchemy)

为支持此功能，需新增以下两个核心模型。

```python
# location: your_app/models.py

import datetime
from your_app.database import db

# 名片模板模型
class BusinessCardTemplate(db.Model):
    __tablename__ = 'business_card_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    preview_image_url = db.Column(db.String(255), nullable=False)
    html_structure = db.Column(db.Text, nullable=False) # 模板的HTML骨架
    css_styles = db.Column(db.Text, nullable=False) # 模板的CSS样式

# 用户生成的名片实例模型
class BusinessCard(db.Model):
    __tablename__ = 'business_cards'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    template_id = db.Column(db.Integer, db.ForeignKey('business_card_templates.id'), nullable=False)
    
    # 使用JSON类型存储动态数据，提供最大灵活性
    card_data = db.Column(db.JSON, nullable=False) 
    
    user = db.relationship('User', backref=db.backref('business_cards', lazy=True))
    template = db.relationship('BusinessCardTemplate')
    
    created_at = db.Column(db.DateTime, default=datetime.datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
```

#### 3.3 功能模块实现逻辑

| 功能模块 | 实现要点 | 技术关注点 |
| :--- | :--- | :--- |
| 信息输入与保存 | 前端表单收集数据，调用 `POST /api/cards` 保存。后端进行严格数据校验。 | 前端可调用 `/api/me` 接口自动填充用户信息。 |
| 模板管理与切换 | 前端通过 `GET /api/templates` 获取模板列表，点击后获取模板详情并应用于预览。 | 数据库存储模板的HTML/CSS，实现动态加载。 |
| 实时预览 | 纯前端实现。利用JS数据绑定，将表单数据实时注入到模板HTML中，实现零延迟预览。 | Vue/React的响应式系统。 |
| JPG图片生成 | 异步任务。前端将渲染好的HTML/CSS提交至后端API，触发Celery任务。 | Celery Worker: 使用无头浏览器 (Playwright/Puppeteer) 截图，`Pillow` 转换格式并优化。 |
| 二维码生成 | 同步任务。`GET /api/cards/<id>/qrcode` 接口实时生成。 | 后端将名片数据转换为vCard格式，使用 `qrcode` 库生成PNG流并直接返回。 |
| 排版与打印 (PDF) | 异步任务。前端提交名片ID列表，触发Celery任务。 | Celery Worker: 使用 `reportlab` 创建PDF，循环将每张名片（渲染为图片）绘制到PDF画布上。 |

#### 3.4 API 接口定义

所有权变更类接口 (`POST`, `PUT`) 必须通过 `@jwt_required()` 保护。

| 方法 | 端点 (Endpoint) | 描述 |
| :--- | :--- | :--- |
| `GET` | `/api/templates` | 获取所有可用名片模板列表。 |
| `GET` | `/api/templates/<template_id>` | 获取单个模板的详细信息 (HTML/CSS)。 |
| `POST` | `/api/cards` | 创建并保存一个新的名片实例。 |
| `PUT` | `/api/cards/<card_id>` | 更新一个已存在的名片。 |
| `GET` | `/api/cards/<card_id>/qrcode` | 实时生成并返回指定名片的二维码 (PNG)。 |
| `POST` | `/api/cards/<card_id>/generate_image` | (异步) 触发服务器端生成JPG图片。 |
| `POST` | `/api/cards/print_layout` | (异步) 触发生成多名片排版的PDF文件。 |
| `GET` | `/api/tasks/<task_id>` | 查询异步任务的状态和结果。 |

---

### 4. UI/UX设计与交互原型

#### 4.1 最终用户界面 (UI)

最终的UI设计采用三栏式布局，结构清晰，操作流程直观。

![名片生成器最终UI设计](https://r2.flowith.net/files/o/<EMAIL>)

#### 4.2 核心用户流程

1.  输入信息: 用户在左侧“Information”表单中填写或修改名片内容。
2.  实时预览: 中间的“Preview”区域会即时响应用户的每一次输入，动态更新名片样式。
3.  选择模板: 用户在右侧“Templates”列表中点击不同的模板缩略图。
4.  更新样式: 中间的“Preview”区域会立即应用新模板的风格，同时保持用户信息不变。
5.  导出文件: 用户点击预览下方的“Download as JPG”、“Arrange for Print”或“Show QR Code”按钮，触发相应的文件生成请求。

#### 4.3 交互原型验证

项目已提供一套基于 `HTML`, `CSS`, 和 `JavaScript` 的高保真交互式Web原型。此原型成功验证了设计的有效性：
-   可行性验证: 证明了仅通过前端技术即可实现流畅的实时预览和模板切换功能，将服务器负载降至最低。
-   体验验证: `script.js` 中的状态管理和DOM更新逻辑，展示了数据与视图的顺滑绑定，用户体验良好。
-   设计一致性: 原型代码 (`style.css`) 已经实现了UI视觉稿中的所有样式，包括字体、颜色和布局，可作为前端开发的直接起点。

> 结论: 该原型不仅是UI/UX的展示，更是一个功能性的前端实现蓝图。它已证明核心交互逻辑是健全且高效的，为后续的正式开发奠定了坚实基础。

---

### 5. 总结与后续步骤

本次规划与设计工作已完成，我们拥有了清晰的技术蓝图、经过验证的UI/UX设计和可执行的实施方案。项目已准备好进入开发阶段。

下一步行动计划:

| 领域 | 任务 | 负责人/团队 | 优先级 |
| :--- | :--- | :--- | :--- |
| 项目管理 | 召开项目启动会，评审本报告，分配开发任务。 | 项目经理 | High |
| 后端开发 | 1. 执行数据库迁移，创建新模型。 <br> 2. 开发 `templates` 和 `cards` 的CRUD API。 <br> 3. 实现二维码生成接口。 | 后端团队 | High |
| 前端开发 | 1. 基于原型代码，将其集成到主应用框架中。 <br> 2. 将本地模拟数据 (`templates.js`) 替换为对后端API的真实调用。 | 前端团队 | High |
| 后端开发 | 1. 开发Celery异步任务：JPG生成。 <br> 2. 开发Celery异步任务：PDF排版生成。 | 后端团队 | Medium |
| 前端开发 | 实现异步任务的状态轮询和结果处理逻辑（如显示加载中、提供下载链接）。 | 前端团队 | Medium |
| DevOps | 在测试/生产环境中配置并部署 Celery worker，并确保安装好无头浏览器 (Playwright) 等依赖。 | DevOps团队 | Medium |
| QA | 编写测试用例，覆盖所有API接口、功能模块和用户流程。 | QA团队 | Low |