const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  devServer: {
    port: 8101,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  },
  configureWebpack: {
    devtool: 'source-map',
    resolve: {
      alias: {
        '@': require('path').resolve(__dirname, 'src')
      }
    }
  },
  chainWebpack: config => {
    // 忽略 ResizeObserver 错误
    config.plugin('define').tap(args => {
      args[0]['process.env'].IGNORE_RESIZE_OBSERVER_ERROR = 'true'
      return args
    })
    
    // 添加 ResizeObserver 错误处理
    config.plugin('html').tap(args => {
      args[0].templateParameters = {
        ...args[0].templateParameters,
        resizeObserverErrorHandler: `
          <script>
            // ResizeObserver 错误处理
            window.addEventListener('error', function(event) {
              if (event.message && event.message.includes('ResizeObserver loop')) {
                event.stopImmediatePropagation();
                event.preventDefault();
                return false;
              }
            });
            
            window.addEventListener('unhandledrejection', function(event) {
              if (event.reason && event.reason.message && event.reason.message.includes('ResizeObserver loop')) {
                event.preventDefault();
                return false;
              }
            });
          </script>
        `
      }
      return args
    })
  }
}) 