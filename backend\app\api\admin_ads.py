#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员广告位管理API
Admin Ad Position Management API
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from app import db
from app.models.ad_position import AdPosition
from datetime import datetime

admin_ads_bp = Blueprint('admin_ads', __name__, url_prefix='/api/admin/ads')

# 获取广告位列表
@admin_ads_bp.route('/positions', methods=['GET'])
@jwt_required()
def get_ad_positions():
    """获取广告位列表"""
    try:
        positions = AdPosition.query.order_by(AdPosition.sort_order.asc(), AdPosition.id.asc()).all()
        return jsonify([position.to_dict() for position in positions])
    except Exception as e:
        return jsonify({'error': f'获取广告位列表失败: {str(e)}'}), 500

# 获取单个广告位
@admin_ads_bp.route('/positions/<int:position_id>', methods=['GET'])
@jwt_required()
def get_ad_position(position_id):
    """获取单个广告位"""
    try:
        position = AdPosition.query.get_or_404(position_id)
        return jsonify(position.to_dict())
    except Exception as e:
        return jsonify({'error': f'获取广告位失败: {str(e)}'}), 500

# 创建广告位
@admin_ads_bp.route('/positions', methods=['POST'])
@jwt_required()
def create_ad_position():
    """创建广告位"""
    try:
        data = request.json
        
        # 检查代码是否已存在
        existing = AdPosition.query.filter_by(code=data['code']).first()
        if existing:
            return jsonify({'error': '广告位代码已存在'}), 400
        
        position = AdPosition(**data)
        db.session.add(position)
        db.session.commit()
        
        return jsonify({
            'msg': '广告位创建成功',
            'position': position.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'创建广告位失败: {str(e)}'}), 500

# 更新广告位
@admin_ads_bp.route('/positions/<int:position_id>', methods=['PUT'])
@jwt_required()
def update_ad_position(position_id):
    """更新广告位"""
    try:
        position = AdPosition.query.get_or_404(position_id)
        data = request.json
        
        # 检查代码是否重复（排除自己）
        if 'code' in data and data['code'] != position.code:
            existing = AdPosition.query.filter_by(code=data['code']).first()
            if existing:
                return jsonify({'error': '广告位代码已存在'}), 400
        
        # 定义允许更新的字段
        allowed_fields = {
            'name', 'code', 'description', 'position_type', 'width', 'height',
            'is_enabled', 'is_visible', 'page_location', 'css_class', 'sort_order'
        }
        
        # 只更新允许的字段
        for key, value in data.items():
            if key in allowed_fields and hasattr(position, key):
                setattr(position, key, value)
        
        position.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'msg': '广告位更新成功',
            'position': position.to_dict()
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'更新广告位失败: {str(e)}'}), 500

# 删除广告位
@admin_ads_bp.route('/positions/<int:position_id>', methods=['DELETE'])
@jwt_required()
def delete_ad_position(position_id):
    """删除广告位"""
    try:
        position = AdPosition.query.get_or_404(position_id)
        db.session.delete(position)
        db.session.commit()
        
        return jsonify({'msg': '广告位删除成功'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'删除广告位失败: {str(e)}'}), 500

# 批量更新广告位状态
@admin_ads_bp.route('/positions/batch-update', methods=['POST'])
@jwt_required()
def batch_update_positions():
    """批量更新广告位状态"""
    try:
        data = request.json
        position_ids = data.get('position_ids', [])
        updates = data.get('updates', {})
        
        # 定义允许批量更新的字段
        allowed_fields = {
            'is_enabled', 'is_visible', 'sort_order'
        }
        
        positions = AdPosition.query.filter(AdPosition.id.in_(position_ids)).all()
        
        for position in positions:
            for key, value in updates.items():
                if key in allowed_fields and hasattr(position, key):
                    setattr(position, key, value)
            position.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return jsonify({'msg': f'成功更新 {len(positions)} 个广告位'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': f'批量更新失败: {str(e)}'}), 500

# 获取广告位统计
@admin_ads_bp.route('/positions/stats', methods=['GET'])
@jwt_required()
def get_position_stats():
    """获取广告位统计"""
    try:
        total = AdPosition.query.count()
        enabled = AdPosition.query.filter_by(is_enabled=True).count()
        visible = AdPosition.query.filter_by(is_visible=True).count()
        
        return jsonify({
            'total': total,
            'enabled': enabled,
            'visible': visible,
            'disabled': total - enabled,
            'hidden': total - visible
        })
    except Exception as e:
        return jsonify({'error': f'获取统计失败: {str(e)}'}), 500 