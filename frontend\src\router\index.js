import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 页面组件
import Login from '@/views/auth/Login.vue'
import Register from '@/views/auth/Register.vue'
import Dashboard from '@/views/Dashboard.vue'
import PhotoEditor from '@/views/PhotoEditor.vue'
import Profile from '@/views/Profile.vue'
import DocumentManager from '@/views/DocumentManager.vue'
import Help from '@/views/Help.vue'
import NotFound from '@/views/error/NotFound.vue'
import ServerError from '@/views/error/ServerError.vue'
import Home from '@/views/Home.vue'

// 简历相关组件
import ResumeManager from '@/views/ResumeManager.vue'
import ResumeEditor from '@/views/ResumeEditor.vue'
import ResumePreview from '@/views/ResumePreview.vue'

// 名片相关组件
import BusinessCardManager from '@/views/BusinessCardManager.vue'
import BusinessCardEditor from '@/views/BusinessCardEditor.vue'
import BusinessCardPreview from '@/views/BusinessCardPreview.vue'

import PdfWorkspace from '@/views/PdfWorkspace.vue';
import CreditsCenter from '@/views/CreditsCenter.vue';

// 管理员相关组件
import AdminDashboard from '@/views/admin/AdminDashboard.vue';
import UserManagement from '@/views/admin/UserManagement.vue';
import UserLogs from '@/views/admin/UserLogs.vue';
import SystemConfig from '@/views/admin/SystemConfig.vue';
import Statistics from '@/views/admin/Statistics.vue';
import ContentManagement from '@/views/admin/ContentManagement.vue';
import Reports from '@/views/admin/Reports.vue';
import AdPositionManagement from '@/views/admin/AdPositionManagement.vue';
import CreditsConfig from '@/views/admin/CreditsConfig.vue';

const routes = [
  { path: '/', name: 'Home', component: Home },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/register',
    name: 'Register',
    component: Register
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/editor',
    name: 'PhotoEditor',
    component: PhotoEditor,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/documents',
    name: 'DocumentManager',
    component: DocumentManager,
    meta: { requiresAuth: true }
  },
  {
    path: '/resumes',
    name: 'ResumeManager',
    component: ResumeManager,
    meta: { requiresAuth: true }
  },
  {
    path: '/resume/edit/:id',
    name: 'ResumeEditor',
    component: ResumeEditor,
    meta: { requiresAuth: true }
  },
  {
    path: '/resume/preview/:id',
    name: 'ResumePreview',
    component: ResumePreview,
    meta: { requiresAuth: true }
  },
  {
    path: '/business-cards',
    name: 'BusinessCardManager',
    component: BusinessCardManager,
    meta: { requiresAuth: true }
  },
  {
    path: '/business-card/edit/:id',
    name: 'BusinessCardEditor',
    component: BusinessCardEditor,
    meta: { requiresAuth: true }
  },
  {
    path: '/business-card/preview/:id',
    name: 'BusinessCardPreview',
    component: BusinessCardPreview,
    meta: { requiresAuth: true }
  },
  {
    path: '/pdf-tools',
    name: 'PdfTools',
    component: PdfWorkspace,
    meta: { requiresAuth: true }
  },
  {
    path: '/help',
    name: 'Help',
    component: Help
  },
  {
    path: '/credits',
    name: 'CreditsCenter',
    component: CreditsCenter,
    meta: { requiresAuth: true }
  },


  // 管理员路由
  {
    path: '/admin',
    name: 'AdminDashboard',
    component: AdminDashboard,
    meta: { requiresAuth: true, requiresAdmin: true },
    children: [
      {
        path: 'users',
        name: 'UserManagement',
        component: UserManagement,
        meta: { requiresAuth: true, requiresAdmin: true }
      },

      {
        path: 'user-logs',
        name: 'UserLogs',
        component: UserLogs,
        meta: { requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'system',
        name: 'SystemConfig',
        component: SystemConfig,
        meta: { requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'statistics',
        name: 'Statistics',
        component: Statistics,
        meta: { requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'content',
        name: 'ContentManagement',
        component: ContentManagement,
        meta: { requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'reports',
        name: 'Reports',
        component: Reports,
        meta: { requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'ad-positions',
        name: 'AdPositionManagement',
        component: AdPositionManagement,
        meta: { requiresAuth: true, requiresAdmin: true }
      },
      {
        path: 'credits',
        name: 'CreditsConfig',
        component: CreditsConfig,
        meta: { requiresAuth: true, requiresAdmin: true }
      }
    ]
  },
  {
    path: '/500',
    name: 'ServerError',
    component: ServerError
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }
  
  // 检查管理员权限
  if (to.meta.requiresAdmin && !authStore.user?.is_admin) {
    next('/')
    return
  }
  
  next()
})

export default router