<template>
  <div class="credits-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">积分中心</h2>
        <p class="page-subtitle">管理您的积分，查看积分记录，充值购买积分</p>
      </div>
    </div>

    <div class="credits-content">
      <!-- 积分概览 -->
      <el-row :gutter="20" class="overview-row">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon">
                <i class="el-icon-wallet"></i>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ creditsInfo.points }}</div>
                <div class="overview-label">当前积分</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon">
                <i class="el-icon-shopping-cart-2"></i>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ creditsInfo.points_used }}</div>
                <div class="overview-label">已使用积分</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon">
                <i class="el-icon-video-play"></i>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ todayAdCount }}/{{ creditsInfo.config?.max_daily_ads || 5 }}</div>
                <div class="overview-label">今日观看广告</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-item">
              <div class="overview-icon">
                <i class="el-icon-user"></i>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ creditsInfo.config?.invite_reward || 50 }}</div>
                <div class="overview-label">邀请奖励积分</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 主要内容区域 -->
      <el-row :gutter="20" class="main-content">
        <!-- 左侧：充值套餐和积分使用 -->
        <el-col :span="16">
          <el-card class="recharge-card">
            <template #header>
              <div class="card-header">
                <span>充值套餐</span>
                <el-tag type="info">充值比例：1积分 = {{ creditsInfo.config?.recharge_ratio || 1 }}元</el-tag>
              </div>
            </template>

            <div class="package-grid">
              <div v-if="loading.packages" class="loading-container">
                <el-skeleton :rows="2" animated />
              </div>
              <div v-else-if="!paymentInfo.packages || paymentInfo.packages.length === 0" class="empty-container">
                <el-empty description="暂无充值套餐" />
              </div>
              <div 
                v-for="pkg in paymentInfo.packages" 
                v-else
                :key="pkg.id"
                class="package-item"
                :class="{ 'package-disabled': !pkg.enabled }"
              >
                <div class="package-header">
                  <h3 class="package-name">{{ pkg.name }}</h3>
                  <div class="package-price">
                    <span class="current-price">¥{{ pkg.price }}</span>
                    <span class="original-price">¥{{ pkg.original_price }}</span>
                  </div>
                </div>
                <div class="package-points">
                  <span class="points-number">{{ pkg.points }}</span>
                  <span class="points-label">积分</span>
                </div>
                <div class="package-actions">
                  <el-button 
                    type="primary" 
                    @click="selectPackage(pkg)"
                    :disabled="!pkg.enabled"
                    class="recharge-btn"
                  >
                    立即充值
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 积分使用场景 -->
          <el-card class="usage-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>积分使用场景</span>
                <el-tooltip content="根据管理员配置，各项功能消耗的积分" placement="top">
                  <i class="el-icon-info" style="cursor: pointer; color: #909399;"></i>
                </el-tooltip>
              </div>
            </template>

            <div class="usage-scenarios">
              <div class="scenario-item">
                <div class="scenario-icon">
                  <i class="el-icon-picture-outline"></i>
                </div>
                <div class="scenario-info">
                  <div class="scenario-title">证件照处理</div>
                  <div class="scenario-desc">AI证件照背景替换、尺寸调整</div>
                </div>
                <div class="scenario-cost">
                  <span class="cost-number">-{{ creditsInfo.config?.photo_process_cost || 10 }}</span>
                  <span class="cost-unit">积分</span>
                </div>
              </div>

              <div class="scenario-item">
                <div class="scenario-icon">
                  <i class="el-icon-document"></i>
                </div>
                <div class="scenario-info">
                  <div class="scenario-title">简历生成</div>
                  <div class="scenario-desc">智能简历模板生成与优化</div>
                </div>
                <div class="scenario-cost">
                  <span class="cost-number">-{{ creditsInfo.config?.resume_generate_cost || 20 }}</span>
                  <span class="cost-unit">积分</span>
                </div>
              </div>

              <div class="scenario-item">
                <div class="scenario-icon">
                  <i class="el-icon-postcard"></i>
                </div>
                <div class="scenario-info">
                  <div class="scenario-title">名片制作</div>
                  <div class="scenario-desc">专业商务名片设计与生成</div>
                </div>
                <div class="scenario-cost">
                  <span class="cost-number">-{{ creditsInfo.config?.business_card_cost || 15 }}</span>
                  <span class="cost-unit">积分</span>
                </div>
              </div>

              <div class="scenario-item">
                <div class="scenario-icon">
                  <i class="el-icon-files"></i>
                </div>
                <div class="scenario-info">
                  <div class="scenario-title">文档对比</div>
                  <div class="scenario-desc">PDF/Word文档智能对比分析</div>
                </div>
                <div class="scenario-cost">
                  <span class="cost-number">-{{ creditsInfo.config?.document_compare_cost || 25 }}</span>
                  <span class="cost-unit">积分</span>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 快速获得积分 -->
          <el-card class="quick-earn-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>免费获得积分</span>
              </div>
            </template>

            <div class="earn-methods">
              <div class="earn-method">
                <div class="method-icon">
                  <i class="el-icon-video-play"></i>
                </div>
                <div class="method-info">
                  <div class="method-title">观看广告</div>
                  <div class="method-desc">每日最多{{ creditsInfo.config?.max_daily_ads || 5 }}次，每次获得{{ creditsInfo.config?.ad_reward || 5 }}积分</div>
                </div>
                <el-button 
                  type="success" 
                  size="small"
                  @click="watchAd"
                  :disabled="todayAdCount >= (creditsInfo.config?.max_daily_ads || 5)"
                >
                  观看广告
                </el-button>
              </div>

              <div class="earn-method">
                <div class="method-icon">
                  <i class="el-icon-share"></i>
                </div>
                <div class="method-info">
                  <div class="method-title">邀请好友</div>
                  <div class="method-desc">成功邀请好友注册，获得{{ creditsInfo.config?.invite_reward || 50 }}积分</div>
                </div>
                <el-button 
                  type="warning" 
                  size="small"
                  @click="showInviteDialog = true"
                >
                  邀请好友
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：积分记录和统计 -->
        <el-col :span="8">
          <el-card class="logs-card">
            <template #header>
              <div class="card-header">
                <span>积分记录</span>
                <el-button type="text" @click="showAllLogs = true">查看全部</el-button>
              </div>
            </template>

            <div class="logs-list">
              <div 
                v-for="log in recentLogs" 
                :key="log.id"
                class="log-item"
              >
                <div class="log-info">
                  <div class="log-description">{{ log.description }}</div>
                  <div class="log-time">{{ formatDate(log.created_at) }}</div>
                </div>
                <div class="log-points" :class="log.points > 0 ? 'positive' : 'negative'">
                  {{ log.points > 0 ? '+' : '' }}{{ log.points }}
                </div>
              </div>
            </div>
          </el-card>

          <!-- 积分统计 -->
          <el-card class="stats-card" style="margin-top: 20px;">
            <template #header>
              <div class="card-header">
                <span>积分统计</span>
              </div>
            </template>

            <div class="stats-content">
              <div class="stat-item">
                <div class="stat-label">注册奖励</div>
                <div class="stat-value">+{{ creditsInfo.config?.register_reward || 100 }}积分</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">总获得积分</div>
                <div class="stat-value">{{ creditsInfo.total_earned || 0 }}积分</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">总消费积分</div>
                <div class="stat-value">{{ creditsInfo.points_used }}积分</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">剩余可用</div>
                <div class="stat-value">{{ creditsInfo.points }}积分</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 充值对话框 -->
    <el-dialog 
      v-model="rechargeDialogVisible" 
      title="充值积分" 
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="recharge-dialog">
        <div class="selected-package">
          <h3>{{ selectedPackage?.name }}</h3>
          <div class="package-details">
            <div class="detail-item">
              <span class="label">积分数量：</span>
              <span class="value">{{ selectedPackage?.points }} 积分</span>
            </div>
            <div class="detail-item">
              <span class="label">支付金额：</span>
              <span class="value price">¥{{ selectedPackage?.price }}</span>
            </div>
          </div>
        </div>

        <div class="payment-methods">
          <h4>选择支付方式</h4>
          <div class="method-options">
            <el-radio-group v-model="selectedPaymentMethod">
              <el-radio 
                v-if="paymentInfo.wechat_enabled" 
                label="wechat"
                class="payment-option"
              >
                <div class="payment-option-content">
                  <i class="el-icon-chat-dot-round wechat-icon"></i>
                  <span>微信支付</span>
                </div>
              </el-radio>
              <el-radio 
                v-if="paymentInfo.alipay_enabled" 
                label="alipay"
                class="payment-option"
              >
                <div class="payment-option-content">
                  <i class="el-icon-money alipay-icon"></i>
                  <span>支付宝</span>
                </div>
              </el-radio>
            </el-radio-group>
          </div>
        </div>

        <div v-if="selectedPaymentMethod && orderInfo" class="payment-info">
          <div class="qr-section">
            <h4>扫码支付</h4>
            <div class="qr-container">
              <img 
                :src="getQrCode()" 
                :alt="`${selectedPaymentMethod === 'wechat' ? '微信' : '支付宝'}收款二维码`"
                class="qr-code"
              />
            </div>
            <div class="payment-note">
              <p><strong>收款人：</strong>{{ getReceiverName() }}</p>
              <p><strong>支付说明：</strong>{{ getPaymentNote() }}</p>
              <p><strong>订单号：</strong>{{ orderInfo.order_no }}</p>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="rechargeDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="confirmPayment"
            :loading="confirmingPayment"
          >
            确认已支付
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 积分记录对话框 -->
    <el-dialog 
      v-model="showAllLogs" 
      title="积分记录" 
      width="800px"
    >
      <div class="logs-dialog">
        <el-table :data="allLogs" style="width: 100%">
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="points" label="积分变动" width="100">
            <template #default="scope">
              <span :class="scope.row.points > 0 ? 'text-success' : 'text-danger'">
                {{ scope.row.points > 0 ? '+' : '' }}{{ scope.row.points }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="balance" label="余额" width="100" />
          <el-table-column prop="created_at" label="时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="logsPage"
            v-model:page-size="logsPageSize"
            :page-sizes="[10, 20, 50]"
            :total="logsTotal"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleLogsSizeChange"
            @current-change="handleLogsCurrentChange"
          />
        </div>
      </div>
    </el-dialog>

    <!-- 邀请好友对话框 -->
    <el-dialog
      v-model="showInviteDialog"
      title="邀请好友"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="invite-content">
        <div class="invite-info">
          <p>成功邀请好友注册，您和好友都将获得 {{ creditsInfo.config?.invite_reward || 50 }} 积分奖励！</p>
        </div>
        <div class="invite-link">
          <div class="link-display">
            <span class="link-text">{{ inviteLink }}</span>
          </div>
          <div class="link-actions">
            <el-button type="primary" @click="copyInviteLink">复制链接</el-button>
            <el-button @click="generateInviteLink">重新生成</el-button>
          </div>
        </div>
        <div class="invite-stats">
          <div class="stat-item">
            <div class="stat-value">{{ inviteStats.total_invited || 0 }}</div>
            <div class="stat-label">已邀请</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ inviteStats.successful_invites || 0 }}</div>
            <div class="stat-label">成功注册</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ (inviteStats.total_reward || 0) }}积分</div>
            <div class="stat-label">获得奖励</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { apiEndpoints } from '@/api'

// 响应式数据
const creditsInfo = reactive({
  points: 0,
  points_used: 0,
  config: {},
  payment: {}
})

const paymentInfo = reactive({
  wechat_enabled: false,
  alipay_enabled: false,
  packages: [],
  wechat_qr_code: '',
  wechat_receiver_name: '',
  wechat_payment_note: '',
  alipay_qr_code: '',
  alipay_receiver_name: '',
  alipay_payment_note: ''
})

// 加载状态
const loading = reactive({
  credits: false,
  logs: false,
  packages: false
})

const rechargeDialogVisible = ref(false)
const showAllLogs = ref(false)
const showInviteDialog = ref(false)
const selectedPackage = ref(null)
const selectedPaymentMethod = ref('')
const orderInfo = ref(null)
const confirmingPayment = ref(false)
const todayAdCount = ref(0)
const inviteLink = ref('')
const inviteStats = reactive({
  total_invited: 0,
  successful_invites: 0,
  total_reward: 0
})

// 积分记录相关
const recentLogs = ref([])
const allLogs = ref([])
const logsPage = ref(1)
const logsPageSize = ref(20)
const logsTotal = ref(0)

// 计算属性
const getQrCode = () => {
  if (selectedPaymentMethod.value === 'wechat') {
    return paymentInfo.wechat_qr_code
  } else if (selectedPaymentMethod.value === 'alipay') {
    return paymentInfo.alipay_qr_code
  }
  return ''
}

const getReceiverName = () => {
  if (selectedPaymentMethod.value === 'wechat') {
    return paymentInfo.wechat_receiver_name
  } else if (selectedPaymentMethod.value === 'alipay') {
    return paymentInfo.alipay_receiver_name
  }
  return ''
}

const getPaymentNote = () => {
  if (selectedPaymentMethod.value === 'wechat') {
    return paymentInfo.wechat_payment_note
  } else if (selectedPaymentMethod.value === 'alipay') {
    return paymentInfo.alipay_payment_note
  }
  return ''
}

// 方法
const loadCreditsInfo = async () => {
  try {
    const response = await apiEndpoints.user.getCreditsInfo()
    if (response.success) {
      Object.assign(creditsInfo, response.data)
      Object.assign(paymentInfo, response.data.payment)
    }
  } catch (error) {
    console.error('加载积分信息失败:', error)
    ElMessage.error('加载积分信息失败')
  }
}

const loadRecentLogs = async () => {
  try {
    const response = await apiEndpoints.user.getCreditsLogs({
      page: 1,
      per_page: 5
    })
    if (response.success) {
      recentLogs.value = response.data.logs
    }
  } catch (error) {
    console.error('加载积分记录失败:', error)
  }
}

const loadAllLogs = async () => {
  try {
    const response = await apiEndpoints.user.getCreditsLogs({
      page: logsPage.value,
      per_page: logsPageSize.value
    })
    if (response.success) {
      allLogs.value = response.data.logs
      logsTotal.value = response.data.total
    }
  } catch (error) {
    console.error('加载积分记录失败:', error)
  }
}

const selectPackage = (pkg) => {
  selectedPackage.value = pkg
  selectedPaymentMethod.value = ''
  orderInfo.value = null
  rechargeDialogVisible.value = true
}

const createOrder = async () => {
  try {
    const response = await apiEndpoints.user.createRechargeOrder({
      package_id: selectedPackage.value.id,
      payment_method: selectedPaymentMethod.value
    })
    if (response.success) {
      orderInfo.value = response.data
    }
  } catch (error) {
    console.error('创建订单失败:', error)
    ElMessage.error('创建订单失败')
  }
}

const confirmPayment = async () => {
  if (!orderInfo.value) {
    ElMessage.warning('请先创建订单')
    return
  }

  confirmingPayment.value = true
  try {
    const response = await apiEndpoints.user.confirmPayment({
      order_no: orderInfo.value.order_no
    })
    if (response.success) {
      ElMessage.success('支付确认成功')
      rechargeDialogVisible.value = false
      loadCreditsInfo()
      loadRecentLogs()
    }
  } catch (error) {
    console.error('确认支付失败:', error)
    ElMessage.error('确认支付失败')
  } finally {
    confirmingPayment.value = false
  }
}

const watchAd = async () => {
  try {
    const response = await apiEndpoints.user.watchAdReward()
    if (response.success) {
      ElMessage.success(response.data.message)
      todayAdCount.value++
      loadCreditsInfo()
      loadRecentLogs()
    }
  } catch (error) {
    console.error('观看广告失败:', error)
    ElMessage.error('观看广告失败')
  }
}

const handleLogsSizeChange = (val) => {
  logsPageSize.value = val
  loadAllLogs()
}

const handleLogsCurrentChange = (val) => {
  logsPage.value = val
  loadAllLogs()
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

const generateInviteLink = async () => {
  try {
    const response = await apiEndpoints.user.generateInviteLink()
    if (response.success) {
      inviteLink.value = response.data.link
      inviteStats.total_invited = response.data.total_invited || 0
      inviteStats.successful_invites = response.data.successful_invites || 0
      inviteStats.total_reward = response.data.total_reward || 0
    }
  } catch (error) {
    console.error('生成邀请链接失败:', error)
    ElMessage.error('生成邀请链接失败')
  }
}

const copyInviteLink = () => {
  navigator.clipboard.writeText(inviteLink.value).then(() => {
    ElMessage.success('邀请链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败，请手动复制')
  })
}

// 监听支付方式变化
const watchPaymentMethod = computed(() => {
  if (selectedPaymentMethod.value && selectedPackage.value && !orderInfo.value) {
    createOrder()
  }
  return selectedPaymentMethod.value
})

// 使用watchPaymentMethod来触发响应式更新
watchPaymentMethod.value

// 生命周期
onMounted(() => {
  loadCreditsInfo()
  loadRecentLogs()
})
</script>

<style scoped>
.credits-center {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-title {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
}

.overview-row {
  margin-bottom: 30px;
}

.overview-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.overview-item {
  display: flex;
  align-items: center;
  padding: 20px;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.overview-icon i {
  font-size: 24px;
  color: white;
}

.overview-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.overview-label {
  font-size: 14px;
  color: #909399;
}

.main-content {
  margin-bottom: 30px;
}

.recharge-card,
.usage-card,
.quick-earn-card,
.stats-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.package-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 30px;
}

.package-item {
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s;
  cursor: pointer;
}

.package-item:hover {
  border-color: #409eff;
  transform: translateY(-2px);
}

.package-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.package-header {
  margin-bottom: 16px;
}

.package-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.package-price {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.current-price {
  font-size: 20px;
  font-weight: bold;
  color: #f56c6c;
}

.original-price {
  font-size: 14px;
  color: #909399;
  text-decoration: line-through;
}

.package-points {
  margin-bottom: 16px;
}

.points-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.points-label {
  font-size: 14px;
  color: #909399;
  margin-left: 4px;
}

.recharge-btn {
  width: 100%;
}

.usage-scenarios {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.scenario-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.scenario-icon {
  font-size: 20px;
  color: #409eff;
  margin-right: 12px;
}

.scenario-info {
  flex: 1;
}

.scenario-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.scenario-desc {
  font-size: 12px;
  color: #909399;
}

.scenario-cost {
  text-align: right;
}

.cost-number {
  font-size: 16px;
  font-weight: bold;
  color: #f56c6c;
}

.cost-unit {
  font-size: 12px;
  color: #909399;
  margin-left: 2px;
}

.earn-methods {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.earn-method {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background: #fafafa;
}

.method-icon {
  font-size: 20px;
  color: #67c23a;
  margin-right: 12px;
}

.method-info {
  flex: 1;
}

.method-title {
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.method-desc {
  font-size: 12px;
  color: #909399;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stats-content .stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stats-content .stat-item:last-child {
  border-bottom: none;
}

.stats-content .stat-label {
  font-size: 14px;
  color: #606266;
}

.stats-content .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.logs-card {
  height: 100%;
}

.logs-list {
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-info {
  flex: 1;
}

.log-description {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.log-time {
  font-size: 12px;
  color: #909399;
}

.log-points {
  font-weight: 600;
  font-size: 14px;
}

.log-points.positive {
  color: #67c23a;
}

.log-points.negative {
  color: #f56c6c;
}

.loading-container {
  text-align: center;
  padding: 40px 0;
}

.empty-container {
  text-align: center;
  padding: 40px 0;
}

.qr-code-container {
  text-align: center;
  padding: 20px 0;
}

.qr-code {
  width: 200px;
  height: 200px;
  margin-bottom: 15px;
}

.payment-info {
  text-align: center;
  margin: 20px 0;
}

.payment-info p {
  margin: 8px 0;
  color: #606266;
}

.payment-info .amount {
  font-size: 24px;
  font-weight: bold;
  color: #f56c6c;
}

.payment-methods {
  margin: 20px 0;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.payment-method:hover {
  border-color: #409eff;
}

.payment-method.selected {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.payment-method-icon {
  width: 30px;
  height: 30px;
  margin-right: 12px;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}

.invite-content {
  text-align: center;
}

.invite-info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f0f9ff;
  border-radius: 8px;
}

.invite-info p {
  margin: 0;
  color: #409eff;
  font-size: 14px;
}

.invite-link {
  margin-bottom: 20px;
}

.link-display {
  margin-bottom: 15px;
}

.link-text {
  display: block;
  padding: 10px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.link-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.invite-stats {
  display: flex;
  justify-content: space-around;
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 8px;
}

.invite-stats .stat-item {
  text-align: center;
}

.invite-stats .stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.invite-stats .stat-label {
  font-size: 12px;
  color: #909399;
}

@media (max-width: 768px) {
  .credits-center {
    padding: 10px;
  }
  
  .package-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-number {
    font-size: 20px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-subtitle {
    font-size: 14px;
  }
  
  .usage-scenarios,
  .earn-methods {
    gap: 10px;
  }
  
  .scenario-item,
  .earn-method {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .invite-stats {
    flex-direction: column;
    gap: 15px;
  }
}
</style>