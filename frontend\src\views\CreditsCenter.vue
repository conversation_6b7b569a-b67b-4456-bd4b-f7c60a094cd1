<template>
  <div class="credits-center">
    <h2>我的积分</h2>
    <div class="balance-box">
      <span class="balance-label">当前积分：</span>
      <span class="balance-value">{{ balance }}</span>
      <el-button type="primary" @click="showRecharge = true">充值积分</el-button>
    </div>
    <RechargeDialog v-model="showRecharge" @success="onRechargeSuccess" />
    <div class="actions">
      <el-button @click="showAdTask">看广告赚积分</el-button>
      <el-button @click="showInviteTask">邀请好友赚积分</el-button>
    </div>
    <h3 style="margin-top:2em;">积分明细</h3>
    <el-table :data="logs" style="width:100%" size="small">
      <el-table-column prop="change" label="变动" width="80" />
      <el-table-column prop="type" label="类型" width="100" />
      <el-table-column prop="remark" label="备注" />
      <el-table-column prop="created_at" label="时间" width="180" />
      <el-table-column prop="balance" label="变动后余额" width="100" />
    </el-table>
    <el-dialog v-model="showAd" title="看广告赚积分" width="400px">
      <div style="text-align:center;">（此处集成广告SDK或模拟广告）<br>看完广告后自动发放积分。</div>
      <el-button type="primary" @click="finishAdTask">模拟完成广告</el-button>
    </el-dialog>
    <el-dialog v-model="showInvite" title="邀请好友赚积分" width="400px">
      <div style="text-align:center;">邀请链接：<el-input v-model="inviteLink" readonly style="width:90%" /></div>
      <el-button type="primary" @click="copyInvite">复制邀请链接</el-button>
    </el-dialog>
    <div class="rule-box">
      <h4>积分规则说明</h4>
      <ul>
        <li>积分可用于相片处理、文档对比、名片/简历制作等服务。</li>
        <li>积分可通过充值、看广告、邀请好友等方式获得。</li>
        <li>充值比例、任务奖励等以平台最新公告为准。</li>
      </ul>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';
import RechargeDialog from '@/components/RechargeDialog.vue';
const balance = ref(0);
const logs = ref([]);
const showRecharge = ref(false);
const showAd = ref(false);
const showInvite = ref(false);
const inviteLink = ref(window.location.origin + '/register?invite=xxxx');

async function fetchBalance() {
  const res = await axios.get('/api/user/points');
  balance.value = res.data.points;
}
async function fetchLogs() {
  const res = await axios.get('/api/admin/credits/logs');
  logs.value = res.data.filter(l => l.user_id === res.data.user_id); // 仅显示当前用户
}
function onRechargeSuccess() {
  fetchBalance();
  fetchLogs();
}
function showAdTask() {
  showAd.value = true;
}
function finishAdTask() {
  ElMessage.success('广告任务完成，积分已到账！');
  showAd.value = false;
  fetchBalance();
  fetchLogs();
}
function showInviteTask() {
  showInvite.value = true;
}
function copyInvite() {
  navigator.clipboard.writeText(inviteLink.value);
  ElMessage.success('邀请链接已复制');
}
onMounted(() => {
  fetchBalance();
  fetchLogs();
});
</script>
<style scoped>
.credits-center {
  max-width: 900px;
  margin: 2em auto;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 16px #3182ce22;
  padding: 2em 2.5em;
}
.balance-box {
  display: flex;
  align-items: center;
  gap: 1.5em;
  font-size: 20px;
  margin-bottom: 1.5em;
}
.balance-label { color: #888; }
.balance-value { color: #2563eb; font-size: 28px; font-weight: bold; }
.actions { margin-bottom: 1.5em; }
.rule-box { margin-top: 2em; background: #f7f8fa; border-radius: 8px; padding: 1em 1.5em; }
</style> 