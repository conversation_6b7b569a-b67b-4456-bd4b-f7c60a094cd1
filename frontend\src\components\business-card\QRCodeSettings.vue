<template>
  <div class="qr-code-settings">
    <div class="settings-form">
      <div class="form-item">
        <label>二维码类型</label>
        <el-select v-model="qrConfig.type" placeholder="选择二维码类型">
          <el-option label="网页链接 (URL)" value="url" />
          <el-option label="电子名片 (vCard)" value="vcard" />
          <el-option label="自定义数据 (JSON)" value="json" />
        </el-select>
      </div>
      
      <div class="form-item">
        <label>二维码大小</label>
        <el-slider 
          v-model="qrConfig.size" 
          :min="100" 
          :max="300" 
          :step="10"
          show-input
        />
      </div>
      
      <div class="form-item">
        <label>前景色</label>
        <el-color-picker 
          v-model="qrConfig.fillColor" 
          show-alpha
        />
      </div>
      
      <div class="form-item">
        <label>背景色</label>
        <el-color-picker 
          v-model="qrConfig.backColor" 
          show-alpha
        />
      </div>
      
      <div class="form-item">
        <label>边框大小</label>
        <el-input-number 
          v-model="qrConfig.border" 
          :min="0" 
          :max="10"
        />
      </div>
    </div>
    
    <!-- 移除action-buttons中的预览按钮，只保留生成二维码按钮 -->
    <div class="action-buttons">
      <el-button type="primary" @click="generateQR" :loading="generating">
        <el-icon><Download /></el-icon>
        生成二维码
      </el-button>
    </div>
    
    <!-- 移除预览区域 -->
  </div>
</template>

<script>
import { ref, reactive, watch } from 'vue'
import { ElSelect, ElOption, ElSlider, ElColorPicker, ElInputNumber, ElButton, ElIcon } from 'element-plus'
import { Download } from '@element-plus/icons-vue'

export default {
  name: 'QRCodeSettings',
  
  components: {
    ElSelect,
    ElOption,
    ElSlider,
    ElColorPicker,
    ElInputNumber,
    ElButton,
    ElIcon,
    Download
  },
  
  props: {
    businessCard: {
      type: Object,
      required: true
    }
  },
  
  emits: ['generate'],
  
  setup(props, { emit }) {
    const generating = ref(false)
    
    const qrConfig = reactive({
      type: 'url',
      size: 400,  // 增加默认大小，确保二维码足够清晰
      fillColor: '#000000',
      backColor: '#ffffff',
      border: 4
    })
    
    // 监听二维码类型变化，提供用户友好的提示
    watch(() => qrConfig.type, (newType) => {
      console.log('二维码类型已更改为:', newType)
    })
    
    const generateQR = async () => {
      generating.value = true
      try {
        const config = {
          type: qrConfig.type,
          style_config: {
            size: qrConfig.size,
            fill_color: qrConfig.fillColor,
            back_color: qrConfig.backColor,
            border: qrConfig.border
          }
        }
        
        console.log('发送二维码配置:', config)
        emit('generate', config)
      } catch (error) {
        console.error('生成失败:', error)
      } finally {
        generating.value = false
      }
    }
    
    return {
      qrConfig,
      generating,
      generateQR
    }
  }
}
</script>

<style scoped>
.qr-code-settings {
  padding: 10px 0;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item label {
  font-size: 13px;
  color: #606266;
  margin-right: 15px;
  min-width: 80px;
}

.form-item .el-select,
.form-item .el-slider {
  flex: 1;
  max-width: 200px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.qr-preview {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.qr-preview h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #303133;
}

.preview-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.preview-container img {
  max-width: 200px;
  border: 1px solid #ddd;
  border-radius: 4px;
}
</style> 