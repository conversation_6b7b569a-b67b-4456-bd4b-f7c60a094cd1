"""
简历项目模型
"""
from .base import db, BaseModel

class ResumeProject(BaseModel):
    """简历项目经历"""
    __tablename__ = 'resume_projects'
    
    resume_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>ey('resumes.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(100))
    start_date = db.Column(db.Date)
    end_date = db.Column(db.Date)
    description = db.Column(db.Text)
    technologies = db.Column(db.String(500))  # 逗号分隔的技术列表
    url = db.Column(db.String(500))  # 项目URL
    
    def to_dict(self):
        data = super().to_dict()
        if self.technologies:
            data['technologies'] = self.technologies.split(',')
        return data

class ResumeSkill(BaseModel):
    """简历技能"""
    __tablename__ = 'resume_skills'
    
    resume_id = db.Column(db.Integer, db.<PERSON><PERSON>ey('resumes.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    level = db.Column(db.Integer)  # 1-5的技能水平
    category = db.Column(db.String(50))  # 技能分类
    years = db.Column(db.Float)  # 使用年限

class ResumeCertification(BaseModel):
    """简历证书"""
    __tablename__ = 'resume_certifications'
    
    resume_id = db.Column(db.Integer, db.ForeignKey('resumes.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    issuer = db.Column(db.String(100))
    issue_date = db.Column(db.Date)
    expiry_date = db.Column(db.Date)
    credential_id = db.Column(db.String(100))
    url = db.Column(db.String(500))
