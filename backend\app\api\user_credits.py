from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.user import User
from app.models.points_log import PointsLog
from app.models.points_config import PointsConfig
from app.models.payment_order import PaymentOrder
import uuid
from datetime import datetime

user_credits_bp = Blueprint('user_credits', __name__, url_prefix='/api/user/points')

# 获取当前用户积分余额
@user_credits_bp.route('', methods=['GET'])
@jwt_required()
def get_points():
    user = User.query.get(get_jwt_identity())
    return jsonify({'points': user.points})

# 获取当前用户积分明细
@user_credits_bp.route('/logs', methods=['GET'])
@jwt_required()
def get_points_logs():
    user_id = get_jwt_identity()
    logs = PointsLog.query.filter_by(user_id=user_id).order_by(PointsLog.created_at.desc()).limit(100).all()
    return jsonify([
        {
            'id': l.id,
            'change': l.change,
            'type': l.type,
            'remark': l.remark,
            'created_at': l.created_at.isoformat(),
            'balance': l.balance
        } for l in logs
    ])

# 积分充值下单（生成订单，返回模拟支付链接）
@user_credits_bp.route('/recharge_order', methods=['POST'])
@jwt_required()
def recharge_order():
    data = request.json
    amount = int(data.get('amount', 0))
    channel = data.get('channel', 'wechat')
    ratio_cfg = PointsConfig.query.filter_by(key='recharge_ratio').first()
    ratio = int(ratio_cfg.value) if ratio_cfg else 10
    points = amount * ratio
    user_id = get_jwt_identity()
    order_no = str(uuid.uuid4()).replace('-', '')[:20]
    order = PaymentOrder(order_no=order_no, user_id=user_id, amount=amount, points=points, channel=channel, status='pending')
    db.session.add(order)
    db.session.commit()
    # 返回模拟支付链接/二维码
    return jsonify({'order_no': order_no, 'pay_url': f'/mockpay/{order_no}', 'amount': amount, 'points': points, 'channel': channel})

# 支付回调接口（模拟，真实对接时需校验签名）
@user_credits_bp.route('/payment_callback', methods=['POST'])
def payment_callback():
    data = request.json
    order_no = data.get('order_no')
    trade_no = data.get('trade_no')
    order = PaymentOrder.query.filter_by(order_no=order_no).first()
    if not order or order.status == 'paid':
        return jsonify({'msg': '订单不存在或已支付'}), 400
    order.status = 'paid'
    order.paid_at = datetime.utcnow()
    order.trade_no = trade_no
    user = User.query.get(order.user_id)
    user.points += order.points
    log = PointsLog(user_id=user.id, change=order.points, type='recharge', remark='充值', balance=user.points)
    db.session.add(log)
    db.session.commit()
    return jsonify({'msg': '支付成功，积分已到账'})

# 广告/邀请等任务积分发放
@user_credits_bp.route('/task', methods=['POST'])
@jwt_required()
def task_points():
    data = request.json
    task_type = data.get('type')  # ad/invite
    user = User.query.get(get_jwt_identity())
    # 奖励积分从配置读取
    cfg = PointsConfig.query.filter_by(key=f'{task_type}_reward').first()
    reward = int(cfg.value) if cfg else 5
    user.points += reward
    log = PointsLog(user_id=user.id, change=reward, type=task_type, remark='任务奖励', balance=user.points)
    db.session.add(log)
    db.session.commit()
    return jsonify({'msg': '任务积分已发放', 'points': user.points}) 