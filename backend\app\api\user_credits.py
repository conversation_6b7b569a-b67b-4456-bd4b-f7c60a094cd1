#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户端积分API
User Credits API
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from app import db
from app.models.points_config import PointsConfig
from app.models.points_log import PointsLog
from app.models.payment_order import PaymentOrder
from app.models.user import User
import json
import uuid
from datetime import datetime

user_credits_bp = Blueprint('user_credits', __name__, url_prefix='/api/user/credits')

# 获取用户积分信息
@user_credits_bp.route('/info', methods=['GET'])
@jwt_required()
def get_user_credits_info():
    """获取用户积分信息"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'}), 404
        
        # 获取积分配置
        config = PointsConfig.query.first()
        if not config:
            config = PointsConfig()
            db.session.add(config)
            db.session.commit()
        
        # 解析套餐配置
        packages = []
        if config.payment_packages:
            try:
                packages = json.loads(config.payment_packages)
            except:
                packages = []
        
        return jsonify({
            'success': True,
            'data': {
                'points': user.points,
                'points_used': user.points_used,
                'config': {
                    'photo_process_cost': config.photo_process_cost,
                    'resume_generate_cost': config.resume_generate_cost,
                    'business_card_cost': config.business_card_cost,
                    'document_compare_cost': config.document_compare_cost,
                    'recharge_ratio': config.recharge_ratio
                },
                'payment': {
                    'wechat_enabled': config.wechat_enabled,
                    'wechat_qr_code': config.wechat_qr_code,
                    'wechat_receiver_name': config.wechat_receiver_name,
                    'wechat_payment_note': config.wechat_payment_note,
                    'alipay_enabled': config.alipay_enabled,
                    'alipay_qr_code': config.alipay_qr_code,
                    'alipay_receiver_name': config.alipay_receiver_name,
                    'alipay_payment_note': config.alipay_payment_note,
                    'packages': packages
                }
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 获取用户积分日志
@user_credits_bp.route('/logs', methods=['GET'])
@jwt_required()
def get_user_points_logs():
    """获取用户积分日志"""
    try:
        user_id = get_jwt_identity()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        log_type = request.args.get('type', '')
        
        query = PointsLog.query.filter(PointsLog.user_id == user_id)
        
        if log_type:
            query = query.filter(PointsLog.type == log_type)
        
        pagination = query.order_by(PointsLog.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        logs = [log.to_dict() for log in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 创建充值订单
@user_credits_bp.route('/create-order', methods=['POST'])
@jwt_required()
def create_recharge_order():
    """创建充值订单"""
    try:
        user_id = get_jwt_identity()
        data = request.json
        
        package_id = data.get('package_id')
        payment_method = data.get('payment_method')  # wechat/alipay
        
        if not package_id or not payment_method:
            return jsonify({'success': False, 'error': '参数不完整'}), 400
        
        # 获取积分配置
        config = PointsConfig.query.first()
        if not config:
            return jsonify({'success': False, 'error': '系统配置错误'}), 500
        
        # 解析套餐配置
        packages = []
        if config.payment_packages:
            try:
                packages = json.loads(config.payment_packages)
            except:
                packages = []
        
        # 查找套餐
        selected_package = None
        for pkg in packages:
            if pkg.get('id') == package_id and pkg.get('enabled', True):
                selected_package = pkg
                break
        
        if not selected_package:
            return jsonify({'success': False, 'error': '套餐不存在或已禁用'}), 400
        
        # 检查支付方式是否启用
        if payment_method == 'wechat' and not config.wechat_enabled:
            return jsonify({'success': False, 'error': '微信支付未启用'}), 400
        
        if payment_method == 'alipay' and not config.alipay_enabled:
            return jsonify({'success': False, 'error': '支付宝支付未启用'}), 400
        
        # 创建订单
        order_no = f"R{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:8]}"
        
        order = PaymentOrder(
            order_no=order_no,
            user_id=user_id,
            amount=selected_package['price'],
            points=selected_package['points'],
            package_name=selected_package['name'],
            payment_method=payment_method,
            status='pending',
            payment_status='pending'
        )
        
        db.session.add(order)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'data': {
                'order_no': order_no,
                'amount': order.amount,
                'points': order.points,
                'package_name': order.package_name,
                'payment_method': order.payment_method,
                'qr_code': config.wechat_qr_code if payment_method == 'wechat' else config.alipay_qr_code,
                'receiver_name': config.wechat_receiver_name if payment_method == 'wechat' else config.alipay_receiver_name,
                'payment_note': config.wechat_payment_note if payment_method == 'wechat' else config.alipay_payment_note
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 确认支付
@user_credits_bp.route('/confirm-payment', methods=['POST'])
@jwt_required()
def confirm_payment():
    """确认支付"""
    try:
        user_id = get_jwt_identity()
        data = request.json
        
        order_no = data.get('order_no')
        transaction_id = data.get('transaction_id', '')
        
        if not order_no:
            return jsonify({'success': False, 'error': '订单号不能为空'}), 400
        
        # 查找订单
        order = PaymentOrder.query.filter(
            PaymentOrder.order_no == order_no,
            PaymentOrder.user_id == user_id
        ).first()
        
        if not order:
            return jsonify({'success': False, 'error': '订单不存在'}), 404
        
        if order.status != 'pending':
            return jsonify({'success': False, 'error': '订单状态错误'}), 400
        
        # 更新订单状态
        order.payment_status = 'paid'
        order.status = 'completed'
        order.payment_time = datetime.utcnow()
        order.transaction_id = transaction_id
        
        # 给用户增加积分
        user = User.query.get(user_id)
        user.add_credits(
            order.points,
            f'充值购买：{order.package_name}',
            order_no
        )
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '支付确认成功',
            'data': {
                'points': user.points
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 获取用户订单列表
@user_credits_bp.route('/orders', methods=['GET'])
@jwt_required()
def get_user_orders():
    """获取用户订单列表"""
    try:
        user_id = get_jwt_identity()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', '')
        
        query = PaymentOrder.query.filter(PaymentOrder.user_id == user_id)
        
        if status:
            query = query.filter(PaymentOrder.status == status)
        
        pagination = query.order_by(PaymentOrder.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        orders = [order.to_dict() for order in pagination.items]
        
        return jsonify({
            'success': True,
            'data': {
                'orders': orders,
                'total': pagination.total,
                'pages': pagination.pages,
                'current_page': page,
                'per_page': per_page
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 观看广告获得积分
@user_credits_bp.route('/watch-ad', methods=['POST'])
@jwt_required()
def watch_ad_reward():
    """观看广告获得积分"""
    try:
        user_id = get_jwt_identity()
        user = User.query.get(user_id)
        
        if not user:
            return jsonify({'success': False, 'error': '用户不存在'}), 404
        
        # 获取积分配置
        config = PointsConfig.query.first()
        if not config:
            return jsonify({'success': False, 'error': '系统配置错误'}), 500
        
        # 检查今日是否已经观看过广告
        from datetime import date
        today = date.today()
        
        today_ad_logs = PointsLog.query.filter(
            PointsLog.user_id == user_id,
            PointsLog.type == 'ad_reward',
            db.func.date(PointsLog.created_at) == today
        ).count()
        
        if today_ad_logs >= 5:  # 每日最多观看5次广告
            return jsonify({'success': False, 'error': '今日观看广告次数已达上限'}), 400
        
        # 增加积分
        user.add_credits(config.ad_reward, '观看广告奖励')
        
        return jsonify({
            'success': True,
            'message': f'获得{config.ad_reward}积分',
            'data': {
                'points': user.points,
                'reward': config.ad_reward
            }
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500 