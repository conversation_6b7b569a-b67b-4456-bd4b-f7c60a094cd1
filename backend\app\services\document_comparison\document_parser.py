import os
from typing import Optional, Tuple, List, Generator, Any, Union, Dict
from docx import Document
import fitz  # PyMuPDF
import logging
import re
from docx.opc.exceptions import PackageNotFoundError
from docx.document import Document as _Document
from docx.oxml.text.paragraph import CT_P
from docx.text.paragraph import Paragraph
from docx.table import _Cell, Table, _Row
import unicodedata
from pathlib import Path
import tempfile

# 核心文档处理库 - 按about.md推荐的技术栈
from docx import Document
from docx.document import Document as _Document
from docx.oxml.text.paragraph import CT_P
from docx.text.paragraph import Paragraph
from docx.table import _Cell, Table
from docx.opc.exceptions import PackageNotFoundError

import fitz  # PyMuPDF - 高性能PDF处理
import pdfplumber  # PDF表格识别
try:
    import camelot  # PDF表格专用解析
    CAMELOT_AVAILABLE = True
except ImportError:
    CAMELOT_AVAILABLE = False

# OCR支持 - 按about.md建议使用PaddleOCR
try:
    from paddleocr import PaddleOCR
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

# 图像处理 - 用于PDF视觉对比
from PIL import Image, ImageChops
import numpy as np
from skimage.metrics import structural_similarity as ssim

logger = logging.getLogger(__name__)


class DocumentParser:
    """
    增强的文档解析器 - 按about.md技术方案实现
    支持Word(.docx)、WPS、PDF(文本版+扫描版)的智能解析
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # 初始化OCR引擎（如果可用）
        self.ocr_engine = None
        if OCR_AVAILABLE:
            try:
                self.ocr_engine = PaddleOCR(
                    use_angle_cls=True,
                    lang='ch'  # 中文支持
                )
                self.logger.info("OCR引擎初始化成功")
            except Exception as e:
                self.logger.warning(f"OCR引擎初始化失败: {e}")
                self.ocr_engine = None

        # 支持的文件格式
        self.supported_formats = {
            'docx': self._parse_docx,
            'wps': self._parse_wps,  # WPS特殊处理
            'pdf': self._parse_pdf,
            'txt': self._parse_txt
        }

    def parse_document(self, file_path: str, options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        智能文档解析 - 主入口

        Args:
            file_path: 文件路径
            options: 解析选项，包括：
                - extract_tables: 是否提取表格结构
                - extract_images: 是否提取图片信息
                - use_ocr: 是否使用OCR（PDF扫描版）
                - preserve_structure: 是否保留文档结构

        Returns:
            Dict: 包含文本、结构化信息、元数据的解析结果
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        # 默认选项
        default_options = {
            'extract_tables': True,
            'extract_images': False,
            'use_ocr': True,
            'preserve_structure': True
        }
        options = {**default_options, **(options or {})}

        # 检测文件格式
        file_format = self._detect_file_format(file_path)
        self.logger.info(f"检测到文件格式: {file_format}")

        # 根据格式选择解析器
        if file_format not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {file_format}")

        try:
            # 调用对应的解析器
            result = self.supported_formats[file_format](file_path, options)

            # 添加通用元数据
            result['file_info'] = self._get_file_info(file_path)
            result['parsing_options'] = options

            return result

        except Exception as e:
            self.logger.error(f"文档解析失败 {file_path}: {str(e)}")
            raise ValueError(f"解析失败: {str(e)}")

    def _detect_file_format(self, file_path: str) -> str:
        """
        增强的文件格式检测 - 基于文件签名和扩展名
        """
        file_ext = Path(file_path).suffix.lower().lstrip('.')

        try:
            with open(file_path, 'rb') as f:
                header = f.read(16)

                # 文件签名检测
                signatures = {
                    b'PK\x03\x04': ['docx', 'wps'],  # ZIP格式
                    b'%PDF': ['pdf'],
                    b'\xD0\xCF\x11\xE0': ['doc'],  # OLE格式（不支持）
                }

                for signature, formats in signatures.items():
                    if header.startswith(signature):
                        if file_ext in formats:
                            return file_ext
                        # WPS文件可能以docx扩展名存储
                        if file_ext == 'docx' and self._is_wps_file(file_path):
                            return 'wps'
                        return formats[0]

                # 纯文本文件
                if file_ext in ['txt', 'text']:
                    return 'txt'

                # 回退到扩展名判断
                if file_ext in self.supported_formats:
                    return file_ext

                raise ValueError(f"无法识别的文件格式: {file_ext}")

        except Exception as e:
            self.logger.warning(f"文件格式检测失败: {e}")
            if file_ext in self.supported_formats:
                return file_ext
            raise ValueError(f"不支持的文件格式: {file_ext}")

    def _is_wps_file(self, file_path: str) -> bool:
        """
        检测是否为WPS文件 - 根据about.md提到的WPS兼容性问题
        """
        try:
            # 尝试用标准docx库打开
            doc = Document(file_path)
            # 检查是否有WPS特有的属性或标记
            if hasattr(doc, 'core_properties'):
                creator = getattr(doc.core_properties, 'creator', '')
                if 'wps' in creator.lower() or 'kingsoft' in creator.lower():
                    return True
            return False
        except:
            return False

    def _parse_docx(self, file_path: str, options: Dict) -> Dict[str, Any]:
        """
        解析标准DOCX文件 - 按about.md建议的结构化解析
        """
        try:
            doc = Document(file_path)

            result = {
                'text': '',
                'structured_content': [],
                'tables': [],
                'metadata': self._extract_docx_metadata(doc),
                'format': 'docx'
            }

            if options.get('preserve_structure', True):
                # 结构化解析 - 保留段落、表格、列表等结构
                try:
                    for element in self._iter_block_items(doc):
                        if isinstance(element, Paragraph):
                            para_info = self._process_paragraph_structured(
                                element)
                            if para_info:
                                result['structured_content'].append(para_info)
                        elif isinstance(element, Table):
                            if options.get('extract_tables', True):
                                table_info = self._process_table_structured(
                                    element)
                                if table_info:
                                    result['tables'].append(table_info)
                                    result['structured_content'].append({
                                        'type': 'table',
                                        'content': table_info
                                    })
                except Exception as e:
                    self.logger.warning(f"结构化解析失败，降级到简单文本提取: {e}")
                    # 降级到简单文本提取
                    for para in doc.paragraphs:
                        para_info = self._process_paragraph_structured(para)
                        if para_info:
                            result['structured_content'].append(para_info)

                    if options.get('extract_tables', True):
                        for table in doc.tables:
                            table_info = self._process_table_structured(table)
                            if table_info:
                                result['tables'].append(table_info)
            else:
                # 简单解析
                for para in doc.paragraphs:
                    text = para.text.strip()
                    if text:
                        result['structured_content'].append({
                            'type': 'paragraph',
                            'content': text
                        })

            # 提取纯文本
            if result['structured_content']:
                result['text'] = self._extract_plain_text(
                    result['structured_content'])
            else:
                # 如果结构化内容为空，直接提取文本
                result['text'] = '\n'.join(
                    [para.text for para in doc.paragraphs if para.text.strip()])

            return result

        except Exception as e:
            self.logger.error(f"DOCX解析失败: {e}")
            raise

    def _extract_docx_metadata(self, doc):
        """提取DOCX文件元数据"""
        try:
            metadata = {}
            if hasattr(doc, 'core_properties'):
                props = doc.core_properties
                metadata.update({
                    'title': props.title or '',
                    'author': props.author or '',
                    'subject': props.subject or '',
                    'created': props.created.isoformat() if props.created else '',
                    'modified': props.modified.isoformat() if props.modified else ''
                })
            return metadata
        except Exception:
            return {}

    def _iter_block_items(self, parent):
        """迭代文档中的块级元素（段落和表格）"""
        try:
            from docx.oxml.ns import qn
            if isinstance(parent, _Document):
                parent_elm = parent.element.body
            else:
                parent_elm = parent._element

            for child in parent_elm.iterchildren():
                if child.tag == qn('w:p'):
                    yield Paragraph(child, parent)
                elif child.tag == qn('w:tbl'):
                    yield Table(child, parent)
        except Exception:
            # 如果无法获取元素，回退到简单方式
            for para in parent.paragraphs:
                yield para
            for table in parent.tables:
                yield table

    def _process_paragraph_structured(self, paragraph):
        """处理段落并返回结构化信息"""
        text = paragraph.text.strip()
        if not text:
            return None

        return {
            'type': 'paragraph',
            'content': text,
            'style': self._get_paragraph_style(paragraph)
        }

    def _get_paragraph_style(self, paragraph):
        """获取段落样式信息"""
        try:
            style_info = {}
            if paragraph.style:
                style_info['style_name'] = paragraph.style.name
            if paragraph.runs:
                run = paragraph.runs[0]
                if run.font.bold:
                    style_info['bold'] = True
                if run.font.italic:
                    style_info['italic'] = True
            return style_info
        except Exception:
            return {}

    def _process_table_structured(self, table):
        """处理表格并返回结构化信息"""
        try:
            rows_data = []
            for row in table.rows:
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    row_data.append(cell_text)
                rows_data.append(row_data)

            return {
                'type': 'table',
                'rows': rows_data,
                'row_count': len(rows_data),
                'col_count': len(rows_data[0]) if rows_data else 0
            }
        except Exception:
            return None

    def _extract_plain_text(self, structured_content):
        """从结构化内容中提取纯文本"""
        text_parts = []
        for item in structured_content:
            if item.get('type') == 'paragraph':
                text_parts.append(item.get('content', ''))
            elif item.get('type') == 'table':
                # 将表格转换为文本
                table_content = item.get('content', {})
                rows = table_content.get('rows', [])
                for row in rows:
                    text_parts.append(' | '.join(row))
        return '\n'.join(text_parts)

    def _get_file_info(self, file_path):
        """获取文件基本信息"""
        try:
            stat = os.stat(file_path)
            return {
                'filename': os.path.basename(file_path),
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'extension': Path(file_path).suffix.lower()
            }
        except Exception:
            return {}

    def _parse_wps(self, file_path: str, options: Dict) -> Dict[str, Any]:
        """解析WPS文件（通常与DOCX兼容）"""
        return self._parse_docx(file_path, options)

    def _parse_pdf(self, file_path: str, options: Dict) -> Dict[str, Any]:
        """解析PDF文件"""
        # 简单的PDF文本提取实现
        try:
            import fitz
            doc = fitz.open(file_path)
            text = ""
            for page in doc:
                text += page.get_text()
            doc.close()

            return {
                'text': text,
                'structured_content': [{'type': 'paragraph', 'content': text}],
                'tables': [],
                'metadata': {'format': 'pdf'},
                'format': 'pdf'
            }
        except Exception as e:
            self.logger.error(f"PDF解析失败: {e}")
            raise

    def _parse_txt(self, file_path: str, options: Dict) -> Dict[str, Any]:
        """解析纯文本文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                text = f.read()

            return {
                'text': text,
                'structured_content': [{'type': 'paragraph', 'content': text}],
                'tables': [],
                'metadata': {'format': 'txt'},
                'format': 'txt'
            }
        except Exception as e:
            self.logger.error(f"TXT解析失败: {e}")
            raise
