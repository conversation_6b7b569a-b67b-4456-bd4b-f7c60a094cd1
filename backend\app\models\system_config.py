from datetime import datetime
import json
from app import db

class SystemConfig(db.Model):
    """系统配置模型"""
    __tablename__ = 'system_configs'
    
    id = db.Column(db.Integer, primary_key=True)
    config_key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    config_value = db.Column(db.Text, nullable=False)
    config_type = db.Column(db.String(20), default='string')  # string, integer, boolean, json
    description = db.Column(db.String(255))
    is_system = db.Column(db.<PERSON><PERSON>, default=False)  # 是否系统配置
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(SystemConfig, self).__init__(**kwargs)
    
    def get_value(self):
        """获取转换后的配置值"""
        if self.config_type == 'integer':
            try:
                return int(self.config_value)
            except ValueError:
                return 0
        elif self.config_type == 'boolean':
            return self.config_value.lower() in ('true', '1', 'yes', 'on')
        elif self.config_type == 'json':
            try:
                return json.loads(self.config_value)
            except json.JSONDecodeError:
                return {}
        else:
            return self.config_value
    
    def set_value(self, value):
        """设置配置值"""
        if self.config_type == 'json':
            if isinstance(value, (dict, list)):
                self.config_value = json.dumps(value, ensure_ascii=False)
            else:
                self.config_value = str(value)
        else:
            self.config_value = str(value)
    
    @classmethod
    def get_config(cls, key, default=None):
        """获取配置值"""
        config = cls.query.filter_by(config_key=key).first()
        if config:
            return config.get_value()
        return default
    
    @classmethod
    def set_config(cls, key, value, config_type='string', description=None):
        """设置配置值"""
        config = cls.query.filter_by(config_key=key).first()
        if config:
            config.set_value(value)
            config.updated_at = datetime.utcnow()
        else:
            config = cls(
                config_key=key,
                config_type=config_type,
                description=description
            )
            config.set_value(value)
            db.session.add(config)
        return config
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'config_key': self.config_key,
            'config_value': self.config_value,
            'config_type': self.config_type,
            'description': self.description,
            'is_system': self.is_system,
            'value': self.get_value(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<SystemConfig {self.config_key}: {self.config_value}>' 