from flask import request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity

from . import api_bp
from .errors import bad_request, unauthorized, not_found, validation_error
from app import db
from app.models import User, UserLog

@api_bp.route('/users/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """获取用户资料"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return unauthorized('用户不存在')
    
    return jsonify({
        'user': user.to_dict(include_sensitive=True)
    })

@api_bp.route('/users/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """更新用户资料"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return unauthorized('用户不存在')
    
    data = request.get_json()
    if not data:
        return bad_request('请求数据为空')
    
    try:
        # 更新允许的字段
        updatable_fields = ['nickname', 'phone', 'gender', 'birthday']
        
        for field in updatable_fields:
            if field in data:
                if field == 'birthday' and data[field]:
                    from datetime import datetime
                    try:
                        birthday = datetime.strptime(data[field], '%Y-%m-%d').date()
                        setattr(user, field, birthday)
                    except ValueError:
                        return validation_error('生日格式不正确，请使用YYYY-MM-DD格式')
                else:
                    setattr(user, field, data[field])
        
        # 记录操作日志
        UserLog.log_action(
            user_id=current_user_id,
            action='profile_update',
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string,
            request_data=data
        )
        
        db.session.commit()
        
        return jsonify({
            'message': '资料更新成功',
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新用户资料失败: {e}")
        return validation_error('更新失败，请重试')

@api_bp.route('/users/change-password', methods=['POST'])
@jwt_required()
def change_password():
    """修改密码"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return unauthorized('用户不存在')
    
    data = request.get_json()
    if not data:
        return bad_request('请求数据为空')
    
    old_password = data.get('old_password')
    new_password = data.get('new_password')
    
    if not old_password or not new_password:
        return bad_request('请提供旧密码和新密码')
    
    # 验证旧密码
    if not user.check_password(old_password):
        return validation_error('旧密码错误')
    
    # 验证新密码
    if len(new_password) < 6:
        return validation_error('新密码长度不能少于6位')
    
    if new_password == old_password:
        return validation_error('新密码不能与旧密码相同')
    
    try:
        # 更新密码
        user.set_password(new_password)
        
        # 记录操作日志
        UserLog.log_action(
            user_id=current_user_id,
            action='password_change',
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        
        db.session.commit()
        
        return jsonify({'message': '密码修改成功'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修改密码失败: {e}")
        return validation_error('修改密码失败，请重试')

@api_bp.route('/users/set-avatar', methods=['POST'])
@jwt_required()
def set_avatar():
    """设置用户头像"""
    current_user_id = get_jwt_identity()
    user = User.query.get(current_user_id)
    
    if not user:
        return unauthorized('用户不存在')
    
    data = request.get_json()
    if not data:
        return bad_request('请求数据为空')
    
    image_id = data.get('image_id')
    if not image_id:
        return bad_request('请提供图片ID')
    
    try:
        # 验证图片是否存在且属于当前用户
        from app.models import Image
        image = Image.query.filter_by(
            id=image_id, 
            user_id=current_user_id,
            status=1
        ).first()
        
        if not image:
            return not_found('图片不存在或无权限访问')
        
        # 更新用户头像URL - 使用图片的URL方法
        avatar_url = image.get_url()
        user.avatar_url = avatar_url
        
        # 记录操作日志
        UserLog.log_action(
            user_id=current_user_id,
            action='avatar_set',
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string,
            request_data={'image_id': image_id, 'avatar_url': avatar_url}
        )
        
        db.session.commit()
        
        return jsonify({
            'message': '头像设置成功',
            'avatar_url': avatar_url,
            'user': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"设置用户头像失败: {e}")
        return validation_error('设置头像失败，请重试') 