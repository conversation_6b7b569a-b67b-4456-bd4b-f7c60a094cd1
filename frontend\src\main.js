import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'

import App from './App.vue'
import router from './router'
import { useAuthStore } from './stores/auth'

// 全局样式
import './assets/styles/main.css'

// API配置
import api from './api'

// ResizeObserver 错误处理
const originalError = console.error;
console.error = (...args) => {
  if (args[0] && typeof args[0] === 'string' && args[0].includes('ResizeObserver loop')) {
    return;
  }
  originalError.apply(console, args);
};

// 全局错误处理
window.addEventListener('error', (event) => {
  if (event.message && event.message.includes('ResizeObserver loop')) {
    event.stopImmediatePropagation();
    event.preventDefault();
    return false;
  }
});

// Promise 错误处理
window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message && event.reason.message.includes('ResizeObserver loop')) {
    event.preventDefault();
    return false;
  }
});

const app = createApp(App)
const pinia = createPinia()

// 安装插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
})

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 全局属性
app.config.globalProperties.$api = api

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // 需要认证的路由
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  
  if (requiresAuth && !authStore.isAuthenticated) {
    // 尝试从本地存储恢复认证状态
    const isAuthenticated = await authStore.checkAuth()
    
    if (!isAuthenticated) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }
  }
  
  // 已登录用户访问登录页面，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

// 全局错误处理
const errorHandler = (error, vm, info) => {
    // 忽略 ResizeObserver 循环错误
    if (error.message && error.message.includes('ResizeObserver loop')) {
        return;
    }
    console.error('Vue Error:', error);
    console.error('Component:', vm);
    console.error('Info:', info);
};

app.config.errorHandler = errorHandler;

// 挂载应用
app.mount('#app')

console.log('🎨 相片制作工具集前端启动成功!')
console.log('📍 环境:', process.env.NODE_ENV || 'development')
console.log('🔗 API地址:', process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000') 