<template>
  <div class="document-manager">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">文档管理</h1>
        <p class="page-description">智能文档对比系统，支持Word、PDF、WPS格式文档的精准对比分析</p>
      </div>
      <div class="tabs">
        <button v-for="tab in tabs" :key="tab.key" :class="['tab-btn', { active: activeTab === tab.key }]" @click="activeTab = tab.key">
          {{ tab.label }}
        </button>
      </div>
    </div>

    <!-- 文档上传区域 -->
    <div v-if="activeTab === 'upload'" class="tab-content">
      <div class="section-header">
        <h2 class="section-title">文档上传</h2>
        <p class="section-subtitle">选择要对比的文档并开始智能分析</p>
      </div>
      
      <div class="upload-container">
        <div class="upload-boxes">
          <!-- 标准版文档上传框 -->
          <div class="upload-box" 
               :class="{ 'has-file': standardFile, 'drag-over': dragStates.standard }"
               @drop="handleDrop($event, 'standard')" 
               @dragover.prevent="handleDragOver"
               @dragenter.prevent="handleDragEnter($event, 'standard')"
               @dragleave.prevent="handleDragLeave($event, 'standard')"
               @click="!standardFile && $refs.standardFileInput.click()">
            <input 
              ref="standardFileInput" 
              type="file" 
              accept=".docx,.pdf,.wps,.doc"
              @change="handleFileSelect($event, 'standard')"
              style="display: none"
            >
            
            <!-- 文件未选择状态 -->
            <div v-if="!standardFile" class="upload-placeholder">
              <h3 class="upload-title">标准版文档</h3>
              <p class="upload-hint">拖拽文件到此处或点击选择</p>
              <p class="upload-format">支持：.docx, .pdf, .wps, .doc（最大50MB）</p>
              <button class="upload-btn" type="button">
                <span class="btn-icon">📁</span>
              选择文件
            </button>
            </div>
            
            <!-- 文件已选择状态 -->
            <div v-else class="file-preview">
              <div class="file-icon">{{ getDocIcon(getFileExtension(standardFile.name)) }}</div>
              <div class="file-info">
                <h4 class="file-name">{{ standardFile.name }}</h4>
                <p class="file-size">{{ formatFileSize(standardFile.size) }}</p>
                <div class="file-type-badge">标准版</div>
              </div>
              <div class="file-actions">
                <button class="action-btn primary" @click.stop="$refs.standardFileInput.click()" title="重新选择">
                  <span class="btn-icon">🔄</span>
                </button>
                <button class="action-btn danger" @click.stop="removeFile('standard')" title="移除文件">
                  <span class="btn-icon">🗑️</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 修改版文档上传框 -->
          <div class="upload-box" 
               :class="{ 'has-file': modifiedFile, 'drag-over': dragStates.modified }"
               @drop="handleDrop($event, 'modified')" 
               @dragover.prevent="handleDragOver"
               @dragenter.prevent="handleDragEnter($event, 'modified')"
               @dragleave.prevent="handleDragLeave($event, 'modified')"
               @click="!modifiedFile && $refs.modifiedFileInput.click()">
            <input 
              ref="modifiedFileInput" 
              type="file" 
              accept=".docx,.pdf,.wps,.doc"
              @change="handleFileSelect($event, 'modified')"
              style="display: none"
            >
            
            <!-- 文件未选择状态 -->
            <div v-if="!modifiedFile" class="upload-placeholder">
              <h3 class="upload-title">修改版文档</h3>
              <p class="upload-hint">拖拽文件到此处或点击选择</p>
              <p class="upload-format">支持：.docx, .pdf, .wps, .doc（最大50MB）</p>
              <button class="upload-btn" type="button">
                <span class="btn-icon">📁</span>
              选择文件
            </button>
        </div>

            <!-- 文件已选择状态 -->
            <div v-else class="file-preview">
              <div class="file-icon">{{ getDocIcon(getFileExtension(modifiedFile.name)) }}</div>
            <div class="file-info">
                <h4 class="file-name">{{ modifiedFile.name }}</h4>
                <p class="file-size">{{ formatFileSize(modifiedFile.size) }}</p>
                <div class="file-type-badge modified">修改版</div>
            </div>
              <div class="file-actions">
                <button class="action-btn primary" @click.stop="$refs.modifiedFileInput.click()" title="重新选择">
                  <span class="btn-icon">🔄</span>
              </button>
                <button class="action-btn danger" @click.stop="removeFile('modified')" title="移除文件">
                  <span class="btn-icon">🗑️</span>
              </button>
            </div>
            </div>
          </div>
        </div>

        <!-- 对比操作区域 -->
        <div v-if="standardFile || modifiedFile" class="comparison-controls">
          <div class="control-header">
            <h3 class="control-title">对比设置</h3>
            <p class="control-subtitle">配置智能对比选项</p>
          </div>
          
          <div class="comparison-options">
            <div class="option-group">
              <label class="option-label">
                <input v-model="compareConfig.ignoreWhitespace" type="checkbox" class="option-checkbox">
                <span class="option-text">忽略空格差异</span>
              </label>
              <label class="option-label">
                <input v-model="compareConfig.detailedReport" type="checkbox" class="option-checkbox">
                <span class="option-text">生成详细结构化报告</span>
              </label>
            </div>
            <div class="option-group">
              <label class="option-label">
                <input v-model="compareConfig.extractTables" type="checkbox" class="option-checkbox">
                <span class="option-text">智能表格对比</span>
              </label>
              <label class="option-label">
                <input v-model="compareConfig.useOcr" type="checkbox" class="option-checkbox">
                <span class="option-text">OCR扫描版PDF支持</span>
              </label>
            </div>
            <div class="option-group" v-if="isPdfFiles">
              <label class="option-label">
                <input v-model="compareConfig.visualComparison" type="checkbox" class="option-checkbox">
                <span class="option-text">PDF视觉对比</span>
              </label>
            </div>
          </div>
          
          <div class="comparison-actions">
            <button 
              class="compare-btn primary" 
              @click="startDirectComparison"
              :disabled="!canStartDirectComparison || uploading"
            >
              <span class="btn-text">{{ uploading ? '处理中...' : '开始智能对比' }}</span>
            </button>
            <button class="compare-btn secondary" @click="clearAllFiles">
              <span class="btn-text">清空文件</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文档列表 -->
    <div v-if="activeTab === 'documents'" class="tab-content">
      <div class="documents-section">
        <!-- 文档列表section表头精简为两行 -->
        <div class="section-header compact">
          <div class="section-title-left">我的文档</div>
          <div class="section-title-right">
            <select v-model="documentFilter" @change="loadDocuments" class="filter-select">
              <option value="documents">我的文档</option>
              <option value="standard">标准版原文</option>
              <option value="modified">修改后原文</option>
            </select>
            <button class="refresh-btn" @click="loadDocuments">
              <span class="btn-icon"><svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M4 4v5h5"/><path d="M20 20v-5h-5"/><path d="M5 19A9 9 0 0021 12.5"/></svg></span>
              <span class="btn-text">刷新</span>
            </button>
            <label class="select-all-label">
              <input type="checkbox" :checked="isAllSelected" @change="toggleSelectAll" class="select-all-checkbox">
              <span class="checkbox-text">全选</span>
            </label>
            <span v-if="selectedDocs.length > 0" class="selection-count">已选{{ selectedDocs.length }}</span>
            <button v-if="selectedDocs.length > 0" class="batch-btn danger" @click="batchDelete">
              <span class="btn-icon"><svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M6 7v10a2 2 0 002 2h8a2 2 0 002-2V7"/><path d="M9 7V4a1 1 0 011-1h4a1 1 0 011 1v3"/><path d="M4 7h16"/></svg></span>
              <span class="btn-text">批量删除</span>
            </button>
            <button v-if="selectedDocs.length === 2" class="batch-btn primary" @click="startComparison">
              <span class="btn-icon"><svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><circle cx="11" cy="11" r="7"/><path d="M21 21l-4.35-4.35"/></svg></span>
              <span class="btn-text">开始对比</span>
            </button>
            <button v-if="selectedDocs.length > 0" class="batch-btn secondary" @click="clearSelection">
              清除选择
            </button>
          </div>
        </div>

        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <p>加载中...</p>
        </div>

        <div v-else-if="documents.length === 0" class="empty-state">
          <div class="empty-icon"></div>
          <p>暂无文档</p>
          <button class="btn btn-primary" @click="activeTab = 'upload'">
            上传文档
          </button>
        </div>

        <div v-else class="documents-grid">
          <div v-for="doc in documents" :key="doc.id" :class="['document-card', { selected: selectedDocs.includes(doc.id) }]" @click="toggleDocSelection(doc.id)">
            <span class="doc-type-badge" :class="doc.document_type">{{ getDocTypeLabel(doc.document_type) }}</span>
            <div class="doc-row">
              <input type="checkbox" :checked="selectedDocs.includes(doc.id)" @click.stop @change="toggleDocSelection(doc.id)" class="doc-checkbox">
              <div class="doc-info">
                <div class="doc-title-row">
                  <span class="doc-title">{{ doc.original_filename }}</span>
                </div>
                <div class="doc-meta-row">
                  <span class="doc-size">{{ formatFileSize(doc.file_size) }}</span>
                  <span class="doc-date">{{ formatDate(doc.upload_time) }}</span>
                </div>
              </div>
            </div>
            <div class="doc-action-row">
              <div class="doc-actions-bottom">
                <button class="action-btn preview" @click.stop="previewDocument(doc)" title="预览文档">
                  <span class="btn-icon"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M1.5 12S5.25 5.25 12 5.25 22.5 12 22.5 12 18.75 18.75 12 18.75 1.5 12 1.5 12Z"/><circle cx="12" cy="12" r="3" /></svg></span>
                </button>
                <button class="action-btn download" @click.stop="downloadDocument(doc)" title="下载文档">
                  <span class="btn-icon"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M12 3v12m0 0l-4-4m4 4l4-4"/><path d="M4 17h16"/></svg></span>
                </button>
                <button class="action-btn delete" @click.stop="deleteDocument(doc)" title="删除文档">
                  <span class="btn-icon"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M6 7v10a2 2 0 002 2h8a2 2 0 002-2V7"/><path d="M9 7V4a1 1 0 011-1h4a1 1 0 011 1v3"/><path d="M4 7h16"/></svg></span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对比任务 -->
    <div v-if="activeTab === 'tasks'" class="tab-content">
      <div class="tasks-section">
        <!-- 对比任务section表头精简为两行 -->
        <div class="section-header compact">
          <div class="section-title-left">对比任务</div>
          <div class="section-title-right">
            <button class="refresh-btn" @click="loadTasks">
              <span class="btn-icon"><svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M4 4v5h5"/><path d="M20 20v-5h-5"/><path d="M5 19A9 9 0 0021 12.5"/></svg></span>
              <span class="btn-text">刷新</span>
            </button>
            <label class="select-all-label">
              <input type="checkbox" :checked="isAllTasksSelected" @change="toggleSelectAllTasks" class="select-all-checkbox">
              <span class="checkbox-text">全选</span>
            </label>
            <span v-if="selectedTasks.length > 0" class="selection-count">已选{{ selectedTasks.length }}</span>
            <button v-if="selectedTasks.length > 0" class="batch-btn danger" @click="batchDeleteTasks">
              <span class="btn-icon"><svg width="18" height="18" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M6 7v10a2 2 0 002 2h8a2 2 0 002-2V7"/><path d="M9 7V4a1 1 0 011-1h4a1 1 0 011 1v3"/><path d="M4 7h16"/></svg></span>
              <span class="btn-text">批量删除</span>
            </button>
          </div>
        </div>

        <div v-if="tasksLoading" class="loading">
          <div class="spinner"></div>
          <p>加载中...</p>
        </div>

        <div v-else-if="tasks.length === 0" class="empty-state">
          <div class="empty-icon"></div>
          <p>暂无对比任务</p>
        </div>

        <div v-else class="tasks-grid">
          <div 
            v-for="task in tasks" 
            :key="task.id" 
            :class="['task-card', task.status, { selected: selectedTasks.includes(task.task_id) }]"
            @click="toggleTaskSelection(task.task_id)"
          >
            <div style="display: flex; align-items: flex-start;">
              <!-- 任务选择框，始终最左侧 -->
              <div class="card-checkbox">
                <input 
                  type="checkbox" 
                  :checked="selectedTasks.includes(task.task_id)"
                  @click.stop
                  @change="toggleTaskSelection(task.task_id)"
                  class="task-checkbox-input"
                >
              </div>
              <!-- 任务信息，顶部对齐 -->
              <div class="task-info" style="flex:1; min-width:0;">
                <h3 class="task-title">{{ task.task_name || `${task.standard_doc?.original_filename || '文档1'} vs ${task.modified_doc?.original_filename || '文档2'}` }}</h3>
                <div class="task-meta">
                  <div class="task-status-badge" :class="task.status">
                    {{ getTaskStatusLabel(task.status) }}
                  </div>
                  <div class="task-date">{{ formatDate(task.created_time) }}</div>
                </div>
                <!-- 任务统计信息 -->
                <div class="task-stats" v-if="task.status === 'completed'">
                  <div class="stat-item" v-if="task.processing_time">
                    <span class="stat-value">{{ task.processing_time.toFixed(2) }}秒</span>
                  </div>
                  <div class="stat-item" v-if="task.diff_count !== null">
                    <span class="stat-value">{{ task.diff_count }}处差异</span>
                  </div>
                </div>
                <!-- 处理进度 -->
                <div v-if="task.status === 'processing'" class="task-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: (task.progress || 0) + '%' }"></div>
                  </div>
                  <p class="progress-text">{{ task.status_message || '处理中...' }} ({{ task.progress || 0 }}%)</p>
                </div>
                <!-- 错误信息 -->
                <div v-if="task.error_message" class="error-message">
                  <span class="error-text">{{ task.error_message }}</span>
                </div>
              </div>
            </div>
            <!-- 底部操作按钮单独一行 -->
            <div class="task-love-row">
              <div class="task-actions-bottom">
                <button 
                  v-if="task.status === 'completed' && task.report_path" 
                  class="action-btn preview" 
                  @click.stop="viewReport(task.task_id)"
                  title="查看报告"
                >
                  <span class="btn-icon"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M1.5 12S5.25 5.25 12 5.25 22.5 12 22.5 12 18.75 18.75 12 18.75 1.5 12 1.5 12Z"/><circle cx="12" cy="12" r="3" /></svg></span>
                </button>
                <button 
                  v-if="task.status === 'completed' && task.report_path" 
                  class="action-btn download" 
                  @click.stop="downloadReport(task.task_id)"
                  title="下载报告"
                >
                  <span class="btn-icon"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M12 3v12m0 0l-4-4m4 4l4-4"/><path d="M4 17h16"/></svg></span>
                </button>
                <button 
                  v-if="task.status === 'processing'" 
                  class="action-btn refresh" 
                  @click.stop="refreshTask(task.task_id)"
                  title="刷新状态"
                >
                  <span class="btn-icon"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M4 4v5h5"/><path d="M20 20v-5h-5"/><path d="M5 19A9 9 0 0021 12.5"/></svg></span>
                </button>
                <button 
                  class="action-btn delete" 
                  @click.stop="deleteTask(task.task_id)"
                  title="删除任务"
                >
                  <span class="btn-icon"><svg width="20" height="20" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24"><path d="M6 7v10a2 2 0 002 2h8a2 2 0 002-2V7"/><path d="M9 7V4a1 1 0 011-1h4a1 1 0 011 1v3"/><path d="M4 7h16"/></svg></span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对比配置对话框 -->
    <div v-if="showCompareDialog" class="modal-overlay" @click="closeCompareDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>文档对比配置</h3>
          <button class="close-btn" @click="closeCompareDialog">×</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="submitComparison">
            <div class="form-group">
              <label>任务名称：</label>
              <input 
                v-model="compareConfig.taskName" 
                type="text" 
                placeholder="例如：合同版本对比_2024"
                class="form-input"
              >
            </div>
            <div class="form-group">
                <label>智能对比选项：</label>
              <div class="checkbox-group">
                <label class="checkbox-label">
                  <input 
                    v-model="compareConfig.ignoreWhitespace" 
                    type="checkbox"
                  >
                    忽略空格差异
                </label>
                <label class="checkbox-label">
                  <input 
                    v-model="compareConfig.detailedReport" 
                    type="checkbox"
                  >
                    生成详细结构化报告
                  </label>
                  <label class="checkbox-label">
                    <input 
                      v-model="compareConfig.extractTables" 
                      type="checkbox"
                    >
                    智能表格对比
                  </label>
                  <label class="checkbox-label">
                    <input 
                      v-model="compareConfig.useOcr" 
                      type="checkbox"
                    >
                    OCR扫描版PDF支持
                  </label>
                  <label class="checkbox-label" v-if="isPdfComparison">
                    <input 
                      v-model="compareConfig.visualComparison" 
                      type="checkbox"
                    >
                    PDF视觉对比
                </label>
              </div>
            </div>
            <div class="form-group">
              <label>选择的文档：</label>
              <div class="selected-docs">
                <div v-for="docId in selectedDocs" :key="docId" class="selected-doc">
                  {{ getDocumentById(docId)?.original_filename }}
                </div>
              </div>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn btn-primary" :disabled="submitting">
                {{ submitting ? '创建中...' : '开始对比' }}
              </button>
              <button type="button" class="btn btn-secondary" @click="closeCompareDialog">
                取消
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <MessageBox ref="messageBox" />
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { api } from '@/api'
import { useAuthStore } from '@/stores/auth'
import MessageBox from '@/components/common/MessageBox.vue'
import { ElMessage } from 'element-plus'
import mammoth from 'mammoth'

export default {
  name: 'DocumentManager',
  components: {
    MessageBox
  },
  setup() {
    const authStore = useAuthStore()
    const messageBox = ref(null)

    // 响应式数据
    const activeTab = ref('upload')
    const loading = ref(false)
    const tasksLoading = ref(false)
    const submitting = ref(false)
    const uploading = ref(false)
    const documents = ref([])
    const tasks = ref([])
    const selectedDocs = ref([])
    const selectedTasks = ref([])
    const documentFilter = ref('documents')
    const showCompareDialog = ref(false)
    
    // 新的文件上传数据
    const standardFile = ref(null)
    const modifiedFile = ref(null)
    const dragStates = reactive({
      standard: false,
      modified: false
    })
    
    const pagination = reactive({
      current_page: 1,
      pages: 1,
      total: 0,
      per_page: 20
    })

    const compareConfig = reactive({
      taskName: '',
      ignoreWhitespace: true,
      detailedReport: true,
      extractTables: true,
      useOcr: true,
      visualComparison: false
    })

    // 选项卡配置
    const tabs = [
      { key: 'upload', label: '文档上传' },
      { key: 'documents', label: '文档列表' },
      { key: 'tasks', label: '对比任务' }
    ]

    // 计算属性
    const getDocumentById = computed(() => {
      return (id) => documents.value.find(doc => doc.id === id)
    })

    // 新的计算属性
    const canStartDirectComparison = computed(() => {
      return standardFile.value && modifiedFile.value
    })
    
    const isPdfFiles = computed(() => {
      const standardExt = standardFile.value ? getFileExtension(standardFile.value.name) : ''
      const modifiedExt = modifiedFile.value ? getFileExtension(modifiedFile.value.name) : ''
      return standardExt === 'pdf' && modifiedExt === 'pdf'
    })

    // 检测是否为PDF对比（原有逻辑保留）
    const isPdfComparison = computed(() => {
      const selectedDocuments = selectedDocs.value.map(id => getDocumentById.value(id))
      return selectedDocuments.length === 2 && 
             selectedDocuments.every(doc => doc && doc.file_type === 'pdf')
    })
    
    // 全选状态
    const isAllSelected = computed(() => {
      return documents.value.length > 0 && selectedDocs.value.length === documents.value.length
    })
    
    const isAllTasksSelected = computed(() => {
      return tasks.value.length > 0 && selectedTasks.value.length === tasks.value.length
    })

    // 方法
    const showMessage = (message, type = 'info') => {
      ElMessage({
        message,
        type,
        duration: 3000
      })
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const formatDate = (dateString) => {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }

    const getDocIcon = (fileType) => {
      const icons = {
        'docx': '📝',
        'doc': '📝',
        'pdf': '📄',
        'wps': '📋',
        'html': '🌐'
      }
      return icons[fileType] || '📄'
    }

    const getDocTypeLabel = (type) => {
      const labels = {
        'standard': '标准版原文',
        'modified': '修改后原文',
        'report': '差异报告'
      }
      return labels[type] || type
    }

    const getTaskStatusLabel = (status) => {
      const labels = {
        'pending': '等待中',
        'processing': '处理中',
        'completed': '已完成',
        'failed': '失败'
      }
      return labels[status] || status
    }
    
    // 新增工具方法
    const getFileExtension = (filename) => {
      return filename.split('.').pop().toLowerCase()
    }

    // 添加新的数据属性
    const maxFileSize = 50 * 1024 * 1024 // 50MB
    const allowedTypes = {
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'pdf': 'application/pdf',
      'wps': 'application/vnd.ms-works',
      'doc': 'application/msword'
    }
    
    // 方法
    const validateFile = (file) => {
      // 检查文件大小
      if (file.size > maxFileSize) {
        return `文件 ${file.name} 超过大小限制(50MB)`
      }

      // 检查文件类型
      const extension = file.name.split('.').pop().toLowerCase()
      if (!allowedTypes[extension]) {
        return `不支持的文件类型: ${extension}`
      }

      // 检查MIME类型
      if (!Object.values(allowedTypes).includes(file.type)) {
        return `文件 ${file.name} 的格式不正确`
      }

      return null
    }

    // 新的文件处理方法
    const handleFileSelect = (event, docType) => {
      const file = event.target.files[0]
      if (!file) return
      const error = validateFile(file)
      if (error) {
        showMessage(error, 'error')
        return
      }
      if (docType === 'standard') {
        standardFile.value = file
      } else {
        modifiedFile.value = file
      }
      event.target.value = '' // 清空input以允许重复选择相同文件
      // 移除：上传后自动刷新文档列表（改为在上传完成后刷新）
    }

    const handleDrop = (event, docType) => {
      event.preventDefault()
      dragStates[docType] = false
      
      const file = event.dataTransfer.files[0]
      if (!file) return
      
      const error = validateFile(file)
      if (error) {
        showMessage(error, 'error')
        return
      }
      
      if (docType === 'standard') {
        standardFile.value = file
      } else {
        modifiedFile.value = file
      }
      // 移除：上传后自动刷新文档列表（改为在上传完成后刷新）
    }
    
    const handleDragOver = (event) => {
      event.preventDefault()
    }
    
    const handleDragEnter = (event, docType) => {
      event.preventDefault()
      dragStates[docType] = true
    }

    const handleDragLeave = (event, docType) => {
      event.preventDefault()
      dragStates[docType] = false
    }
    
    const removeFile = (docType) => {
      if (docType === 'standard') {
        standardFile.value = null
      } else {
        modifiedFile.value = null
      }
    }

    const clearAllFiles = () => {
      standardFile.value = null
      modifiedFile.value = null
    }

    // 文档管理
    const loadDocuments = async (page = 1) => {
      loading.value = true
      try {
        // 根据过滤条件设置请求参数
        let requestType = documentFilter.value
        if (requestType === 'documents') {
          requestType = 'all' // 后端仍使用 'all'，但在前端过滤掉报告
        }
        
        const response = await api.get('/documents', {
          params: {
            type: requestType,
            page,
            per_page: pagination.per_page
          }
        })

        if (response.data.success) {
          let docs = response.data.data.documents
          
          // 如果是"我的文档"模式，过滤掉报告类型
          if (documentFilter.value === 'documents') {
            docs = docs.filter(doc => doc.document_type !== 'report')
          }
          
          documents.value = docs
          Object.assign(pagination, {
            current_page: response.data.data.current_page,
            pages: response.data.data.pages,
            total: response.data.data.total,
            per_page: response.data.data.per_page
          })
        }
      } catch (error) {
        showMessage('加载文档列表失败', 'error')
      } finally {
        loading.value = false
      }
    }

    const toggleDocSelection = (docId) => {
      const index = selectedDocs.value.indexOf(docId)
      if (index > -1) {
        selectedDocs.value.splice(index, 1)
      } else {
        selectedDocs.value.push(docId)
      }
    }

    const clearSelection = () => {
      selectedDocs.value = []
    }

    const downloadDocument = async (doc) => {
      try {
        const response = await api.get(`/documents/${doc.id}/preview`, {
          responseType: 'blob'
        })
        
        const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const url = window.URL.createObjectURL(blob)
        
      const link = document.createElement('a')
      link.href = url
      link.download = doc.original_filename
      link.click()
        
        // 延迟清理blob URL
        setTimeout(() => {
          window.URL.revokeObjectURL(url)
        }, 1000)
      } catch (error) {
        console.error('下载文档失败:', error)
        showMessage('下载文档失败', 'error')
      }
    }

    const deleteDocument = async (doc) => {
      if (confirm(`确定要删除文档 "${doc.original_filename}" 吗？`)) {
        try {
          const response = await api.delete(`/documents/${doc.id}`)
          if (response.data.success) {
            showMessage('文档删除成功', 'success')
            loadDocuments()
          }
        } catch (error) {
          showMessage('删除失败', 'error')
        }
      }
    }

    const batchDelete = async () => {
      if (confirm(`确定要删除选中的 ${selectedDocs.value.length} 个文档吗？`)) {
        try {
          await Promise.all(
            selectedDocs.value.map(docId => api.delete(`/documents/${docId}`))
          )
          showMessage('批量删除成功', 'success')
          clearSelection()
          loadDocuments()
        } catch (error) {
          showMessage('批量删除失败', 'error')
        }
      }
    }
    
    // 新增方法
    const toggleSelectAll = () => {
      if (isAllSelected.value) {
        selectedDocs.value = []
      } else {
        selectedDocs.value = documents.value.map(doc => doc.id)
      }
    }
    
    const previewDocument = async (doc) => {
      try {
        // 使用axios获取文件blob
        const response = await api.get(`/documents/${doc.id}/preview`, {
          responseType: 'blob'
        })
        
        // 创建blob URL
        const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const fileType = doc.file_type.toLowerCase()
        
        if (fileType === 'pdf') {
          // PDF文件使用内嵌预览
          const url = window.URL.createObjectURL(blob)
          const previewWindow = window.open('', '_blank')
          previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>预览 - ${doc.original_filename}</title>
                <style>
                  body, html {
                    margin: 0;
                    padding: 0;
                    height: 100%;
                    overflow: hidden;
                  }
                  #pdf-viewer {
                    width: 100%;
                    height: 100%;
                    border: none;
                  }
                </style>
              </head>
              <body>
                <iframe id="pdf-viewer" src="${url}#toolbar=0" type="application/pdf"></iframe>
              </body>
            </html>
          `)
          previewWindow.document.close()
          
          // 延迟清理blob URL
          setTimeout(() => {
            window.URL.revokeObjectURL(url)
          }, 1000)
        } else if (['docx', 'doc', 'wps'].includes(fileType)) {
          // Word文档使用mammoth.js预览
          const arrayBuffer = await blob.arrayBuffer()
          const result = await mammoth.convertToHtml({ arrayBuffer })
          const previewWindow = window.open('', '_blank')
          previewWindow.document.write(`
            <!DOCTYPE html>
            <html>
              <head>
                <title>预览 - ${doc.original_filename}</title>
                <style>
                  body {
                    font-family: Arial, sans-serif;
                    line-height: 1.6;
                    max-width: 800px;
                    margin: 20px auto;
                    padding: 0 20px;
                  }
                  img {
                    max-width: 100%;
                    height: auto;
                  }
                </style>
              </head>
              <body>
                ${result.value}
              </body>
            </html>
          `)
          previewWindow.document.close()
        } else {
          // 其他类型文件直接下载
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = doc.original_filename
          link.click()
          
          // 延迟清理blob URL
          setTimeout(() => {
            window.URL.revokeObjectURL(url)
          }, 1000)
        }
      } catch (error) {
        console.error('预览文档失败:', error)
        showMessage('预览文档失败', 'error')
      }
    }

    const changePage = (page) => {
      loadDocuments(page)
    }

    // 对比功能
    const startComparison = () => {
      if (selectedDocs.value.length !== 2) {
        showMessage('请选择两个文档进行对比', 'warning')
        return
      }
      showCompareDialog.value = true
    }

    const closeCompareDialog = () => {
      showCompareDialog.value = false
      Object.assign(compareConfig, {
        taskName: '',
        ignoreWhitespace: true,
        detailedReport: true,
        extractTables: true,
        useOcr: true,
        visualComparison: false
      })
    }

    const submitComparison = async () => {
      submitting.value = true
      try {
        const response = await api.post('/documents/compare', {
          standard_doc_id: selectedDocs.value[0],
          modified_doc_id: selectedDocs.value[1],
          task_name: compareConfig.taskName,
          ignore_whitespace: compareConfig.ignoreWhitespace,
          detailed_report: compareConfig.detailedReport,
          extract_tables: compareConfig.extractTables,
          use_ocr: compareConfig.useOcr,
          visual_comparison: compareConfig.visualComparison
        })

        if (response.data.success) {
          showMessage('对比任务创建成功，正在后台处理...', 'success')
          closeCompareDialog()
          clearSelection()
          activeTab.value = 'tasks'
          loadTasks()
        }
      } catch (error) {
        showMessage('创建对比任务失败', 'error')
      } finally {
        submitting.value = false
      }
    }

    // 任务管理
    const loadTasks = async () => {
      tasksLoading.value = true
      try {
        const response = await api.get('/documents/tasks')
        if (response.data.success) {
          tasks.value = response.data.data.tasks
        }
      } catch (error) {
        showMessage('加载任务列表失败', 'error')
      } finally {
        tasksLoading.value = false
      }
    }

    const refreshTask = async (taskId) => {
      try {
        const response = await api.get(`/documents/tasks/${taskId}`)
        if (response.data.success) {
          const index = tasks.value.findIndex(t => t.task_id === taskId)
          if (index > -1) {
            tasks.value[index] = response.data.data
          }
        }
      } catch (error) {
        showMessage('刷新任务状态失败', 'error')
      }
    }

    const viewReport = async (taskId) => {
      try {
        const response = await api.get(`/documents/tasks/${taskId}/report`, {
          responseType: 'blob'
        })
        
        const blob = new Blob([response.data], { type: 'text/html' })
        const url = window.URL.createObjectURL(blob)
        
        // 在新窗口中显示报告
        const reportWindow = window.open('', '_blank')
        reportWindow.location.href = url
        
        // 延迟清理blob URL
        setTimeout(() => {
          window.URL.revokeObjectURL(url)
        }, 5000)
      } catch (error) {
        console.error('查看报告失败:', error)
        showMessage('查看报告失败', 'error')
      }
    }
    
    const downloadReport = async (taskId) => {
      try {
        // 获取任务信息以获取报告名称
        const task = tasks.value.find(t => t.task_id === taskId)
        const reportName = task ? `${task.task_name || '对比报告'}_${taskId.slice(0, 8)}.html` : `对比报告_${taskId.slice(0, 8)}.html`
        
        const response = await api.get(`/documents/tasks/${taskId}/report`, {
          responseType: 'blob'
        })
        
        const blob = new Blob([response.data], { type: 'text/html' })
        const url = window.URL.createObjectURL(blob)
        
        const link = document.createElement('a')
        link.href = url
        link.download = reportName
        link.click()
        
        // 延迟清理blob URL
        setTimeout(() => {
          window.URL.revokeObjectURL(url)
        }, 1000)
        
        showMessage('报告下载成功', 'success')
      } catch (error) {
        console.error('下载报告失败:', error)
        showMessage('下载报告失败', 'error')
      }
      }

    // 新增任务管理方法
    const toggleTaskSelection = (taskId) => {
      const index = selectedTasks.value.indexOf(taskId)
      if (index > -1) {
        selectedTasks.value.splice(index, 1)
      } else {
        selectedTasks.value.push(taskId)
      }
    }
    
    const toggleSelectAllTasks = () => {
      if (isAllTasksSelected.value) {
        selectedTasks.value = []
      } else {
        selectedTasks.value = tasks.value.map(task => task.task_id)
      }
    }
    
    const deleteTask = async (taskId) => {
      if (confirm('确定要删除这个任务吗？删除任务不会删除原文档。')) {
        try {
          const response = await api.delete(`/documents/tasks/${taskId}`)
          if (response.data.success) {
            showMessage('任务删除成功', 'success')
            loadTasks()
          }
        } catch (error) {
          showMessage('删除任务失败', 'error')
        }
      }
    }
    
    const batchDeleteTasks = async () => {
      if (confirm(`确定要删除选中的 ${selectedTasks.value.length} 个任务吗？删除任务不会删除原文档。`)) {
        try {
          await Promise.all(
            selectedTasks.value.map(taskId => api.delete(`/documents/tasks/${taskId}`))
          )
          showMessage('批量删除任务成功', 'success')
          selectedTasks.value = []
          loadTasks()
        } catch (error) {
          showMessage('批量删除任务失败', 'error')
        }
      }
    }
    
    // 直接对比方法
    const startDirectComparison = async () => {
      if (!canStartDirectComparison.value) {
        showMessage('请选择两个文档进行对比', 'warning')
        return
      }

      uploading.value = true
      
      try {
        // 先上传标准版文档
        const standardFormData = new FormData()
        standardFormData.append('file', standardFile.value)
        standardFormData.append('document_type', 'standard')
        
        const standardResponse = await api.post('/documents/upload', standardFormData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        
        if (!standardResponse.data.success) {
          throw new Error('标准版文档上传失败')
        }
        
        // 再上传修改版文档
        const modifiedFormData = new FormData()
        modifiedFormData.append('file', modifiedFile.value)
        modifiedFormData.append('document_type', 'modified')
        
        const modifiedResponse = await api.post('/documents/upload', modifiedFormData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        })
        
        if (!modifiedResponse.data.success) {
          throw new Error('修改版文档上传失败')
        }
        
        // 创建对比任务
        const compareResponse = await api.post('/documents/compare', {
          standard_doc_id: standardResponse.data.data.id,
          modified_doc_id: modifiedResponse.data.data.id,
          task_name: `${standardFile.value.name} vs ${modifiedFile.value.name}`,
          ignore_whitespace: compareConfig.ignoreWhitespace,
          detailed_report: compareConfig.detailedReport,
          extract_tables: compareConfig.extractTables,
          use_ocr: compareConfig.useOcr,
          visual_comparison: compareConfig.visualComparison
        })

        if (compareResponse.data.success) {
          showMessage('对比任务创建成功，正在后台处理...', 'success')
          clearAllFiles()
          
          // 刷新文档列表，确保新上传的文档显示
          await loadDocuments()
          
          // 询问用户是否查看文档列表
          if (confirm('文档上传成功！是否查看文档列表？')) {
            activeTab.value = 'documents'
          } else {
            activeTab.value = 'tasks'
            loadTasks()
          }
        }
      } catch (error) {
        showMessage(error.message || '操作失败', 'error')
      } finally {
        uploading.value = false
      }
    }

    // 检查登录状态
    const checkAuth = () => {
      if (!authStore.isLoggedIn) {
        showMessage('请先登录后再使用文档管理功能', 'warning')
        return false
      }
      return true
    }



    // 生命周期
    onMounted(() => {
      if (!authStore.isAuthenticated || !authStore.user) {
        showMessage('请先登录后再使用文档管理功能', 'warning');
        authStore.logout();
        if (typeof window !== 'undefined' && window.location) {
          window.location.href = '/login';
        }
        return;
      }
      loadDocuments();
      loadTasks();
    });

    return {
      // 响应式数据
      activeTab,
      loading,
      tasksLoading,
      submitting,
      uploading,
      documents,
      tasks,
      selectedDocs,
      selectedTasks,
      documentFilter,
      showCompareDialog,
      pagination,
      compareConfig,
      tabs,
      messageBox,
      authStore,
      standardFile,
      modifiedFile,
      dragStates,

      // 计算属性
      getDocumentById,
      canStartDirectComparison,
      isPdfFiles,
      isPdfComparison,
      isAllSelected,
      isAllTasksSelected,

      // 方法
      formatFileSize,
      formatDate,
      getDocIcon,
      getDocTypeLabel,
      getTaskStatusLabel,
      getFileExtension,
      handleFileSelect,
      handleDrop,
      handleDragOver,
      handleDragEnter,
      handleDragLeave,
      removeFile,
      clearAllFiles,
      loadDocuments,
      toggleDocSelection,
      toggleSelectAll,
      clearSelection,
      previewDocument,
      downloadDocument,
      deleteDocument,
      batchDelete,
      changePage,
      startComparison,
      closeCompareDialog,
      submitComparison,
      loadTasks,
      toggleTaskSelection,
      toggleSelectAllTasks,
      deleteTask,
      batchDeleteTasks,
      refreshTask,
      viewReport,
      downloadReport,
      checkAuth,
      startDirectComparison,
      showMessage,
      validateFile
    }
  }
}
</script>

<style scoped>
/* 统一设计风格 - 基于首页设计系统 */
:root {
  --brand-main: #4A90E2;
  --brand-gold: #F5C242;
  --brand-bg: #f8fafc;
  --brand-gray: #D6E4F0;
  --center-bg: #f4f8ff;
}

.document-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 0 48px 0;
  background: var(--brand-bg);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 页面头部 - 统一首页风格 */
.page-header {
  background: linear-gradient(90deg, #4A90E2 0%, #D6E4F0 100%);
  color: #fff;
  border-radius: 0 0 24px 24px;
  padding: 36px 32px 28px 32px;
  margin-bottom: 32px;
  box-shadow: 0 4px 24px #4A90E222;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
  margin: 0 0 0.5rem 0;
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  line-height: 1.5;
}

.auth-warning {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 193, 7, 0.3);
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem auto 0;
  max-width: 500px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.warning-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.warning-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-text p {
  margin: 0;
  color: #856404;
  font-weight: 500;
}

.login-link {
  color: #4A90E2;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.login-link:hover {
  color: #F5C242;
  text-decoration: underline;
}

/* 选项卡样式 - 统一首页风格 */
.tabs {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.tab-btn {
  padding: 7px 24px;
  font-size: 1rem;
  border-radius: 12px;
  border: 2px solid #fff;
  background: transparent;
  color: #fff;
  transition: all 0.2s;
  font-weight: 500;
}

.tab-btn.active {
  background: #fff;
  color: #4A90E2;
  border-color: #fff;
}

.tab-btn:hover {
  background: rgba(255,255,255,0.15);
  color: #fff;
}

/* 内容区域 - 统一首页风格 */
.tab-content {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px #4A90E211;
  padding: 32px;
  margin-bottom: 24px;
}

.section-header {
  text-align: center;
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #222;
  margin: 0 0 8px 0;
}

.section-subtitle {
  font-size: 1rem;
  color: #666;
  margin: 0;
}

/* 上传区域样式 */
.upload-container {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0,0,0,0.08);
  border: 1px solid rgba(255,255,255,0.2);
}

.upload-boxes {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

@media (max-width: 768px) {
  .upload-boxes {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

.upload-box {
  position: relative;
  border: 1.5px dashed #4A90E2;
  border-radius: 18px;
  padding: 24px;
  text-align: center;
  background: white;
  transition: all 0.3s ease;
  min-height: 220px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  overflow: hidden;
  box-shadow: 0 2px 12px #4A90E222;
}

.upload-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.05) 0%, rgba(245, 194, 66, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-box:hover::before,
.upload-box.drag-over::before {
  opacity: 1;
}

.upload-box:hover {
  border-color: #F5C242;
  box-shadow: 0 8px 32px #4A90E244;
  transform: translateY(-2px);
}

.upload-box.drag-over {
  border-color: #F5C242;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px #4A90E244;
}

.upload-box.has-file {
  border-color: #F5C242;
  background: linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
}

/* 文件预览状态 */
.file-preview {
  position: relative;
  z-index: 2;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.8rem;
}

.file-icon {
  font-size: 2.5rem;
  margin-bottom: 0.3rem;
}

.file-info {
  text-align: center;
  flex: 1;
}

.file-name {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.3rem 0;
  word-break: break-word;
}

.file-size {
  font-size: 0.8rem;
  color: #718096;
  margin: 0 0 0.5rem 0;
}

.file-type-badge {
  display: inline-block;
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

/* 上传占位符 */
.upload-placeholder {
  position: relative;
  z-index: 2;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  font-size: 2.5rem;
  margin-bottom: 0.8rem;
  opacity: 0.7;
}

.upload-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
}

.upload-hint {
  font-size: 0.85rem;
  color: #718096;
  margin: 0 0 0.3rem 0;
}

.upload-format {
  font-size: 0.75rem;
  color: #a0aec0;
  margin: 0 0 1rem 0;
}

.upload-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(90deg, #F5C242 0%, #FFD700 100%);
  color: #333;
  border: none;
  padding: 12px 24px;
  border-radius: 18px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px #4A90E222;
}

.upload-btn:hover {
  background: linear-gradient(90deg, #FFD700 0%, #F5C242 100%);
  color: #222;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px #4A90E244;
}

/* 对比按钮区域 */
.comparison-section {
  margin-top: 1.5rem;
  text-align: center;
}

.comparison-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(90deg, #F5C242 0%, #FFD700 100%);
  color: #333;
  border: none;
  padding: 12px 24px;
  border-radius: 18px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px #4A90E222;
}

.comparison-btn:disabled {
  background: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
  box-shadow: none;
}

.comparison-btn:hover:not(:disabled) {
  background: linear-gradient(90deg, #FFD700 0%, #F5C242 100%);
  color: #222;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px #4A90E244;
}

/* 过滤器组件 - 统一首页风格 */
.filters {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-select {
  padding: 8px 16px;
  border: 1.5px solid #4A90E2;
  border-radius: 18px;
  background: white;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px #4A90E222;
}

.filter-select:hover {
  border-color: #F5C242;
  box-shadow: 0 4px 16px #4A90E233;
}

.filter-select:focus {
  outline: none;
  border-color: #F5C242;
  box-shadow: 0 4px 16px #4A90E233;
}

/* 刷新按钮 - 统一首页风格 */
.refresh-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(90deg, #4A90E2 0%, #F5C242 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 18px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px #4A90E222;
}

.refresh-btn:hover {
  background: linear-gradient(90deg, #F5C242 0%, #4A90E2 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px #4A90E244;
}

/* 批量工具栏 - 统一首页风格 */
.batch-toolbar {
  background: white;
  border: 1.5px solid #4A90E2;
  border-radius: 18px;
  padding: 16px 24px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 12px #4A90E222;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.select-all-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.select-all-checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid #4A90E2;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.select-all-checkbox:checked {
  background: #4A90E2;
  border-color: #4A90E2;
}

.checkbox-text {
  font-size: 1rem;
  color: #333;
}

.selection-count {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  gap: 12px;
}

/* 批量操作按钮 - 统一首页风格 */
.batch-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 18px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px #4A90E222;
}

.batch-btn.primary {
  background: linear-gradient(90deg, #F5C242 0%, #FFD700 100%);
  color: #333;
}

.batch-btn.primary:hover {
  background: linear-gradient(90deg, #FFD700 0%, #F5C242 100%);
  color: #222;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px #4A90E244;
}

.batch-btn.danger {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
}

.batch-btn.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(245, 101, 101, 0.4);
}

/* 文档网格 */
.documents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.document-card {
  background: #f8fafc;
  border: 1.5px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 4px #4A90E211;
  transition: box-shadow 0.2s, border 0.2s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 56px;
  position: relative;
}

.document-card.selected {
  border-color: #F5C242;
  background: #fffbe6;
}

.doc-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doc-checkbox {
  margin-right: 8px;
}

.doc-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.doc-title-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.05rem;
  font-weight: 600;
  color: #222;
}

.doc-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
}

.doc-type-badge {
  position: absolute;
  top: 10px;
  right: 14px;
  z-index: 2;
  box-shadow: 0 1px 4px #4A90E211;
}

.doc-meta-row {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.92em;
  color: #888;
}

.doc-action-row {
  background: #f4f8ff;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin: 0 -16px -12px -16px;
  padding: 4px 16px 4px 16px;
}

.doc-actions-bottom {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.doc-icon {
  font-size: 2.2rem;
  text-align: center;
  margin-bottom: 0.8rem;
}

.doc-info {
  flex: 1;
}

.doc-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.6rem 0;
  line-height: 1.3;
  word-break: break-word;
}

.doc-meta {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.doc-type-badge {
  display: inline-block;
  padding: 0.2rem 0.6rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  width: fit-content;
}

.doc-type-badge.standard {
  background: linear-gradient(135deg, #4A90E2 0%, #F5C242 100%);
  color: white;
}

.doc-type-badge.modified {
  background: linear-gradient(135deg, #F5C242 0%, #FFD700 100%);
  color: #333;
}

.doc-type-badge.report {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
}

.doc-size, .doc-date {
  font-size: 0.75rem;
  color: #718096;
}

/* 文档操作按钮 - 优化版本 */
.doc-actions {
  display: none !important;
}

/* 任务网格布局 */
.tasks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 32px;
  background: var(--center-bg);
  border-radius: 32px;
  box-shadow: 0 8px 40px #4A90E222;
  padding: 32px;
  margin-bottom: 32px;
}

/* 任务卡片 - 统一首页风格 */
.task-card {
  background: white;
  border: 1.5px solid #4A90E2;
  border-radius: 18px;
  padding: 24px;
  box-shadow: 0 2px 12px #4A90E222;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 16px;
}

.task-card .doc-type-badge {
  position: absolute;
  right: 18px;
  top: 12px;
  z-index: 2;
  box-shadow: 0 1px 4px #4A90E211;
  display: inline-block;
  padding: 0.2rem 0.6rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  width: fit-content;
}

.task-card .doc-type-badge.modified {
  top: 38px !important;
}

.task-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.task-card.selected {
  border-color: #F5C242;
  background: linear-gradient(135deg, #f7faff 0%, #eef4ff 100%);
}

/* 卡片选择框 */
.card-checkbox {
  flex-shrink: 0;
  margin-top: 4px;
}

.task-checkbox-input {
  width: 18px;
  height: 18px;
  border: 2px solid #4A90E2;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.task-checkbox-input:checked {
  background: #4A90E2;
  border-color: #4A90E2;
}

/* 任务信息 */
.task-info {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  line-height: 1.3;
  word-break: break-word;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
}

.task-status-badge {
  display: inline-block;
  padding: 0.2rem 0.6rem;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
}

.task-status-badge.pending {
  background: linear-gradient(135deg, #F5C242 0%, #FFD700 100%);
  color: #333;
}

.task-status-badge.processing {
  background: linear-gradient(135deg, #4A90E2 0%, #F5C242 100%);
  color: white;
}

.task-status-badge.completed {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
}

.task-status-badge.failed {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
}

.task-date {
  font-size: 0.85rem;
  color: #718096;
}

/* 文档对比信息 */
.task-documents {
  margin-bottom: 0.8rem;
}

.doc-pair {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 0.8rem;
  align-items: center;
}

.doc-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: white;
  border: 1px solid #4A90E2;
  border-radius: 12px;
  font-size: 0.8rem;
  box-shadow: 0 2px 8px #4A90E222;
}

.doc-details {
  flex: 1;
  min-width: 0;
}

.doc-label {
  display: block;
  font-size: 0.65rem;
  color: #4A90E2;
  font-weight: 600;
  margin-bottom: 0.1rem;
}

.doc-filename {
  display: block;
  font-weight: 500;
  color: #2d3748;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vs-divider {
  font-size: 0.8rem;
  color: #4A90E2;
  font-weight: 600;
  background: linear-gradient(135deg, #4A90E2 0%, #F5C242 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  text-align: center;
}

/* 任务统计 */
.task-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.8rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  font-size: 0.8rem;
  color: #4a5568;
}

.stat-value {
  font-size: 0.8rem;
  font-weight: 600;
  color: #2d3748;
}

/* 任务操作按钮 - 统一首页风格 */
.task-actions {
  display: none !important;
}

/* 进度条 */
.task-progress {
  margin-bottom: 0.8rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4A90E2 0%, #F5C242 100%);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: #718096;
  margin: 0;
}

/* 错误信息 */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 8px;
  margin-bottom: 0.8rem;
}

.error-text {
  font-size: 0.8rem;
  color: #c53030;
}

/* 批量操作按钮 */
.batch-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.batch-btn.danger {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
}

.batch-btn.danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(245, 101, 101, 0.4);
}

.batch-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

.batch-btn:hover::before {
  transform: translateX(0);
}

/* 对比操作区域 - 统一首页风格 */
.comparison-controls {
  background: white;
  border: 1.5px solid #4A90E2;
  border-radius: 18px;
  padding: 24px;
  margin-top: 24px;
  box-shadow: 0 2px 12px #4A90E222;
}

.control-header {
  margin-bottom: 24px;
}

.control-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.control-subtitle {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.comparison-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.option-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.option-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #333;
  transition: all 0.3s ease;
}

.option-label:hover {
  color: #4A90E2;
}

.option-checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid #4A90E2;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.option-checkbox:checked {
  background: #4A90E2;
  border-color: #4A90E2;
}

.option-text {
  font-size: 1rem;
}

.comparison-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.compare-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 18px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 12px #4A90E222;
}

.compare-btn.primary {
  background: linear-gradient(90deg, #F5C242 0%, #FFD700 100%);
  color: #333;
}

.compare-btn.primary:hover:not(:disabled) {
  background: linear-gradient(90deg, #FFD700 0%, #F5C242 100%);
  color: #222;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px #4A90E244;
}

.compare-btn.primary:disabled {
  background: #e2e8f0;
  color: #a0aec0;
  cursor: not-allowed;
  box-shadow: none;
}

.compare-btn.secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.compare-btn.secondary:hover {
  background: #cbd5e0;
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.spinner {
  width: 48px;
  height: 48px;
  border: 3px solid transparent;
  border-top: 3px solid #4A90E2;
  border-right: 3px solid #F5C242;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
  margin-bottom: 24px;
  position: relative;
}

.spinner::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid rgba(74, 144, 226, 0.1);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.1);
    opacity: 0.7;
  }
}

/* 响应式设计 - 统一首页风格 */
@media (max-width: 768px) {
  .document-manager {
    padding: 0 16px 48px 16px;
  }
  
  .page-header {
    padding: 24px 16px;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .tabs {
    flex-direction: column;
    gap: 12px;
  }
  
  .tab-btn {
    width: 100%;
    justify-content: center;
  }
  
  .tab-content {
    padding: 24px 16px;
  }
  
  .upload-boxes {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .upload-box {
    min-height: 200px;
    padding: 20px;
  }
  
  .documents-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .document-card {
    padding: 20px;
  }
  
  .tasks-grid {
    grid-template-columns: 1fr;
    gap: 24px;
    padding: 24px 16px;
  }
  
  .task-card {
    padding: 20px;
    flex-direction: column;
    gap: 12px;
  }
  
  .doc-actions {
    position: static;
    transform: none;
    opacity: 1;
    flex-direction: row;
    justify-content: center;
    margin-top: 16px;
  }
  
  .task-actions {
    position: static;
    opacity: 1;
    flex-direction: row;
    justify-content: center;
    margin-top: 16px;
    transform: none;
  }
  
  .section-header.compact {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .section-title-right {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .comparison-options {
    grid-template-columns: 1fr;
  }
  
  .comparison-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .compare-btn {
    width: 100%;
    justify-content: center;
  }
  
  .filters {
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-select,
  .refresh-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.75rem;
  }
  
  .page-description {
    font-size: 1rem;
  }
  
  .task-card {
    padding: 16px;
  }
  
  .task-title {
    font-size: 1.1rem;
  }
  
  .doc-pair {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .vs-divider {
    text-align: center;
    margin: 0.5rem 0;
  }
  
  .task-actions {
    justify-content: space-around;
  }
  
  .task-actions .action-btn {
    width: 36px;
    height: 36px;
  }
}

.task-similarity {
  color: #2d3748 !important;
  font-weight: 500;
}

.task-diffs {
  color: #2d3748 !important;
  font-weight: 500;
}

.task-status-badge {
  color: #2d3748 !important;
}

.task-status-badge.pending,
.task-status-badge.processing,
.task-status-badge.completed,
.task-status-badge.failed {
  color: #2d3748 !important;
}

.action-btn,
.action-btn.preview,
.action-btn.download,
.action-btn.delete,
.action-btn.refresh,
.task-actions .action-btn,
.doc-actions .action-btn {
  color: #2d3748 !important;
}

.btn,
.btn-primary,
.btn-danger,
.btn-secondary {
  color: #2d3748 !important;
}

.doc-label,
.doc-filename,
.task-title,
.task-meta,
.stat-item,
.stat-value,
.batch-actions p,
.vs-divider {
  color: #2d3748 !important;
}

.section-header.compact {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  margin: 0 0 12px 0;
  padding: 0;
  text-align: left;
}

.section-title-left {
  font-size: 1.3rem;
  font-weight: 700;
  color: #2d3748;
}

.section-title-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 加载动画 - 统一首页风格 */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
  background: var(--center-bg);
  border-radius: 32px;
  box-shadow: 0 8px 40px #4A90E222;
  margin-bottom: 32px;
  color: #333;
}

/* 空状态 - 统一首页风格 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 4rem 2rem;
  background: var(--center-bg);
  border-radius: 32px;
  box-shadow: 0 8px 40px #4A90E222;
  margin-bottom: 32px;
  color: #333;
}

.empty-state .empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
  opacity: 0.7;
}

.empty-state p {
  font-size: 1.1rem;
  margin-bottom: 24px;
  font-weight: 500;
}

/* 任务操作底部按钮行 */
.task-action-row {
  background: #f4f8ff;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 18px 18px;
  min-height: 36px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin: 0 -24px -24px -24px;
  padding: 4px 24px 4px 24px;
}

.task-actions-bottom {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

/* 移除旧的 .task-actions 样式 */
.task-actions {
  display: none !important;
}

/* 新增底部爱你行样式 */
.task-love-row {
  font-size: 0.8rem;
  color: #718096;
  margin-top: 0.5rem;
  line-height: 1.2;
  min-height: 1.2em;
  background: #f5f6fa;
  border-radius: 0 0 12px 12px;
  margin-left: -24px;
  margin-right: -24px;
  margin-bottom: -24px;
  padding: 2px 16px 2px 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.task-love-row .task-actions-bottom {
  display: flex;
  gap: 8px;
  margin-left: auto;
}
</style> 