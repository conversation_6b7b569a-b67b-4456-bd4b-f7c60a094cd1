<template>
  <ErrorBoundary>
    <div class="profile-container">
    <!-- 左侧导航菜单 -->
    <div class="sidebar">
      <div class="sidebar-header">
        <div class="user-avatar">
          <el-avatar 
            :size="80" 
            :src="user?.avatar_url ? `http://localhost:5000${user.avatar_url}` : null"
            :icon="!user?.avatar_url ? 'User' : null"
            class="avatar"
          ></el-avatar>
        </div>
        <h2 class="username">{{ user?.username || '用户' }}</h2>
        <p class="user-id">UID: {{ user?.id || '---' }}</p>
      </div>
      
      <nav class="sidebar-nav">
        <div 
          v-for="item in menuItems" 
          :key="item.key"
          class="nav-item"
          :class="{ active: activeTab === item.key }"
          @click="activeTab = item.key"
        >
          <el-icon class="nav-icon">
            <component :is="item.icon" />
          </el-icon>
          <span class="nav-label">{{ item.label }}</span>
          <el-icon class="nav-arrow" v-if="item.children">
            <ArrowRight />
          </el-icon>
        </div>
      </nav>
    </div>

    <!-- 右侧内容区域 -->
    <div class="main-content">
      <!-- 账户信息 -->
      <div v-if="activeTab === 'account'" class="content-section">
        <div class="section-header">
          <h1 class="section-title">账户信息</h1>
          <p class="section-subtitle">管理您的个人资料和账户设置</p>
          <!-- 临时测试按钮 -->
          <div style="margin: 20px 0;">
            <el-button @click="testUserAPI" type="primary">测试用户API</el-button>
            <el-button @click="testImagesAPI" type="success">测试图片API</el-button>
            <el-button @click="manualLogin" type="warning">手动登录</el-button>
          </div>
        </div>
        
        <div class="info-cards">
          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><User /></el-icon>
              <h3>基本信息</h3>
            </div>
            <div class="card-content">
              <div class="info-row">
                <label>用户名</label>
                <span>{{ user?.username || '---' }}</span>
              </div>
              <div class="info-row">
                <label>邮箱</label>
                <span>{{ user?.email || '---' }}</span>
              </div>
              <div class="info-row">
                <label>角色</label>
                <el-tag size="small" :type="user?.username === 'admin' ? 'danger' : 'primary'">
                  {{ user?.username === 'admin' ? '管理员' : '普通用户' }}
                </el-tag>
              </div>
            </div>
          </div>

          <div class="info-card">
            <div class="card-header">
              <el-icon class="card-icon"><Clock /></el-icon>
              <h3>账户状态</h3>
            </div>
            <div class="card-content">
              <div class="info-row">
                <label>注册时间</label>
                <span>{{ formatDate(user?.created_at) }}</span>
              </div>
              <div class="info-row">
                <label>最后登录</label>
                <span>{{ formatDate(user?.last_login_time) }}</span>
              </div>
              <div class="info-row">
                <label>账户状态</label>
                <el-tag size="small" type="success">正常</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 照片管理 -->
      <div v-if="activeTab === 'photos'" class="content-section">
        <div class="section-header">
          <h1 class="section-title">照片管理</h1>
          <p class="section-subtitle">查看和管理您上传的照片和处理结果</p>
        </div>

        <div class="photo-tabs">
          <div class="tab-nav">
            <div 
              class="tab-item"
              :class="{ active: activePhotoTab === 'uploaded' }"
              @click="switchPhotoTab('uploaded')"
            >
              <el-icon><Upload /></el-icon>
              <span>我上传的</span>
              <span class="count">{{ uploadedPhotos.length }}</span>
            </div>
            <div 
              class="tab-item"
              :class="{ active: activePhotoTab === 'processed' }"
              @click="switchPhotoTab('processed')"
            >
              <el-icon><PictureRounded /></el-icon>
              <span>制作完成</span>
              <span class="count">{{ processedPhotos.length }}</span>
            </div>
          </div>

          <div class="tab-content">
            <div v-if="loading" class="loading-state">
              <el-loading-spinner size="large" />
              <p>加载中...</p>
            </div>
            
            <div v-else-if="currentPhotos.length === 0" class="empty-state">
              <el-icon size="64" class="empty-icon"><PictureFilled /></el-icon>
              <h3>暂无照片</h3>
              <p>{{ activePhotoTab === 'uploaded' ? '您还没有上传任何照片' : '您还没有制作完成的照片' }}</p>
              <el-button type="primary" @click="goToPhotoEditor">
                开始制作
              </el-button>
            </div>

            <div v-else class="photo-grid">
              <div 
                v-for="photo in currentPhotos" 
                :key="photo.id" 
                class="photo-card"
              >
                <div class="photo-preview">
                  <img 
                    :src="photo.thumbnail_url || photo.url" 
                    :alt="photo.original_filename"
                    @load="onImageLoad"
                    @error="onImageError"
                  />
                  <!-- 当前头像标识 -->
                  <div v-if="user?.avatar_url && user.avatar_url.includes(photo.file_id)" class="current-avatar-badge">
                    <el-icon><User /></el-icon>
                    <span>当前头像</span>
                  </div>
                  <div class="photo-overlay">
                    <div class="photo-actions">
                      <el-button 
                        circle 
                        size="small" 
                        @click="viewPhoto(photo.url)"
                        title="查看大图"
                      >
                        <el-icon><ZoomIn /></el-icon>
                      </el-button>
                      <el-button 
                        circle 
                        size="small" 
                        type="primary"
                        @click="setAsAvatar(photo)"
                        title="设为头像"
                      >
                        <el-icon><User /></el-icon>
                      </el-button>
                      <el-button 
                        circle 
                        size="small" 
                        type="danger"
                        @click="deletePhoto(photo.file_id, activePhotoTab)"
                        title="删除照片"
                      >
                        <el-icon><Delete /></el-icon>
                      </el-button>
                    </div>
                  </div>
                </div>
                <div class="photo-info">
                  <div class="photo-title-container">
                    <h4 
                      class="photo-title" 
                      v-if="!photo.isEditing"
                      @click="startEditingName(photo)"
                      title="点击编辑文件名"
                    >
                      {{ photo.original_filename }}
                    </h4>
                    <div v-else class="photo-title-edit">
                      <el-input
                        v-model="photo.editingName"
                        size="small"
                        @blur="savePhotoName(photo)"
                        @keyup.enter="savePhotoName(photo)"
                        @keyup.esc="cancelEditingName(photo)"
                        ref="nameInput"
                        class="name-input"
                      />
                      <div class="edit-actions">
                        <el-button size="small" type="primary" @click="savePhotoName(photo)">保存</el-button>
                        <el-button size="small" @click="cancelEditingName(photo)">取消</el-button>
                      </div>
                    </div>
                  </div>
                  <div class="photo-meta">
                    <span class="photo-size">{{ formatFileSize(photo.file_size) }}</span>
                    <span class="photo-date">{{ formatDate(photo.created_at) }}</span>
                  </div>
                  <div v-if="photo.width && photo.height" class="photo-dimensions">
                    {{ photo.width }} × {{ photo.height }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 文档管理 -->
      <div v-if="activeTab === 'documents'" class="content-section">
        <div class="section-header">
          <h1 class="section-title">文档管理</h1>
          <p class="section-subtitle">管理您的文档对比和下载历史</p>
        </div>
        
        <div class="document-tabs">
          <div class="tab-nav">
            <div 
              class="tab-item"
              :class="{ active: activeDocumentTab === 'documents' }"
              @click="switchDocumentTab('documents')"
            >
              <el-icon><Document /></el-icon>
              <span>我的文档</span>
              <span class="count">{{ documents.length }}</span>
            </div>
            <div 
              class="tab-item"
              :class="{ active: activeDocumentTab === 'tasks' }"
              @click="switchDocumentTab('tasks')"
            >
              <el-icon><Clock /></el-icon>
              <span>对比任务</span>
              <span class="count">{{ tasks.length }}</span>
            </div>
          </div>

          <div class="tab-content">
            <!-- 文档列表 -->
            <div v-if="activeDocumentTab === 'documents'">
              <div class="document-header">
                <div class="filters">
                  <el-select v-model="documentFilter" @change="loadDocuments" placeholder="选择文档类型" size="small">
                    <el-option label="所有文档" value="all"></el-option>
                    <el-option label="标准版文档" value="standard"></el-option>
                    <el-option label="修改版文档" value="modified"></el-option>
                  </el-select>
                  <el-button @click="loadDocuments" size="small" :loading="documentsLoading">
                    <el-icon><Refresh /></el-icon>
                    刷新
                  </el-button>
                </div>
                <el-button type="primary" @click="goToDocumentManager" size="small">
                  <el-icon><Plus /></el-icon>
                  上传文档
                </el-button>
              </div>

              <div v-if="documentsLoading" class="loading-state">
                <el-loading-spinner size="large" />
                <p>加载中...</p>
              </div>
              
              <div v-else-if="documents.length === 0" class="empty-state">
                <el-icon size="64" class="empty-icon"><Document /></el-icon>
                <h3>暂无文档</h3>
                <p>您还没有上传任何文档，开始上传文档进行智能对比分析</p>
                <el-button type="primary" @click="goToDocumentManager">
                  上传文档
                </el-button>
              </div>

              <div v-else class="document-grid">
                <div 
                  v-for="doc in documents" 
                  :key="doc.id" 
                  class="document-card"
                >
                  <div class="document-preview">
                    <el-icon class="document-icon"><Document /></el-icon>
                  </div>
                  <div class="document-info">
                    <h4 class="document-title">{{ doc.original_filename }}</h4>
                    <div class="document-meta">
                      <span class="document-size">{{ formatFileSize(doc.file_size) }}</span>
                      <span class="document-date">{{ formatDate(doc.upload_time) }}</span>
                    </div>
                    <div class="document-type">
                      <el-tag size="small" :type="getDocTypeColor(doc.document_type)">
                        {{ getDocTypeLabel(doc.document_type) }}
                      </el-tag>
                    </div>
                    <div class="document-actions">
                      <el-button 
                        size="small" 
                        @click="previewDocument(doc)"
                        title="预览文档"
                      >
                        <el-icon><ViewIcon /></el-icon>
                        预览
                      </el-button>
                      <el-button 
                        size="small" 
                        @click="downloadDocument(doc)"
                        title="下载文档"
                      >
                        <el-icon><Download /></el-icon>
                        下载
                      </el-button>
                      <el-button 
                        size="small" 
                        type="danger"
                        @click="deleteDocument(doc)"
                        title="删除文档"
                      >
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 对比任务列表 -->
            <div v-if="activeDocumentTab === 'tasks'">
              <div class="task-header">
                <el-button @click="loadTasks" size="small" :loading="tasksLoading">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button type="primary" @click="goToDocumentManager" size="small">
                  <el-icon><Plus /></el-icon>
                  新建对比
                </el-button>
              </div>

              <div v-if="tasksLoading" class="loading-state">
                <el-loading-spinner size="large" />
                <p>加载中...</p>
              </div>
              
              <div v-else-if="tasks.length === 0" class="empty-state">
                <el-icon size="64" class="empty-icon"><Clock /></el-icon>
                <h3>暂无对比任务</h3>
                <p>您还没有创建任何文档对比任务</p>
                <el-button type="primary" @click="goToDocumentManager">
                  创建对比任务
                </el-button>
              </div>

              <div v-else class="task-grid">
                <div 
                  v-for="task in tasks" 
                  :key="task.task_id" 
                  class="task-card"
                >
                  <div class="task-preview">
                    <el-icon class="task-status">
                      <Clock v-if="task.status === 'pending'" />
                      <el-icon v-else-if="task.status === 'processing'"><Loading /></el-icon>
                      <el-icon v-else-if="task.status === 'completed'"><CircleCheck /></el-icon>
                      <el-icon v-else-if="task.status === 'failed'"><CircleClose /></el-icon>
                      <Clock v-else />
                    </el-icon>
                  </div>
                  <div class="task-info">
                    <h4 class="task-title">{{ task.task_name || `对比任务 ${task.task_id.slice(0, 8)}` }}</h4>
                    <div class="task-meta">
                      <span class="task-date">{{ formatDate(task.created_time) }}</span>
                      <span v-if="task.similarity" class="task-similarity">
                        相似度: {{ task.similarity.toFixed(1) }}%
                      </span>
                      <span v-if="task.diff_count !== undefined" class="task-diffs">
                        差异: {{ task.diff_count }}处
                      </span>
                    </div>
                    <div class="task-status-tag">
                      <el-tag :type="getTaskStatusType(task.status)" size="small">
                        {{ getTaskStatusLabel(task.status) }}
                      </el-tag>
                    </div>
                    <div class="task-actions">
                      <el-button 
                        v-if="task.status === 'completed' && task.report_path"
                        size="small" 
                        @click="viewReport(task.task_id)"
                        title="查看报告"
                      >
                        <el-icon><ViewIcon /></el-icon>
                        报告
                      </el-button>
                      <el-button 
                        v-if="task.status === 'completed' && task.report_path"
                        size="small" 
                        @click="downloadReport(task.task_id)"
                        title="下载报告"
                      >
                        <el-icon><Download /></el-icon>
                        下载
                      </el-button>
                      <el-button 
                        size="small" 
                        type="danger"
                        @click="deleteTask(task.task_id)"
                        title="删除任务"
                      >
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图片预览对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      width="80%" 
      :show-close="false"
      class="preview-dialog"
    >
      <div class="preview-container">
        <img 
          :src="dialogImageUrl" 
          alt="图片预览" 
          class="preview-image"
        />
        <div class="preview-actions">
          <el-button @click="dialogVisible = false" size="large">
            <el-icon><Close /></el-icon>
            关闭
          </el-button>
        </div>
      </div>
    </el-dialog>
    </div>
  </ErrorBoundary>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  User, Clock, Upload, PictureRounded, PictureFilled, Document, 
  ZoomIn, Delete, Close, ArrowRight, Refresh, Plus, View as ViewIcon, Download
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/api'
import moment from 'moment'
import ErrorBoundary from '@/components/common/ErrorBoundary.vue'

export default {
  name: 'Profile',
  components: {
    User, Clock, Upload, PictureRounded, PictureFilled, Document,
    ZoomIn, Delete, Close, ArrowRight, Refresh, Plus, ViewIcon, Download,
    ErrorBoundary
  },
  setup() {
    const authStore = useAuthStore()
    const router = useRouter()
    
    // 响应式数据
    const activeTab = ref('account')
    const activePhotoTab = ref('uploaded')
    const activeDocumentTab = ref('documents') // 新增：文档管理tab
    const uploadedPhotos = ref([])
    const processedPhotos = ref([])
    const loading = ref(false)
    const dialogVisible = ref(false)
    const dialogImageUrl = ref('')
    
    // 文档管理相关数据
    const documents = ref([])
    const documentsLoading = ref(false)
    const documentFilter = ref('all')

    const tasks = ref([])
    const tasksLoading = ref(false)

    // 计算属性
    const user = computed(() => authStore.user)
    
    const currentPhotos = computed(() => {
      if (activePhotoTab.value === 'uploaded') {
        return uploadedPhotos.value
      }
      return processedPhotos.value
    })
    
    // 菜单项
    const menuItems = [
      { key: 'account', label: '账户信息', icon: 'User' },
      { key: 'photos', label: '照片管理', icon: 'PictureRounded' },
      { key: 'documents', label: '文档管理', icon: 'Document' }
    ]
    
    // 方法
    const formatDate = (date) => {
      if (!date) return '---'
      return moment(date).format('YYYY-MM-DD HH:mm')
    }
    
    const formatFileSize = (bytes) => {
      if (!bytes) return '---'
      const sizes = ['B', 'KB', 'MB', 'GB']
      let i = 0
      while (bytes >= 1024 && i < sizes.length - 1) {
        bytes /= 1024
        i++
      }
      return `${bytes.toFixed(1)} ${sizes[i]}`
    }
    
    const fetchPhotos = async (type) => {
      loading.value = true
      try {
        console.log('开始获取照片列表，类型:', type)
        // 获取所有正常状态的照片（status=1）
        const params = { status: 1 }
        
        const response = await api.images.getList(params)
        console.log('获取到的照片列表:', response.data)
        
        const allPhotos = response.data.images || []
        
        // 处理照片URL
        allPhotos.forEach(photo => {
          if (!photo.url.startsWith('http')) {
            photo.url = `http://localhost:5000${photo.url}`
          }
          if (photo.thumbnail_url && !photo.thumbnail_url.startsWith('http')) {
            photo.thumbnail_url = `http://localhost:5000${photo.thumbnail_url}`
          }
        })
        
        if (type === 'uploaded') {
          // 原始上传的照片
          uploadedPhotos.value = allPhotos.filter(photo => 
            photo.upload_source === 'editor' || photo.upload_source === 'web'
          )
          console.log('上传的照片:', uploadedPhotos.value)
        } else {
          // 处理完成的照片
          processedPhotos.value = allPhotos.filter(photo => 
            photo.upload_source === 'processed' || photo.upload_source === 'editor_processed'
          )
          console.log('处理完成的照片:', processedPhotos.value)
        }
      } catch (error) {
        console.error('获取照片列表失败:', error)
        ElMessage.error('获取照片列表失败: ' + (error.response?.data?.message || error.message))
      } finally {
        loading.value = false
      }
    }
    
    const switchPhotoTab = (tab) => {
      activePhotoTab.value = tab
      fetchPhotos(tab)
    }
    
    const switchDocumentTab = (tab) => {
      activeDocumentTab.value = tab
      if (tab === 'documents') {
        loadDocuments()
      } else if (tab === 'tasks') {
        loadTasks()
      }
    }
    
    const viewPhoto = (url) => {
      dialogImageUrl.value = url
      dialogVisible.value = true
    }
    
    const deletePhoto = async (fileId, type) => {
      try {
        await ElMessageBox.confirm(
          '确定要永久删除这张照片吗？删除后无法恢复。',
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            customClass: 'apple-confirm-box'
          }
        )
        
        await api.images.delete(fileId)
        ElMessage.success('删除成功')
        
        // 重新获取列表
        fetchPhotos(type)
      } catch (error) {
        if (error === 'cancel') {
          ElMessage.info('已取消删除')
        } else {
          console.error('删除照片失败:', error)
          ElMessage.error(`删除失败: ${error.response?.data?.message || error.message}`)
        }
      }
    }
    
    const goToPhotoEditor = () => {
      router.push('/photo-editor')
    }
    
    // 文档管理相关方法
    const loadDocuments = async () => {
      documentsLoading.value = true
      try {
        const response = await api.get('/documents', {
          params: {
            type: documentFilter.value,
            page: 1,
            per_page: 50
          }
        })
        
        if (response.data.success) {
          documents.value = response.data.data.documents || []
          console.log('获取文档列表成功:', documents.value)
        }
      } catch (error) {
        console.error('获取文档列表失败:', error)
        ElMessage.error('获取文档列表失败: ' + (error.response?.data?.message || error.message))
      } finally {
        documentsLoading.value = false
      }
    }

    const goToDocumentManager = () => {
      router.push('/document-manager')
    }

    const previewDocument = async (doc) => {
      try {
        const response = await api.get(`/documents/${doc.id}/preview`, {
          responseType: 'blob'
        })
        
        const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const url = window.URL.createObjectURL(blob)
        
        // 在新窗口中打开预览
        window.open(url, '_blank')
        
        // 延迟清理blob URL
        setTimeout(() => {
          window.URL.revokeObjectURL(url)
        }, 1000)
      } catch (error) {
        console.error('预览文档失败:', error)
        ElMessage.error('预览文档失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const downloadDocument = async (doc) => {
      try {
        const response = await api.get(`/documents/${doc.id}/preview`, {
          responseType: 'blob'
        })
        
        const blob = new Blob([response.data], { type: response.headers['content-type'] })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = doc.original_filename
        link.click()
        
        // 延迟清理blob URL
        setTimeout(() => {
          window.URL.revokeObjectURL(url)
        }, 1000)
        
        ElMessage.success('文档下载成功')
      } catch (error) {
        console.error('文档下载失败:', error)
        ElMessage.error('文档下载失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const deleteDocument = async (doc) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除文档 "${doc.original_filename}" 吗？删除后无法恢复。`,
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            customClass: 'apple-confirm-box'
          }
        )
        
        const response = await api.delete(`/documents/${doc.id}`)
        if (response.data.success) {
          ElMessage.success('文档删除成功')
          loadDocuments()
        }
      } catch (error) {
        if (error === 'cancel') {
          ElMessage.info('已取消删除')
        } else {
          console.error('删除文档失败:', error)
          ElMessage.error(`删除失败: ${error.response?.data?.message || error.message}`)
        }
      }
    }

    const loadTasks = async () => {
      tasksLoading.value = true
      try {
        const response = await api.get('/documents/tasks')
        
        if (response.data.success) {
          tasks.value = response.data.data.tasks || []
          console.log('获取任务列表成功:', tasks.value)
        }
      } catch (error) {
        console.error('获取任务列表失败:', error)
        ElMessage.error('获取任务列表失败: ' + (error.response?.data?.message || error.message))
      } finally {
        tasksLoading.value = false
      }
    }

    const deleteTask = async (taskId) => {
      try {
        await ElMessageBox.confirm(
          '确定要删除此对比任务吗？删除后无法恢复。',
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            customClass: 'apple-confirm-box'
          }
        )
        
        const response = await api.delete(`/documents/tasks/${taskId}`)
        if (response.data.success) {
          ElMessage.success('任务删除成功')
          loadTasks()
        }
      } catch (error) {
        if (error === 'cancel') {
          ElMessage.info('已取消删除')
        } else {
          console.error('删除任务失败:', error)
          ElMessage.error(`删除失败: ${error.response?.data?.message || error.message}`)
        }
      }
    }

    const viewReport = async (taskId) => {
      try {
        const response = await api.get(`/documents/tasks/${taskId}/report`)
        const url = response.data.url || `http://localhost:5000/documents/tasks/${taskId}/report`
        
        // 在新窗口中打开报告
        window.open(url, '_blank')
      } catch (error) {
        console.error('查看报告失败:', error)
        ElMessage.error('查看报告失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const downloadReport = async (taskId) => {
      try {
        const response = await api.get(`/documents/tasks/${taskId}/report`, {
          responseType: 'blob'
        })
        
        const blob = new Blob([response.data], { type: 'text/html' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `对比报告_${taskId}.html`
        link.click()
        
        // 延迟清理blob URL
        setTimeout(() => {
          window.URL.revokeObjectURL(url)
        }, 1000)
        
        ElMessage.success('报告下载成功')
      } catch (error) {
        console.error('报告下载失败:', error)
        ElMessage.error('报告下载失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const getDocIcon = (fileType) => {
      switch (fileType) {
        case 'pdf':
          return '📄'
        case 'doc':
        case 'docx':
          return '📝'
        case 'xls':
        case 'xlsx':
          return '📊'
        case 'txt':
          return '📄'
        default:
          return '📄'
      }
    }

    const getDocTypeLabel = (documentType) => {
      switch (documentType) {
        case 'standard':
          return '标准版'
        case 'modified':
          return '修改版'
        default:
          return '未知'
      }
    }

    const getDocTypeColor = (documentType) => {
      switch (documentType) {
        case 'standard':
          return 'success'
        case 'modified':
          return 'warning'
        default:
          return 'info'
      }
    }

    const getTaskStatusType = (status) => {
      switch (status) {
        case 'pending':
          return 'info'
        case 'processing':
          return 'warning'
        case 'completed':
          return 'success'
        case 'failed':
          return 'danger'
        default:
          return 'info'
      }
    }

    const getTaskStatusLabel = (status) => {
      switch (status) {
        case 'pending':
          return '待处理'
        case 'processing':
          return '处理中'
        case 'completed':
          return '已完成'
        case 'failed':
          return '失败'
        default:
          return '未知'
      }
    }
    
    // 编辑照片名称相关方法
    const startEditingName = (photo) => {
      photo.isEditing = true
      photo.editingName = photo.original_filename
      // 使用nextTick确保DOM更新后再聚焦
      nextTick(() => {
        const input = document.querySelector('.name-input input')
        if (input) {
          input.focus()
          input.select()
        }
      })
    }
    
    const cancelEditingName = (photo) => {
      photo.isEditing = false
      photo.editingName = ''
    }
    
    const savePhotoName = async (photo) => {
      if (!photo.editingName || photo.editingName.trim() === '') {
        ElMessage.warning('文件名不能为空')
        return
      }
      
      if (photo.editingName === photo.original_filename) {
        cancelEditingName(photo)
        return
      }
      
      try {
        const response = await api.images.updateName(photo.file_id, {
          original_filename: photo.editingName.trim()
        })
        
        if (response.data.success) {
          photo.original_filename = photo.editingName.trim()
          photo.isEditing = false
          photo.editingName = ''
          ElMessage.success('文件名修改成功')
        } else {
          ElMessage.error('修改失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('修改文件名失败:', error)
        ElMessage.error('修改失败: ' + (error.response?.data?.message || error.message))
      }
    }
    
    // 图片加载和错误处理
    const onImageLoad = (event) => {
      try {
        console.log('图片加载成功:', event.target.src)
        event.target.classList.add('loaded')
      } catch (error) {
        console.warn('图片加载处理错误:', error)
      }
    }

    // UTF-8 编码函数
    const utf8_to_b64 = (str) => {
      try {
        return window.btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g,
          function toSolidBytes(match, p1) {
            return String.fromCharCode('0x' + p1)
          }))
      } catch (e) {
        console.error('编码SVG失败:', e)
        return window.btoa('PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMjAwIDIwMCI+PHJlY3Qgd2lkdGg9IjIwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNmNWY3ZmEiLz48dGV4dCB4PSIxMDAiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjOTA5Mzk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5JbWFnZSBMb2FkIEVycm9yPC90ZXh0Pjwvc3ZnPg==')
      }
    }

    const onImageError = (event) => {
      try {
        console.error('图片加载失败:', event.target.src)
        
        // 如果已经是完整URL但仍然失败，使用base64占位图
        const svgContent = `
          <svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <rect width="200" height="200" fill="#f5f7fa"/>
            <text x="100" y="90" font-family="Arial" font-size="14" fill="#909399" text-anchor="middle">图片加载失败</text>
            <text x="100" y="110" font-family="Arial" font-size="12" fill="#c0c4cc" text-anchor="middle">${event.target.alt || '未知图片'}</text>
          </svg>
        `.trim()
        
        const placeholderImage = `data:image/svg+xml;base64,${utf8_to_b64(svgContent)}`
        event.target.src = placeholderImage
        event.target.classList.add('error')
      } catch (error) {
        console.warn('图片错误处理失败:', error)
      }
    }
    
    const testUserAPI = async () => {
      try {
        console.log('测试用户API - 当前token:', authStore.token)
        const response = await api.auth.getCurrentUser()
        console.log('用户API响应:', response.data)
        ElMessage.success('用户API测试成功')
      } catch (error) {
        console.error('用户API测试失败:', error)
        ElMessage.error('用户API测试失败: ' + (error.response?.data?.message || error.message))
      }
    }
    
    const testImagesAPI = async () => {
      try {
        console.log('测试图片API')
        const response = await api.images.getList({ status: 1 })
        console.log('图片API响应:', response.data)
        ElMessage.success(`图片API测试成功，获取到${response.data.images?.length || 0}张图片`)
      } catch (error) {
        console.error('图片API测试失败:', error)
        ElMessage.error('图片API测试失败: ' + (error.response?.data?.message || error.message))
      }
    }
    
    const manualLogin = async () => {
      try {
        console.log('手动登录admin用户')
        const response = await api.auth.login({
          username: 'admin',
          password: 'admin123'
        })
        console.log('登录响应:', response.data)
        // 更新store
        authStore.token = response.data.tokens.access_token
        authStore.refreshToken = response.data.tokens.refresh_token
        authStore.user = response.data.user
        authStore.isAuthenticated = true
        localStorage.setItem('token', response.data.tokens.access_token)
        localStorage.setItem('refreshToken', response.data.tokens.refresh_token)
        ElMessage.success('登录成功')
      } catch (error) {
        console.error('登录失败:', error)
        ElMessage.error('登录失败: ' + (error.response?.data?.message || error.message))
      }
    }

    const setAsAvatar = async (photo) => {
      try {
        await ElMessageBox.confirm(
          `确定要将 "${photo.original_filename}" 设为头像吗？`,
          '设置头像',
          {
            confirmButtonText: '确定设置',
            cancelButtonText: '取消',
            type: 'info',
            customClass: 'apple-confirm-box'
          }
        )
        
        const response = await api.user.setAvatar({ image_id: photo.id })
        
        if (response.data.message) {
          // 更新本地用户信息
          authStore.user = response.data.user
          ElMessage.success('头像设置成功')
        }
      } catch (error) {
        if (error === 'cancel') {
          ElMessage.info('已取消设置')
        } else {
          console.error('设置头像失败:', error)
          ElMessage.error('设置头像失败: ' + (error.response?.data?.message || error.message))
        }
      }
    }
    
    // ResizeObserver 错误处理
    const handleResizeObserverError = (event) => {
      if (event.message && event.message.includes('ResizeObserver loop')) {
        event.stopImmediatePropagation();
        event.preventDefault();
        return false;
      }
    };
    
    // 生命周期
    onMounted(async () => {
      // 添加 ResizeObserver 错误处理
      window.addEventListener('error', handleResizeObserverError);
      
      // 清理函数
      const cleanup = () => {
        window.removeEventListener('error', handleResizeObserverError);
      };
      
      // 组件卸载时清理
      onUnmounted(cleanup);
      console.log('Profile页面开始加载，当前用户:', user.value)
      console.log('当前认证状态:', authStore.isAuthenticated)
      console.log('当前token:', authStore.token)
      
      if (!user.value) {
        try {
          console.log('用户信息为空，开始获取用户信息...')
          const userData = await authStore.fetchUserInfo()
          console.log('获取到用户信息:', userData)
        } catch (error) {
          console.error('获取用户信息失败:', error)
          router.push('/login')
          return
        }
      }
      
      console.log('开始获取照片列表...')
      // 默认加载上传的照片
      fetchPhotos('uploaded')
      loadDocuments() // 初始加载文档
      loadTasks() // 初始加载任务
    })
    
    return {
      activeTab,
      activePhotoTab,
      activeDocumentTab, // 新增：文档管理tab
      uploadedPhotos,
      processedPhotos,
      loading,
      dialogVisible,
      dialogImageUrl,
      user,
      currentPhotos,
      documents, // 新增：文档列表数据
      documentsLoading, // 新增：文档加载状态
      documentFilter, // 新增：文档过滤器
      tasks, // 新增：任务列表数据
      tasksLoading, // 新增：任务加载状态
      menuItems,
      formatDate,
      formatFileSize,
      fetchPhotos,
      switchPhotoTab,
      switchDocumentTab, // 新增：切换文档管理tab方法
      viewPhoto,
      deletePhoto,
      onImageLoad,
      onImageError,
      goToPhotoEditor,
      startEditingName,
      cancelEditingName,
      savePhotoName,
      testUserAPI,
      testImagesAPI,
      manualLogin,
      loadDocuments, // 新增：加载文档方法
      goToDocumentManager, // 新增：跳转到文档管理方法
      previewDocument, // 新增：预览文档方法
      downloadDocument, // 新增：下载文档方法
      deleteDocument, // 新增：删除文档方法
      loadTasks, // 新增：加载任务方法
      deleteTask, // 新增：删除任务方法
      viewReport, // 新增：查看报告方法
      downloadReport, // 新增：下载报告方法
      getDocIcon, // 新增：获取文档图标方法
      getDocTypeLabel, // 新增：获取文档类型标签方法
      getDocTypeColor, // 新增：获取文档类型颜色方法
      getTaskStatusType, // 新增：获取任务状态类型方法
      getTaskStatusLabel, // 新增：获取任务状态标签方法
      setAsAvatar // 新增：设置头像方法
    }
  }
}
</script>

<style scoped>
.profile-container {
  display: flex;
  min-height: 100vh;
  background: #f5f5f7;
}

/* 左侧边栏 */
.sidebar {
  width: 300px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  flex-shrink: 0;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 40px 30px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
  margin-bottom: 20px;
}

.avatar {
  border: 3px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

.username {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: white;
}

.user-id {
  font-size: 14px;
  opacity: 0.8;
  margin: 0;
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 16px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-left-color: rgba(255, 255, 255, 0.5);
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.15);
  border-left-color: white;
}

.nav-icon {
  font-size: 20px;
  margin-right: 15px;
}

.nav-label {
  flex: 1;
  font-size: 16px;
  font-weight: 500;
}

.nav-arrow {
  font-size: 14px;
  opacity: 0.6;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 40px;
  overflow-y: auto;
}

.content-section {
  max-width: 1200px;
  margin: 0 auto;
}

.section-header {
  margin-bottom: 40px;
}

.section-title {
  font-size: 36px;
  font-weight: 700;
  color: #1d1d1f;
  margin: 0 0 12px 0;
}

.section-subtitle {
  font-size: 18px;
  color: #86868b;
  margin: 0;
}

/* 信息卡片 */
.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
}

.info-card {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.info-card:hover {
  transform: translateY(-4px);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.card-icon {
  font-size: 24px;
  color: #007aff;
  margin-right: 12px;
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.card-content .info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f1f3;
}

.card-content .info-row:last-child {
  border-bottom: none;
}

.card-content label {
  font-weight: 500;
  color: #515154;
}

.card-content span {
  color: #1d1d1f;
}

/* 照片管理 */
.photo-tabs {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.tab-nav {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.tab-item:hover {
  background: #e9ecef;
}

.tab-item.active {
  background: white;
  border-bottom-color: #007aff;
  color: #007aff;
}

.tab-item .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.tab-item span {
  font-weight: 500;
}

.tab-item .count {
  margin-left: 8px;
  background: #007aff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.tab-item.active .count {
  background: #007aff;
}

.tab-content {
  padding: 40px;
  min-height: 400px;
}

/* 加载和空状态 */
.loading-state, .empty-state, .coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon, .feature-icon {
  color: #c7c7cc;
  margin-bottom: 20px;
}

.loading-state p, .empty-state h3, .coming-soon h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 16px 0 8px 0;
}

.empty-state p, .coming-soon p {
  font-size: 16px;
  color: #86868b;
  margin: 0 0 20px 0;
}

/* 照片网格 */
.photo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

.photo-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.photo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.photo-preview {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  background: #f5f7fa;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
    opacity: 0;
    
    &.loaded {
      opacity: 1;
    }
    
    &.error {
      opacity: 1;
      object-fit: contain;
      padding: 20px;
    }
  }

  .current-avatar-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 122, 255, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
    z-index: 2;
  }
  
  .photo-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      opacity: 1;
    }
    
    .photo-actions {
      display: flex;
      gap: 16px;
      
      .el-button {
        background: rgba(255, 255, 255, 0.95);
        border: none;
        width: 48px;
        height: 48px;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        will-change: transform;
        
        .el-icon {
          font-size: 20px;
          color: #1d1d1f;
        }
        
        &:hover {
          background: #fff;
          transform: scale(1.05);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }
        
        &.el-button--primary {
          background: rgba(64, 158, 255, 0.95);
          
          .el-icon {
            color: white;
          }
          
          &:hover {
            background: #409eff;
          }
        }
        
        &.el-button--danger {
          background: rgba(245, 108, 108, 0.95);
          
          .el-icon {
            color: white;
          }
          
          &:hover {
            background: #f56c6c;
          }
        }
      }
    }
  }
}

.photo-info {
  padding: 20px;
}

.photo-title-container {
  margin-bottom: 8px;
}

.photo-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: color 0.3s ease;
}

.photo-title:hover {
  color: #007aff;
}

.photo-title-edit {
  margin-bottom: 8px;
}

.name-input {
  margin-bottom: 8px;
}

.edit-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.photo-meta {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #86868b;
  margin-bottom: 4px;
}

.photo-dimensions {
  font-size: 12px;
  color: #c7c7cc;
}

/* 预览对话框 */
.preview-dialog {
  border-radius: 20px;
  overflow: hidden;
}

.preview-container {
  position: relative;
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  border-radius: 12px;
}

.preview-actions {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 25px;
  padding: 10px 20px;
}

/* 文档管理 */
.document-tabs {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

.document-header .filters {
  display: flex;
  gap: 15px;
}

.document-header .filters .el-select {
  width: 150px;
}

.document-grid, .task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  padding: 20px 30px;
}

.document-card, .task-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.document-card:hover, .task-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.document-preview, .task-preview {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  background: #f5f7fa;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.document-icon, .task-status {
  font-size: 48px;
  color: #007aff;
  opacity: 0.8;
}

.document-preview .document-icon {
  font-size: 64px;
  color: #007aff;
  opacity: 0.6;
}

.task-preview .task-status {
  font-size: 64px;
  color: #67c23a;
  opacity: 0.6;
}

.document-info, .task-info {
  padding: 20px;
}

.document-title, .task-title {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  transition: color 0.3s ease;
}

.document-title:hover, .task-title:hover {
  color: #007aff;
}

.document-meta, .task-meta {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
  color: #86868b;
  margin-bottom: 4px;
}

.document-size {
  color: #007aff;
  font-weight: 500;
}

.document-date, .task-date {
  color: #c7c7cc;
}

.task-similarity {
  color: #67c23a;
  font-weight: 500;
}

.task-diffs {
  color: #f56c6c;
  font-weight: 500;
}

.document-actions, .task-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.document-actions .el-button, .task-actions .el-button {
  background: rgba(255, 255, 255, 0.95);
  border: none;
  padding: 10px 16px;
  font-size: 14px;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .el-icon {
    color: #1d1d1f;
  }
  
  &:hover {
    background: #fff;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.task-actions .el-button {
  color: #f56c6c;
  border-color: #f56c6c;
  
  &:hover {
    background: #f56c6c;
    color: white;
  }
}

.document-type, .task-status-tag {
  margin: 8px 0;
}

.task-preview .task-status {
  font-size: 64px;
  opacity: 0.6;
}

.task-preview .task-status .el-icon {
  font-size: 64px;
}

/* 标签页导航样式 */
.tab-nav {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.tab-item:hover {
  background: #f0f0f0;
  color: #007aff;
}

.tab-item.active {
  background: white;
  color: #007aff;
  border-bottom-color: #007aff;
}

.tab-item .count {
  background: #007aff;
  color: white;
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.tab-content {
  background: white;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sidebar {
    width: 250px;
  }
  
  .main-content {
    padding: 20px;
  }
  
  .info-cards {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .profile-container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    position: relative;
  }
  
  .sidebar-header {
    padding: 20px;
  }
  
  .sidebar-nav {
    display: flex;
    overflow-x: auto;
    padding: 0;
  }
  
  .nav-item {
    flex-shrink: 0;
    min-width: 120px;
    padding: 12px 16px;
    text-align: center;
    flex-direction: column;
    border-left: none;
    border-bottom: 3px solid transparent;
  }
  
  .nav-item.active {
    border-left: none;
    border-bottom-color: white;
  }
  
  .nav-icon {
    margin-right: 0;
    margin-bottom: 4px;
  }
  
  .nav-label {
    font-size: 14px;
  }
  
  .nav-arrow {
    display: none;
  }
  
  .main-content {
    padding: 20px 16px;
  }
  
  .section-title {
    font-size: 28px;
  }
  
  .photo-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
  }

  .document-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 15px 20px;
  }

  .document-header .filters {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }

  .document-header .filters .el-select {
    width: 100%;
  }

  .document-grid, .task-grid {
    grid-template-columns: 1fr;
    padding: 15px 20px;
  }

  .tab-nav {
    flex-direction: column;
  }

  .tab-item {
    padding: 12px 16px;
  }

  .document-header, .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 15px 20px;
  }

  .document-header .filters {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
  }

  .document-header .filters .el-select {
    width: 100%;
  }
}

/* 苹果风格确认框 */
:deep(.apple-confirm-box) {
  border-radius: 16px;
}

:deep(.apple-confirm-box .el-message-box__header) {
  padding: 24px 24px 16px;
}

:deep(.apple-confirm-box .el-message-box__content) {
  padding: 0 24px 24px;
}

:deep(.apple-confirm-box .el-message-box__btns) {
  padding: 16px 24px 24px;
}

/* 预览对话框样式 */
:deep(.apple-preview-dialog) {
  border-radius: 20px;
  overflow: hidden;
}

:deep(.apple-preview-dialog .el-dialog__header) {
  display: none;
}

:deep(.apple-preview-dialog .el-dialog__body) {
  padding: 0;
}

:deep(.apple-preview-dialog .el-dialog__footer) {
  display: none;
}
</style> 