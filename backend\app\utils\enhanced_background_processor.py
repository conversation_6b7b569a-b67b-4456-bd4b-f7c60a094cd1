"""
增强背景处理器 - 使用removebg和多种深度学习模型
支持精确的人体分割，特别加强衣服、头发等细节检测
"""

import cv2
import numpy as np
import logging
from PIL import Image as PILImage
import time
from typing import Tuple, Optional, Union
import os
import gc

# 主要背景移除库
try:
    import rembg
    REMBG_AVAILABLE = True
except ImportError:
    REMBG_AVAILABLE = False
    logging.warning("rembg未安装，使用备用方法")

# MediaPipe人体分割 (暂时禁用，Python 3.13兼容性问题)
MEDIAPIPE_AVAILABLE = False
try:
    import mediapipe as mp  # type: ignore
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    mp = None  # 设置为None以避免未定义变量警告

# scikit-image用于图像处理
try:
    from skimage import segmentation, color, filters, morphology  # type: ignore
    from skimage.future import graph  # type: ignore
    SCIKIT_IMAGE_AVAILABLE = True
except ImportError:
    SCIKIT_IMAGE_AVAILABLE = False
    segmentation = None  # 设置为None以避免未定义变量警告
    color = None
    filters = None
    morphology = None
    graph = None

# PyTorch和transformers用于更高级的分割
try:
    import torch  # type: ignore
    from transformers import pipeline  # type: ignore
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None  # 设置为None以避免未定义变量警告
    pipeline = None

logger = logging.getLogger(__name__)

class EnhancedBackgroundProcessor:
    """增强背景处理器"""
    
    def __init__(self):
        self.rembg_session = None
        self.mediapipe_selfie = None
        self.segmentation_pipeline = None
        self._models_loaded = False
        self._last_use_time = 0
        self._cleanup_interval = 300  # 5分钟清理一次
    
    def _lazy_load_models(self):
        """懒加载模型 - 只在需要时加载"""
        if self._models_loaded:
            return
        
        current_time = time.time()
        
        # 如果距离上次使用超过清理间隔，先清理内存
        if current_time - self._last_use_time > self._cleanup_interval:
            self._cleanup_memory()
        
        try:
            # 初始化RemBG - 使用最适合人像的模型
            if REMBG_AVAILABLE and self.rembg_session is None:
                self.rembg_session = rembg.new_session('u2net')
                logger.info("RemBG u2net模型懒加载成功")
            
            # 初始化MediaPipe自拍分割
            if MEDIAPIPE_AVAILABLE and mp is not None and self.mediapipe_selfie is None:
                mp_selfie_segmentation = mp.solutions.selfie_segmentation
                self.mediapipe_selfie = mp_selfie_segmentation.SelfieSegmentation(
                    model_selection=1  # 0为一般模型，1为高精度模型
                )
                logger.info("MediaPipe自拍分割模型懒加载成功")
            
            self._models_loaded = True
            self._last_use_time = current_time
            
        except Exception as e:
            logger.warning(f"模型懒加载失败: {e}")
            self._cleanup_memory()
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            # 清理模型实例
            if self.rembg_session is not None:
                del self.rembg_session
                self.rembg_session = None
            
            if self.mediapipe_selfie is not None:
                del self.mediapipe_selfie
                self.mediapipe_selfie = None
            
            if self.segmentation_pipeline is not None:
                del self.segmentation_pipeline
                self.segmentation_pipeline = None
            
            self._models_loaded = False
            
            # 强制垃圾回收
            gc.collect()
            
            logger.info("内存清理完成")
            
        except Exception as e:
            logger.warning(f"内存清理失败: {e}")
    
    def process_background(self, img: Union[np.ndarray, PILImage.Image], 
                          bg_color: Tuple[int, int, int],
                          method: str = 'auto') -> PILImage.Image:
        """
        处理背景替换
        
        Args:
            img: 输入图像（OpenCV格式或PIL格式）
            bg_color: 背景颜色 (R, G, B)
            method: 处理方法 ('auto', 'rembg', 'mediapipe', 'advanced', 'hybrid')
        
        Returns:
            处理后的PIL图像
        """
        start_time = time.time()
        
        try:
            # 懒加载模型
            self._lazy_load_models()
            
            # 统一转换为PIL图像
            if isinstance(img, np.ndarray):
                if img.shape[2] == 3:  # BGR
                    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                pil_img = PILImage.fromarray(img)
            else:
                pil_img = img
            
            logger.info(f"开始背景处理，方法: {method}, 图像尺寸: {pil_img.size}")
            
            # 根据方法选择处理策略
            if method == 'auto':
                result = self._auto_select_method(pil_img, bg_color)
            elif method == 'rembg':
                result = self._process_with_rembg(pil_img, bg_color)
            elif method == 'mediapipe':
                result = self._process_with_mediapipe(pil_img, bg_color)
            elif method == 'advanced':
                result = self._process_with_advanced_segmentation(pil_img, bg_color)
            elif method == 'hybrid':
                result = self._process_with_hybrid_method(pil_img, bg_color)
            else:
                result = self._process_with_fallback(pil_img, bg_color)
            
            processing_time = time.time() - start_time
            logger.info(f"背景处理完成，耗时: {processing_time:.2f}秒")
            
            return result
            
        except Exception as e:
            logger.error(f"背景处理失败: {e}")
            # 处理失败时也清理内存
            self._cleanup_memory()
            raise
        finally:
            # 处理完成后清理临时变量
            if 'pil_img' in locals():
                del pil_img
            if 'result' in locals():
                del result
            gc.collect()
    
    def _auto_select_method(self, img: PILImage.Image, bg_color: Tuple[int, int, int]) -> PILImage.Image:
        """自动选择最佳处理方法"""
        # 优先级：RemBG > MediaPipe > 高级分割 > 混合方法 > 备用方法
        methods = [
            ('rembg', self._process_with_rembg),
            ('mediapipe', self._process_with_mediapipe),
            ('advanced', self._process_with_advanced_segmentation),
            ('hybrid', self._process_with_hybrid_method),
            ('fallback', self._process_with_fallback)
        ]
        
        for method_name, method_func in methods:
            try:
                logger.info(f"尝试方法: {method_name}")
                result = method_func(img, bg_color)
                if result is not None:
                    logger.info(f"方法 {method_name} 成功")
                    return result
            except Exception as e:
                logger.warning(f"方法 {method_name} 失败: {e}")
                continue
        
        # 所有方法都失败，返回原图
        logger.error("所有背景处理方法都失败，返回原图")
        return img
    
    def _process_with_rembg(self, img: PILImage.Image, bg_color: Tuple[int, int, int]) -> Optional[PILImage.Image]:
        """使用RemBG处理背景"""
        if not REMBG_AVAILABLE or self.rembg_session is None:
            return None
        
        try:
            # 使用RemBG移除背景
            img_no_bg = rembg.remove(img, session=self.rembg_session)
            
            # 创建新背景
            result = PILImage.new('RGB', img.size, bg_color)
            result.paste(img_no_bg, (0, 0), img_no_bg)
            
            # 后处理：边缘平滑
            result = self._post_process_edges(result, img_no_bg)
            
            return result
            
        except Exception as e:
            logger.error(f"RemBG处理失败: {e}")
            return None
    
    def _process_with_mediapipe(self, img: PILImage.Image, bg_color: Tuple[int, int, int]) -> Optional[PILImage.Image]:
        """使用MediaPipe处理背景"""
        if not MEDIAPIPE_AVAILABLE or self.mediapipe_selfie is None:
            return None
        
        try:
            # 转换为OpenCV格式
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            
            # MediaPipe处理
            results = self.mediapipe_selfie.process(cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB))
            
            if results.segmentation_mask is not None:
                # 获取分割掩码
                mask = results.segmentation_mask
                mask = (mask > 0.5).astype(np.uint8) * 255
                
                # 后处理掩码
                mask = self._enhance_mask(mask, img_cv)
                
                # 应用背景
                result_cv = self._apply_background_with_mask(img_cv, mask, bg_color)
                
                # 转换回PIL格式
                result = PILImage.fromarray(cv2.cvtColor(result_cv, cv2.COLOR_BGR2RGB))
                
                return result
            
        except Exception as e:
            logger.error(f"MediaPipe处理失败: {e}")
            return None
    
    def _process_with_advanced_segmentation(self, img: PILImage.Image, bg_color: Tuple[int, int, int]) -> Optional[PILImage.Image]:
        """使用高级分割技术"""
        if not SCIKIT_IMAGE_AVAILABLE:
            return None
        
        try:
            img_array = np.array(img)
            
            # 使用SLIC超像素分割
            segments = segmentation.slic(img_array, n_segments=300, compactness=10, sigma=1)
            
            # 使用区域邻接图进行分割
            g = graph.rag_mean_color(img_array, segments)
            labels = graph.cut_threshold(segments, g, 30)
            
            # 基于人脸检测确定前景区域
            mask = self._create_person_mask_from_segments(img_array, labels)
            
            # 应用背景
            result_array = self._apply_background_with_mask_array(img_array, mask, bg_color)
            
            return PILImage.fromarray(result_array)
            
        except Exception as e:
            logger.error(f"高级分割处理失败: {e}")
            return None
    
    def _process_with_hybrid_method(self, img: PILImage.Image, bg_color: Tuple[int, int, int]) -> Optional[PILImage.Image]:
        """使用混合方法：结合多种技术"""
        try:
            masks = []
            
            # 方法1：MediaPipe掩码
            if MEDIAPIPE_AVAILABLE and self.mediapipe_selfie:
                try:
                    img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
                    results = self.mediapipe_selfie.process(cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB))
                    if results.segmentation_mask is not None:
                        mask1 = (results.segmentation_mask > 0.5).astype(np.uint8) * 255
                        masks.append(mask1)
                except:
                    pass
            
            # 方法2：基于颜色的分割
            mask2 = self._create_color_based_mask(np.array(img))
            masks.append(mask2)
            
            # 方法3：基于边缘的分割
            mask3 = self._create_edge_based_mask(np.array(img))
            masks.append(mask3)
            
            if masks:
                # 融合多个掩码
                final_mask = self._fuse_masks(masks)
                
                # 后处理
                final_mask = self._enhance_mask(final_mask, cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR))
                
                # 应用背景
                img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
                result_cv = self._apply_background_with_mask(img_cv, final_mask, bg_color)
                
                return PILImage.fromarray(cv2.cvtColor(result_cv, cv2.COLOR_BGR2RGB))
            
        except Exception as e:
            logger.error(f"混合方法处理失败: {e}")
            return None
    
    def _process_with_fallback(self, img: PILImage.Image, bg_color: Tuple[int, int, int]) -> PILImage.Image:
        """备用处理方法：使用传统计算机视觉技术"""
        try:
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            
            # 使用GrabCut算法
            mask = self._grabcut_segmentation(img_cv)
            
            # 应用背景
            result_cv = self._apply_background_with_mask(img_cv, mask, bg_color)
            
            return PILImage.fromarray(cv2.cvtColor(result_cv, cv2.COLOR_BGR2RGB))
            
        except Exception as e:
            logger.error(f"备用方法失败: {e}")
            # 最后的备用：返回原图
            return img
    
    def _enhance_mask(self, mask: np.ndarray, img: np.ndarray) -> np.ndarray:
        """增强掩码质量，特别针对头发和衣服边缘"""
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        
        # 闭运算：填补小洞
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        # 开运算：去除噪点
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel, iterations=1)
        
        # 高斯模糊：平滑边缘
        mask = cv2.GaussianBlur(mask, (3, 3), 0)
        
        # 针对头发的特殊处理
        mask = self._enhance_hair_region(mask, img)
        
        # 针对衣服边缘的特殊处理
        mask = self._enhance_clothing_edges(mask, img)
        
        return mask
    
    def _enhance_hair_region(self, mask: np.ndarray, img: np.ndarray) -> np.ndarray:
        """增强头发区域的检测 - 专门针对头发边缘优化"""
        try:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            height, width = mask.shape
            
            # 1. 多层次头发检测
            _, very_dark = cv2.threshold(gray, 60, 255, cv2.THRESH_BINARY_INV)
            _, dark = cv2.threshold(gray, 100, 255, cv2.THRESH_BINARY_INV)
            
            # 2. 纹理分析检测头发
            kernel_hair = np.array([[-1, -1, -1], [2, 2, 2], [-1, -1, -1]], dtype=np.float32)
            hair_texture = cv2.filter2D(gray, -1, kernel_hair)
            _, hair_texture_mask = cv2.threshold(hair_texture, 20, 255, cv2.THRESH_BINARY)
            
            # 3. 边缘检测增强头发轮廓
            edges = cv2.Canny(gray, 30, 80)
            
            # 4. 重点关注图像上半部分（头发区域）
            hair_region_height = int(height * 0.6)
            
            # 5. 组合多种检测方法
            combined_hair = cv2.bitwise_or(very_dark[:hair_region_height, :], 
                                         dark[:hair_region_height, :])
            combined_hair = cv2.bitwise_or(combined_hair, 
                                         hair_texture_mask[:hair_region_height, :])
            combined_hair = cv2.bitwise_or(combined_hair, 
                                         edges[:hair_region_height, :])
            
            # 6. 与现有掩码的交集，避免误检测
            existing_mask_top = mask[:hair_region_height, :]
            refined_hair = cv2.bitwise_and(combined_hair, existing_mask_top)
            
            # 7. 形态学处理，连接头发丝
            hair_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 7))
            refined_hair = cv2.morphologyEx(refined_hair, cv2.MORPH_CLOSE, hair_kernel, iterations=2)
            
            # 8. 精细膨胀，确保头发边缘完整
            hair_dilate_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
            refined_hair = cv2.dilate(refined_hair, hair_dilate_kernel, iterations=1)
            
            # 9. 应用高斯模糊实现柔和边缘（确保核大小为奇数）
            blur_kernel_size = 3
            if blur_kernel_size % 2 == 0:
                blur_kernel_size = 3
            refined_hair = cv2.GaussianBlur(refined_hair, (blur_kernel_size, blur_kernel_size), 1)

            # === 新增：将头发与背景轮廓向内20像素全部改为黑色 ===
            # 计算 refined_hair 的边界
            contours, _ = cv2.findContours(refined_hair, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            hair_boundary_mask = np.zeros_like(refined_hair)
            cv2.drawContours(hair_boundary_mask, contours, -1, 255, thickness=40)  # 40像素宽度
            # 取边界内区域
            hair_inner_mask = cv2.erode(hair_boundary_mask, np.ones((21, 21), np.uint8), iterations=1)
            # 将 mask 对应区域设为黑色
            mask[:hair_region_height, :][hair_inner_mask > 0] = 0

            # === 羽化后变黑处理 ===
            # 10. 生成羽化权重（distance transform）
            dist = cv2.distanceTransform(refined_hair, cv2.DIST_L2, 5)
            max_dist = np.max(dist) if np.max(dist) > 0 else 1
            feather = (dist / max_dist * 255).astype(np.uint8)

            # 11. 生成"变黑"掩码（羽化区权重高的地方更黑）
            hair_region = mask[:hair_region_height, :].copy()
            hair_darkened = hair_region.copy()
            alpha = feather.astype(np.float32) / 255.0  # 0~1
            # 变黑处理：将羽化区的像素值降低（例如降低到原值的 0.4~1.0 之间，按羽化权重线性插值）
            hair_darkened = (hair_region.astype(np.float32) * (1 - 0.6 * alpha)).astype(np.uint8)

            # 12. 融合到原掩码
            mask[:hair_region_height, :] = np.maximum(hair_darkened, refined_hair)

            logger.info("头发区域检测增强完成（含羽化变黑）")
        except Exception as e:
            logger.warning(f"头发区域增强失败: {e}")
        return mask
    
    def _enhance_clothing_edges(self, mask: np.ndarray, img: np.ndarray) -> np.ndarray:
        """增强衣服边缘的检测 - 专门针对服装轮廓优化"""
        try:
            # 1. 多尺度边缘检测
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # 细节边缘
            edges_fine = cv2.Canny(gray, 50, 120)
            # 粗略边缘  
            edges_coarse = cv2.Canny(gray, 30, 80)
            
            # 2. 梯度幅度检测
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            _, gradient_mask = cv2.threshold(gradient_magnitude, 30, 255, cv2.THRESH_BINARY)
            gradient_mask = gradient_mask.astype(np.uint8)
            
            # 3. 现有掩码的边缘扩展
            mask_edges = cv2.Canny(mask, 50, 150)
            
            # 膨胀掩码边缘以创建搜索区域
            search_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            search_area = cv2.dilate(mask_edges, search_kernel, iterations=2)
            
            # 4. 在搜索区域内寻找衣服边缘
            clothing_edges = cv2.bitwise_and(edges_fine, search_area)
            clothing_edges = cv2.bitwise_or(clothing_edges, 
                                          cv2.bitwise_and(edges_coarse, search_area))
            clothing_edges = cv2.bitwise_or(clothing_edges,
                                          cv2.bitwise_and(gradient_mask, search_area))
            
            # 5. 连接断裂的边缘
            connect_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            clothing_edges = cv2.morphologyEx(clothing_edges, cv2.MORPH_CLOSE, connect_kernel, iterations=1)
            
            # 6. 将检测到的衣服边缘添加到掩码
            mask = cv2.bitwise_or(mask, clothing_edges)
            
            logger.info("衣服边缘检测增强完成")
            
        except Exception as e:
            logger.warning(f"衣服边缘增强失败: {e}")
        
        return mask
    
    def _create_color_based_mask(self, img: np.ndarray) -> np.ndarray:
        """基于颜色的人体分割"""
        try:
            # 转换到HSV色彩空间
            hsv = cv2.cvtColor(img, cv2.COLOR_RGB2HSV)
            
            # 肤色检测
            lower_skin = np.array([0, 20, 70])
            upper_skin = np.array([20, 255, 255])
            skin_mask = cv2.inRange(hsv, lower_skin, upper_skin)
            
            # 扩展肤色区域以包含相邻的人体部分
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (10, 10))
            skin_mask = cv2.dilate(skin_mask, kernel, iterations=3)
            
            return skin_mask
            
        except Exception as e:
            logger.warning(f"颜色分割失败: {e}")
            return np.zeros(img.shape[:2], dtype=np.uint8)
    
    def _create_edge_based_mask(self, img: np.ndarray) -> np.ndarray:
        """基于边缘的人体分割"""
        try:
            gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)
            
            # 查找轮廓
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 创建掩码
            mask = np.zeros(gray.shape, dtype=np.uint8)
            
            if contours:
                # 选择最大轮廓（假设是人体）
                largest_contour = max(contours, key=cv2.contourArea)
                cv2.fillPoly(mask, [largest_contour], 255)
            
            return mask
            
        except Exception as e:
            logger.warning(f"边缘分割失败: {e}")
            return np.zeros(img.shape[:2], dtype=np.uint8)
    
    def _fuse_masks(self, masks: list) -> np.ndarray:
        """融合多个掩码"""
        if not masks:
            return np.zeros((100, 100), dtype=np.uint8)
        
        # 确保所有掩码尺寸一致
        base_shape = masks[0].shape
        aligned_masks = []
        
        for mask in masks:
            if mask.shape != base_shape:
                mask = cv2.resize(mask, (base_shape[1], base_shape[0]))
            aligned_masks.append(mask)
        
        # 加权融合
        fused = np.zeros(base_shape, dtype=np.float32)
        weights = [1.0, 0.7, 0.5]  # 给不同方法不同权重
        
        for i, mask in enumerate(aligned_masks):
            weight = weights[i] if i < len(weights) else 0.3
            fused += mask.astype(np.float32) * weight
        
        # 归一化并转换为二值掩码
        fused = (fused / len(aligned_masks)).astype(np.uint8)
        _, fused = cv2.threshold(fused, 127, 255, cv2.THRESH_BINARY)
        
        return fused
    
    def _apply_background_with_mask(self, img: np.ndarray, mask: np.ndarray, bg_color: Tuple[int, int, int]) -> np.ndarray:
        """使用掩码应用背景"""
        try:
            # 确保掩码是单通道
            if len(mask.shape) == 3:
                mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)
            
            # 创建3通道掩码
            mask_3ch = cv2.merge([mask, mask, mask])
            
            # 归一化掩码
            mask_norm = mask_3ch.astype(np.float32) / 255.0
            
            # 创建背景图像
            background = np.full(img.shape, bg_color[::-1], dtype=np.uint8)  # BGR格式
            
            # 混合前景和背景
            result = img.astype(np.float32) * mask_norm + background.astype(np.float32) * (1 - mask_norm)
            
            return result.astype(np.uint8)
            
        except Exception as e:
            logger.error(f"背景应用失败: {e}")
            return img
    
    def _apply_background_with_mask_array(self, img: np.ndarray, mask: np.ndarray, bg_color: Tuple[int, int, int]) -> np.ndarray:
        """使用掩码应用背景（RGB格式）"""
        try:
            # 确保掩码是单通道
            if len(mask.shape) == 3:
                mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
            
            # 创建3通道掩码
            mask_3ch = np.stack([mask, mask, mask], axis=2)
            
            # 归一化掩码
            mask_norm = mask_3ch.astype(np.float32) / 255.0
            
            # 创建背景图像
            background = np.full(img.shape, bg_color, dtype=np.uint8)  # RGB格式
            
            # 混合前景和背景
            result = img.astype(np.float32) * mask_norm + background.astype(np.float32) * (1 - mask_norm)
            
            return result.astype(np.uint8)
            
        except Exception as e:
            logger.error(f"背景应用失败: {e}")
            return img
    
    def _grabcut_segmentation(self, img: np.ndarray) -> np.ndarray:
        """使用GrabCut算法进行分割"""
        try:
            height, width = img.shape[:2]
            
            # 创建初始矩形（假设人物在中心）
            rect = (width//6, height//8, width*2//3, height*7//8)
            
            # GrabCut算法
            mask = np.zeros((height, width), np.uint8)
            bgd_model = np.zeros((1, 65), np.float64)
            fgd_model = np.zeros((1, 65), np.float64)
            
            cv2.grabCut(img, mask, rect, bgd_model, fgd_model, 5, cv2.GC_INIT_WITH_RECT)
            
            # 创建最终掩码
            mask2 = np.where((mask == 2) | (mask == 0), 0, 255).astype(np.uint8)
            
            return mask2
            
        except Exception as e:
            logger.error(f"GrabCut分割失败: {e}")
            return np.ones(img.shape[:2], dtype=np.uint8) * 255
    
    def _create_person_mask_from_segments(self, img: np.ndarray, labels: np.ndarray) -> np.ndarray:
        """从分割标签创建人物掩码"""
        try:
            # 简单的人脸检测来确定人物区域
            gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            mask = np.zeros(img.shape[:2], dtype=np.uint8)
            
            if len(faces) > 0:
                # 找到包含人脸的分割区域
                face = faces[0]  # 取第一个检测到的人脸
                fx, fy, fw, fh = face
                face_center = (fx + fw//2, fy + fh//2)
                
                # 获取人脸区域的标签
                face_label = labels[face_center[1], face_center[0]]
                
                # 包含相似标签的区域都被认为是人物
                person_mask = (labels == face_label).astype(np.uint8) * 255
                
                # 扩展人物区域
                kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (20, 20))
                person_mask = cv2.dilate(person_mask, kernel, iterations=2)
                
                return person_mask
            
            # 如果没有检测到人脸，返回中心区域
            center_y, center_x = img.shape[0]//2, img.shape[1]//2
            center_label = labels[center_y, center_x]
            return (labels == center_label).astype(np.uint8) * 255
            
        except Exception as e:
            logger.error(f"创建人物掩码失败: {e}")
            return np.ones(img.shape[:2], dtype=np.uint8) * 255
    
    def _post_process_edges(self, result: PILImage.Image, alpha_img: PILImage.Image) -> PILImage.Image:
        """后处理：平滑边缘"""
        try:
            from PIL import ImageFilter
            # 提取alpha通道
            if alpha_img.mode == 'RGBA':
                alpha = alpha_img.split()[-1]
                
                # 对alpha通道进行轻微模糊
                alpha_blurred = alpha.filter(ImageFilter.GaussianBlur(radius=5))
                
                # 重新合成图像
                result_array = np.array(result)
                alpha_array = np.array(alpha_blurred).astype(np.float32) / 255.0
                
                # 应用平滑的alpha
                for i in range(3):  # RGB通道
                    result_array[:, :, i] = (result_array[:, :, i] * alpha_array + 
                                           result_array[:, :, i] * (1 - alpha_array))
                
                return PILImage.fromarray(result_array)
            
        except Exception as e:
            logger.warning(f"边缘后处理失败: {e}")
        
        return result


# 全局处理器实例
_processor_instance = None

def get_enhanced_processor():
    """获取增强背景处理器实例（单例模式）"""
    global _processor_instance
    if _processor_instance is None:
        _processor_instance = EnhancedBackgroundProcessor()
    return _processor_instance

def enhanced_background_replace(img: Union[np.ndarray, PILImage.Image], 
                              bg_color: Tuple[int, int, int],
                              method: str = 'auto') -> PILImage.Image:
    """
    增强背景替换的便捷函数
    
    Args:
        img: 输入图像
        bg_color: 背景颜色 (R, G, B)
        method: 处理方法
    
    Returns:
        处理后的图像
    """
    processor = get_enhanced_processor()
    return processor.process_background(img, bg_color, method) 