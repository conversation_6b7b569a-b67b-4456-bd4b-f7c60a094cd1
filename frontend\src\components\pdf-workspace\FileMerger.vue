<template>
  <div class="modal-bg" @click.self="$emit('close')">
    <div class="modal">
      <h3>合并PDF文件</h3>
      <p>拖拽文件调整合并顺序</p>
      <draggable 
        v-model="draggableFiles" 
        item-key="id" 
        class="file-list-draggable"
        ghost-class="ghost"
      >
        <template #item="{element}">
          <div class="file-item">
            <span class="drag-handle">⠿</span>
            {{ element.name }}
          </div>
        </template>
      </draggable>
      <div class="modal-actions">
        <button class="btn btn-primary" @click="$emit('merge', draggableFiles)">确认合并</button>
        <button class="btn" @click="$emit('close')">取消</button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, watch } from 'vue';
import draggable from 'vuedraggable';

const props = defineProps(['files']);
const draggableFiles = ref([]);

watch(
  () => props.files,
  (newVal) => {
    draggableFiles.value = [...newVal];
  },
  { immediate: true, deep: true }
);
</script>
<style scoped>
.modal-bg { position: fixed; top:0; left:0; right:0; bottom:0; background:rgba(0,0,0,0.2); display:flex; align-items:center; justify-content:center; z-index:1000; }
.modal { background:#fff; border-radius:8px; padding:2rem; min-width:300px; width: 450px; }
.btn { margin: 1rem 1rem 0 0; }
.btn-primary {
  background: #2563eb;
  color: white;
}
.file-list-draggable {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 0.5rem;
  margin: 1rem 0;
  max-height: 300px;
  overflow-y: auto;
}
.file-item {
  padding: 0.75rem 1rem;
  background: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  cursor: grab;
}
.file-item:last-child {
  margin-bottom: 0;
}
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
.drag-handle {
  margin-right: 0.75rem;
  font-size: 1.2em;
  color: #aaa;
}
.modal-actions {
  text-align: right;
  margin-top: 1.5rem;
}
</style> 