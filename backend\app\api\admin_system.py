#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员系统设置API
Admin System Config API
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required
from app import db
from app.models.system_config import SystemConfig
from datetime import datetime

admin_system_bp = Blueprint('admin_system', __name__, url_prefix='/api/admin')

# 获取系统参数配置
@admin_system_bp.route('/system/config', methods=['GET'])
@jwt_required()
def get_system_config():
    configs = SystemConfig.query.filter_by(is_system=True).all()
    return jsonify([
        {
            'key': c.config_key,
            'value': c.get_value(),
            'type': c.config_type,
            'description': c.description
        } for c in configs
    ])

# 保存系统参数配置
@admin_system_bp.route('/system/config', methods=['POST'])
@jwt_required()
def save_system_config():
    data = request.json
    for item in data:
        key = item['key']
        value = item['value']
        config_type = item.get('type', 'string')
        description = item.get('description', None)
        config = SystemConfig.set_config(key, value, config_type, description)
        config.is_system = True
        db.session.add(config)
    db.session.commit()
    return jsonify({'msg': '系统参数配置已保存'})

# 获取支付配置
@admin_system_bp.route('/payment/config', methods=['GET'])
@jwt_required()
def get_payment_config():
    keys = [
        'wechat_appid', 'wechat_mch_id', 'wechat_api_key', 'wechat_enabled',
        'alipay_appid', 'alipay_private_key', 'alipay_public_key', 'alipay_enabled'
    ]
    configs = SystemConfig.query.filter(SystemConfig.config_key.in_(keys)).all()
    return jsonify([
        {
            'key': c.config_key,
            'value': c.get_value(),
            'type': c.config_type,
            'description': c.description
        } for c in configs
    ])

# 保存支付配置
@admin_system_bp.route('/payment/config', methods=['POST'])
@jwt_required()
def save_payment_config():
    data = request.json
    for item in data:
        key = item['key']
        value = item['value']
        config_type = item.get('type', 'string')
        description = item.get('description', None)
        config = SystemConfig.set_config(key, value, config_type, description)
        db.session.add(config)
    db.session.commit()
    return jsonify({'msg': '支付配置已保存'})

# 获取广告配置
@admin_system_bp.route('/ads/config', methods=['GET'])
@jwt_required()
def get_ads_config():
    keys = [
        'baidu_appid', 'baidu_appkey', 'baidu_ad_unit_id', 'baidu_enabled',
        'toutiao_appid', 'toutiao_appkey', 'toutiao_ad_unit_id', 'toutiao_enabled',
        'admob_app_id', 'admob_ad_unit_id', 'admob_enabled'
    ]
    configs = SystemConfig.query.filter(SystemConfig.config_key.in_(keys)).all()
    return jsonify([
        {
            'key': c.config_key,
            'value': c.get_value(),
            'type': c.config_type,
            'description': c.description
        } for c in configs
    ])

# 保存广告配置
@admin_system_bp.route('/ads/config', methods=['POST'])
@jwt_required()
def save_ads_config():
    data = request.json
    for item in data:
        key = item['key']
        value = item['value']
        config_type = item.get('type', 'string')
        description = item.get('description', None)
        config = SystemConfig.set_config(key, value, config_type, description)
        db.session.add(config)
    db.session.commit()
    return jsonify({'msg': '广告配置已保存'})

# 获取邮件配置
@admin_system_bp.route('/email/config', methods=['GET'])
@jwt_required()
def get_email_config():
    keys = [
        'smtp_host', 'smtp_port', 'email_username', 'email_password',
        'sender_name', 'email_enabled'
    ]
    configs = SystemConfig.query.filter(SystemConfig.config_key.in_(keys)).all()
    return jsonify([
        {
            'key': c.config_key,
            'value': c.get_value(),
            'type': c.config_type,
            'description': c.description
        } for c in configs
    ])

# 保存邮件配置
@admin_system_bp.route('/email/config', methods=['POST'])
@jwt_required()
def save_email_config():
    data = request.json
    for item in data:
        key = item['key']
        value = item['value']
        config_type = item.get('type', 'string')
        description = item.get('description', None)
        config = SystemConfig.set_config(key, value, config_type, description)
        db.session.add(config)
    db.session.commit()
    return jsonify({'msg': '邮件配置已保存'})

# 测试邮件发送（伪实现）
@admin_system_bp.route('/email/test', methods=['POST'])
@jwt_required()
def test_email():
    data = request.json
    test_email = data.get('email')
    # 这里应集成邮件发送服务，现仅返回成功
    return jsonify({'msg': f'测试邮件已发送到 {test_email}'}) 