<template>
  <div class="modern-minimal-card">
    <div class="card-wrapper modern-minimal" :style="styleVariables">
      <div class="left-panel">
        <h3 class="company" :style="companyStyle">{{ cardData.company || '公司名称' }}</h3>
      </div>
      <div class="right-panel">
        <div class="identity">
          <h2 class="name" :style="nameStyle">{{ cardData.name || '姓名' }}</h2>
          <p class="title" v-if="cardData.title" :style="titleStyle">{{ cardData.title }}</p>
        </div>
        <div class="contact-info">
          <div class="contact-item" v-if="cardData.phone" :style="contactStyle">
            <svg class="icon" viewBox="0 0 1024 1024"><path d="M896 64H128C92.672 64 64 92.672 64 128v768c0 35.328 28.672 64 64 64h768c35.328 0 64-28.672 64-64V128c0-35.328-28.672-64-64-64zM256 192h512v128H256V192z m640 640H128V448h768v384z" fill="currentColor"></path></svg>
            <span>{{ cardData.phone }}</span>
          </div>
          <div class="contact-item" v-if="cardData.email" :style="contactStyle">
            <svg class="icon" viewBox="0 0 1024 1024"><path d="M512 562.176L64 278.912V128c0-35.328 28.672-64 64-64h768c35.328 0 64 28.672 64 64v150.912L512 562.176zM64 364.8v499.2c0 35.328 28.672 64 64 64h768c35.328 0 64-28.672 64-64V364.8L512 640 64 364.8z" fill="currentColor"></path></svg>
            <span>{{ cardData.email }}</span>
          </div>
          <div class="contact-item" v-if="cardData.address" :style="contactStyle">
            <svg class="icon" viewBox="0 0 1024 1024"><path d="M512 64C337.92 64 192 210.048 192 384c0 133.248 83.2 250.88 204.8 307.2L512 960l115.2-268.8C748.8 634.88 832 517.248 832 384 832 210.048 686.08 64 512 64z m0 512c-70.656 0-128-57.344-128-128s57.344-128 128-128 128 57.344 128 128-57.344 128-128 128z" fill="currentColor"></path></svg>
            <span>{{ cardData.address }}</span>
          </div>
          <div class="contact-item" v-if="cardData.website" :style="contactStyle">
            <svg class="icon" viewBox="0 0 1024 1024"><path d="M512 64C264.576 64 64 264.576 64 512s200.576 448 448 448 448-200.576 448-448S759.424 64 512 64z m281.6 281.6L684.8 454.4c-19.2-19.2-51.2-19.2-70.4 0l-38.4 38.4-140.8-140.8 38.4-38.4c19.2-19.2 19.2-51.2 0-70.4L364.8 134.4c-19.2-19.2-51.2-19.2-70.4 0L224 204.8c-19.2 19.2-19.2 51.2 0 70.4l320 320c19.2 19.2 51.2 19.2 70.4 0l70.4-70.4c19.2-19.2 19.2-51.2 0-70.4z" fill="currentColor"></path></svg>
            <span>{{ cardData.website }}</span>
          </div>
        </div>
      </div>
      <div class="qr-section" v-if="qrCodeUrl">
        <img :src="qrCodeUrl" alt="QR Code" class="qr-img" />
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue';

export default {
  name: 'ModernMinimal',
  props: {
    cardData: {
      type: Object,
      required: true,
    },
    styleConfig: {
      type: Object,
      default: () => ({}),
    },
    qrCodeUrl: {
      type: String,
      default: '',
    },
  },
  setup(props) {
    const styleVariables = computed(() => {
      const config = props.styleConfig || {};
      const multiplier = (config.fontSize || 100) / 100;
      return {
        '--primary-color': config.primary || '#1a1a1a',
        '--secondary-color': config.secondary || '#f0f2f5',
        '--accent-color': config.accent || '#4a90e2',
        '--text-color': config.text || '#333333',
        '--light-text-color': config.light || '#555555',
        '--font-size-company': `${60 * multiplier}px`,
        '--font-size-name': `${90 * multiplier}px`,
        '--font-size-title': `${50 * multiplier}px`,
        '--font-size-contact': `${40 * multiplier}px`,
      };
    });
    const nameStyle = computed(() => ({
      color: props.styleConfig.nameColor || 'var(--accent-color)',
      fontWeight: props.styleConfig.nameFontStyle === 'bold' ? 'bold' : 'normal',
      fontStyle: props.styleConfig.nameFontStyle === 'italic' ? 'italic' : 'normal',
    }));
    const companyStyle = computed(() => ({
      color: props.styleConfig.companyColor || 'var(--secondary-color)',
      fontWeight: props.styleConfig.companyFontStyle === 'bold' ? 'bold' : 'normal',
      fontStyle: props.styleConfig.companyFontStyle === 'italic' ? 'italic' : 'normal',
    }));
    const titleStyle = computed(() => ({
      color: props.styleConfig.text || 'var(--text-color)',
      fontWeight: props.styleConfig.textFontStyle === 'bold' ? 'bold' : 'normal',
      fontStyle: props.styleConfig.textFontStyle === 'italic' ? 'italic' : 'normal',
    }));
    const contactStyle = computed(() => ({
      color: props.styleConfig.light || 'var(--light-text-color)',
      fontWeight: props.styleConfig.lightFontStyle === 'bold' ? 'bold' : 'normal',
      fontStyle: props.styleConfig.lightFontStyle === 'italic' ? 'italic' : 'normal',
    }));
    return { styleVariables, nameStyle, companyStyle, titleStyle, contactStyle };
  },
};
</script>

<style scoped>
.modern-minimal-card {
  width: 100%;
  height: 100%;
  background: var(--secondary-color);
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  display: flex;
  box-shadow: 0 4px 15px rgba(0,0,0,0.08);
  border-radius: 8px;
  overflow: hidden;
  position: relative; /* Added for positioning QR code */
}
.left-panel {
  background-color: var(--primary-color);
  color: #fff;
  padding: 2em;
  width: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.left-panel .company {
  font-size: var(--font-size-company);
  color: var(--company-color);
  font-weight: var(--company-weight);
}
.right-panel {
  flex-grow: 1;
  padding: 40px 32px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 24px;
}
.identity {
  margin-bottom: 24px;
}
.identity .name {
  font-size: var(--font-size-name);
  font-weight: 700;
  color: var(--accent-color);
  margin: 0 0 8px 0;
}
.identity .title {
  font-size: var(--font-size-title);
  color: var(--text-color);
  margin: 0;
}
.contact-info {
  display: grid;
  grid-template-columns: 1fr;
  gap: 14px;
}
.contact-item {
  display: flex;
  align-items: center;
  font-size: var(--font-size-contact);
  color: var(--light-text-color);
  font-weight: 500;
}
.contact-item .icon {
  width: 16px;
  height: 16px;
  margin-right: 12px;
  color: var(--accent-color);
}
.qr-section {
  position: absolute;
  bottom: 6mm;
  right: 6mm;
  width: 16mm;
  height: 16mm;
  background: white;
  padding: 1mm;
  border-radius: 2mm;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qr-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
}
</style> 