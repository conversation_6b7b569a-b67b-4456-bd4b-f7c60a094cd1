<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白泽智能工具集</title>
    <script src="https://cdn.jsdelivr.net/npm/html-docx-js/dist/html-docx.js"></script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        overflow-x: hidden;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
      }
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
    </style>
    <script>
      // ResizeObserver 错误处理
      window.addEventListener('error', function(event) {
        if (event.message && event.message.includes('ResizeObserver loop')) {
          event.stopImmediatePropagation();
          event.preventDefault();
          return false;
        }
      });
      
      window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message && event.reason.message.includes('ResizeObserver loop')) {
          event.preventDefault();
          return false;
        }
      });
      
      // 重写 console.error 来过滤 ResizeObserver 错误
      const originalError = console.error;
      console.error = function(...args) {
        if (args[0] && typeof args[0] === 'string' && args[0].includes('ResizeObserver loop')) {
          return;
        }
        originalError.apply(console, args);
      };
    </script>
  </head>
  <body>
    <div id="app"></div>
  </body>
</html> 