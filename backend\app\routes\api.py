from flask import request, jsonify, send_file
from werkzeug.utils import secure_filename
import os
import time

@api_bp.route('/process-dark-clothing', methods=['POST'])
def process_dark_clothing():
    """专门处理深色衣物的背景替换API"""
    try:
        current_app.logger.info("收到深色衣物处理请求")
        
        # 检查文件
        if 'image' not in request.files:
            return jsonify({'error': '没有上传图片'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        # 获取背景颜色
        bg_color_str = request.form.get('bg_color', '255,255,255')
        try:
            bg_color = tuple(map(int, bg_color_str.split(',')))
            if len(bg_color) != 3:
                raise ValueError("颜色格式错误")
        except ValueError:
            return jsonify({'error': '背景颜色格式错误，应为 R,G,B 格式'}), 400
        
        # 保存上传的文件
        filename = secure_filename(file.filename)
        timestamp = int(time.time())
        unique_filename = f"{timestamp}_{filename}"
        input_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(input_path)
        
        current_app.logger.info(f"文件保存至: {input_path}")
        current_app.logger.info(f"背景颜色: {bg_color}")
        
        # 使用优化的深色衣物检测器
        from optimized_dark_clothing_detector import OptimizedDarkClothingDetector
        import cv2
        
        # 读取图像
        image = cv2.imread(input_path)
        if image is None:
            return jsonify({'error': '无法读取图像文件'}), 400
        
        # 创建检测器并处理
        detector = OptimizedDarkClothingDetector()
        result_image = detector.replace_background(image, bg_color)
        
        # 保存结果
        output_filename = f"dark_clothing_result_{timestamp}.jpg"
        output_path = os.path.join(current_app.config['UPLOAD_FOLDER'], output_filename)
        cv2.imwrite(output_path, result_image)
        
        current_app.logger.info(f"深色衣物处理完成: {output_path}")
        
        # 清理输入文件
        try:
            os.remove(input_path)
        except:
            pass
        
        # 返回处理后的图片
        return send_file(output_path, as_attachment=True, download_name=f"processed_{filename}")
        
    except Exception as e:
        current_app.logger.error(f"深色衣物处理失败: {str(e)}")
        return jsonify({'error': f'处理失败: {str(e)}'}), 500 