<template>
  <div class="system-config">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">系统配置</h2>
        <p class="page-description">管理系统全局参数，支付配置，广告配置等</p>
      </div>
    </div>

    <!-- 配置选项卡 -->
    <el-tabs v-model="activeTab" type="border-card" class="config-tabs">
      <!-- 系统参数 -->
      <el-tab-pane label="系统参数" name="system">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>基础系统参数</span>
              <el-button type="primary" @click="saveSystemConfig" :loading="savingSystem">
                保存配置
              </el-button>
            </div>
          </template>

          <el-form :model="systemForm" label-width="200px" class="config-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="系统名称">
                  <el-input v-model="systemForm.site_name" placeholder="网站名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="系统版本">
                  <el-input v-model="systemForm.version" placeholder="系统版本号" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最大上传文件大小(MB)">
                  <el-input-number 
                    v-model="systemForm.max_upload_size" 
                    :min="1" 
                    :max="100"
                    placeholder="最大上传文件大小"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="允许的图片格式">
                  <el-select v-model="systemForm.allowed_image_types" multiple placeholder="选择允许的图片格式">
                    <el-option label="JPG" value="jpg" />
                    <el-option label="PNG" value="png" />
                    <el-option label="GIF" value="gif" />
                    <el-option label="WEBP" value="webp" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="系统维护模式">
                  <el-switch v-model="systemForm.maintenance_mode" />
                  <span class="form-tip">开启后用户无法访问系统</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册功能">
                  <el-switch v-model="systemForm.allow_register" />
                  <span class="form-tip">是否允许新用户注册</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="系统公告">
                  <el-input 
                    v-model="systemForm.announcement" 
                    type="textarea" 
                    :rows="4"
                    placeholder="系统公告内容，支持HTML"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客服QQ">
                  <el-input v-model="systemForm.customer_service_qq" placeholder="客服QQ号码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客服微信">
                  <el-input v-model="systemForm.customer_service_wechat" placeholder="客服微信号" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="版权信息">
                  <el-input v-model="systemForm.copyright" placeholder="版权信息" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 邮件配置 -->
      <el-tab-pane label="邮件配置" name="email">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>邮件服务配置</span>
              <el-button type="primary" @click="saveEmailConfig" :loading="savingEmail">
                保存配置
              </el-button>
            </div>
          </template>

          <el-form :model="emailForm" label-width="200px" class="config-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="SMTP服务器">
                  <el-input v-model="emailForm.smtp_host" placeholder="SMTP服务器地址" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="SMTP端口">
                  <el-input-number v-model="emailForm.smtp_port" :min="1" :max="65535" placeholder="SMTP端口" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱账号">
                  <el-input v-model="emailForm.email_username" placeholder="邮箱账号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱密码">
                  <el-input 
                    v-model="emailForm.email_password" 
                    type="password" 
                    placeholder="邮箱密码或授权码"
                    show-password
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="发件人名称">
                  <el-input v-model="emailForm.sender_name" placeholder="发件人显示名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮件功能开关">
                  <el-switch v-model="emailForm.email_enabled" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="测试邮件">
                  <el-input v-model="emailForm.test_email" placeholder="测试邮件接收地址" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button type="success" @click="testEmail" :loading="testingEmail">
                发送测试邮件
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import api from '@/api'

// 响应式数据
const activeTab = ref('system')
const savingSystem = ref(false)
const savingEmail = ref(false)
const testingEmail = ref(false)

// 系统参数表单
const systemForm = reactive({
  site_name: '智能证件照系统',
  version: '1.0.0',
  max_upload_size: 16,
  allowed_image_types: ['jpg', 'png', 'gif', 'webp'],
  maintenance_mode: false,
  allow_register: true,
  announcement: '',
  customer_service_qq: '',
  customer_service_wechat: '',
  copyright: '© 2024 智能证件照系统. All rights reserved.'
})

// 邮件配置表单
const emailForm = reactive({
  smtp_host: '',
  smtp_port: 587,
  email_username: '',
  email_password: '',
  sender_name: '',
  email_enabled: false,
  test_email: ''
})

// 方法
const loadSystemConfig = async () => {
  try {
    const response = await api.admin.getSystemConfig()
    const configs = response.data
    configs.forEach(config => {
      if (Object.prototype.hasOwnProperty.call(systemForm, config.key)) {
        if (config.key === 'allowed_image_types') {
          systemForm[config.key] = config.value.split(',')
        } else if (config.key === 'maintenance_mode' || config.key === 'allow_register') {
          systemForm[config.key] = config.value === 'true'
        } else {
          systemForm[config.key] = config.value
        }
      }
    })
  } catch (error) {
    console.error('加载系统配置失败:', error)
  }
}

const loadEmailConfig = async () => {
  try {
    const response = await api.admin.getEmailConfig()
    const configs = response.data
    configs.forEach(config => {
      if (Object.prototype.hasOwnProperty.call(emailForm, config.key)) {
        if (config.key === 'email_enabled') {
          emailForm[config.key] = config.value === 'true'
        } else if (config.key === 'smtp_port') {
          emailForm[config.key] = parseInt(config.value) || 587
        } else {
          emailForm[config.key] = config.value
        }
      }
    })
  } catch (error) {
    console.error('加载邮件配置失败:', error)
  }
}

const saveSystemConfig = async () => {
  savingSystem.value = true
  try {
    const configs = Object.entries(systemForm).map(([key, value]) => ({
      key,
      value: Array.isArray(value) ? value.join(',') : value.toString()
    }))
    await api.admin.saveSystemConfig(configs)
    ElMessage.success('系统配置保存成功')
  } catch (error) {
    ElMessage.error('系统配置保存失败')
  } finally {
    savingSystem.value = false
  }
}

const saveEmailConfig = async () => {
  savingEmail.value = true
  try {
    const configs = Object.entries(emailForm).map(([key, value]) => ({
      key,
      value: value.toString()
    }))
    await api.admin.saveEmailConfig(configs)
    ElMessage.success('邮件配置保存成功')
  } catch (error) {
    ElMessage.error('邮件配置保存失败')
  } finally {
    savingEmail.value = false
  }
}

const testEmail = async () => {
  if (!emailForm.test_email) {
    ElMessage.warning('请输入测试邮件地址')
    return
  }
  
  testingEmail.value = true
  try {
    await api.admin.testEmail({ email: emailForm.test_email })
    ElMessage.success('测试邮件发送成功')
  } catch (error) {
    ElMessage.error('测试邮件发送失败')
  } finally {
    testingEmail.value = false
  }
}

onMounted(() => {
  loadSystemConfig()
  loadEmailConfig()
})
</script>

<style scoped>
.system-config {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.page-description {
  color: #7f8c8d;
  margin: 0;
}

.config-tabs {
  margin-bottom: 24px;
}

.config-card {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-form {
  margin-top: 16px;
}

.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #7f8c8d;
}

:deep(.el-divider__text) {
  font-weight: 600;
  color: #2c3e50;
}
</style> 