<template>
  <div class="system-config">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">系统配置</h2>
        <p class="page-description">管理系统全局参数，邮件配置，广告配置等</p>
      </div>
    </div>

    <!-- 配置选项卡 -->
    <el-tabs v-model="activeTab" type="border-card" class="config-tabs">
      <!-- 系统参数 -->
      <el-tab-pane label="系统参数" name="system">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>基础系统参数</span>
              <el-button type="primary" @click="saveSystemConfig" :loading="savingSystem">
                保存配置
              </el-button>
            </div>
          </template>

          <el-form :model="systemForm" label-width="200px" class="config-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="系统名称">
                  <el-input v-model="systemForm.site_name" placeholder="网站名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="系统版本">
                  <el-input v-model="systemForm.version" placeholder="系统版本号" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最大上传文件大小(MB)">
                  <el-input-number 
                    v-model="systemForm.max_upload_size" 
                    :min="1" 
                    :max="100"
                    placeholder="最大上传文件大小"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="允许的图片格式">
                  <el-select v-model="systemForm.allowed_image_types" multiple placeholder="选择允许的图片格式">
                    <el-option label="JPG" value="jpg" />
                    <el-option label="PNG" value="png" />
                    <el-option label="GIF" value="gif" />
                    <el-option label="WEBP" value="webp" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="系统维护模式">
                  <el-switch v-model="systemForm.maintenance_mode" />
                  <span class="form-tip">开启后用户无法访问系统</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册功能">
                  <el-switch v-model="systemForm.allow_register" />
                  <span class="form-tip">是否允许新用户注册</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="系统公告">
                  <el-input 
                    v-model="systemForm.announcement" 
                    type="textarea" 
                    :rows="4"
                    placeholder="系统公告内容，支持HTML"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客服QQ">
                  <el-input v-model="systemForm.customer_service_qq" placeholder="客服QQ号码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客服微信">
                  <el-input v-model="systemForm.customer_service_wechat" placeholder="客服微信号" />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="版权信息">
                  <el-input v-model="systemForm.copyright" placeholder="版权信息" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 邮件配置 -->
      <el-tab-pane label="邮件配置" name="email">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>邮件服务配置</span>
              <div class="header-actions">
                <el-button type="info" @click="testEmailConfig" :loading="testingEmail">
                  测试邮件
                </el-button>
                <el-button type="primary" @click="saveEmailConfig" :loading="savingEmail">
                  保存配置
                </el-button>
              </div>
            </div>
          </template>

          <el-form :model="emailForm" label-width="200px" class="config-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="启用邮件服务">
                  <el-switch v-model="emailForm.email_enabled" />
                  <span class="form-tip">是否启用邮件发送功能</span>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="SMTP服务器">
                  <el-input 
                    v-model="emailForm.smtp_host" 
                    placeholder="如：smtp.gmail.com"
                    :disabled="!emailForm.email_enabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="SMTP端口">
                  <el-input-number 
                    v-model="emailForm.smtp_port" 
                    :min="1" 
                    :max="65535"
                    placeholder="如：587"
                    :disabled="!emailForm.email_enabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱账号">
                  <el-input 
                    v-model="emailForm.email_username" 
                    placeholder="邮箱地址"
                    :disabled="!emailForm.email_enabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱密码">
                  <el-input 
                    v-model="emailForm.email_password" 
                    type="password"
                    placeholder="邮箱密码或应用密码"
                    :disabled="!emailForm.email_enabled"
                    show-password
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="发件人名称">
                  <el-input 
                    v-model="emailForm.sender_name" 
                    placeholder="发件人显示名称"
                    :disabled="!emailForm.email_enabled"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="测试邮箱">
                  <el-input 
                    v-model="emailForm.test_email" 
                    placeholder="用于测试的邮箱地址"
                    :disabled="!emailForm.email_enabled"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </el-tab-pane>

      <!-- 广告配置 -->
      <el-tab-pane label="广告配置" name="ads">
        <el-card class="config-card">
          <template #header>
            <div class="card-header">
              <span>广告平台配置</span>
              <el-button type="primary" @click="saveAdsConfig" :loading="savingAds">
                保存配置
              </el-button>
            </div>
          </template>

          <div class="ads-config">
            <!-- 百度广告 -->
            <el-card class="ads-platform-card">
              <template #header>
                <div class="ads-header">
                  <span>百度广告</span>
                  <el-tag :type="adsForm.baidu.enabled ? 'success' : 'info'">
                    {{ adsForm.baidu.enabled ? '已启用' : '未启用' }}
                  </el-tag>
                </div>
              </template>

              <el-form :model="adsForm.baidu" label-width="120px" class="ads-form">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="启用状态">
                      <el-switch v-model="adsForm.baidu.enabled" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="应用ID">
                      <el-input 
                        v-model="adsForm.baidu.appid" 
                        placeholder="百度应用ID"
                        :disabled="!adsForm.baidu.enabled"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="应用密钥">
                      <el-input 
                        v-model="adsForm.baidu.appkey" 
                        placeholder="百度应用密钥"
                        :disabled="!adsForm.baidu.enabled"
                        show-password
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="广告位ID">
                      <el-input 
                        v-model="adsForm.baidu.ad_unit_id" 
                        placeholder="百度广告位ID"
                        :disabled="!adsForm.baidu.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>

            <!-- 头条广告 -->
            <el-card class="ads-platform-card">
              <template #header>
                <div class="ads-header">
                  <span>头条广告</span>
                  <el-tag :type="adsForm.toutiao.enabled ? 'success' : 'info'">
                    {{ adsForm.toutiao.enabled ? '已启用' : '未启用' }}
                  </el-tag>
                </div>
              </template>

              <el-form :model="adsForm.toutiao" label-width="120px" class="ads-form">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="启用状态">
                      <el-switch v-model="adsForm.toutiao.enabled" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="应用ID">
                      <el-input 
                        v-model="adsForm.toutiao.appid" 
                        placeholder="头条应用ID"
                        :disabled="!adsForm.toutiao.enabled"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="应用密钥">
                      <el-input 
                        v-model="adsForm.toutiao.appkey" 
                        placeholder="头条应用密钥"
                        :disabled="!adsForm.toutiao.enabled"
                        show-password
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="广告位ID">
                      <el-input 
                        v-model="adsForm.toutiao.ad_unit_id" 
                        placeholder="头条广告位ID"
                        :disabled="!adsForm.toutiao.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>

            <!-- AdMob广告 -->
            <el-card class="ads-platform-card">
              <template #header>
                <div class="ads-header">
                  <span>AdMob广告</span>
                  <el-tag :type="adsForm.admob.enabled ? 'success' : 'info'">
                    {{ adsForm.admob.enabled ? '已启用' : '未启用' }}
                  </el-tag>
                </div>
              </template>

              <el-form :model="adsForm.admob" label-width="120px" class="ads-form">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="启用状态">
                      <el-switch v-model="adsForm.admob.enabled" />
                    </el-form-item>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="应用ID">
                      <el-input 
                        v-model="adsForm.admob.app_id" 
                        placeholder="AdMob应用ID"
                        :disabled="!adsForm.admob.enabled"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="广告位ID">
                      <el-input 
                        v-model="adsForm.admob.ad_unit_id" 
                        placeholder="AdMob广告位ID"
                        :disabled="!adsForm.admob.enabled"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </el-card>
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

import { apiEndpoints } from '@/api'

// 响应式数据
const activeTab = ref('system')
const savingSystem = ref(false)
const savingEmail = ref(false)
const testingEmail = ref(false)
const savingAds = ref(false)

// 系统参数表单
const systemForm = reactive({
  site_name: '智能证件照系统',
  version: '1.0.0',
  max_upload_size: 16,
  allowed_image_types: ['jpg', 'png', 'gif', 'webp'],
  maintenance_mode: false,
  allow_register: true,
  announcement: '',
  customer_service_qq: '',
  customer_service_wechat: '',
  copyright: '© 2024 智能证件照系统. All rights reserved.'
})

// 邮件配置表单
const emailForm = reactive({
  smtp_host: '',
  smtp_port: 587,
  email_username: '',
  email_password: '',
  sender_name: '',
  email_enabled: false,
  test_email: ''
})

// 广告配置表单
const adsForm = reactive({
  baidu: {
    appid: '',
    appkey: '',
    ad_unit_id: '',
    enabled: false
  },
  toutiao: {
    appid: '',
    appkey: '',
    ad_unit_id: '',
    enabled: false
  },
  admob: {
    app_id: '',
    ad_unit_id: '',
    enabled: false
  }
})

// 方法
const loadSystemConfig = async () => {
  try {
    const response = await apiEndpoints.admin.system.getSystemConfig()
    const configs = response.data || response
    configs.forEach(config => {
      if (Object.prototype.hasOwnProperty.call(systemForm, config.key)) {
        if (config.key === 'allowed_image_types') {
          systemForm[config.key] = config.value ? config.value.split(',') : []
        } else if (config.key === 'maintenance_mode' || config.key === 'allow_register') {
          systemForm[config.key] = config.value === 'true'
        } else {
          systemForm[config.key] = config.value || ''
        }
      }
    })
  } catch (error) {
    console.error('加载系统配置失败:', error)
  }
}

const loadEmailConfig = async () => {
  try {
    const response = await apiEndpoints.admin.system.getEmailConfig()
    const configs = response.data || response
    configs.forEach(config => {
      if (Object.prototype.hasOwnProperty.call(emailForm, config.key)) {
        if (config.key === 'smtp_port') {
          emailForm[config.key] = parseInt(config.value) || 587
        } else if (config.key === 'email_enabled') {
          emailForm[config.key] = config.value === 'true'
        } else {
          emailForm[config.key] = config.value || ''
        }
      }
    })
  } catch (error) {
    console.error('加载邮件配置失败:', error)
  }
}

const loadAdsConfig = async () => {
  try {
    const response = await apiEndpoints.admin.system.getAdsConfig()
    const configs = response.data || response
    
    // 百度广告配置
    const baiduConfig = configs.find(c => c.key === 'baidu_enabled')
    if (baiduConfig) {
      adsForm.baidu.enabled = baiduConfig.value === 'true'
    }
    
    const baiduAppid = configs.find(c => c.key === 'baidu_appid')
    if (baiduAppid) {
      adsForm.baidu.appid = baiduAppid.value || ''
    }
    
    const baiduAppkey = configs.find(c => c.key === 'baidu_appkey')
    if (baiduAppkey) {
      adsForm.baidu.appkey = baiduAppkey.value || ''
    }
    
    const baiduAdUnit = configs.find(c => c.key === 'baidu_ad_unit_id')
    if (baiduAdUnit) {
      adsForm.baidu.ad_unit_id = baiduAdUnit.value || ''
    }
    
    // 头条广告配置
    const toutiaoConfig = configs.find(c => c.key === 'toutiao_enabled')
    if (toutiaoConfig) {
      adsForm.toutiao.enabled = toutiaoConfig.value === 'true'
    }
    
    const toutiaoAppid = configs.find(c => c.key === 'toutiao_appid')
    if (toutiaoAppid) {
      adsForm.toutiao.appid = toutiaoAppid.value || ''
    }
    
    const toutiaoAppkey = configs.find(c => c.key === 'toutiao_appkey')
    if (toutiaoAppkey) {
      adsForm.toutiao.appkey = toutiaoAppkey.value || ''
    }
    
    const toutiaoAdUnit = configs.find(c => c.key === 'toutiao_ad_unit_id')
    if (toutiaoAdUnit) {
      adsForm.toutiao.ad_unit_id = toutiaoAdUnit.value || ''
    }
    
    // AdMob广告配置
    const admobConfig = configs.find(c => c.key === 'admob_enabled')
    if (admobConfig) {
      adsForm.admob.enabled = admobConfig.value === 'true'
    }
    
    const admobAppId = configs.find(c => c.key === 'admob_app_id')
    if (admobAppId) {
      adsForm.admob.app_id = admobAppId.value || ''
    }
    
    const admobAdUnit = configs.find(c => c.key === 'admob_ad_unit_id')
    if (admobAdUnit) {
      adsForm.admob.ad_unit_id = admobAdUnit.value || ''
    }
  } catch (error) {
    console.error('加载广告配置失败:', error)
  }
}

const saveSystemConfig = async () => {
  savingSystem.value = true
  try {
    const configs = []
    Object.keys(systemForm).forEach(key => {
      let value = systemForm[key]
      if (Array.isArray(value)) {
        value = value.join(',')
      } else if (typeof value === 'boolean') {
        value = value.toString()
      }
      configs.push({ key, value })
    })
    
    await apiEndpoints.admin.system.updateSystemConfig(configs)
    ElMessage.success('系统配置保存成功')
  } catch (error) {
    console.error('保存系统配置失败:', error)
    ElMessage.error('保存系统配置失败')
  } finally {
    savingSystem.value = false
  }
}

const saveEmailConfig = async () => {
  savingEmail.value = true
  try {
    const configs = []
    Object.keys(emailForm).forEach(key => {
      let value = emailForm[key]
      if (typeof value === 'boolean') {
        value = value.toString()
      }
      configs.push({ key, value })
    })
    
    await apiEndpoints.admin.system.updateEmailConfig(configs)
    ElMessage.success('邮件配置保存成功')
  } catch (error) {
    console.error('保存邮件配置失败:', error)
    ElMessage.error('保存邮件配置失败')
  } finally {
    savingEmail.value = false
  }
}

const testEmailConfig = async () => {
  if (!emailForm.test_email) {
    ElMessage.warning('请先输入测试邮箱地址')
    return
  }
  
  testingEmail.value = true
  try {
    await apiEndpoints.admin.system.testEmailConfig({
      test_email: emailForm.test_email
    })
    ElMessage.success('测试邮件发送成功')
  } catch (error) {
    console.error('测试邮件发送失败:', error)
    ElMessage.error('测试邮件发送失败')
  } finally {
    testingEmail.value = false
  }
}

const saveAdsConfig = async () => {
  savingAds.value = true
  try {
    const configs = [
      { key: 'baidu_enabled', value: adsForm.baidu.enabled.toString() },
      { key: 'baidu_appid', value: adsForm.baidu.appid },
      { key: 'baidu_appkey', value: adsForm.baidu.appkey },
      { key: 'baidu_ad_unit_id', value: adsForm.baidu.ad_unit_id },
      { key: 'toutiao_enabled', value: adsForm.toutiao.enabled.toString() },
      { key: 'toutiao_appid', value: adsForm.toutiao.appid },
      { key: 'toutiao_appkey', value: adsForm.toutiao.appkey },
      { key: 'toutiao_ad_unit_id', value: adsForm.toutiao.ad_unit_id },
      { key: 'admob_enabled', value: adsForm.admob.enabled.toString() },
      { key: 'admob_app_id', value: adsForm.admob.app_id },
      { key: 'admob_ad_unit_id', value: adsForm.admob.ad_unit_id }
    ]
    
    await apiEndpoints.admin.system.updateAdsConfig(configs)
    ElMessage.success('广告配置保存成功')
  } catch (error) {
    console.error('保存广告配置失败:', error)
    ElMessage.error('保存广告配置失败')
  } finally {
    savingAds.value = false
  }
}

// 生命周期
onMounted(() => {
  loadSystemConfig()
  loadEmailConfig()
  loadAdsConfig()
})
</script>

<style scoped>
.system-config {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.config-tabs {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.config-card {
  border: none;
  box-shadow: none;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.config-form {
  padding: 20px 0;
}

.form-tip {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}

/* 广告配置样式 */
.ads-config {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.ads-platform-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.ads-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.ads-form {
  padding: 20px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-config {
    padding: 10px;
  }
  
  .config-form {
    padding: 10px 0;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 5px;
  }
}
</style>