"""
简历相关枚举和基础模型
"""
from datetime import datetime
from typing import Dict, Any, List, Optional, Union, cast
from enum import Enum
from sqlalchemy import Column, Integer, String, Text, Boolean, JSON, ForeignKey, DateTime, Float, Table, Enum as SQLEnum, MetaData
from sqlalchemy.orm import relationship
from sqlalchemy.sql.schema import Column as SQLColumn
from ..database import db

class ResumeStatus(str, Enum):
    """简历状态枚举"""
    DRAFT = "draft"        # 草稿
    ACTIVE = "active"      # 活跃
    ARCHIVED = "archived"  # 归档

class TemplateCategory(str, Enum):
    """简历模板分类"""
    SIMPLE = "simple"      # 简约
    MODERN = "modern"      # 现代
    CREATIVE = "creative"  # 创意
    PROFESSIONAL = "professional"  # 专业

class IndustryType(str, Enum):
    """行业类型"""
    IT = "it"              # 互联网
    FINANCE = "finance"    # 金融
    EDUCATION = "education"  # 教育
    MARKETING = "marketing"  # 市场营销
    SALES = "sales"        # 销售
    DESIGN = "design"      # 设计
    OTHER = "other"        # 其他

# 创建一个新的MetaData实例
resume_metadata = MetaData()

# 辅助表定义
resume_projects = Table(
    'resume_projects',
    resume_metadata,
    Column('id', Integer, primary_key=True),
    Column('resume_id', Integer, ForeignKey('resumes.id'), nullable=False),
    Column('name', String(100), nullable=False),
    Column('role', String(100)),
    Column('description', Text),
    Column('technologies', JSON),
    Column('start_date', DateTime),
    Column('end_date', DateTime),
    Column('order', Integer, default=0),
    Column('created_at', DateTime, default=datetime.utcnow),
    Column('updated_at', DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
)

resume_skills = Table(
    'resume_skills',
    resume_metadata,
    Column('id', Integer, primary_key=True),
    Column('resume_id', Integer, ForeignKey('resumes.id'), nullable=False),
    Column('name', String(100), nullable=False),
    Column('level', Integer),
    Column('description', Text),
    Column('category', String(50)),
    Column('years_experience', Float),
    Column('order', Integer, default=0),
    Column('created_at', DateTime, default=datetime.utcnow),
    Column('updated_at', DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
)

resume_certifications = Table(
    'resume_certifications',
    resume_metadata,
    Column('id', Integer, primary_key=True),
    Column('resume_id', Integer, ForeignKey('resumes.id'), nullable=False),
    Column('name', String(100), nullable=False),
    Column('issuer', String(100)),
    Column('issue_date', DateTime),
    Column('expiry_date', DateTime),
    Column('credential_id', String(100)),
    Column('credential_url', String(500)),
    Column('description', Text),
    Column('gpa', Float),
    Column('order', Integer, default=0),
    Column('created_at', DateTime, default=datetime.utcnow),
    Column('updated_at', DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
)
