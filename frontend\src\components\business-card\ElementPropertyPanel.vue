<template>
  <div v-if="element" class="property-panel">
    <h4>属性面板</h4>
    <el-form label-width="60px" size="small">
      <el-form-item label="类型">
        <span>{{ element.type }}</span>
      </el-form-item>
      <el-form-item v-if="element.type==='text'" label="内容">
        <el-input v-model="element.config.text" @input="emitUpdate" />
      </el-form-item>
      <el-form-item v-if="element.type==='text'" label="字体">
        <el-select v-model="element.config.fontFamily" @change="emitUpdate">
          <el-option label="默认" value="" />
          <el-option label="微软雅黑" value="Microsoft YaHei" />
          <el-option label="PingFang SC" value="PingFang SC" />
          <el-option label="Arial" value="Arial" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="element.type==='text'" label="字号">
        <el-input-number v-model="element.config.fontSize" :min="8" :max="120" @change="emitUpdate" />
      </el-form-item>
      <el-form-item v-if="element.type==='text'" label="颜色">
        <el-color-picker v-model="element.config.fill" @change="emitUpdate" />
      </el-form-item>
      <el-form-item v-if="element.type==='image'" label="图片">
        <el-input v-model="element.config.src" @input="emitUpdate" placeholder="图片URL" />
      </el-form-item>
      <el-form-item label="X">
        <el-input-number v-model="element.config.x" :min="0" :max="900" @change="emitUpdate" />
      </el-form-item>
      <el-form-item label="Y">
        <el-input-number v-model="element.config.y" :min="0" :max="540" @change="emitUpdate" />
      </el-form-item>
      <el-form-item label="宽度" v-if="element.type==='image'">
        <el-input-number v-model="element.config.width" :min="10" :max="900" @change="emitUpdate" />
      </el-form-item>
      <el-form-item label="高度" v-if="element.type==='image'">
        <el-input-number v-model="element.config.height" :min="10" :max="540" @change="emitUpdate" />
      </el-form-item>
      <el-form-item label="层级">
        <el-input-number v-model="element.config.zIndex" :min="0" :max="100" @change="emitUpdate" />
      </el-form-item>
    </el-form>
  </div>
  <div v-if="isBatch">
    <h4>批量属性编辑</h4>
    <label>颜色 <input v-model="batchProps.color" type="color" /></label>
    <label>字体大小 <input v-model.number="batchProps.fontSize" type="number" /></label>
    <label>透明度 <input v-model.number="batchProps.opacity" type="range" min="0" max="1" step="0.01" /></label>
    <button @click="applyBatchProps">应用到所有选中</button>
  </div>
</template>
<script setup>
import { defineProps, defineEmits, watch, ref, computed } from 'vue'
const props = defineProps(['selectedElement', 'selectedIds', 'updateSelectedProps']);
const isBatch = computed(() => props.selectedIds && props.selectedIds.length > 1);
const batchProps = ref({ color: '', fontSize: '', opacity: 1 });
function applyBatchProps() {
  props.updateSelectedProps(batchProps.value);
}
const emit = defineEmits(['update'])
const emitUpdate = () => emit('update', props.element)
watch(() => props.element, emitUpdate)
</script>
<style scoped>
.property-panel {
  width: 260px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px #0001;
  padding: 18px 16px;
  margin-left: 24px;
}
</style> 