<template>
  <div class="modal-bg" @click.self="$emit('close')">
    <div class="modal">
      <h3>OCR文字识别</h3>
      <div class="options">
        <label>识别语言: <select v-model="options.lang"><option value="chi_sim">中文</option><option value="eng">English</option></select></label>
        <label>导出格式: <select v-model="options.format"><option value="txt">TXT</option><option value="json">JSON</option></select></label>
      </div>
      <div v-if="progress > 0 && progress < 100" class="progress-bar">
        <div class="progress" :style="{width: progress + '%'}"></div>
      </div>
      <div v-if="ocrResult" class="result-preview">
        <textarea readonly v-model="ocrResult"></textarea>
      </div>
      <div class="modal-actions">
        <button class="btn" :disabled="recognizing" @click="startRecognition">开始识别</button>
        <button class="btn" :disabled="!ocrResult" @click="copyResult">复制结果</button>
        <button class="btn" :disabled="!ocrResult" @click="downloadResult">下载结果</button>
        <button class="btn" @click="$emit('close')">关闭</button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from 'vue';

defineProps(['pages', 'file']);
defineEmits(['close']);

const options = ref({ lang: 'chi_sim', format: 'txt' });
const recognizing = ref(false);
const progress = ref(0);
const ocrResult = ref('');

async function startRecognition() {
  recognizing.value = true;
  progress.value = 10;
  // ... (API call logic with progress update)
}
function copyResult() { /* ... */ }
function downloadResult() { /* ... */ }
</script>
<style scoped>
.modal-bg { position: fixed; top:0; left:0; right:0; bottom:0; background:rgba(0,0,0,0.2); display:flex; align-items:center; justify-content:center; z-index:1000; }
.modal { background:#fff; border-radius:8px; padding:2rem; min-width:300px; }
.btn { margin: 1rem 1rem 0 0; }
pre { background: #f5f5f5; padding: 1rem; border-radius: 6px; }
.options { margin-bottom: 1.5rem; }
.progress-bar { margin-bottom: 1.5rem; height: 8px; background-color: #e0e0e0; border-radius: 4px; overflow: hidden; }
.progress { height: 100%; background-color: #4CAF50; border-radius: 4px; transition: width 0.3s ease-in-out; }
.result-preview textarea { width: 100%; height: 200px; padding: 1rem; border: 1px solid #ccc; border-radius: 6px; font-size: 0.9rem; line-height: 1.5; }
.modal-actions { display: flex; justify-content: flex-end; gap: 1rem; margin-top: 1.5rem; }
</style> 