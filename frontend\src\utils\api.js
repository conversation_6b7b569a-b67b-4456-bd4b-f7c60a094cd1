/**
 * API工具函数
 * 用于统一处理API地址生成和错误处理
 */

/**
 * 获取完整的API基础URL
 */
export function getApiBaseUrl() {
  return process.env.VUE_APP_API_BASE_URL || 'http://localhost:5000';
}

/**
 * 拼接完整的API URL
 * @param {string} path - API路径
 * @returns {string} 完整的API URL
 */
export function getFullApiUrl(path) {
  const baseUrl = getApiBaseUrl();
  // 确保路径以/开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${normalizedPath}`;
}

/**
 * 处理图片URL
 * @param {string} url - 相对图片URL
 * @returns {string} 完整的图片URL
 */
export function getImageUrl(url) {
  if (!url) return '';
  if (url.startsWith('http')) return url;
  return `${getApiBaseUrl()}${url}`;
}

/**
 * 处理静态资源URL
 * @param {string} url - 相对静态资源URL
 * @returns {string} 完整的静态资源URL
 */
export function getStaticUrl(url) {
  return getImageUrl(url);
}

/**
 * 处理API错误
 * @param {Error} error - 错误对象
 * @param {string} defaultMessage - 默认错误消息
 */
export function handleApiError(error, defaultMessage = '操作失败') {
  if (error.response) {
    const { status, data } = error.response;
    switch (status) {
      case 400:
        console.error('请求错误:', data.message || defaultMessage);
        break;
      case 401:
        console.error('未授权，请重新登录');
        break;
      case 403:
        console.error('没有权限执行此操作');
        break;
      case 404:
        console.error('请求的资源不存在');
        break;
      case 500:
        console.error('服务器内部错误');
        break;
      default:
        console.error('未知错误:', data.message || defaultMessage);
    }
  } else if (error.request) {
    console.error('网络连接失败，请检查网络设置');
  } else {
    console.error('请求配置错误:', error.message);
  }
  
  // 将错误抛出以便上层处理
  throw error;
}