from datetime import datetime
import os
import uuid
from PIL import Image as PILImage
from app import db

class Image(db.Model):
    """图像文件模型"""
    __tablename__ = 'images'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False, index=True)
    file_id = db.Column(db.String(100), unique=True, nullable=False, index=True)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    thumbnail_path = db.Column(db.String(500))
    file_size = db.Column(db.BigInteger, nullable=False)
    width = db.Column(db.Integer)
    height = db.Column(db.Integer)
    mime_type = db.Column(db.String(100), nullable=False)
    upload_source = db.Column(db.String(50), default='web')  # web/miniprogram/app
    status = db.Column(db.SmallInteger, default=1)  # 1:正常 2:删除
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    processing_tasks = db.relationship('ProcessingTask', backref='image', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, **kwargs):
        super(Image, self).__init__(**kwargs)
        if not self.file_id:
            self.file_id = self.generate_file_id()
    
    @staticmethod
    def generate_file_id():
        """生成唯一文件ID"""
        return str(uuid.uuid4()).replace('-', '')
    
    def get_file_extension(self):
        """获取文件扩展名"""
        return os.path.splitext(self.original_filename)[1].lower()
    
    def get_full_path(self):
        """获取完整文件路径"""
        from flask import current_app
        # 使用uploads作为存储目录
        return os.path.join(current_app.config['UPLOAD_FOLDER'], self.file_path)
    
    def get_thumbnail_path(self):
        """获取缩略图路径"""
        if self.thumbnail_path:
            from flask import current_app
            # 使用uploads作为存储目录
            return os.path.join(current_app.config['UPLOAD_FOLDER'], self.thumbnail_path)
        return None
    
    def get_url(self):
        """获取图片访问URL"""
        from flask import current_app
        # 确保使用正斜杠替换反斜杠
        file_path = self.file_path.replace('\\', '/')
        # 对于简历照片，使用/static/前缀
        if self.upload_source == 'resume':
            return f"/static/{file_path}"
        # 对于编辑器上传的照片
        elif self.upload_source == 'editor':
            return f"/uploads/{file_path}"
        # 对于处理完成的照片
        elif self.upload_source == 'processed':
            return f"/static/{file_path}"
        return f"/uploads/{file_path}"
    
    def get_thumbnail_url(self):
        """获取缩略图URL"""
        if self.thumbnail_path:
            # 确保使用正斜杠替换反斜杠
            thumbnail_path = self.thumbnail_path.replace('\\', '/')
            return f"/static/{thumbnail_path}"
        return self.get_url()
    
    def create_thumbnail(self):
        """创建缩略图"""
        try:
            from flask import current_app
            
            # 打开原图
            full_path = self.get_full_path()
            if not os.path.exists(full_path):
                current_app.logger.error(f"原图不存在: {full_path}")
                return False
            
            with PILImage.open(full_path) as img:
                # 获取缩略图尺寸
                thumbnail_size = current_app.config.get('THUMBNAIL_SIZE', (300, 300))
                
                # 创建缩略图
                img.thumbnail(thumbnail_size, PILImage.Resampling.LANCZOS)
                
                # 生成缩略图路径 - 使用uploads/thumbnails目录
                thumbnail_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'thumbnails')
                os.makedirs(thumbnail_dir, exist_ok=True)
                
                # 保存缩略图
                thumbnail_filename = f"{self.file_id}_thumb{self.get_file_extension()}"
                thumbnail_path = os.path.join(thumbnail_dir, thumbnail_filename)
                img.save(thumbnail_path, quality=85, optimize=True)
                
                # 更新缩略图路径
                self.thumbnail_path = f"thumbnails/{thumbnail_filename}"
                current_app.logger.info(f"缩略图创建成功: {thumbnail_path}")
                return True
                
        except Exception as e:
            from flask import current_app
            current_app.logger.error(f"创建缩略图失败: {e}")
            return False
    
    def extract_image_info(self):
        """提取图片信息"""
        try:
            full_path = self.get_full_path()
            if not os.path.exists(full_path):
                return False
            
            with PILImage.open(full_path) as img:
                self.width = img.width
                self.height = img.height
                
                # 更新文件大小
                self.file_size = os.path.getsize(full_path)
                
                return True
                
        except Exception as e:
            print(f"提取图片信息失败: {e}")
            return False
    
    def delete_files(self):
        """删除相关文件"""
        files_to_delete = []
        
        # 添加原文件
        full_path = self.get_full_path()
        if os.path.exists(full_path):
            files_to_delete.append(full_path)
        
        # 添加缩略图
        thumbnail_path = self.get_thumbnail_path()
        if thumbnail_path and os.path.exists(thumbnail_path):
            files_to_delete.append(thumbnail_path)
        
        # 删除文件
        for file_path in files_to_delete:
            try:
                os.remove(file_path)
            except Exception as e:
                print(f"删除文件失败 {file_path}: {e}")
    
    def get_processing_history(self):
        """获取处理历史"""
        return self.processing_tasks.filter_by(status='completed').order_by(
            self.processing_tasks.property.mapper.class_.created_at.desc()
        ).all()
    
    def to_dict(self, include_paths=False):
        """转换为字典"""
        data = {
            'id': self.id,
            'file_id': self.file_id,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'width': self.width,
            'height': self.height,
            'mime_type': self.mime_type,
            'upload_source': self.upload_source,
            'status': self.status,
            'url': self.get_url(),
            'thumbnail_url': self.get_thumbnail_url(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
        
        if include_paths:
            data.update({
                'file_path': self.file_path,
                'thumbnail_path': self.thumbnail_path,
                'full_path': self.get_full_path()
            })
        
        return data
    
    def __repr__(self):
        return f'<Image {self.file_id}: {self.original_filename}>'