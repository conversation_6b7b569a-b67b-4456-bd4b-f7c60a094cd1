#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级美颜处理器 - 全面的人像美化算法
包含：脸部校正、肩膀平衡、眉毛对称、去痣、光线调整等功能
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import logging
from typing import Tuple, List, Dict, Optional, Any
import math
import gc
import time

logger = logging.getLogger(__name__)

class AdvancedBeautyProcessor:
    """高级美颜处理器"""
    
    def __init__(self):
        """初始化美颜处理器"""
        self.face_cascade = None
        self.profile_cascade = None
        self.eye_cascade = None
        self.mouth_cascade = None
        self._cascades_loaded = False
        self._last_use_time = 0
        self._cleanup_interval = 300  # 5分钟清理一次
        self.logger = logger
    
    def _lazy_load_cascades(self):
        """懒加载级联分类器"""
        if self._cascades_loaded:
            return
        
        current_time = time.time()
        
        # 如果距离上次使用超过清理间隔，先清理内存
        if current_time - self._last_use_time > self._cleanup_interval:
            self._cleanup_memory()
        
        try:
            # 懒加载人脸检测器
            if self.face_cascade is None:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            
            if self.profile_cascade is None:
                self.profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
            
            # 懒加载眼部检测器
            if self.eye_cascade is None:
                self.eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
            
            # 懒加载嘴部检测器（如果可用）
            if self.mouth_cascade is None:
                try:
                    self.mouth_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_smile.xml')
                except:
                    self.mouth_cascade = None
            
            self._cascades_loaded = True
            self._last_use_time = current_time
            self.logger.info("级联分类器懒加载成功")
            
        except Exception as e:
            self.logger.error(f"级联分类器懒加载失败: {e}")
            self._cleanup_memory()
    
    def _cleanup_memory(self):
        """清理内存"""
        try:
            # 清理级联分类器
            if self.face_cascade is not None:
                del self.face_cascade
                self.face_cascade = None
            
            if self.profile_cascade is not None:
                del self.profile_cascade
                self.profile_cascade = None
            
            if self.eye_cascade is not None:
                del self.eye_cascade
                self.eye_cascade = None
            
            if self.mouth_cascade is not None:
                del self.mouth_cascade
                self.mouth_cascade = None
            
            self._cascades_loaded = False
            
            # 强制垃圾回收
            gc.collect()
            
            self.logger.info("美颜处理器内存清理完成")
            
        except Exception as e:
            self.logger.warning(f"美颜处理器内存清理失败: {e}")
    
    def process(self, img: Image.Image, params: Dict[str, Any]) -> Image.Image:
        """主处理函数"""
        try:
            self.logger.info("开始高级美颜处理")
            
            # 懒加载级联分类器
            self._lazy_load_cascades()
            
            # 转换为OpenCV格式
            img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
            
            # 1. 人脸检测和分析
            face_data = self._detect_and_analyze_face(img_cv)
            
            if face_data['detected']:
                # 2. 脸部校正（纠正歪斜）
                if params.get('face_alignment', True):
                    img_cv = self._correct_face_alignment(img_cv, face_data)
                
                # 3. 眉毛对称化
                if params.get('eyebrow_symmetry', True):
                    img_cv = self._correct_eyebrow_symmetry(img_cv, face_data)
                
                # 4. 嘴部校正
                if params.get('mouth_correction', True):
                    img_cv = self._correct_mouth_alignment(img_cv, face_data)
                
                # 5. 肩膀平衡
                if params.get('shoulder_balance', True):
                    img_cv = self._balance_shoulders(img_cv, face_data)
                
                # 6. 去痣处理
                if params.get('blemish_removal', True):
                    img_cv = self._remove_blemishes(img_cv, face_data)
            
            # 7. 光线和色彩调整
            if params.get('lighting_enhancement', True):
                img_cv = self._enhance_lighting_and_color(img_cv)
            
            # 8. 清晰度和亮度优化
            if params.get('clarity_enhancement', True):
                img_cv = self._enhance_clarity_and_brightness(img_cv)
            
            # 转换回PIL格式
            result_img = Image.fromarray(cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB))
            
            self.logger.info("高级美颜处理完成")
            return result_img
            
        except Exception as e:
            self.logger.error(f"美颜处理失败: {e}")
            # 处理失败时清理内存
            self._cleanup_memory()
            return img
        finally:
            # 清理临时变量
            if 'img_cv' in locals():
                del img_cv
            if 'face_data' in locals():
                del face_data
            if 'result_img' in locals():
                del result_img
            gc.collect()
    
    def _detect_and_analyze_face(self, img_cv: np.ndarray) -> Dict[str, Any]:
        """检测和分析人脸特征"""
        height, width = img_cv.shape[:2]
        gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
        
        face_data = {
            'detected': False,
            'face': None,
            'eyes': [],
            'mouth': None,
            'angle': 0,
            'center': None,
            'landmarks': {}
        }
        
        try:
            # 多角度人脸检测
            faces = self.face_cascade.detectMultiScale(gray, scaleFactor=1.05, minNeighbors=3, 
                                                     minSize=(30, 30), maxSize=(int(width*0.8), int(height*0.8)))
            
            if len(faces) == 0:
                faces = self.profile_cascade.detectMultiScale(gray, scaleFactor=1.05, minNeighbors=3,
                                                           minSize=(30, 30), maxSize=(int(width*0.8), int(height*0.8)))
            
            if len(faces) > 0:
                # 选择最大的人脸
                face = max(faces, key=lambda f: f[2] * f[3])
                fx, fy, fw, fh = face
                
                face_data['detected'] = True
                face_data['face'] = face
                face_data['center'] = (fx + fw//2, fy + fh//2)
                
                # 检测眼部
                face_roi_gray = gray[fy:fy+fh, fx:fx+fw]
                eyes = self.eye_cascade.detectMultiScale(face_roi_gray, scaleFactor=1.1, minNeighbors=3)
                
                # 转换眼部坐标到全图坐标系
                for (ex, ey, ew, eh) in eyes:
                    face_data['eyes'].append((fx+ex, fy+ey, ew, eh))
                
                # 检测嘴部
                if self.mouth_cascade:
                    mouths = self.mouth_cascade.detectMultiScale(face_roi_gray, scaleFactor=1.1, minNeighbors=3)
                    if len(mouths) > 0:
                        mx, my, mw, mh = mouths[0]
                        face_data['mouth'] = (fx+mx, fy+my, mw, mh)
                
                # 计算脸部倾斜角度
                if len(face_data['eyes']) >= 2:
                    face_data['angle'] = self._calculate_face_angle(face_data['eyes'])
                
                self.logger.info(f"检测到人脸: {face}, 眼部数量: {len(face_data['eyes'])}")
            
        except Exception as e:
            self.logger.warning(f"人脸分析失败: {e}")
        
        return face_data
    
    def _calculate_face_angle(self, eyes: List[Tuple[int, int, int, int]]) -> float:
        """计算脸部倾斜角度"""
        if len(eyes) < 2:
            return 0
        
        # 选择最上方的两个眼部
        eyes_sorted = sorted(eyes, key=lambda e: e[1])[:2]
        
        # 计算眼部中心点
        eye1_center = (eyes_sorted[0][0] + eyes_sorted[0][2]//2, eyes_sorted[0][1] + eyes_sorted[0][3]//2)
        eye2_center = (eyes_sorted[1][0] + eyes_sorted[1][2]//2, eyes_sorted[1][1] + eyes_sorted[1][3]//2)
        
        # 计算角度
        dx = eye2_center[0] - eye1_center[0]
        dy = eye2_center[1] - eye1_center[1]
        
        if dx == 0:
            return 0
        
        angle = math.degrees(math.atan2(dy, dx))
        return angle
    
    def _correct_face_alignment(self, img_cv: np.ndarray, face_data: Dict[str, Any]) -> np.ndarray:
        """纠正脸部歪斜"""
        if not face_data['detected'] or abs(face_data['angle']) < 2:
            return img_cv
        
        try:
            height, width = img_cv.shape[:2]
            center = face_data['center']
            angle = -face_data['angle']  # 反向旋转纠正
            
            # 限制旋转角度
            angle = max(-15, min(15, angle))
            
            # 创建旋转矩阵
            center_tuple = (int(center[0]), int(center[1]))
            rotation_matrix = cv2.getRotationMatrix2D(center_tuple, angle, 1.0)
            
            # 应用旋转
            rotated_img = cv2.warpAffine(img_cv, rotation_matrix, (width, height), 
                                       flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT)
            
            self.logger.info(f"脸部校正：旋转角度 {angle:.2f}°")
            return rotated_img
            
        except Exception as e:
            self.logger.warning(f"脸部校正失败: {e}")
            return img_cv
    
    def _correct_eyebrow_symmetry(self, img_cv: np.ndarray, face_data: Dict[str, Any]) -> np.ndarray:
        """纠正眉毛不对称"""
        if not face_data['detected'] or len(face_data['eyes']) < 2:
            return img_cv
        
        try:
            fx, fy, fw, fh = face_data['face']
            
            # 估算眉毛区域（眼部上方）
            eyebrow_height = int(fh * 0.15)
            eyebrow_y_start = fy - eyebrow_height
            eyebrow_y_end = fy + int(fh * 0.3)
            
            if eyebrow_y_start < 0:
                return img_cv
            
            # 提取眉毛区域
            eyebrow_region = img_cv[eyebrow_y_start:eyebrow_y_end, fx:fx+fw].copy()
            
            # 分析左右眉毛的高度差异
            left_eyebrow = eyebrow_region[:, :fw//2]
            right_eyebrow = eyebrow_region[:, fw//2:]
            
            # 使用边缘检测找到眉毛轮廓
            gray_left = cv2.cvtColor(left_eyebrow, cv2.COLOR_BGR2GRAY)
            gray_right = cv2.cvtColor(right_eyebrow, cv2.COLOR_BGR2GRAY)
            
            edges_left = cv2.Canny(gray_left, 50, 150)
            edges_right = cv2.Canny(gray_right, 50, 150)
            
            # 找到眉毛的主要轮廓
            contours_left, _ = cv2.findContours(edges_left, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            contours_right, _ = cv2.findContours(edges_right, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours_left and contours_right:
                # 计算眉毛高度差异并进行微调
                left_top = min([c[:, :, 1].min() for c in contours_left])
                right_top = min([c[:, :, 1].min() for c in contours_right])
                
                height_diff = left_top - right_top
                
                if abs(height_diff) > 2:  # 只有明显差异时才调整
                    # 轻微调整较高的眉毛
                    if height_diff > 0:  # 左眉高，调整右眉
                        self._adjust_eyebrow_region(img_cv, fx + fw//2, fx + fw, 
                                                  eyebrow_y_start, eyebrow_y_end, height_diff//2)
                    else:  # 右眉高，调整左眉
                        self._adjust_eyebrow_region(img_cv, fx, fx + fw//2, 
                                                  eyebrow_y_start, eyebrow_y_end, -height_diff//2)
                    
                    self.logger.info(f"眉毛对称化：调整高度差 {height_diff} 像素")
            
            return img_cv
            
        except Exception as e:
            self.logger.warning(f"眉毛对称化失败: {e}")
            return img_cv
    
    def _adjust_eyebrow_region(self, img_cv: np.ndarray, x1: int, x2: int, y1: int, y2: int, offset: int):
        """调整眉毛区域"""
        if offset == 0:
            return
        
        try:
            region = img_cv[y1:y2, x1:x2].copy()
            height, width = region.shape[:2]
            
            # 创建变换矩阵进行微调
            transform_matrix = np.float32([[1, 0, 0], [0, 1, offset]])
            adjusted_region = cv2.warpAffine(region, transform_matrix, (width, height), 
                                           borderMode=cv2.BORDER_REFLECT)
            
            # 使用渐变混合避免突兀变化
            alpha = 0.3  # 调整强度
            img_cv[y1:y2, x1:x2] = cv2.addWeighted(img_cv[y1:y2, x1:x2], 1-alpha, 
                                                 adjusted_region, alpha, 0)
            
        except Exception as e:
            self.logger.warning(f"眉毛区域调整失败: {e}")
    
    def _correct_mouth_alignment(self, img_cv: np.ndarray, face_data: Dict[str, Any]) -> np.ndarray:
        """纠正嘴部歪斜"""
        if not face_data['detected'] or not face_data['mouth']:
            return img_cv
        
        try:
            fx, fy, fw, fh = face_data['face']
            mx, my, mw, mh = face_data['mouth']
            
            # 分析嘴部区域
            mouth_region = img_cv[my:my+mh, mx:mx+mw].copy()
            gray_mouth = cv2.cvtColor(mouth_region, cv2.COLOR_BGR2GRAY)
            
            # 使用边缘检测找到嘴唇轮廓
            edges = cv2.Canny(gray_mouth, 30, 100)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if contours:
                # 找到最大轮廓（可能是嘴唇）
                mouth_contour = max(contours, key=cv2.contourArea)
                
                # 计算轮廓的方向
                if len(mouth_contour) > 5:
                    ellipse = cv2.fitEllipse(mouth_contour)
                    angle = ellipse[2]
                    
                    # 如果嘴部明显倾斜，进行微调
                    if abs(angle - 90) > 5:  # 90度表示水平
                        correction_angle = -(angle - 90) * 0.3  # 轻微调整
                        
                        # 创建旋转矩阵
                        center = (int(mw//2), int(mh//2))
                        rotation_matrix = cv2.getRotationMatrix2D(center, correction_angle, 1.0)
                        
                        # 旋转嘴部区域
                        corrected_mouth = cv2.warpAffine(mouth_region, rotation_matrix, (mw, mh),
                                                       borderMode=cv2.BORDER_REFLECT)
                        
                        # 渐变混合
                        alpha = 0.4
                        img_cv[my:my+mh, mx:mx+mw] = cv2.addWeighted(
                            img_cv[my:my+mh, mx:mx+mw], 1-alpha, corrected_mouth, alpha, 0)
                        
                        self.logger.info(f"嘴部校正：调整角度 {correction_angle:.2f}°")
            
            return img_cv
            
        except Exception as e:
            self.logger.warning(f"嘴部校正失败: {e}")
            return img_cv
    
    def _balance_shoulders(self, img_cv: np.ndarray, face_data: Dict[str, Any]) -> np.ndarray:
        """平衡肩膀高度"""
        if not face_data['detected']:
            return img_cv
        
        try:
            height, width = img_cv.shape[:2]
            fx, fy, fw, fh = face_data['face']
            
            # 估算肩膀区域（脸部下方）
            shoulder_y_start = fy + int(fh * 1.2)
            shoulder_y_end = min(height, fy + int(fh * 2.5))
            
            if shoulder_y_start >= height:
                return img_cv
            
            # 扩展检测区域
            shoulder_width = int(fw * 2.5)
            shoulder_x_start = max(0, fx + fw//2 - shoulder_width//2)
            shoulder_x_end = min(width, fx + fw//2 + shoulder_width//2)
            
            # 提取肩膀区域
            shoulder_region = img_cv[shoulder_y_start:shoulder_y_end, shoulder_x_start:shoulder_x_end]
            gray_shoulder = cv2.cvtColor(shoulder_region, cv2.COLOR_BGR2GRAY)
            
            # 使用边缘检测找到肩膀轮廓
            edges = cv2.Canny(gray_shoulder, 50, 150)
            
            # 分析左右肩膀的高度
            region_height, region_width = shoulder_region.shape[:2]
            left_shoulder = edges[:, :region_width//2]
            right_shoulder = edges[:, region_width//2:]
            
            # 找到左右肩膀的最高点
            left_peaks = []
            right_peaks = []
            
            for y in range(region_height):
                if np.sum(left_shoulder[y, :]) > 0:
                    left_peaks.append(y)
                    break
            
            for y in range(region_height):
                if np.sum(right_shoulder[y, :]) > 0:
                    right_peaks.append(y)
                    break
            
            if left_peaks and right_peaks:
                height_diff = left_peaks[0] - right_peaks[0]
                
                if abs(height_diff) > 5:  # 明显的高度差异
                    # 轻微调整较低的肩膀
                    adjustment = height_diff // 3  # 温和调整
                    
                    if height_diff > 0:  # 左肩低，调整左肩
                        self._adjust_shoulder_region(img_cv, shoulder_x_start, shoulder_x_start + region_width//2,
                                                   shoulder_y_start, shoulder_y_end, -adjustment)
                    else:  # 右肩低，调整右肩
                        self._adjust_shoulder_region(img_cv, shoulder_x_start + region_width//2, shoulder_x_end,
                                                   shoulder_y_start, shoulder_y_end, adjustment)
                    
                    self.logger.info(f"肩膀平衡：调整高度差 {height_diff} 像素")
            
            return img_cv
            
        except Exception as e:
            self.logger.warning(f"肩膀平衡失败: {e}")
            return img_cv
    
    def _adjust_shoulder_region(self, img_cv: np.ndarray, x1: int, x2: int, y1: int, y2: int, offset: int):
        """调整肩膀区域"""
        if offset == 0:
            return
        
        try:
            region = img_cv[y1:y2, x1:x2].copy()
            height, width = region.shape[:2]
            
            # 创建变换矩阵
            transform_matrix = np.float32([[1, 0, 0], [0, 1, offset]])
            adjusted_region = cv2.warpAffine(region, transform_matrix, (width, height),
                                           borderMode=cv2.BORDER_REFLECT)
            
            # 渐变混合
            alpha = 0.25  # 轻微调整
            img_cv[y1:y2, x1:x2] = cv2.addWeighted(img_cv[y1:y2, x1:x2], 1-alpha,
                                                 adjusted_region, alpha, 0)
            
        except Exception as e:
            self.logger.warning(f"肩膀区域调整失败: {e}")
    
    def _remove_blemishes(self, img_cv: np.ndarray, face_data: Dict[str, Any]) -> np.ndarray:
        """去除痣和瑕疵"""
        if not face_data['detected']:
            return img_cv
        
        try:
            fx, fy, fw, fh = face_data['face']
            
            # 扩展面部区域用于瑕疵检测
            padding = int(min(fw, fh) * 0.2)
            face_x1 = max(0, fx - padding)
            face_y1 = max(0, fy - padding)
            face_x2 = min(img_cv.shape[1], fx + fw + padding)
            face_y2 = min(img_cv.shape[0], fy + fh + padding)
            
            # 提取扩展的面部区域
            face_region = img_cv[face_y1:face_y2, face_x1:face_x2].copy()
            
            # 转换到LAB颜色空间进行更好的瑕疵检测
            lab_face = cv2.cvtColor(face_region, cv2.COLOR_BGR2LAB)
            gray_face = cv2.cvtColor(face_region, cv2.COLOR_BGR2GRAY)
            
            # 使用多种方法检测瑕疵
            blemishes = self._detect_blemishes(gray_face, lab_face)
            
            # 修复检测到的瑕疵
            for blemish in blemishes:
                x, y, radius = blemish
                if radius > 1:
                    face_region = self._heal_blemish(face_region, x, y, radius)
            
            # 将处理后的区域放回原图
            img_cv[face_y1:face_y2, face_x1:face_x2] = face_region
            
            if blemishes:
                self.logger.info(f"去痣处理：修复了 {len(blemishes)} 个瑕疵")
            
            return img_cv
            
        except Exception as e:
            self.logger.warning(f"去痣处理失败: {e}")
            return img_cv
    
    def _detect_blemishes(self, gray_face: np.ndarray, lab_face: np.ndarray) -> List[Tuple[int, int, int]]:
        """检测面部瑕疵"""
        blemishes = []
        
        try:
            height, width = gray_face.shape
            
            # 方法1：基于局部对比度的检测
            blurred = cv2.GaussianBlur(gray_face, (5, 5), 0)
            contrast = cv2.absdiff(gray_face, blurred)
            
            # 阈值化找到高对比度区域
            _, thresh = cv2.threshold(contrast, 15, 255, cv2.THRESH_BINARY)
            
            # 形态学操作去除噪声
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
            
            # 找到轮廓
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in contours:
                area = cv2.contourArea(contour)
                if 3 <= area <= 100:  # 合理的瑕疵大小
                    # 计算外接圆
                    (x, y), radius = cv2.minEnclosingCircle(contour)
                    x, y = int(x), int(y)
                    radius = int(radius)
                    
                    # 验证是否为真正的瑕疵
                    if self._validate_blemish(gray_face, lab_face, x, y, radius):
                        blemishes.append((x, y, radius))
            
            # 方法2：基于颜色差异的检测（针对痣等深色瑕疵）
            l_channel = lab_face[:, :, 0]
            l_blur = cv2.GaussianBlur(l_channel, (9, 9), 0)
            dark_spots = cv2.absdiff(l_channel, l_blur)
            
            _, dark_thresh = cv2.threshold(dark_spots, 20, 255, cv2.THRESH_BINARY)
            dark_contours, _ = cv2.findContours(dark_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            for contour in dark_contours:
                area = cv2.contourArea(contour)
                if 2 <= area <= 50:
                    (x, y), radius = cv2.minEnclosingCircle(contour)
                    x, y = int(x), int(y)
                    radius = int(radius)
                    
                    # 避免重复检测
                    if not any(abs(x-bx) < radius*2 and abs(y-by) < radius*2 for bx, by, _ in blemishes):
                        if self._validate_blemish(gray_face, lab_face, x, y, radius):
                            blemishes.append((x, y, radius))
            
        except Exception as e:
            self.logger.warning(f"瑕疵检测失败: {e}")
        
        return blemishes
    
    def _validate_blemish(self, gray_face: np.ndarray, lab_face: np.ndarray, x: int, y: int, radius: int) -> bool:
        """验证检测到的区域是否为瑕疵"""
        try:
            height, width = gray_face.shape
            
            # 确保区域在图像内
            if x - radius < 0 or x + radius >= width or y - radius < 0 or y + radius >= height:
                return False
            
            # 提取瑕疵区域和周围区域
            blemish_region = gray_face[y-radius:y+radius+1, x-radius:x+radius+1]
            
            # 扩大周围区域用于比较
            expand_radius = radius * 2
            surround_x1 = max(0, x - expand_radius)
            surround_y1 = max(0, y - expand_radius)
            surround_x2 = min(width, x + expand_radius)
            surround_y2 = min(height, y + expand_radius)
            
            surrounding_region = gray_face[surround_y1:surround_y2, surround_x1:surround_x2]
            
            # 计算对比度
            blemish_mean = np.mean(blemish_region)
            surrounding_mean = np.mean(surrounding_region)
            
            # 瑕疵应该明显深于或浅于周围区域
            contrast_ratio = abs(blemish_mean - surrounding_mean) / (surrounding_mean + 1)
            
            return contrast_ratio > 0.1  # 至少10%的对比度差异
            
        except Exception as e:
            return False
    
    def _heal_blemish(self, img_region: np.ndarray, x: int, y: int, radius: int) -> np.ndarray:
        """修复单个瑕疵"""
        try:
            # 使用OpenCV的inpaint函数进行修复
            mask = np.zeros(img_region.shape[:2], dtype=np.uint8)
            cv2.circle(mask, (x, y), radius, 255, -1)
            
            # 应用修复算法
            healed = cv2.inpaint(img_region, mask, radius*2, cv2.INPAINT_TELEA)
            
            # 渐变混合，避免过度修复
            alpha = 0.7
            result = cv2.addWeighted(img_region, 1-alpha, healed, alpha, 0)
            
            return result
            
        except Exception as e:
            self.logger.warning(f"瑕疵修复失败: {e}")
            return img_region
    
    def _enhance_lighting_and_color(self, img_cv: np.ndarray) -> np.ndarray:
        """增强光线和色彩"""
        try:
            # 转换到LAB颜色空间进行光线调整
            lab = cv2.cvtColor(img_cv, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            
            # 1. 改善亮度分布
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l_enhanced = clahe.apply(l)
            
            # 2. 轻微增加整体亮度
            l_enhanced = cv2.add(l_enhanced, 10)
            l_enhanced = np.clip(l_enhanced, 0, 255)
            
            # 3. 增强色彩饱和度
            enhanced_lab = cv2.merge([l_enhanced, a, b])
            enhanced_bgr = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
            
            # 4. 转到HSV进行饱和度调整
            hsv = cv2.cvtColor(enhanced_bgr, cv2.COLOR_BGR2HSV)
            h, s, v = cv2.split(hsv)
            
            # 适度增加饱和度
            s_enhanced = cv2.multiply(s, 1.15)
            s_enhanced = np.clip(s_enhanced, 0, 255)
            
            enhanced_hsv = cv2.merge([h, s_enhanced, v])
            result = cv2.cvtColor(enhanced_hsv, cv2.COLOR_HSV2BGR)
            
            # 5. 温度调整（偏暖色调）
            result = self._adjust_color_temperature(result, 1.05)
            
            self.logger.info("光线和色彩增强完成")
            return result
            
        except Exception as e:
            self.logger.warning(f"光线色彩增强失败: {e}")
            return img_cv
    
    def _adjust_color_temperature(self, img_cv: np.ndarray, temperature: float) -> np.ndarray:
        """调整色温"""
        try:
            # 创建色温调整矩阵
            if temperature > 1.0:  # 暖色调
                # 增加红色和黄色
                temp_matrix = np.array([
                    [1.0, 0.0, 0.0],
                    [0.0, 1.0, 0.0],
                    [0.0, 0.0, 0.9]
                ]) * temperature
            else:  # 冷色调
                # 增加蓝色
                temp_matrix = np.array([
                    [0.9, 0.0, 0.0],
                    [0.0, 1.0, 0.0],
                    [0.0, 0.0, 1.0]
                ]) / temperature
            
            # 应用色温调整
            result = cv2.transform(img_cv, temp_matrix)
            result = np.clip(result, 0, 255).astype(np.uint8)
            
            return result
            
        except Exception as e:
            self.logger.warning(f"色温调整失败: {e}")
            return img_cv
    
    def _enhance_clarity_and_brightness(self, img_cv: np.ndarray) -> np.ndarray:
        """增强清晰度和亮度"""
        try:
            # 1. 锐化处理
            kernel_sharpen = np.array([[-1, -1, -1],
                                     [-1, 9, -1],
                                     [-1, -1, -1]])
            sharpened = cv2.filter2D(img_cv, -1, kernel_sharpen)
            
            # 混合原图和锐化图像
            alpha = 0.3  # 控制锐化强度
            clarity_enhanced = cv2.addWeighted(img_cv, 1-alpha, sharpened, alpha, 0)
            
            # 2. 对比度增强
            lab = cv2.cvtColor(clarity_enhanced, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            
            # 使用自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(8, 8))
            l_contrast = clahe.apply(l)
            
            # 3. 亮度微调
            l_bright = cv2.add(l_contrast, 8)  # 轻微增亮
            l_bright = np.clip(l_bright, 0, 255)
            
            # 合并通道
            enhanced_lab = cv2.merge([l_bright, a, b])
            result = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
            
            # 4. 最终的全局调整
            result = cv2.convertScaleAbs(result, alpha=1.05, beta=5)
            
            self.logger.info("清晰度和亮度增强完成")
            return result
            
        except Exception as e:
            self.logger.warning(f"清晰度亮度增强失败: {e}")
            return img_cv

# 美颜处理的便捷函数
def advanced_beautify(img: Image.Image, params: Optional[Dict[str, Any]] = None) -> Image.Image:
    """
    高级美颜处理便捷函数
    
    参数:
        img: PIL图像
        params: 处理参数字典
            - face_alignment: 是否纠正脸部歪斜 (默认: True)
            - eyebrow_symmetry: 是否纠正眉毛对称 (默认: True)
            - mouth_correction: 是否纠正嘴部歪斜 (默认: True)
            - shoulder_balance: 是否平衡肩膀高度 (默认: True)
            - blemish_removal: 是否去除痣和瑕疵 (默认: True)
            - lighting_enhancement: 是否增强光线色彩 (默认: True)
            - clarity_enhancement: 是否增强清晰度亮度 (默认: True)
    """
    if params is None:
        params = {}
    
    # 设置默认参数
    default_params = {
        'face_alignment': True,
        'eyebrow_symmetry': True,
        'mouth_correction': True,
        'shoulder_balance': True,
        'blemish_removal': True,
        'lighting_enhancement': True,
        'clarity_enhancement': True
    }
    
    # 合并参数
    final_params = {**default_params, **params}
    
    # 创建处理器并处理
    processor = AdvancedBeautyProcessor()
    return processor.process(img, final_params) 