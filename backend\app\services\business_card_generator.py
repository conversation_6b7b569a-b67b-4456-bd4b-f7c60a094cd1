import os
import io
import logging
import tempfile
from typing import Dict, Any, Optional
from datetime import datetime
from jinja2 import Template, Environment, FileSystemLoader
from app.models.business_card import BusinessCard, BusinessCardTemplate
from app.services.business_card_templates.renderer import BusinessCardRenderer
import traceback

logger = logging.getLogger(__name__)

class BusinessCardGenerator:
    """
    名片生成器 - 按照简历生成器的实现方式实现名片生成功能
    支持HTML、PNG、JPG格式的名片导出功能
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.renderer = BusinessCardRenderer()
        
        # 默认样式配置
        self.default_style_config = {
            'fontSize': 100,  # 字体大小百分比
            'primary': '#2c3e50',  # 主色调
            'secondary': '#3498db',  # 次要色调
            'text': '#34495e',  # 文本颜色
            'light': '#7f8c8d',  # 浅色文本
            'backgroundColor': '#ffffff',  # 背景色
            'borderColor': '#ddd',  # 边框色
            'nameColor': '#2c3e50',  # 姓名颜色
            'companyColor': '#3498db',  # 公司颜色
            'nameFontStyle': 'normal',  # 姓名字体样式
            'companyFontStyle': 'normal',  # 公司字体样式
            'textFontStyle': 'normal'  # 文本字体样式
        }

    def generate_html(self, business_card: BusinessCard) -> str:
        """生成名片HTML内容

        Args:
            business_card (BusinessCard): 名片对象

        Returns:
            str: HTML内容
        """
        try:
            self.logger.info("开始生成名片HTML内容")
            
            # 获取模板ID，如果没有则使用商务经典模板
            template_id = 'business_classic'
            if business_card.template_id:
                # 根据数据库模板ID映射到后端模板ID
                template_mapping = {
                    1: 'business_classic',
                    2: 'modern_minimal', 
                    3: 'creative_design',
                    4: 'elegant_business'
                }
                template_id = template_mapping.get(business_card.template_id, 'business_classic')
            
            self.logger.info(f"使用模板: {template_id}")
            
            # 准备名片数据
            card_data = {
                'id': business_card.id,
                'name': business_card.card_data.get('name', ''),
                'title': business_card.card_data.get('title', ''),
                'company': business_card.card_data.get('company', ''),
                'phone': business_card.card_data.get('phone', ''),
                'email': business_card.card_data.get('email', ''),
                'address': business_card.card_data.get('address', ''),
                'website': business_card.card_data.get('website', ''),
                'qr_code_url': business_card.qr_code_image or ''
            }
            
            # 合并样式配置
            style_config = self._merge_style_config(business_card.style_config)
            
            # 使用模板渲染器
            html_content = self.renderer.render_html(card_data, template_id, style_config)
            
            self.logger.info("名片HTML内容生成成功")
            return html_content
            
        except Exception as e:
            self.logger.error(f"生成名片HTML内容失败: {str(e)}\n{traceback.format_exc()}")
            # 如果新渲染器失败，回退到旧方法
            return self._generate_html_fallback(business_card)

    def generate_png(self, business_card: BusinessCard) -> bytes:
        """生成名片PNG图片

        Args:
            business_card (BusinessCard): 名片对象

        Returns:
            bytes: PNG图片数据
        """
        try:
            self.logger.info("开始生成名片PNG图片")
            
            # 获取模板ID
            template_id = 'business_classic'
            if business_card.template_id:
                template_mapping = {
                    1: 'business_classic',
                    2: 'modern_minimal', 
                    3: 'creative_design',
                    4: 'elegant_business'
                }
                template_id = template_mapping.get(business_card.template_id, 'business_classic')
            
            # 准备名片数据
            card_data = {
                'id': business_card.id,
                'name': business_card.card_data.get('name', ''),
                'title': business_card.card_data.get('title', ''),
                'company': business_card.card_data.get('company', ''),
                'phone': business_card.card_data.get('phone', ''),
                'email': business_card.card_data.get('email', ''),
                'address': business_card.card_data.get('address', ''),
                'website': business_card.card_data.get('website', ''),
                'qr_code_url': business_card.qr_code_image or ''
            }
            
            # 合并样式配置
            style_config = self._merge_style_config(business_card.style_config)
            
            # 使用模板渲染器生成PNG
            png_data = self.renderer.render_png(card_data, template_id, style_config)
            
            self.logger.info("名片PNG图片生成成功")
            return png_data
            
        except Exception as e:
            self.logger.error(f"生成名片PNG图片失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def generate_jpg(self, business_card: BusinessCard, quality: int = 95) -> bytes:
        """生成名片JPG图片

        Args:
            business_card (BusinessCard): 名片对象
            quality (int): JPG质量 (1-100)

        Returns:
            bytes: JPG图片数据
        """
        try:
            self.logger.info("开始生成名片JPG图片")
            
            # 获取模板ID
            template_id = 'business_classic'
            if business_card.template_id:
                template_mapping = {
                    1: 'business_classic',
                    2: 'modern_minimal', 
                    3: 'creative_design',
                    4: 'elegant_business'
                }
                template_id = template_mapping.get(business_card.template_id, 'business_classic')
            
            # 准备名片数据
            card_data = {
                'id': business_card.id,
                'name': business_card.card_data.get('name', ''),
                'title': business_card.card_data.get('title', ''),
                'company': business_card.card_data.get('company', ''),
                'phone': business_card.card_data.get('phone', ''),
                'email': business_card.card_data.get('email', ''),
                'address': business_card.card_data.get('address', ''),
                'website': business_card.card_data.get('website', ''),
                'qr_code_url': business_card.qr_code_image or ''
            }
            
            # 合并样式配置
            style_config = self._merge_style_config(business_card.style_config)
            
            # 使用模板渲染器生成JPG
            jpg_data = self.renderer.render_jpg(card_data, template_id, style_config, quality)
            
            self.logger.info("名片JPG图片生成成功")
            return jpg_data
            
        except Exception as e:
            self.logger.error(f"生成名片JPG图片失败: {str(e)}\n{traceback.format_exc()}")
            raise

    def preview_from_data(self, data: Dict[str, Any], template_id: str = 'business_classic') -> str:
        """
        从数据直接生成HTML预览内容（用于实时预览）
        
        Args:
            data: 名片数据字典
            template_id: 模板ID
            
        Returns:
            str: HTML内容
        """
        try:
            # 准备名片数据
            card_data = {
                'name': data.get('name', ''),
                'title': data.get('title', ''),
                'company': data.get('company', ''),
                'phone': data.get('phone', ''),
                'email': data.get('email', ''),
                'address': data.get('address', ''),
                'website': data.get('website', ''),
                'qr_code_url': data.get('qr_code_url', '')
            }
            
            # 合并样式配置
            style_config = self._merge_style_config(data.get('style_config', {}))
            
            # 使用模板渲染器
            html_content = self.renderer.render_html(card_data, template_id, style_config)
            
            self.logger.info("名片数据预览生成成功")
            return html_content
            
        except Exception as e:
            self.logger.error(f"名片数据预览生成失败: {str(e)}")
            raise ValueError(f"预览生成失败: {str(e)}")

    def _generate_html_fallback(self, business_card: BusinessCard) -> str:
        """回退方法：使用旧的HTML生成逻辑"""
        try:
            self.logger.info("使用回退方法生成名片HTML")
            
            # 获取模板
            template = business_card.template
            template_html = template.template_html if template else ''
            template_css = template.template_css if template else ''
            
            # 准备模板变量
            template_vars = {
                'card': business_card.card_data,
                'template': template,
                'style': self._merge_style_config(business_card.style_config),
                'qr_code_url': business_card.qr_code_image or '',
                'css': template_css
            }
            
            # 渲染模板
            if template_html:
                template_obj = Template(template_html)
                html_content = template_obj.render(**template_vars)
            else:
                # 使用默认模板
                html_content = self._render_default_html(template_vars)
            
            return html_content
            
        except Exception as e:
            self.logger.error(f"回退方法也失败了: {str(e)}")
            raise

    def _render_default_html(self, template_vars: Dict[str, Any]) -> str:
        """渲染默认HTML模板"""
        card = template_vars['card']
        style = template_vars['style']
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{card.get('name', '名片')}</title>
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    font-family: {style.get('font_family', 'Microsoft YaHei, Arial, sans-serif')};
                    background-color: {style.get('background_color', '#ffffff')};
                }}
                .business-card {{
                    width: 90mm;
                    height: 54mm;
                    padding: 8mm;
                    box-sizing: border-box;
                    position: relative;
                    border: 1px solid {style.get('border_color', '#ddd')};
                }}
                .name {{
                    font-size: {style.get('name_size', '18px')};
                    font-weight: {style.get('name_weight', 'bold')};
                    color: {style.get('name_color', '#2c3e50')};
                    margin: 0 0 2mm 0;
                }}
                .title {{
                    font-size: {style.get('title_size', '14px')};
                    color: {style.get('text_color', '#34495e')};
                    margin: 0 0 4mm 0;
                }}
                .company {{
                    font-size: 14px;
                    color: {style.get('company_color', '#3498db')};
                    font-weight: 500;
                    margin: 0 0 4mm 0;
                }}
                .contact-item {{
                    font-size: 11px;
                    color: {style.get('light_color', '#7f8c8d')};
                    margin-bottom: 1mm;
                }}
                .qr-code {{
                    position: absolute;
                    right: 8mm;
                    bottom: 8mm;
                    width: 12mm;
                    height: 12mm;
                }}
                .qr-code img {{
                    width: 100%;
                    height: 100%;
                }}
            </style>
        </head>
        <body>
            <div class="business-card">
                <div class="header">
                    <h1 class="name">{card.get('name', '姓名')}</h1>
                    <p class="title">{card.get('title', '职位')}</p>
                </div>
                <div class="company">
                    <p class="company">{card.get('company', '公司名称')}</p>
                </div>
                <div class="contact">
                    {f'<div class="contact-item">📞 {card.get("phone", "")}</div>' if card.get('phone') else ''}
                    {f'<div class="contact-item">📧 {card.get("email", "")}</div>' if card.get('email') else ''}
                    {f'<div class="contact-item">📍 {card.get("address", "")}</div>' if card.get('address') else ''}
                    {f'<div class="contact-item">🌐 {card.get("website", "")}</div>' if card.get('website') else ''}
                </div>
                {f'<div class="qr-code"><img src="{template_vars.get("qr_code_url", "")}" alt="二维码"></div>' if template_vars.get('qr_code_url') else ''}
            </div>
        </body>
        </html>
        """

    def _merge_style_config(self, user_style_config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """合并用户样式配置和默认样式配置"""
        merged = self.default_style_config.copy()
        
        if user_style_config:
            # 用户自定义样式覆盖默认样式
            merged.update(user_style_config)
            
        return merged

    def validate_template(self, template_html: str, template_css: str = '') -> Dict[str, Any]:
        """
        验证模板有效性
        
        Args:
            template_html: 模板HTML
            template_css: 模板CSS
            
        Returns:
            Dict: 验证结果
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            # 验证Jinja2模板语法
            template = Template(template_html)
            
            # 尝试渲染模板（使用空数据）
            template.render(
                card={'name': '测试姓名', 'title': '测试职位', 'company': '测试公司'},
                template={},
                style=self.default_style_config,
                qr_code_url='',
                css=template_css
            )
            
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"模板语法错误: {str(e)}")
        
        return result 