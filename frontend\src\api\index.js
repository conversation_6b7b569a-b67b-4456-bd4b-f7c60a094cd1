import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'

// 创建axios实例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'development' ? 'http://localhost:5000/api' : '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 导出API基础URL供其他组件使用
export const API_BASE_URL = process.env.NODE_ENV === 'development' ? 'http://localhost:5000' : ''

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    // 添加认证token
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.config.url)
    return response
  },
  async (error) => {
    const { response } = error
    
    console.error('API错误:', response?.status, response?.config?.url, response?.data)
    
    // 处理不同类型的错误
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401: {
          ElMessage.error('登录已过期，请重新登录')
          const authStore = useAuthStore()
          authStore.logout()
          break
        }
        case 403:
          ElMessage.error('没有权限访问此资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else {
      ElMessage.error('网络连接失败，请检查网络设置')
    }
    
    return Promise.reject(error)
  }
)

// API接口定义
export const apiEndpoints = {
  // 认证相关
  auth: {
    login: (data) => api.post('/auth/login', data),
    register: (data) => api.post('/auth/register', data),
    logout: () => api.post('/auth/logout'),
    profile: () => api.get('/auth/profile'),
    refresh: () => api.post('/auth/refresh'),
    getCurrentUser: () => api.get('/auth/me')
  },

  // 用户相关
  user: {
    updateProfile: (data) => api.put('/users/profile', data),
    changePassword: (data) => api.put('/users/password', data),
    getProfile: () => api.get('/users/profile'),
    
    // 积分管理
    getCreditsInfo: () => api.get('/user/credits/info'),
    getCreditsLogs: (params) => api.get('/user/credits/logs', { params }),
    createRechargeOrder: (data) => api.post('/user/credits/create-order', data),
    confirmPayment: (data) => api.post('/user/credits/confirm-payment', data),
    getUserOrders: (params) => api.get('/user/credits/orders', { params }),
    watchAdReward: () => api.post('/user/credits/watch-ad')
  },

  // 图片管理
  images: {
    list: () => api.get('/images'),
    upload: (file) => {
      const formData = new FormData()
      formData.append('file', file)
      return api.post('/images/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    },
    delete: (id) => api.delete(`/images/${id}`),
    getUrl: (id) => api.get(`/images/${id}/url`),
    saveProcessed: (data) => api.post('/images/save-processed', data)
  },

  // 排版布局
  layout: {
    getPaperSizes: () => api.get('/layout/paper-sizes'),
    calculateLayout: (paperId, data) => api.post(`/layout/calculate/${paperId}`, data),
    previewLayout: (data) => api.post('/layout/preview', data),
    optimizeLayout: (data) => api.post('/layout/optimize', data)
  },

  // 证件照处理
  idPhoto: {
    process: (data) => api.post('/processing/id-photo', data),
    detectCropArea: (data) => api.post('/processing/detect-crop-area', data),
    pureCrop: (data) => api.post('/processing/pure-crop', data),
    unifiedCrop: (data) => api.post('/processing/unified-crop', data),
    processBeauty: (data) => api.post('/processing/beauty', data),
    processBackground: (data) => api.post('/processing/background', data),
    processFinalSize: (data) => api.post('/processing/final-size', data)
  },
  
  // 模板管理
  templates: {
    getPhotoTemplates: () => api.get('/templates/photo'),
    getBusinessCardTemplates: () => api.get('/templates/business-cards'),
    getResumeTemplates: () => api.get('/templates/resumes')
  },
  
  // 名片功能
  businessCards: {
    getList: (params) => api.get('/business-cards', { params }),
    getDetail: (id) => api.get(`/business-cards/${id}`),
    create: (data) => api.post('/business-cards', data),
    update: (id, data) => api.put(`/business-cards/${id}`, data),
    delete: (id) => api.delete(`/business-cards/${id}`),
    batchDelete: (data) => api.post('/business-cards/batch-delete', data),
    download: (id, format) => api.get(`/business-cards/${id}/download?format=${format}`, { responseType: 'blob' }),
    generateQR: (id, data) => api.post(`/business-cards/${id}/qr-code`, data)
  },

  // 简历功能
  resumes: {
    getList: () => api.get('/resumes'),
    create: (data) => api.post('/resumes', data),
    get: (id) => api.get(`/resumes/${id}`),
    getDetail: (id) => api.get(`/resumes/${id}`),
    update: (id, data) => api.put(`/resumes/${id}`, data),
    delete: (id) => api.delete(`/resumes/${id}`),
    getTemplates: () => api.get('/resumes/templates'),
    preview: (id, data) => api.post(`/resumes/${id}/preview`, data),
    export: (id, format) => api.post(`/resumes/${id}/export`, { format }, { responseType: 'blob' })
  },
  
  // 文档对比
  documents: {
    list: (params) => api.get('/documents', { params }),
    upload: (file, documentType = 'standard') => {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('document_type', documentType)
      return api.post('/documents/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    },
    delete: (id) => api.delete(`/documents/${id}`),
    preview: (id, responseType = 'blob') => api.get(`/documents/${id}/preview`, { responseType }),
    compare: (data) => api.post('/documents/compare', data),
    getTasks: (params) => api.get('/documents/tasks', { params }),
    getTask: (taskId) => api.get(`/documents/tasks/${taskId}`),
    getTaskReport: (taskId, responseType = 'blob') => api.get(`/documents/tasks/${taskId}/report`, { responseType }),
    deleteTask: (taskId) => api.delete(`/documents/tasks/${taskId}`),
    getComparisonResult: (taskId) => api.get(`/documents/compare/${taskId}`)
  },
  
  // 通用接口
  common: {
    getConfig: () => api.get('/common/config'),
    healthCheck: () => api.get('/common/health'),
    getAdPosition: (positionCode) => api.get(`/common/ad-positions/${positionCode}`),
    getPageAdPositions: (pageLocation) => api.get(`/common/ad-positions/page/${pageLocation}`),
    getAdsConfig: () => api.get('/common/ads-config')
  },

  // 管理员接口
  admin: {
    // 用户管理
    users: {
      getUsers: (params) => api.get('/admin/users', { params }),
      getUserDetail: (id) => api.get(`/admin/users/${id}`),
      toggleUserStatus: (id) => api.post(`/admin/users/${id}/toggle-status`),
      batchToggleStatus: (data) => api.post('/admin/users/batch-toggle-status', data),

      exportUsers: (params) => api.get('/admin/users/export', { params, responseType: 'blob' }),
      getUserLogs: (params) => api.get('/admin/users/logs', { params }),
      getUserLogStats: () => api.get('/admin/users/logs/stats'),
      exportUserLogs: (params) => api.get('/admin/users/logs/export', { params, responseType: 'blob' }),
      batchDeleteUserLogs: (data) => api.delete('/admin/users/logs/batch', { data })
    },

    // 内容管理
    content: {
      // 名片管理
      getBusinessCards: (params) => api.get('/admin/business-cards', { params }),
      deleteBusinessCard: (id) => api.delete(`/admin/business-cards/${id}`),
      batchDeleteBusinessCards: (data) => api.delete('/admin/business-cards/batch', { data }),
      exportBusinessCards: (params) => api.get('/admin/business-cards/export', { params, responseType: 'blob' }),

      // 简历管理
      getResumes: (params) => api.get('/admin/resumes', { params }),
      deleteResume: (id) => api.delete(`/admin/resumes/${id}`),
      batchDeleteResumes: (data) => api.delete('/admin/resumes/batch', { data }),
      exportResumes: (params) => api.get('/admin/resumes/export', { params, responseType: 'blob' }),

      // 文档管理
      getDocuments: (params) => api.get('/admin/documents', { params }),
      deleteDocument: (id) => api.delete(`/admin/documents/${id}`),
      batchDeleteDocuments: (data) => api.delete('/admin/documents/batch', { data }),
      downloadDocument: (id) => api.get(`/admin/documents/${id}/download`, { responseType: 'blob' }),
      exportDocuments: (params) => api.get('/admin/documents/export', { params, responseType: 'blob' }),

      // 图片管理
      getImages: (params) => api.get('/admin/images', { params }),
      deleteImage: (id) => api.delete(`/admin/images/${id}`),
      batchDeleteImages: (data) => api.delete('/admin/images/batch', { data }),
      downloadImage: (id) => api.get(`/admin/images/${id}/download`, { responseType: 'blob' }),
      exportImages: (params) => api.get('/admin/images/export', { params, responseType: 'blob' }),

      // 内容统计
      getContentStats: () => api.get('/admin/content/stats')
    },



    // 系统配置
    system: {
      getSystemConfig: () => api.get('/admin/system/config'),
      saveSystemConfig: (data) => api.post('/admin/system/config', data),
      getEmailConfig: () => api.get('/admin/email/config'),
      saveEmailConfig: (data) => api.post('/admin/email/config', data),
      testEmail: (data) => api.post('/admin/email/test', data),
      testAdConfig: (data) => api.post('/admin/ads/test', data),

      getAdsConfig: () => api.get('/admin/ads/config'),
      saveAdsConfig: (data) => api.post('/admin/ads/config', data)
    },

    // 广告位管理
    ads: {
      getPositions: () => api.get('/admin/ads/positions'),
      getPosition: (id) => api.get(`/admin/ads/positions/${id}`),
      createPosition: (data) => api.post('/admin/ads/positions', data),
      updatePosition: (id, data) => api.put(`/admin/ads/positions/${id}`, data),
      deletePosition: (id) => api.delete(`/admin/ads/positions/${id}`),
      batchUpdatePositions: (data) => api.post('/admin/ads/positions/batch-update', data),
      getPositionStats: () => api.get('/admin/ads/positions/stats')
    },

    // 统计报表
    statistics: {
      getStatistics: () => api.get('/admin/statistics'),
      getUserChart: (params) => api.get('/admin/statistics/user-chart', { params }),
      getBusinessChart: (params) => api.get('/admin/statistics/business-chart', { params }),
      getUserStats: (params) => api.get('/admin/statistics/user-stats', { params }),
      getSystemMetrics: () => api.get('/admin/statistics/system-metrics'),
      exportReport: (params) => api.get('/admin/statistics/export', { params, responseType: 'blob' })
    },

    // 积分配置
    credits: {
      getConfig: () => api.get('/admin/credits/config'),
      saveConfig: (data) => api.post('/admin/credits/config', data),
      uploadQr: (formData) => {
        return api.post('/admin/credits/upload-qr', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
      },
      getLogs: (params) => api.get('/admin/credits/logs', { params }),
      getStats: () => api.get('/admin/credits/stats'),
      adjustPoints: (data) => api.post('/admin/credits/adjust', data)
    }
  }
}

// 默认导出
export default apiEndpoints
