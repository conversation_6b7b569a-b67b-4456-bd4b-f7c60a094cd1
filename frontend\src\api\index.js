import axios from 'axios'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '../stores/auth'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 导出API基础URL供其他组件使用
export const API_BASE_URL = 'http://localhost:5000'

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    // 添加认证token
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.config.url)
    return response
  },
  async (error) => {
    const { response } = error
    
    console.error('API错误:', response?.status, response?.config?.url, response?.data)
    
    // 处理不同类型的错误
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401: {
          ElMessage.error('登录已过期，请重新登录')
          const authStore = useAuthStore()
          authStore.logout()
          break
        }
        case 403:
          ElMessage.error('没有权限访问此资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else {
      ElMessage.error('网络连接失败，请检查网络设置')
    }
    
    return Promise.reject(error)
  }
)

// API接口定义
export const apiEndpoints = {
  // 认证相关
  auth: {
    login: (data) => api.post('/auth/login', data),
    register: (data) => api.post('/auth/register', data),
    logout: () => api.post('/auth/logout'),
    profile: () => api.get('/auth/profile'),
    refresh: () => api.post('/auth/refresh'),
    getCurrentUser: () => api.get('/auth/me')
  },

  // 用户相关
  user: {
    updateProfile: (data) => api.put('/users/profile', data),
    changePassword: (data) => api.put('/users/password', data),
    getProfile: () => api.get('/users/profile')
  },

  // 图片管理
  images: {
    list: () => api.get('/images'),
    upload: (file) => {
      const formData = new FormData()
      formData.append('file', file)
      return api.post('/images/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
    },
    delete: (id) => api.delete(`/images/${id}`),
    getUrl: (id) => api.get(`/images/${id}/url`),
    saveProcessed: (data) => api.post('/images/save-processed', data)
  },

  // 排版布局
  layout: {
    getPaperSizes: () => api.get('/layout/paper-sizes'),
    calculateLayout: (paperId, data) => api.post(`/layout/calculate/${paperId}`, data),
    previewLayout: (data) => api.post('/layout/preview', data),
    optimizeLayout: (data) => api.post('/layout/optimize', data)
  },

  // 证件照处理
  idPhoto: {
    process: (data) => api.post('/processing/id-photo', data),
    detectCropArea: (data) => api.post('/processing/detect-crop-area', data),
    pureCrop: (data) => api.post('/processing/pure-crop', data),
    processBeauty: (data) => api.post('/processing/beauty', data),
    processBackground: (data) => api.post('/processing/background', data),
    processFinalSize: (data) => api.post('/processing/final-size', data)
  },
  
  // 模板管理
  templates: {
    getPhotoTemplates: () => api.get('/templates/photo'),
    getBusinessCardTemplates: () => api.get('/templates/business-cards'),
    getResumeTemplates: () => api.get('/templates/resumes')
  },
  
  // 名片功能
  businessCards: {
    list: () => api.get('/business-cards'),
    create: (data) => api.post('/business-cards', data),
    update: (id, data) => api.put(`/business-cards/${id}`, data),
    delete: (id) => api.delete(`/business-cards/${id}`),
    download: (id, format) => api.get(`/business-cards/${id}/download?format=${format}`, { responseType: 'blob' })
  },

  // 简历功能
  resumes: {
    getList: () => api.get('/resumes'),
    create: (data) => api.post('/resumes', data),
    get: (id) => api.get(`/resumes/${id}`),
    update: (id, data) => api.put(`/resumes/${id}`, data),
    delete: (id) => api.delete(`/resumes/${id}`),
    getTemplates: () => api.get('/resumes/templates'),
    preview: (id, data) => api.post(`/resumes/${id}/preview`, data),
    export: (id, format) => api.post(`/resumes/${id}/export`, { format }, { responseType: 'blob' })
  },
  
  // 文档对比
  documents: {
    compare: (data) => api.post('/documents/compare', data),
    getComparisonResult: (taskId) => api.get(`/documents/compare/${taskId}`)
  },
  
  // 通用接口
  common: {
    getConfig: () => api.get('/common/config'),
    healthCheck: () => api.get('/common/health')
  }
}

// 默认导出
export default apiEndpoints
