import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '../stores/auth'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 导出API基础URL供其他组件使用
export const API_BASE_URL = 'http://localhost:5000'

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    // 添加认证token
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url, config.data)
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.config.url, response.data)
    return response
  },
  async (error) => {
    const { response } = error
    
    console.error('API错误:', response?.status, response?.config?.url, response?.data)
    
    // 处理不同类型的错误
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401: {
          // 未授权，尝试刷新token
          const authStore = useAuthStore()
          
          try {
            // 尝试刷新token
            await authStore.refreshAccessToken()
            
            // 刷新成功，重试原请求
            const originalRequest = error.config
            if (originalRequest) {
              originalRequest.headers.Authorization = `Bearer ${authStore.token}`
              return api(originalRequest)
            }
          } catch (refreshError) {
            console.warn('Token刷新失败，清除认证状态:', refreshError)
          authStore.logout()
          
          ElMessage.error(data.message || '登录已过期，请重新登录')
          
          // 避免在登录页面重复跳转
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
            }
          }
          break
        }
          
        case 403:
          ElMessage.error(data.message || '权限不足')
          break
          
        case 404:
          ElMessage.error(data.message || '请求的资源不存在')
          break
          
        case 422:
          ElMessage.error(data.message || '请求参数有误')
          break
          
        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break
          
        case 500:
          ElMessage.error('服务器内部错误，请稍后重试')
          break
          
        default:
          ElMessage.error(data.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'NETWORK_ERROR') {
      ElMessage.error('网络连接失败，请检查网络')
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请稍后重试')
    } else {
      ElMessage.error('网络异常，请稍后重试')
    }
    
    return Promise.reject(error)
  }
)

// 文件上传配置
const createUploadConfig = (onProgress) => ({
  headers: {
    'Content-Type': 'multipart/form-data'
  },
  onUploadProgress: onProgress
})

// API方法集合
const apiMethods = {
  // 认证相关
  auth: {
    login: (data) => api.post('/auth/login', data),
    register: (data) => api.post('/auth/register', data),
    logout: () => api.post('/auth/logout'),
    refresh: () => api.post('/auth/refresh'),
    getCurrentUser: () => api.get('/auth/me'),
    checkUsername: (username) => api.post('/auth/check-username', { username }),
    checkEmail: (email) => api.post('/auth/check-email', { email })
  },
  
  // 用户相关
  user: {
    getProfile: () => api.get('/users/profile'),
    updateProfile: (data) => api.put('/users/profile', data),
    changePassword: (data) => api.post('/users/change-password', data),
    setAvatar: (data) => api.post('/users/set-avatar', data)
  },
  
  // 图像相关
  images: {
    upload: (file, onProgress) => {
      const formData = new FormData()
      formData.append('file', file)
      return api.post('/images/upload', formData, createUploadConfig(onProgress))
    },
    saveProcessedImage: (data) => api.post('/images/save-processed', data),
    getList: (params) => api.get('/images', { params }),
    getDetail: (fileId) => api.get(`/images/${fileId}`),
    delete: (fileId) => api.delete(`/images/${fileId}`),
    download: (fileId) => api.get(`/images/${fileId}/download`, { responseType: 'blob' }),
    getStats: () => api.get('/images/stats'),
    updateName: (fileId, data) => api.put(`/images/${fileId}/name`, data)
  },
  
  // 证件照处理
  idPhoto: {
    process: (data) => api.post('/processing/id-photo', data),
    detectCropArea: (data) => api.post('/processing/detect-crop-area', data),
    pureCrop: (data) => api.post('/processing/pure-crop', data),
    // 工作流分步处理API
    processBeauty: (data) => api.post('/processing/beauty', data),
    processBackground: (data) => api.post('/processing/background', data),
    processFinalSize: (data) => api.post('/processing/final-size', data)
  },
  
  // 通用接口
  common: {
    getConfig: () => api.get('/common/config'),
    healthCheck: () => api.get('/common/health')
  },
  
  // 排版相关接口
  layout: {
    getPaperSizes: () => api.get('/paper-sizes'),
    calculateLayout: (sizeId, data) => api.post(`/paper-sizes/${sizeId}/layout`, data)
  },
  
  // 简历相关接口
  resumes: {
    getList: (params) => api.get('/resumes', { params }),
    getDetail: (id) => api.get(`/resumes/${id}`),
    create: (data) => api.post('/resumes', data),
    update: (id, data) => api.put(`/resumes/${id}`, data),
    delete: (id) => api.delete(`/resumes/${id}`),
    preview: (id, templateId) => api.post(`/resumes/${id}/preview`, { template_id: templateId }),
    generatePreview: (data) => api.post('/resumes/preview', data),
    export: (id, format) => api.post(`/resumes/${id}/export`, { format }, { responseType: 'blob' }),
    getTemplates: () => api.get('/resumes/templates'),
    getTemplate: (id) => api.get(`/resumes/templates/${id}`),
    generateWorkDescription: (data) => api.post('/resumes/ai/work-description', data),
    generateProjectDescription: (data) => api.post('/resumes/ai/project-description', data),
    optimizeSummary: (data) => api.post('/resumes/ai/optimize-summary', data),
    getImages: (params) => api.get('/resumes/images', { params })
  },
  
  // 文档管理相关接口
  documents: {
    upload: (file, onProgress) => {
      const formData = new FormData()
      formData.append('file', file)
      return api.post('/documents/upload', formData, createUploadConfig(onProgress))
    },
    getList: (params) => api.get('/documents', { params }),
    getDetail: (id) => api.get(`/documents/${id}`),
    delete: (id) => api.delete(`/documents/${id}`),
    download: (id) => api.get(`/documents/${id}/download`, { responseType: 'blob' }),
    compare: (data) => api.post('/documents/compare', data),
    getReport: (id) => api.get(`/documents/reports/${id}`)
  },

  // 管理员相关接口
  admin: {
    // 用户管理
    getUsers: (params) => api.get('/admin/users', { params }),
    getUserDetail: (id) => api.get(`/admin/users/${id}`),
    toggleUserStatus: (id) => api.post(`/admin/users/${id}/toggle-status`),
    batchToggleUserStatus: (userIds, action) => api.post('/admin/users/batch-toggle-status', { user_ids: userIds, action }),
    adjustUserPoints: (userId, data) => api.post(`/admin/users/${userId}/adjust-points`, data),
    exportUsers: (filters) => api.get('/admin/users/export', { params: filters, responseType: 'blob' }),
    exportBusinessCards: (filters) => api.get('/admin/business-cards/export', { params: filters, responseType: 'blob' }),
    exportResumes: (filters) => api.get('/admin/resumes/export', { params: filters, responseType: 'blob' }),
    exportDocuments: (filters) => api.get('/admin/documents/export', { params: filters, responseType: 'blob' }),
    exportImages: (filters) => api.get('/admin/images/export', { params: filters, responseType: 'blob' }),
    exportUserLogs: (filters) => api.get('/admin/users/logs/export', { params: filters, responseType: 'blob' }),

    // 用户日志管理
    getUserLogs: (params) => api.get('/admin/users/logs', { params }),
    getUserLogsStats: () => api.get('/admin/users/logs/stats'),

    // 积分管理
    getPointsStats: () => api.get('/admin/points/stats'),
    getPointsConfig: () => api.get('/admin/points/config'),
    savePointsConfig: (configs) => api.post('/admin/points/config', configs),
    getPointsLogs: (params) => api.get('/admin/points/logs', { params }),
    exportPointsLogs: (filters) => api.get('/admin/points/logs/export', { params: filters, responseType: 'blob' }),

    // 系统配置
    getSystemConfig: () => api.get('/admin/system/config'),
    saveSystemConfig: (configs) => api.post('/admin/system/config', configs),
    getPaymentConfig: () => api.get('/admin/payment/config'),
    savePaymentConfig: (configs) => api.post('/admin/payment/config', configs),
    getAdConfig: () => api.get('/admin/ads/config'),
    saveAdConfig: (configs) => api.post('/admin/ads/config', configs),
    getEmailConfig: () => api.get('/admin/email/config'),
    saveEmailConfig: (configs) => api.post('/admin/email/config', configs),
    testEmail: (data) => api.post('/admin/email/test', data),

    // 数据统计
    getStatistics: () => api.get('/admin/statistics'),
    getUserChart: (params) => api.get('/admin/statistics/user-chart', { params }),
    getBusinessChart: (params) => api.get('/admin/statistics/business-chart', { params }),
    getUserStats: () => api.get('/admin/statistics/user-stats'),
    getBusinessStats: () => api.get('/admin/statistics/business-stats'),
    getRevenueStats: () => api.get('/admin/statistics/revenue-stats'),
    getSystemMetrics: () => api.get('/admin/statistics/system-metrics'),
    exportStatisticsReport: () => api.get('/admin/statistics/export', { responseType: 'blob' }),

    // 内容管理
    getBusinessCards: (params) => api.get('/admin/business-cards', { params }),
    getResumes: (params) => api.get('/admin/resumes', { params }),
    getDocuments: (params) => api.get('/admin/documents', { params }),
    getImages: (params) => api.get('/admin/images', { params }),
    deleteBusinessCard: (id) => api.delete(`/admin/business-cards/${id}`),
    deleteResume: (id) => api.delete(`/admin/resumes/${id}`),
    deleteDocument: (id) => api.delete(`/admin/documents/${id}`),
    deleteImage: (id) => api.delete(`/admin/images/${id}`),
    downloadDocument: (id) => api.get(`/admin/documents/${id}/download`, { responseType: 'blob' }),
    downloadImage: (id) => api.get(`/admin/images/${id}/download`, { responseType: 'blob' }),
    getContentStats: () => api.get('/admin/content/stats')
  },

  // 名片相关接口
  businessCards: {
    getList: (params) => api.get('/business-cards', { params }),
    getDetail: (id) => api.get(`/business-cards/${id}`),
    create: (data) => api.post('/business-cards', data),
    update: (id, data) => api.put(`/business-cards/${id}`, data),
    delete: (id) => api.delete(`/business-cards/${id}`),
    batchDelete: (data) => api.post('/business-cards/batch-delete', data),
    generateQR: (id, data) => api.post(`/business-cards/${id}/qr-code`, data),
    download: (id, format = 'png') => api.get(`/business-cards/${id}/download`, { 
      params: { format },
      responseType: 'blob'
    }),
    renderPreview: (data) => api.post('/business-cards/render-preview', data, { 
      responseType: 'blob' 
    }),
    getTemplates: () => api.get('/business-card-templates')
  }
}

// 错误处理辅助函数
export const handleApiError = (error, customMessage) => {
  console.error('API Error:', error)
  
  const message = customMessage || 
    error.response?.data?.message || 
    error.message || 
    '操作失败'
    
  ElMessage.error(message)
  return Promise.reject(error)
}

// 成功提示辅助函数
export const showSuccess = (message = '操作成功') => {
  ElMessage.success(message)
}

// 确认对话框辅助函数
export const showConfirm = (message = '确定要执行此操作吗？', title = '确认') => {
  return ElMessageBox.confirm(message, title, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
}

// 将apiMethods合并到api对象中
Object.assign(api, apiMethods)

// 导出axios实例供直接使用
export { api }

// 导出API方法集合
export default api 