#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
证件照制作系统启动器
支持前端、后端和数据库的自动启动
"""

import os
import sys
import time
import subprocess
import signal
import threading
import requests
from pathlib import Path

class PhotoMakerLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def check_dependencies(self):
        """检查系统依赖"""
        print("🔍 检查系统依赖...")
        
        # 检查Python依赖
        try:
            import flask, sqlalchemy, PIL
            print("✅ Python依赖检查通过")
        except ImportError as e:
            print(f"❌ Python依赖缺失: {e}")
            return False
        
        # 检查Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                node_version = result.stdout.strip()
                print(f"✅ Node.js 版本: {node_version}")
            else:
                print("❌ Node.js 未安装")
                return False
        except FileNotFoundError:
            print("❌ Node.js 未找到")
            return False
        
        # 检查前端依赖
        frontend_path = Path("frontend")
        if frontend_path.exists() and (frontend_path / "node_modules").exists():
            print("✅ 前端依赖检查通过")
        else:
            print("⚠️ 前端依赖可能缺失，尝试安装...")
            if not self.install_frontend_deps():
                return False
        
        return True
    
    def install_frontend_deps(self):
        """安装前端依赖"""
        try:
            frontend_path = Path("frontend")
            if frontend_path.exists():
                os.chdir(frontend_path)
                result = subprocess.run(['npm', 'install'], check=True)
                os.chdir('..')
                print("✅ 前端依赖安装完成")
                return True
        except Exception as e:
            print(f"❌ 前端依赖安装失败: {e}")
            return False
    
    def setup_database(self):
        """设置数据库"""
        print("🔧 初始化数据库...")
        
        # 确保backend目录存在
        backend_path = Path("backend")
        if not backend_path.exists():
            print("❌ backend目录不存在")
            return False
        
        # 切换到backend目录
        original_cwd = os.getcwd()
        os.chdir(backend_path)
        
        try:
            # 设置数据库环境变量
            os.environ['FLASK_APP'] = 'run.py'
            os.environ['FLASK_ENV'] = 'development'
            
            # 导入并初始化数据库
            sys.path.insert(0, str(backend_path.absolute()))
            from app import create_app, db
            from app.models import User, Image, ProcessingTask, PhotoTemplate, PaperSize, SystemConfig
            
            app = create_app('development')
            with app.app_context():
                db_path = Path(app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', ''))
                
                # 只有当数据库文件不存在时，才创建所有表
                if not db_path.exists():
                    print("...数据库文件不存在，正在创建新的数据库...")
                    db.create_all()
                    print("...数据库表结构创建完成...")
                else:
                    print("...数据库文件已存在，跳过创建步骤...")
                
                # 检查管理员用户
                admin_user = User.query.filter_by(username='admin').first()
                if not admin_user:
                    admin_user = User(
                        username='admin',
                        email='<EMAIL>',
                        password_hash='pbkdf2:sha256:260000$...',  # 默认密码：admin123
                        is_admin=True,
                        user_type='admin',
                        credits=999999
                    )
                    admin_user.set_password('admin123')
                    db.session.add(admin_user)
                    db.session.commit()
                    print("✅ 管理员用户创建成功 (admin/admin123)")
                else:
                    print("✅ 管理员用户已存在")
            
            print("✅ 数据库初始化完成")
            return True
            
        except Exception as e:
            print(f"❌ 数据库初始化失败: {e}")
            return False
        finally:
            os.chdir(original_cwd)
    
    def start_backend(self):
        """启动后端服务"""
        print("🚀 启动后端服务...")
        
        backend_path = Path("backend")
        if not backend_path.exists():
            print("❌ backend目录不存在")
            return False
        
        try:
            # 设置环境变量
            env = os.environ.copy()
            env['FLASK_APP'] = 'run.py'
            env['FLASK_ENV'] = 'development'
            env['PYTHONPATH'] = str(backend_path.absolute())
            
            # 启动Flask应用
            self.backend_process = subprocess.Popen(
                [sys.executable, 'run.py'],
                cwd=backend_path,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
            
            # 等待后端启动
            time.sleep(3)
            
            # 检查后端是否启动成功
            if self.check_backend_health():
                print("✅ 后端服务启动成功 (http://localhost:5000)")
                return True
            else:
                print("❌ 后端服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 后端启动失败: {e}")
            return False
    
    def start_frontend(self):
        """启动前端服务"""
        print("🚀 启动前端服务...")
        
        frontend_path = Path("frontend")
        if not frontend_path.exists():
            print("❌ frontend目录不存在")
            return False
        
        try:
            self.frontend_process = subprocess.Popen(
                ['npm', 'run', 'serve'],
                cwd=frontend_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1
            )
            
            # 等待前端启动
            time.sleep(5)
            
            # 检查前端是否启动成功
            if self.check_frontend_health():
                print("✅ 前端服务启动成功 (http://localhost:8080)")
                return True
            else:
                print("❌ 前端服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ 前端启动失败: {e}")
            return False
    
    def check_backend_health(self, timeout=10):
        """检查后端健康状态"""
        for i in range(timeout):
            try:
                response = requests.get('http://localhost:5000/api/common/health', timeout=2)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)
        return False
    
    def check_frontend_health(self, timeout=15):
        """检查前端健康状态"""
        for i in range(timeout):
            try:
                response = requests.get('http://localhost:8080', timeout=2)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)
        return False
    
    def monitor_services(self):
        """监控服务状态"""
        while self.running:
            time.sleep(5)
            
            # 检查后端状态
            if self.backend_process and self.backend_process.poll() is not None:
                print("⚠️ 后端服务意外停止")
                break
            
            # 检查前端状态
            if self.frontend_process and self.frontend_process.poll() is not None:
                print("⚠️ 前端服务意外停止")
                break
            
            # 检查服务健康状态
            if not self.check_backend_health(timeout=1):
                print("⚠️ 后端服务连接失败")
            
            if not self.check_frontend_health(timeout=1):
                print("⚠️ 前端服务连接失败")
    
    def stop_services(self):
        """停止所有服务"""
        print("\n🛑 正在停止服务...")
        self.running = False
        
        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ 前端服务已停止")
            except:
                self.frontend_process.kill()
        
        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ 后端服务已停止")
            except:
                self.backend_process.kill()
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.stop_services()
        sys.exit(0)
    
    def run(self):
        """主运行函数"""
        print("=" * 50)
        print("🎯 证件照制作系统启动器")
        print("=" * 50)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            # 检查依赖
            if not self.check_dependencies():
                print("❌ 依赖检查失败，请先安装必要依赖")
                return False
            
            # 设置数据库
            if not self.setup_database():
                print("❌ 数据库初始化失败")
                return False
            
            # 启动后端
            if not self.start_backend():
                print("❌ 后端启动失败")
                return False
            
            # 启动前端
            if not self.start_frontend():
                print("❌ 前端启动失败")
                return False
            
            print("\n⏳ 等待服务启动...")
            time.sleep(2)
            
            # 最终健康检查
            backend_ok = self.check_backend_health()
            frontend_ok = self.check_frontend_health()
            
            if not backend_ok:
                print("⚠️ 后端服务连接失败")
            if not frontend_ok:
                print("⚠️ 前端服务连接失败")
            
            print("\n🎉 服务启动完成!")
            print("📱 前端地址: http://localhost:8080")
            print("🔧 后端API: http://localhost:5000")
            print("📖 API文档: http://localhost:5000/docs")
            print("👤 管理员账号: admin/admin123")
            print("\n按 Ctrl+C 停止服务")
            
            # 启动监控线程
            monitor_thread = threading.Thread(target=self.monitor_services)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 等待用户中断
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                self.signal_handler(signal.SIGINT, None)
            
            return True
            
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            self.stop_services()
            return False

if __name__ == '__main__':
    launcher = PhotoMakerLauncher()
    success = launcher.run()
    sys.exit(0 if success else 1) 