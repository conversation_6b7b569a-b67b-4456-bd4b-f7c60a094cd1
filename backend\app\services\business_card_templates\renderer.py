"""名片模板渲染器

此模块负责将名片数据按照指定模板渲染成HTML、PNG、JPG或PDF格式。
"""

import os
from typing import Dict, Any, Optional
from jinja2 import Environment, FileSystemLoader
from . import basic

class BusinessCardRenderer:
    """Business card template renderer class."""
    
    def __init__(self):
        """Initialize the renderer."""
        self.templates_dir = os.path.join(os.path.dirname(__file__), 'templates')
        self.env = Environment(
            loader=FileSystemLoader(self.templates_dir),
            autoescape=True
        )
        
        # 可用模板定义
        self.available_templates = {
            'business_classic': basic.get_business_classic_template(),
            'modern_minimal': basic.get_modern_minimal_template(),
            'creative_design': basic.get_creative_design_template(),
            'elegant_business': basic.get_elegant_business_template()
        }

    def get_template_info(self, template_id: str) -> Optional[Dict[str, Any]]:
        """获取模板信息"""
        return self.available_templates.get(template_id)

    def list_templates(self) -> Dict[str, Dict[str, Any]]:
        """列出所有可用模板"""
        return {
            template_id: {
                'id': template['id'],
                'name': template['name'],
                'description': template['description'],
                'preview_image': template['preview_image'],
                'category': template.get('category', 'business'),
                'industry': template.get('industry', 'other'),
                'layout_type': template.get('layout_type', 'standard'),
                'width_mm': template.get('width_mm', 90.0),
                'height_mm': template.get('height_mm', 54.0)
            }
            for template_id, template in self.available_templates.items()
        }

    def render_html(self, card_data: Dict[str, Any], template_id: str = 'business_classic', 
                   style_config: Optional[Dict[str, Any]] = None) -> str:
        """渲染名片HTML"""
        if template_id not in self.available_templates:
            raise ValueError(f"Template {template_id} not found")
            
        template_info = self.available_templates[template_id]
        
        # 合并样式配置
        merged_style_config = self._merge_style_config(template_info, style_config)
        
        # 准备渲染上下文
        context = {
            'card': card_data,
            'template': template_info,
            'style': merged_style_config,
            'qr_code_url': card_data.get('qr_code_url', '')
        }
        
        try:
            template = self.env.get_template(f"{template_id}/card.html")
            return template.render(**context)
        except Exception as e:
            # 如果模板文件不存在，使用默认模板
            return self._render_default_html(context)

    def render_png(self, card_data: Dict[str, Any], template_id: str = 'business_classic',
                  style_config: Optional[Dict[str, Any]] = None) -> bytes:
        """渲染名片PNG图片"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            import base64
            import tempfile
            
            # 生成HTML
            html_content = self.render_html(card_data, template_id, style_config)
            
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=900,540')
            
            # 创建临时HTML文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name
            
            try:
                driver = webdriver.Chrome(options=chrome_options)
                driver.get(f'file://{temp_file}')
                
                # 等待页面加载完成
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
                
                # 截图
                screenshot = driver.get_screenshot_as_png()
                driver.quit()
                
                return screenshot
                
            finally:
                # 清理临时文件
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
                    
        except ImportError:
            raise NotImplementedError("Selenium is required for PNG rendering")
        except Exception as e:
            raise RuntimeError(f"PNG rendering failed: {str(e)}")

    def render_jpg(self, card_data: Dict[str, Any], template_id: str = 'business_classic',
                  style_config: Optional[Dict[str, Any]] = None, quality: int = 95) -> bytes:
        """渲染名片JPG图片"""
        try:
            from PIL import Image
            import io
            
            # 先渲染PNG
            png_data = self.render_png(card_data, template_id, style_config)
            
            # 转换为JPG
            image = Image.open(io.BytesIO(png_data))
            if image.mode in ('RGBA', 'LA'):
                # 创建白色背景
                background = Image.new('RGB', image.size, (255, 255, 255))
                background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                image = background
            
            # 保存为JPG
            output = io.BytesIO()
            image.save(output, format='JPEG', quality=quality)
            return output.getvalue()
            
        except ImportError:
            raise NotImplementedError("Pillow is required for JPG rendering")
        except Exception as e:
            raise RuntimeError(f"JPG rendering failed: {str(e)}")

    def render_pdf(self, card_data: Dict[str, Any], template_id: str = 'business_classic',
                  style_config: Optional[Dict[str, Any]] = None) -> bytes:
        """渲染名片PDF（暂未实现）"""
        raise NotImplementedError("PDF rendering is not implemented yet")

    def preview_template(self, template_id: str) -> Dict[str, Any]:
        """获取模板预览信息"""
        template_info = self.get_template_info(template_id)
        if not template_info:
            raise ValueError(f"Template {template_id} not found")
            
        return {
            'template': template_info,
            'sample_html': self.render_html(self._get_sample_card_data(), template_id)
        }
        
    def _merge_style_config(self, template_info: Dict[str, Any], 
                           style_config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """合并模板默认样式和用户自定义样式"""
        merged = template_info.get('styles', {}).copy()
        
        if style_config:
            # 用户自定义样式覆盖默认样式
            merged.update(style_config)
            
        return merged
    
    def _render_default_html(self, context: Dict[str, Any]) -> str:
        """渲染默认HTML模板（当模板文件不存在时使用）"""
        card = context['card']
        style = context['style']
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{card.get('name', '名片')}</title>
            <style>
                body {{
                    margin: 0;
                    padding: 0;
                    font-family: {style.get('font_family', 'Microsoft YaHei, Arial, sans-serif')};
                    background-color: {style.get('background_color', '#ffffff')};
                }}
                .business-card {{
                    width: 90mm;
                    height: 54mm;
                    padding: 8mm;
                    box-sizing: border-box;
                    position: relative;
                    border: 1px solid {style.get('border_color', '#ddd')};
                }}
                .name {{
                    font-size: {style.get('name_size', '18px')};
                    font-weight: {style.get('name_weight', 'bold')};
                    color: {style.get('primary_color', '#2c3e50')};
                    margin: 0 0 2mm 0;
                }}
                .title {{
                    font-size: {style.get('title_size', '14px')};
                    color: {style.get('text_color', '#34495e')};
                    margin: 0 0 4mm 0;
                }}
                .company {{
                    font-size: 14px;
                    color: {style.get('secondary_color', '#3498db')};
                    font-weight: 500;
                    margin: 0 0 4mm 0;
                }}
                .contact-item {{
                    font-size: 11px;
                    color: {style.get('light_color', '#7f8c8d')};
                    margin-bottom: 1mm;
                }}
                .qr-code {{
                    position: absolute;
                    right: 8mm;
                    bottom: 8mm;
                    width: 12mm;
                    height: 12mm;
                }}
                .qr-code img {{
                    width: 100%;
                    height: 100%;
                }}
            </style>
        </head>
        <body>
            <div class="business-card">
                <div class="header">
                    <h1 class="name">{card.get('name', '姓名')}</h1>
                    <p class="title">{card.get('title', '职位')}</p>
                </div>
                <div class="company">
                    <p>{card.get('company', '公司名称')}</p>
                </div>
                <div class="contact">
                    {f'<div class="contact-item">📞 {card.get("phone", "")}</div>' if card.get('phone') else ''}
                    {f'<div class="contact-item">📧 {card.get("email", "")}</div>' if card.get('email') else ''}
                    {f'<div class="contact-item">📍 {card.get("address", "")}</div>' if card.get('address') else ''}
                </div>
                {f'<div class="qr-code"><img src="{context.get("qr_code_url", "")}" alt="二维码"></div>' if context.get('qr_code_url') else ''}
            </div>
        </body>
        </html>
        """
        
    def _get_sample_card_data(self) -> Dict[str, Any]:
        """获取示例名片数据用于预览"""
        return {
            'name': '张三',
            'title': '高级软件工程师',
            'company': '某科技有限公司',
            'phone': '138-0013-8000',
            'email': '<EMAIL>',
            'address': '北京市朝阳区某某大厦',
            'website': 'www.example.com',
            'qr_code_url': ''
        } 