<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ resume.full_name }} - 简历</title>
    <!-- 样式将通过CSS文件加载 -->
</head>
<body>
    <div class="resume-container">
        <div class="left-column">
            {% if resume.photo_url %}
            <div class="profile-photo">
                <img src="{{ resume.photo_url }}" alt="{{ resume.full_name }}">
            </div>
            {% endif %}
            
            <div class="contact-section">
                <h3>联系方式</h3>
                {% if resume.phone %}<p><strong>电话:</strong> {{ resume.phone }}</p>{% endif %}
                {% if resume.email %}<p><strong>邮箱:</strong> {{ resume.email }}</p>{% endif %}
                {% if resume.address %}<p><strong>地址:</strong> {{ resume.address }}</p>{% endif %}
            </div>

            {% if resume.skills %}
            <div class="skills-section">
                <h3>专业技能</h3>
                {% for skill in resume.skills %}
                <div class="skill-item">
                    <span class="skill-name">{{ skill.name }}</span>
                    <div class="skill-bar">
                        <div class="skill-progress" style="width: {% if skill.proficiency == '专家' %}100{% elif skill.proficiency == '高级' %}80{% elif skill.proficiency == '中级' %}60{% else %}40{% endif %}%"></div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <div class="right-column">
            <header class="header">
                <h1>{{ resume.full_name }}</h1>
                <h2>{{ resume.objective or '专业人士' }}</h2>
            </header>

            {% if resume.summary %}
            <section class="section">
                <h3>个人总结</h3>
                <p>{{ resume.summary }}</p>
            </section>
            {% endif %}

            {% if resume.work_experiences %}
            <section class="section">
                <h3>工作经历</h3>
                {% for exp in resume.work_experiences %}
                <div class="experience-item">
                    <div class="exp-header">
                        <h4>{{ exp.position }}</h4>
                        <span class="date">{{ exp.start_date }} - {{ exp.end_date or '至今' }}</span>
                    </div>
                    <h5>{{ exp.company_name }}</h5>
                    {% if exp.description %}<p>{{ exp.description }}</p>{% endif %}
                </div>
                {% endfor %}
            </section>
            {% endif %}

            {% if resume.educations %}
            <section class="section">
                <h3>教育背景</h3>
                {% for edu in resume.educations %}
                <div class="education-item">
                    <h4>{{ edu.school_name }}</h4>
                    <p>{{ edu.degree }} - {{ edu.major }}</p>
                    <span class="date">{{ edu.start_date }} - {{ edu.end_date }}</span>
                </div>
                {% endfor %}
            </section>
            {% endif %}
        </div>
    </div>
</body>
</html> 