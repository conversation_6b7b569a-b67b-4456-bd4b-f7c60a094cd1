from flask import request, jsonify, current_app, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from werkzeug.utils import secure_filename
import os
import uuid
from datetime import datetime
import numpy as np
import cv2
from PIL import Image as PILImage
import base64

from . import api_bp
from .errors import bad_request, unauthorized, not_found, validation_error
from app import db
from app.models import User, Image, UserLog

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_uploaded_file(file, user_id):
    """保存上传的文件"""
    try:
        # 生成唯一文件名
        file_id = str(uuid.uuid4()).replace('-', '')
        ext = os.path.splitext(file.filename)[1].lower()
        filename = f"{file_id}{ext}"
        
        # 创建用户专属目录
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
        user_folder = os.path.join(upload_folder, 'images', str(user_id))
        os.makedirs(user_folder, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(user_folder, filename)
        file.save(file_path)
        
        # 返回相对路径
        relative_path = f"images/{user_id}/{filename}"
        
        return relative_path, file_id
    
    except Exception as e:
        current_app.logger.error(f"保存文件失败: {e}")
        raise

@api_bp.route('/images/upload', methods=['POST'])
@jwt_required()
def upload_image():
    """上传新图片并保存记录"""
    current_user_id = int(get_jwt_identity())
    user = User.query.get(current_user_id)
    if not user:
        return unauthorized('用户不存在')

    if 'file' not in request.files:
        return bad_request('请求中没有文件部分')
    
    file = request.files['file']
    if file.filename == '':
        return bad_request('没有选择文件')
        
    if file and allowed_file(file.filename):
        # 生成安全的文件名和唯一ID
        original_filename = secure_filename(file.filename)
        extension = original_filename.rsplit('.', 1)[1].lower()
        file_id = uuid.uuid4().hex
        new_filename = f"{file_id}.{extension}"
        
        # 构建保存路径 - 使用uploads/images作为存储目录
        base_upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'images')
        user_upload_dir = os.path.join(base_upload_dir, str(user.id))
        os.makedirs(user_upload_dir, exist_ok=True)
        file_path = os.path.join(user_upload_dir, new_filename)
        
        # 保存文件
        file.save(file_path)

        # 获取文件大小
        file_size = os.path.getsize(file_path)
        
        # 创建数据库记录
        image = Image(
            user_id=user.id,
            file_id=file_id,
            original_filename=original_filename,
            file_path=f'images/{user.id}/{new_filename}',
            mime_type=file.mimetype,
            file_size=file_size,
            upload_source='editor',
            status=1 # 标记为活动状态
        )
        
        # 提取图片尺寸信息
        image.extract_image_info()
        
        # 创建缩略图
        image.create_thumbnail()
        
        db.session.add(image)
        db.session.commit()
        
        # 记录上传日志
        UserLog.log_action(
            user_id=user.id,
            action='image_upload',
            resource_type='image',
            resource_id=image.id,
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string,
            request_data={
                'filename': original_filename,
                'file_size': file_size,
                'mime_type': file.mimetype
            }
        )
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'image': image.to_dict()
        }), 201
    
    return bad_request('文件类型不被允许')

@api_bp.route('/images', methods=['GET'])
@jwt_required()
def get_user_images():
    """获取当前用户的图片列表，支持按状态筛选"""
    current_user_id = int(get_jwt_identity())
    
    # 增加一个查询参数来筛选图片状态
    # status=1 表示正常（包括上传的和处理完成的），status=2 表示已删除
    status_filter = request.args.get('status', type=int)
    
    query = Image.query.filter_by(user_id=current_user_id)
    
    if status_filter:
        query = query.filter_by(status=status_filter)
        
    images = query.order_by(Image.created_at.desc()).all()
    
    return jsonify({
        'images': [img.to_dict() for img in images]
    })

@api_bp.route('/images/<file_id>', methods=['GET'])
@jwt_required()
def get_image(file_id):
    """获取图像详情"""
    current_user_id = int(get_jwt_identity())
    
    image = Image.query.filter_by(
        file_id=file_id,
        user_id=current_user_id,
        status=1
    ).first()
    
    if not image:
        return not_found('图像不存在')
    
    return jsonify({
        'image': image.to_dict(include_paths=True)
    })

@api_bp.route('/images/<file_id>', methods=['DELETE'])
@jwt_required()
def delete_image(file_id):
    """删除图像"""
    current_user_id = int(get_jwt_identity())
    
    image = Image.query.filter_by(
        file_id=file_id,
        user_id=current_user_id,
        status=1
    ).first()
    
    if not image:
        return not_found('图像不存在')
    
    try:
        # 标记为删除状态
        image.status = 2
        
        # 记录操作日志
        UserLog.log_action(
            user_id=current_user_id,
            action='image_delete',
            resource_type='image',
            resource_id=image.id,
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        
        db.session.commit()
        
        # 异步删除文件（可以放到Celery任务中）
        try:
            image.delete_files()
        except Exception as e:
            current_app.logger.error(f"删除文件失败: {e}")
        
        return jsonify({'message': '图像删除成功'})
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除图像失败: {e}")
        return validation_error('删除失败，请重试')

@api_bp.route('/images/<file_id>/name', methods=['PUT'])
@jwt_required()
def update_image_name(file_id):
    """修改图像文件名"""
    current_user_id = int(get_jwt_identity())
    
    image = Image.query.filter_by(
        file_id=file_id,
        user_id=current_user_id,
        status=1
    ).first()
    
    if not image:
        return not_found('图像不存在')
    
    data = request.get_json()
    if not data or 'original_filename' not in data:
        return bad_request('缺少文件名参数')
    
    new_filename = data['original_filename'].strip()
    if not new_filename:
        return bad_request('文件名不能为空')
    
    try:
        # 记录修改日志
        UserLog.log_action(
            user_id=current_user_id,
            action='image_rename',
            resource_type='image',
            resource_id=image.id,
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string,
            details={
                'old_filename': image.original_filename,
                'new_filename': new_filename
            }
        )
        
        # 更新文件名
        image.original_filename = new_filename
        image.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件名修改成功',
            'image': image.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"修改文件名失败: {e}")
        return validation_error('修改失败，请重试')

@api_bp.route('/images/<file_id>/download', methods=['GET'])
@jwt_required()
def download_image(file_id):
    """下载图像"""
    current_user_id = int(get_jwt_identity())
    
    image = Image.query.filter_by(
        file_id=file_id,
        user_id=current_user_id,
        status=1
    ).first()
    
    if not image:
        return not_found('图像不存在')
    
    try:
        file_path = image.get_full_path()
        
        if not os.path.exists(file_path):
            return not_found('文件不存在')
        
        # 记录下载日志
        UserLog.log_action(
            user_id=current_user_id,
            action='image_download',
            resource_type='image',
            resource_id=image.id,
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        db.session.commit()
        
        return send_file(
            file_path,
            as_attachment=True,
            download_name=image.original_filename,
            mimetype=image.mime_type
        )
        
    except Exception as e:
        current_app.logger.error(f"下载图像失败: {e}")
        return validation_error('下载失败，请重试')

@api_bp.route('/images/stats', methods=['GET'])
@jwt_required()
def get_image_stats():
    """获取用户图像统计"""
    current_user_id = int(get_jwt_identity())
    
    try:
        # 总图像数
        total_images = Image.query.filter_by(
            user_id=current_user_id,
            status=1
        ).count()
        
        # 总存储大小
        total_size = db.session.query(
            db.func.sum(Image.file_size)
        ).filter_by(
            user_id=current_user_id,
            status=1
        ).scalar() or 0
        
        # 本月上传数
        from datetime import datetime, timedelta
        start_of_month = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        monthly_uploads = Image.query.filter(
            Image.user_id == current_user_id,
            Image.status == 1,
            Image.created_at >= start_of_month
        ).count()
        
        return jsonify({
            'stats': {
                'total_images': total_images,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'monthly_uploads': monthly_uploads
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"获取图像统计失败: {e}")
        return validation_error('获取统计失败')

@api_bp.route('/images/processed/<user_id>/<filename>', methods=['GET'])
def get_processed_image(user_id, filename):
    """获取处理后的图片"""
    try:
        # 验证文件名安全性
        filename = secure_filename(filename)
        
        # 构建文件路径
        file_path = os.path.join(current_app.config['PROCESSED_FOLDER'], str(user_id), filename)
        
        if not os.path.exists(file_path):
            return jsonify({'error': '文件不存在'}), 404
        
        return send_file(file_path, mimetype='image/jpeg')
        
    except Exception as e:
        current_app.logger.error(f"获取处理结果失败: {e}")
        return jsonify({'error': f'获取文件失败: {str(e)}'}), 500

@api_bp.route('/images/save-processed', methods=['POST'])
@jwt_required()
def save_processed_image():
    """保存处理完成的图片"""
    current_user_id = int(get_jwt_identity())
    user = User.query.get(current_user_id)
    if not user:
        return unauthorized('用户不存在')

    data = request.get_json()
    if not data or 'image_data' not in data:
        return bad_request('缺少图片数据')

    image_data_url = data.get('image_data')
    original_id = data.get('original_id') # 可选，用于追溯原图

    try:
        # 解码Base64图片
        if 'data:image' in image_data_url:
            header, encoded = image_data_url.split(',', 1)
            mime_type = header.split(';')[0].split(':')[1]
            extension = mime_type.split('/')[1]
        else:
            return bad_request('无效的图片数据格式')
        
        # 检查配置
        static_folder = current_app.config.get('STATIC_FOLDER')
        if not static_folder:
            current_app.logger.error("STATIC_FOLDER 配置未设置")
            return bad_request('服务器配置错误')
        
        try:
            image_data = base64.b64decode(encoded)
        except Exception as decode_error:
            current_app.logger.error(f"Base64解码失败: {decode_error}")
            return bad_request('图片数据解码失败')

        # 生成文件名和路径
        file_id = uuid.uuid4().hex
        new_filename = f"processed_{file_id}.{extension}"
        
        user_processed_dir = os.path.join(static_folder, 'processed', str(user.id))
        
        try:
            os.makedirs(user_processed_dir, exist_ok=True)
        except Exception as dir_error:
            current_app.logger.error(f"创建目录失败: {dir_error}")
            return bad_request('无法创建保存目录')
            
        file_path = os.path.join(user_processed_dir, new_filename)

        # 保存文件
        try:
            with open(file_path, 'wb') as f:
                f.write(image_data)
        except Exception as file_error:
            current_app.logger.error(f"文件写入失败: {file_error}")
            return bad_request('文件保存失败')
        
        # 创建数据库记录
        try:
            # 计算文件大小
            file_size = len(image_data)
            
            image = Image(
                user_id=user.id,
                file_id=file_id,
                original_filename=f"processed_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}.{extension}",
                file_path=os.path.join('processed', str(user.id), new_filename),
                file_size=file_size,
                mime_type=mime_type,
                upload_source='editor_processed',
                status=1 # 标记为正常状态（可访问）
            )
            
            # 提取图片尺寸信息
            image.extract_image_info()
            
            # 创建缩略图
            image.create_thumbnail()
            
            db.session.add(image)
            db.session.commit()
        except Exception as db_error:
            current_app.logger.error(f"数据库保存失败: {db_error}")
            # 如果数据库保存失败，删除已保存的文件
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass
            return bad_request('数据库保存失败')

        return jsonify({
            'success': True,
            'message': '处理结果保存成功',
            'image': image.to_dict()
        }), 201

    except Exception as e:
        current_app.logger.error(f"保存处理图片失败: {e}")
        import traceback
        current_app.logger.error(traceback.format_exc())
        return bad_request(f'保存失败: {str(e)}')