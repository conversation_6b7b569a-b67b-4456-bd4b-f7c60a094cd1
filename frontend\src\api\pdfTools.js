import axios from 'axios';
import { useAuthStore } from '@/stores/auth';

const api = axios.create({
  baseURL: '/api/pdf',
  timeout: 60000
});

api.interceptors.request.use(config => {
  const authStore = useAuthStore();
  const token = authStore.token;
  if (token) config.headers.Authorization = `Bearer ${token}`;
  return config;
});

export function mergePdfs(formData) {
  return api.post('/merge', formData, { responseType: 'blob' });
}
export function splitPdf(formData) {
  return api.post('/split', formData, { responseType: 'blob' });
}
export function longImage(formData) {
  return api.post('/long-image', formData, { responseType: 'blob' });
}
export function annotatePdf(formData) {
  return api.post('/annotate', formData, { responseType: 'blob' });
}
export function ocrPdf(formData) {
  return api.post('/ocr', formData, {
    onUploadProgress: progressEvent => {
      const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
      // You can use a store or event bus to pass this progress to the component
      console.log(`Upload Progress: ${percentCompleted}%`);
    }
  });
}
export function exportPdf(formData) {
  return api.post('/export', formData, { responseType: 'blob' });
} 