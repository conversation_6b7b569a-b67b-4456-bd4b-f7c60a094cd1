<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="优雅商务风格名片">
    <title>{{ card.name|default('名片', true) }}</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ style.font_family|default('Arial, sans-serif', true) }};
            background-color: {{ style.background_color|default('#2c3e50', true) }};
            line-height: 1.4;
            color: {{ style.text_color|default('#ffffff', true) }};
        }
        
        /* 名片容器 */
        .elegant-business .business-card {
            width: 90mm;
            height: 54mm;
            padding: 8mm;
            background: {{ style.background_color|default('#2c3e50', true) }};
            color: {{ style.text_color|default('#ffffff', true) }};
            font-family: {{ style.font_family|default('Arial, sans-serif', true) }};
            position: relative;
            border: 1px solid {{ style.border_color|default('#34495e', true) }};
            overflow: hidden;
        }
        
        /* 金色装饰线 */
        .elegant-business .gold-line {
            position: absolute;
            top: 0;
            left: 8mm;
            right: 8mm;
            height: 2px;
            background: linear-gradient(90deg, transparent, {{ style.gold_color|default('#f39c12', true) }}, transparent);
        }
        
        /* 姓名样式 */
        .elegant-business .name {
            font-size: {{ style.name_size|default(style.font_size_name, true)|default('18px', true) }};
            font-weight: {{ style.name_weight|default(style.name_font_weight, true)|default('bold', true) }};
            color: {{ style.name_color|default(style.gold_color, true)|default('#f39c12', true) }};
            margin: 0 0 2mm 0;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }
        
        /* 职位标题 */
        .elegant-business .title {
            font-size: {{ style.title_size|default('12px', true) }};
            color: {{ style.light_color|default('rgba(255,255,255,0.7)', true) }};
            margin: 0 0 4mm 0;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: normal;
        }
        
        /* 公司名称 */
        .elegant-business .company-name {
            font-size: {{ style.company_size|default(style.font_size_company, true)|default('14px', true) }};
            color: {{ style.company_color|default(style.text_color, true)|default('#ffffff', true) }};
            margin: 0 0 4mm 0;
            font-weight: {{ style.company_weight|default(style.company_font_weight, true)|default('500', true) }};
        }
        
        /* 联系信息行 */
        .elegant-business .contact-row {
            display: flex;
            align-items: center;
            margin-bottom: 2mm;
            font-size: {{ style.contact_size|default(style.font_size_contact, true)|default('11px', true) }};
            color: {{ style.contact_color|default(style.light_color, true)|default('rgba(255,255,255,0.7)', true) }};
            font-weight: {{ style.contact_weight|default(style.contact_font_weight, true)|default('normal', true) }};
            min-height: 3mm;
        }
        
        /* 图标样式 */
        .elegant-business .icon {
            margin-right: 3mm;
            font-size: 10px;
            color: {{ style.gold_color|default('#f39c12', true) }};
            width: 4mm;
            text-align: center;
            flex-shrink: 0;
        }
        
        /* 角部装饰 */
        .elegant-business .corner-decoration {
            position: absolute;
            bottom: 6mm;
            right: 6mm;
            width: 8mm;
            height: 8mm;
            border: 1px solid {{ style.gold_color|default('#f39c12', true) }};
            border-radius: 50%;
        }
        
        .elegant-business .corner-decoration::after {
            content: "";
            position: absolute;
            top: 2mm;
            left: 2mm;
            width: 4mm;
            height: 4mm;
            background: radial-gradient(circle, {{ style.gold_color|default('#f39c12', true) }} 30%, transparent 30%);
            border-radius: 50%;
        }
        
        /* 图标样式 */
        .icon-phone::before { content: "📞"; }
        .icon-email::before { content: "📧"; }
        .icon-location::before { content: "📍"; }
        .icon-website::before { content: "🌐"; }
        
        /* 响应式设计 */
        @media print {
            body {
                background: white;
            }
            .business-card {
                border: none;
                box-shadow: none;
            }
        }
        
        /* 可访问性增强 */
        .elegant-business .name:focus,
        .elegant-business .title:focus,
        .elegant-business .company-name:focus {
            outline: 2px solid {{ style.gold_color|default('#f39c12', true) }};
            outline-offset: 1px;
        }
    </style>
</head>
<body>
    <div class="business-card elegant-business" role="article" aria-label="优雅商务名片">
        <div class="gold-line" aria-hidden="true"></div>
        
        <header class="header">
            <h1 class="name" id="card-name">{{ card.name|default('姓名', true) }}</h1>
            <p class="title" id="card-title">{{ card.title|default('职位', true) }}</p>
        </header>
        
        <section class="company" aria-labelledby="card-company">
            <p class="company-name" id="card-company">{{ card.company|default('公司名称', true) }}</p>
        </section>
        
        <section class="contact" aria-label="联系信息">
            {% if card.phone %}
            <div class="contact-row" role="group" aria-labelledby="phone-label">
                <i class="icon icon-phone" aria-hidden="true"></i>
                <span id="phone-label">{{ card.phone }}</span>
            </div>
            {% endif %}
            
            {% if card.email %}
            <div class="contact-row" role="group" aria-labelledby="email-label">
                <i class="icon icon-email" aria-hidden="true"></i>
                <span id="email-label">{{ card.email }}</span>
            </div>
            {% endif %}
            
            {% if card.address %}
            <div class="contact-row" role="group" aria-labelledby="address-label">
                <i class="icon icon-location" aria-hidden="true"></i>
                <span id="address-label">{{ card.address }}</span>
            </div>
            {% endif %}
            
            {% if card.website %}
            <div class="contact-row" role="group" aria-labelledby="website-label">
                <i class="icon icon-website" aria-hidden="true"></i>
                <span id="website-label">{{ card.website }}</span>
            </div>
            {% endif %}
        </section>
        
        <div class="corner-decoration" aria-hidden="true"></div>
    </div>
</body>
</html> 