<template>
  <aside class="sidebar">
    <div class="sidebar-title">目录</div>
    <div class="page-thumbs">
      <div v-for="(page, idx) in pages" :key="page.id" :class="['page-thumb', {selected: idx === currentPage}]" @click="$emit('select-page', idx)">
        <canvas :ref="el => setCanvasRef(el, idx)" />
        <span class="page-num">{{ idx + 1 }}</span>
      </div>
    </div>
  </aside>
</template>
<script setup>
import { ref, watch, nextTick } from 'vue';
const props = defineProps(['pages', 'currentPage'])
const canvasRefs = ref([])
function setCanvasRef(el, idx) { if (el) canvasRefs.value[idx]=el; }
watch(() => props.pages, renderThumbs, {immediate:true, deep:true})
async function renderThumbs() {
  await nextTick();
  for (let i=0; i<props.pages.length; i++) {
    const page = props.pages[i]
    const canvas = canvasRefs.value[i]
    if (canvas && page.previewUrl) {
      const img = new window.Image(); img.src = page.previewUrl;
      img.onload = () => { canvas.width=img.width; canvas.height=img.height; canvas.getContext('2d').drawImage(img,0,0); }
    }
  }
}
</script>
<style scoped>
.sidebar {
  width: 200px;
  background: #f7f8fa;
  border-right: 1px solid #e5e6eb;
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 120px;
}
.sidebar-title {
  font-size: 15px;
  font-weight: 600;
  color: #888;
  margin-bottom: 0.7rem;
  padding-left: 0.5em;
}
.page-thumbs {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  overflow-y: auto;
}
.page-thumb {
  border: 2px solid #eee;
  border-radius: 6px;
  padding: 2px;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  background: #fff;
  transition: border 0.2s, box-shadow 0.2s;
}
.page-thumb.selected {
  border-color: #2563eb;
  background: #e3eaff;
  box-shadow: 0 2px 8px #2563eb22;
}
.page-thumb:hover {
  border-color: #3182ce;
}
.page-num {
  margin-left: 0.5em;
  color: #888;
  font-size: 14px;
}
</style> 