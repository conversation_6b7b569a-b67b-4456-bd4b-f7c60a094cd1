#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能证件照系统完整重建脚本
解决网络异常和数据库连接问题，重新构建整个系统
"""

import os
import sys
import shutil
import sqlite3
import glob
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
backend_path = os.path.join(project_root, 'backend')
sys.path.insert(0, backend_path)

def clean_database():
    """清理现有数据库文件"""
    print("🗑️  清理现有数据库文件...")

    db_files = [
        'backend/photo_maker.db',
        'backend/photo_maker.db-shm',
        'backend/photo_maker.db-wal',
        'backend/instance/photo_maker.db'
    ]

    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                os.remove(db_file)
                print(f"   删除: {db_file}")
            except PermissionError:
                print(f"   文件被占用，尝试强制删除: {db_file}")
                try:
                    # 尝试修改文件权限后删除
                    import stat
                    os.chmod(db_file, stat.S_IWRITE)
                    os.remove(db_file)
                    print(f"   强制删除成功: {db_file}")
                except Exception as e:
                    print(f"   无法删除文件 {db_file}: {e}")
                    print(f"   请手动删除该文件后重新运行脚本")

    print("✅ 数据库文件清理完成")

def create_directories():
    """创建必要的目录结构"""
    print("📁 创建目录结构...")
    
    directories = [
        'backend/instance',
        'backend/uploads',
        'backend/uploads/images',
        'backend/uploads/processed', 
        'backend/uploads/thumbnails',
        'backend/uploads/business_cards',
        'backend/uploads/documents',
        'backend/uploads/reports',
        'backend/static',
        'backend/static/processed',
        'backend/static/business_cards',
        'backend/static/qr_codes',
        'uploads',
        'uploads/images',
        'uploads/processed',
        'uploads/thumbnails'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"   创建: {directory}")
    
    print("✅ 目录结构创建完成")

def fix_network_config():
    """修复网络配置"""
    print("🌐 修复网络配置...")
    
    # 修复前端API配置
    frontend_api_config = """import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '../stores/auth'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 导出API基础URL供其他组件使用
export const API_BASE_URL = 'http://localhost:5000'

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    // 添加认证token
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    console.log('API请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.config.url)
    return response
  },
  async (error) => {
    const { response } = error
    
    console.error('API错误:', response?.status, response?.config?.url, response?.data)
    
    // 处理不同类型的错误
    if (response) {
      const { status, data } = response
      
      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          const authStore = useAuthStore()
          authStore.logout()
          break
        case 403:
          ElMessage.error('没有权限访问此资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else {
      ElMessage.error('网络连接失败，请检查网络设置')
    }
    
    return Promise.reject(error)
  }
)

export default api

// API接口定义
export const apiEndpoints = {
  // 认证相关
  auth: {
    login: (data) => api.post('/auth/login', data),
    register: (data) => api.post('/auth/register', data),
    logout: () => api.post('/auth/logout'),
    profile: () => api.get('/auth/profile'),
    refreshToken: () => api.post('/auth/refresh')
  },
  
  // 证件照处理
  idPhoto: {
    process: (data) => api.post('/processing/id-photo', data),
    detectCropArea: (data) => api.post('/processing/detect-crop-area', data),
    pureCrop: (data) => api.post('/processing/pure-crop', data),
    processBeauty: (data) => api.post('/processing/beauty', data),
    processBackground: (data) => api.post('/processing/background', data),
    processFinalSize: (data) => api.post('/processing/final-size', data)
  },
  
  // 模板管理
  templates: {
    getPhotoTemplates: () => api.get('/templates/photo'),
    getBusinessCardTemplates: () => api.get('/templates/business-cards'),
    getResumeTemplates: () => api.get('/templates/resumes')
  },
  
  // 名片功能
  businessCards: {
    list: () => api.get('/business-cards'),
    create: (data) => api.post('/business-cards', data),
    update: (id, data) => api.put(`/business-cards/${id}`, data),
    delete: (id) => api.delete(`/business-cards/${id}`),
    download: (id, format) => api.get(`/business-cards/${id}/download?format=${format}`, { responseType: 'blob' })
  },
  
  // 简历功能
  resumes: {
    list: () => api.get('/resumes'),
    create: (data) => api.post('/resumes', data),
    update: (id, data) => api.put(`/resumes/${id}`, data),
    delete: (id) => api.delete(`/resumes/${id}`),
    download: (id, format) => api.get(`/resumes/${id}/download?format=${format}`, { responseType: 'blob' })
  },
  
  // 文档对比
  documents: {
    compare: (data) => api.post('/documents/compare', data),
    getComparisonResult: (taskId) => api.get(`/documents/compare/${taskId}`)
  },
  
  // 通用接口
  common: {
    getConfig: () => api.get('/common/config'),
    healthCheck: () => api.get('/common/health')
  }
}
"""
    
    # 写入前端API配置文件
    frontend_api_path = 'frontend/src/api/index.js'
    if os.path.exists(frontend_api_path):
        with open(frontend_api_path, 'w', encoding='utf-8') as f:
            f.write(frontend_api_config)
        print(f"   修复前端API配置: {frontend_api_path}")
    
    print("✅ 网络配置修复完成")

def init_database():
    """初始化数据库"""
    print("🗄️  初始化数据库...")
    
    try:
        # 设置环境变量
        os.environ['FLASK_ENV'] = 'development'
        
        # 导入Flask应用
        from app import create_app, db
        
        app = create_app()
        
        with app.app_context():
            # 创建所有表
            db.create_all()
            print("   数据库表创建完成")
            
            # 初始化系统配置
            init_system_config()
            
            # 初始化用户数据
            init_users()
            
            # 初始化模板数据
            init_templates()
            
            # 初始化名片模板
            init_business_card_templates()
            
            # 初始化简历模板
            init_resume_templates()

            # 初始化相纸规格
            init_paper_sizes()

            # 提交所有更改
            db.session.commit()
            print("✅ 数据库初始化完成")
            
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def init_system_config():
    """初始化系统配置"""
    print("⚙️  初始化系统配置...")
    
    from app.models.system_config import SystemConfig
    from app import db
    
    configs = [
        # 基础配置
        {'config_key': 'app_name', 'config_value': '智能证件照制作工具', 'description': '应用名称'},
        {'config_key': 'app_version', 'config_value': '2.0.0', 'description': '应用版本'},
        {'config_key': 'app_description', 'config_value': '基于AI技术的多平台证件照制作工具', 'description': '应用描述'},

        # 文件上传配置
        {'config_key': 'max_upload_size', 'config_value': '16777216', 'description': '最大上传文件大小(字节)'},
        {'config_key': 'allowed_image_types', 'config_value': '["jpg", "jpeg", "png", "bmp", "gif"]', 'description': '允许的图片类型'},
        {'config_key': 'allowed_document_types', 'config_value': '["pdf", "doc", "docx", "txt"]', 'description': '允许的文档类型'},

        # 图像处理配置
        {'config_key': 'image_quality', 'config_value': '95', 'description': '图片质量'},
        {'config_key': 'max_image_resolution', 'config_value': '4096', 'description': '最大图片分辨率'},
        {'config_key': 'thumbnail_size', 'config_value': '300', 'description': '缩略图尺寸'},

        # 功能开关
        {'config_key': 'enable_face_beautify', 'config_value': 'true', 'description': '启用人脸美化'},
        {'config_key': 'enable_background_removal', 'config_value': 'true', 'description': '启用背景移除'},
        {'config_key': 'enable_document_comparison', 'config_value': 'true', 'description': '启用文档对比'},
        {'config_key': 'enable_resume_generation', 'config_value': 'true', 'description': '启用简历生成'},
        {'config_key': 'enable_business_cards', 'config_value': 'true', 'description': '启用名片制作'},

        # 默认背景颜色
        {'config_key': 'default_background_colors', 'config_value': '["#ffffff", "#ff0000", "#0000ff", "#e6f3ff"]', 'description': '默认背景颜色'},

        # 用户限制
        {'config_key': 'free_user_daily_limit', 'config_value': '50', 'description': '免费用户每日使用限制'},
        {'config_key': 'vip_user_daily_limit', 'config_value': '500', 'description': 'VIP用户每日使用限制'},

        # 安全配置
        {'config_key': 'password_min_length', 'config_value': '6', 'description': '密码最小长度'},
        {'config_key': 'session_timeout', 'config_value': '3600', 'description': '会话超时时间(秒)'},
    ]

    for config_data in configs:
        existing = SystemConfig.query.filter_by(config_key=config_data['config_key']).first()
        if not existing:
            config = SystemConfig(**config_data)
            db.session.add(config)
    
    print("✅ 系统配置初始化完成")

def init_users():
    """初始化用户数据"""
    print("👤 初始化用户数据...")

    from app.models.user import User
    from app import db

    # 创建管理员用户
    admin_user = User.query.filter_by(username='admin').first()
    if not admin_user:
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_admin=True,
            is_active=True,
            points=10000
        )
        db.session.add(admin_user)
        print("   创建管理员用户: admin/admin123")

    # 创建测试用户
    test_user = User.query.filter_by(username='test').first()
    if not test_user:
        test_user = User(
            username='test',
            email='<EMAIL>',
            password='test123',
            is_active=True,
            points=100
        )
        db.session.add(test_user)
        print("   创建测试用户: test/test123")

    print("✅ 用户数据初始化完成")

def init_templates():
    """初始化证件照模板"""
    print("📋 初始化证件照模板...")

    from app.models.photo_template import PhotoTemplate
    from app import db

    if PhotoTemplate.query.count() == 0:
        templates = [
            {
                'name': '一寸照',
                'category': '常用证件照',
                'width': 295,
                'height': 413,
                'width_mm': 25,
                'height_mm': 35,
                'description': '标准一寸证件照，适用于各种证件申请',
                'is_active': True,
                'sort_order': 1
            },
            {
                'name': '二寸照',
                'category': '常用证件照',
                'width': 413,
                'height': 531,
                'width_mm': 35,
                'height_mm': 45,
                'description': '标准二寸证件照，适用于护照、签证等',
                'is_active': True,
                'sort_order': 2
            },
            {
                'name': '护照照片',
                'category': '出国证件',
                'width': 354,
                'height': 472,
                'width_mm': 30,
                'height_mm': 40,
                'description': '护照专用照片规格',
                'is_active': True,
                'sort_order': 3
            },
            {
                'name': '签证照片',
                'category': '出国证件',
                'width': 354,
                'height': 472,
                'width_mm': 30,
                'height_mm': 40,
                'description': '签证申请专用照片',
                'is_active': True,
                'sort_order': 4
            },
            {
                'name': '驾驶证照片',
                'category': '证件照',
                'width': 260,
                'height': 378,
                'width_mm': 22,
                'height_mm': 32,
                'description': '驾驶证专用照片规格',
                'is_active': True,
                'sort_order': 5
            },
            {
                'name': '身份证照片',
                'category': '证件照',
                'width': 260,
                'height': 378,
                'width_mm': 22,
                'height_mm': 32,
                'description': '身份证专用照片规格',
                'is_active': True,
                'sort_order': 6
            }
        ]

        for template_data in templates:
            template = PhotoTemplate(**template_data)
            db.session.add(template)

        print(f"   创建了 {len(templates)} 个证件照模板")

    print("✅ 证件照模板初始化完成")

def init_business_card_templates():
    """初始化名片模板"""
    print("💼 初始化名片模板...")

    from app.models.business_card import BusinessCardTemplate
    from app import db

    if BusinessCardTemplate.query.count() == 0:
        templates = [
            {
                'name': '经典商务',
                'description': '简洁大方的商务风格名片',
                'category': '商务',
                'industry': '通用',
                'layout_type': 'horizontal',
                'width_mm': 90,
                'height_mm': 54,
                'template_html': '''
                <div class="business-card classic">
                    <div class="header">
                        <h1 class="name">{{name}}</h1>
                        <p class="title">{{title}}</p>
                    </div>
                    <div class="content">
                        <div class="contact">
                            <p class="phone">{{phone}}</p>
                            <p class="email">{{email}}</p>
                            <p class="address">{{address}}</p>
                        </div>
                        <div class="qr-code">
                            {{qr_code}}
                        </div>
                    </div>
                </div>
                ''',
                'template_css': '''
                .business-card.classic {
                    width: 90mm;
                    height: 54mm;
                    background: #ffffff;
                    border: 1px solid #e0e0e0;
                    padding: 8mm;
                    font-family: "Microsoft YaHei", Arial, sans-serif;
                    box-sizing: border-box;
                }
                .header .name {
                    font-size: 18px;
                    font-weight: bold;
                    color: #333;
                    margin: 0 0 4px 0;
                }
                .header .title {
                    font-size: 12px;
                    color: #666;
                    margin: 0 0 8px 0;
                }
                .content {
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                }
                .contact p {
                    font-size: 10px;
                    color: #555;
                    margin: 2px 0;
                }
                .qr-code {
                    width: 20mm;
                    height: 20mm;
                }
                ''',
                'default_colors': '["#333333", "#666666", "#ffffff"]',
                'is_active': True,
                'sort_order': 1
            }
        ]

        for template_data in templates:
            template = BusinessCardTemplate(**template_data)
            db.session.add(template)

        print(f"   创建了 {len(templates)} 个名片模板")

    print("✅ 名片模板初始化完成")

def init_resume_templates():
    """初始化简历模板"""
    print("📄 初始化简历模板...")

    from app.models.resume import ResumeTemplate
    from app import db

    if ResumeTemplate.query.count() == 0:
        templates = [
            {
                'name': '经典简历',
                'description': '传统经典的简历模板，适合大多数行业',
                'category': '经典',
                'layout': {
                    'type': 'single_column',
                    'sections': ['header', 'objective', 'experience', 'education', 'skills']
                },
                'style': {
                    'font_family': 'Microsoft YaHei',
                    'font_size': '14px',
                    'line_height': '1.6',
                    'color': '#333',
                    'background': '#ffffff'
                },
                'sections': {
                    'header': {
                        'enabled': True,
                        'fields': ['name', 'phone', 'email', 'address'],
                        'style': 'center'
                    },
                    'objective': {
                        'enabled': True,
                        'title': '求职意向'
                    },
                    'experience': {
                        'enabled': True,
                        'title': '工作经历',
                        'fields': ['company', 'position', 'start_date', 'end_date', 'description']
                    },
                    'education': {
                        'enabled': True,
                        'title': '教育背景',
                        'fields': ['school', 'degree', 'major', 'start_date', 'end_date']
                    },
                    'skills': {
                        'enabled': True,
                        'title': '专业技能',
                        'style': 'grid'
                    }
                },
                'is_active': True,
                'sort_order': 1
            }
        ]

        for template_data in templates:
            template = ResumeTemplate(**template_data)
            db.session.add(template)

        print(f"   创建了 {len(templates)} 个简历模板")

    print("✅ 简历模板初始化完成")

def init_paper_sizes():
    """初始化相纸规格"""
    print("📄 初始化相纸规格...")

    from app.models.paper_size import PaperSize
    from app import db

    if PaperSize.query.count() == 0:
        paper_sizes = [
            {
                'name': 'A4',
                'width_mm': 210.00,
                'height_mm': 297.00,
                'width_px': 2480,
                'height_px': 3508,
                'margin_mm': 10.00,
                'is_common': True,
                'sort_order': 1
            },
            {
                'name': 'A5',
                'width_mm': 148.00,
                'height_mm': 210.00,
                'width_px': 1748,
                'height_px': 2480,
                'margin_mm': 10.00,
                'is_common': False,
                'sort_order': 2
            },
            {
                'name': '5寸照片',
                'width_mm': 127.00,
                'height_mm': 89.00,
                'width_px': 1500,
                'height_px': 1050,
                'margin_mm': 5.00,
                'is_common': True,
                'sort_order': 3
            },
            {
                'name': '6寸照片',
                'width_mm': 152.00,
                'height_mm': 102.00,
                'width_px': 1800,
                'height_px': 1200,
                'margin_mm': 5.00,
                'is_common': True,
                'sort_order': 4
            },
            {
                'name': '7寸照片',
                'width_mm': 178.00,
                'height_mm': 127.00,
                'width_px': 2100,
                'height_px': 1500,
                'margin_mm': 5.00,
                'is_common': False,
                'sort_order': 5
            },
            {
                'name': '8寸照片',
                'width_mm': 203.00,
                'height_mm': 152.00,
                'width_px': 2400,
                'height_px': 1800,
                'margin_mm': 5.00,
                'is_common': False,
                'sort_order': 6
            }
        ]

        for paper_data in paper_sizes:
            paper_size = PaperSize(**paper_data)
            db.session.add(paper_size)

        print(f"   创建了 {len(paper_sizes)} 个相纸规格")

    print("✅ 相纸规格初始化完成")

def clean_unnecessary_files():
    """清理不必要的文件"""
    print("🧹 清理不必要的文件...")

    # 要删除的文件列表
    files_to_remove = [
        # 测试文件
        'backend/test_*.py',
        'backend/simple_test_qr.png',
        'backend/test_qr_*.png',
        'backend/test_vcard_qr.png',

        # 临时文件
        'backend/*.db-shm',
        'backend/*.db-wal',

        # 不必要的脚本
        'backend/migrate_points.py',
        'backend/clean_db.py',
        'backend/update_db.py',
        'backend/init_business_cards.py',
    ]

    removed_count = 0

    for pattern in files_to_remove:
        for file_path in glob.glob(pattern):
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"   删除: {file_path}")
                removed_count += 1

    # 删除不必要的文档
    docs_to_remove = [
        '二维码生成问题修复完成报告.md',
        '内存优化完成报告.md',
        '内容管理功能完成报告.md',
        '名片功能实现完成报告.md',
        '名片模板系统实现报告.md',
        '文档对比功能修复完成报告.md',
        '管理员界面功能完成报告.md',
        '项目清理完成总结.md',
        'DocumentManager界面优化完成报告.md'
    ]

    for doc in docs_to_remove:
        if os.path.exists(doc):
            os.remove(doc)
            print(f"   删除文档: {doc}")
            removed_count += 1

    print(f"✅ 清理完成，删除了 {removed_count} 个文件")

def main():
    """主函数"""
    print("🚀 开始系统重建...")
    print("=" * 50)
    
    try:
        # 1. 清理数据库
        clean_database()
        
        # 2. 创建目录结构
        create_directories()
        
        # 3. 修复网络配置
        fix_network_config()
        
        # 4. 初始化数据库
        if not init_database():
            print("❌ 数据库初始化失败，停止重建")
            return False

        # 5. 清理不必要的文件
        clean_unnecessary_files()

        print("=" * 50)
        print("🎉 系统重建完成!")
        print()
        print("📋 系统信息:")
        print("   - 管理员账号: admin / admin123")
        print("   - 测试账号: test / test123")
        print("   - 数据库: SQLite (开发环境)")
        print("   - 前端地址: http://localhost:8097")
        print("   - 后端地址: http://localhost:5000")
        print()
        print("🔧 启动命令:")
        print("   后端: cd backend && python run.py")
        print("   前端: cd frontend && npm run serve")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ 系统重建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
