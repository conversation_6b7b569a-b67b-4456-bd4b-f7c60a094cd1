"""
数据库初始化脚本
用于创建和初始化数据库，包括：
1. 创建所有数据表
2. 添加管理员账户
3. 添加基础模板数据
"""
import os
import sys
import shutil
import logging
from pathlib import Path
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置项目根目录
root_dir = Path(__file__).parent.parent
sys.path.append(str(root_dir))

from flask import Flask
from app import create_app
from app.database import db, init_app, create_tables
from app.models.user import User
from app.models.resume_template import ResumeTemplate
from app.utils.security_utils import generate_password_hash

def clean_instance_folder():
    """清理instance文件夹"""
    try:
        instance_dir = root_dir / 'instance'
        if instance_dir.exists():
            shutil.rmtree(instance_dir)
            logger.info("已删除instance文件夹")
        instance_dir.mkdir(exist_ok=True)
        logger.info("已创建新的instance文件夹")
    except Exception as e:
        logger.error(f"清理instance文件夹失败: {str(e)}")
        raise

def check_database():
    """检查数据库状态"""
    db_path = root_dir / 'instance' / 'photo_maker.db'
    exists = db_path.exists()
    if exists:
        logger.info(f"数据库文件已存在: {db_path}")
    return exists

def drop_database():
    """删除旧的数据库文件"""
    try:
        db_path = root_dir / 'instance' / 'photo_maker.db'
        if db_path.exists():
            db_path.unlink()
            logger.info("已删除旧的数据库文件")
    except Exception as e:
        logger.error(f"删除数据库文件失败: {str(e)}")
        raise

def create_admin_user(db) -> None:
    """创建管理员用户"""
    try:
        # 创建管理员用户
        password_hash, salt = generate_password_hash('admin123')
        admin = User(
            username='admin',
            password_hash=password_hash,
            salt=salt,
            email='<EMAIL>',
            is_admin=True,
            status=1,  # 1表示正常状态
            nickname='系统管理员'
        )
        db.session.add(admin)
        db.session.commit()
        logger.info("管理员用户创建成功")
    except Exception as e:
        logger.error(f"创建管理员用户失败: {str(e)}")
        db.session.rollback()
        raise

def create_base_templates(db) -> None:
    """创建基础模板"""
    try:
        templates = [
            {
                'name': '简约专业模板',
                'description': '清晰简约的专业简历模板',
                'category': 'professional',
                'layout': {
                    'sections': ['basic_info', 'education', 'experience', 'skills']
                },
                'style': {
                    'primary_color': '#2c3e50',
                    'font_family': 'Arial, sans-serif',
                    'spacing': '1.5'
                },
                'is_active': True,
                'sort_order': 1
            },
            {
                'name': '创意设计模板',
                'description': '突出创意和设计感的简历模板',
                'category': 'creative',
                'layout': {
                    'sections': ['header', 'portfolio', 'skills', 'experience']
                },
                'style': {
                    'primary_color': '#e74c3c',
                    'font_family': 'Helvetica, sans-serif',
                    'spacing': '2.0'
                },
                'is_active': True,
                'sort_order': 2
            }
        ]
        
        for template_data in templates:
            template = ResumeTemplate(**template_data)
            db.session.add(template)
        db.session.commit()
        logger.info("基础简历模板创建成功")
    except Exception as e:
        logger.error(f"创建基础模板失败: {str(e)}")
        db.session.rollback()
        raise

def initialize_database(app) -> None:
    """初始化数据库"""
    with app.app_context():
        try:
            # 创建所有表
            create_tables(app)
            logger.info("数据库表创建成功")
            
            # 创建管理员账户
            create_admin_user(db)
            
            # 创建基础模板
            create_base_templates(db)
            
            logger.info("数据库初始化完成")
        except Exception as e:
            if hasattr(db, 'session'):
                db.session.rollback()
            logger.error(f"数据库初始化失败: {str(e)}")
            raise

def main():
    """主函数"""
    try:
        # 创建应用实例
        app = create_app()
        
        # 确保数据库被正确初始化
        init_app(app)
        
        # 初始化数据库
        initialize_database(app)
        
        logger.info("所有操作完成")
        
    except Exception as e:
        logger.error(f"初始化过程失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
