from flask import jsonify, current_app

from . import api_bp
from app.models import SystemConfig
from app.models.ad_position import AdPosition

@api_bp.route('/common/config', methods=['GET'])
def get_public_config():
    """获取公共配置"""
    # 获取前端需要的配置信息
    configs = {
        'max_upload_size': SystemConfig.get_config('max_upload_size', 16777216),
        'allowed_image_types': SystemConfig.get_config('allowed_image_types', ['jpg', 'jpeg', 'png', 'bmp', 'gif']),
        'default_background_colors': SystemConfig.get_config('default_background_colors', ['#ffffff', '#ff0000', '#0000ff', '#e6f3ff']),
        # 移除每日使用限制配置
        'enable_face_beautify': SystemConfig.get_config('enable_face_beautify', True),
        'enable_background_removal': SystemConfig.get_config('enable_background_removal', True)
    }
    
    return jsonify({
        'config': configs,
        'app_info': {
            'name': '智能证件照制作工具',
            'version': '1.0.0',
            'description': '基于AI技术的多平台证件照制作工具'
        }
    })

@api_bp.route('/common/ad-positions/<position_code>', methods=['GET'])
def get_ad_position(position_code):
    """获取广告位信息"""
    try:
        position = AdPosition.query.filter_by(code=position_code).first()
        if not position:
            return jsonify({'error': '广告位不存在'}), 404
        
        return jsonify(position.to_dict())
    except Exception as e:
        return jsonify({'error': f'获取广告位失败: {str(e)}'}), 500

@api_bp.route('/common/ad-positions/page/<page_location>', methods=['GET'])
def get_page_ad_positions(page_location):
    """获取页面广告位列表"""
    try:
        positions = AdPosition.query.filter_by(
            page_location=page_location,
            is_enabled=True,
            is_visible=True
        ).order_by(AdPosition.sort_order.asc()).all()
        
        return jsonify([position.to_dict() for position in positions])
    except Exception as e:
        return jsonify({'error': f'获取页面广告位失败: {str(e)}'}), 500

@api_bp.route('/common/ads-config', methods=['GET'])
def get_public_ads_config():
    """获取公共广告配置"""
    try:
        # 只返回启用的广告配置
        keys = [
            'baidu_appid', 'baidu_appkey', 'baidu_ad_unit_id', 'baidu_enabled',
            'toutiao_appid', 'toutiao_appkey', 'toutiao_ad_unit_id', 'toutiao_enabled',
            'admob_app_id', 'admob_ad_unit_id', 'admob_enabled'
        ]
        configs = SystemConfig.query.filter(SystemConfig.config_key.in_(keys)).all()
        
        # 转换为对象格式
        config_dict = {}
        for config in configs:
            config_dict[config.config_key] = config.get_value()
        
        return jsonify(config_dict)
    except Exception as e:
        return jsonify({'error': f'获取广告配置失败: {str(e)}'}), 500

@api_bp.route('/common/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        from app import db
        from sqlalchemy import text
        db.session.execute(text('SELECT 1'))
        
        return jsonify({
            'status': 'healthy',
            'message': 'All services are running normally',
            'timestamp': current_app.config.get('SERVER_NAME', 'unknown')
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'message': str(e)
        }), 503 