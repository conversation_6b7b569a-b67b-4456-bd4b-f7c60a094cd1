from flask import jsonify, current_app

from . import api_bp
from app.models import SystemConfig

@api_bp.route('/common/config', methods=['GET'])
def get_public_config():
    """获取公共配置"""
    # 获取前端需要的配置信息
    configs = {
        'max_upload_size': SystemConfig.get_config('max_upload_size', 16777216),
        'allowed_image_types': SystemConfig.get_config('allowed_image_types', ['jpg', 'jpeg', 'png', 'bmp', 'gif']),
        'default_background_colors': SystemConfig.get_config('default_background_colors', ['#ffffff', '#ff0000', '#0000ff', '#e6f3ff']),
        # 移除每日使用限制配置
        'enable_face_beautify': SystemConfig.get_config('enable_face_beautify', True),
        'enable_background_removal': SystemConfig.get_config('enable_background_removal', True)
    }
    
    return jsonify({
        'config': configs,
        'app_info': {
            'name': '智能证件照制作工具',
            'version': '1.0.0',
            'description': '基于AI技术的多平台证件照制作工具'
        }
    })

@api_bp.route('/common/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        from app import db
        from sqlalchemy import text
        db.session.execute(text('SELECT 1'))
        
        return jsonify({
            'status': 'healthy',
            'message': 'All services are running normally',
            'timestamp': current_app.config.get('SERVER_NAME', 'unknown')
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'message': str(e)
        }), 503 