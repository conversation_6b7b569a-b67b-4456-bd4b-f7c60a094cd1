from datetime import datetime
from app import db

class PhotoTemplate(db.Model):
    """证件照模板模型"""
    __tablename__ = 'photo_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 模板名称
    category = db.Column(db.String(50), nullable=False, index=True)  # 分类
    width = db.Column(db.Integer, nullable=False)  # 宽度(像素)
    height = db.Column(db.Integer, nullable=False)  # 高度(像素)
    width_mm = db.Column(db.Numeric(5, 2))  # 宽度(毫米)
    height_mm = db.Column(db.Numeric(5, 2))  # 高度(毫米)
    dpi = db.Column(db.Integer, default=300)  # 分辨率
    background_color = db.Column(db.String(7), default='#ffffff')  # 默认背景色
    head_ratio = db.Column(db.Numeric(3, 2), default=0.75)  # 头部占比
    description = db.Column(db.Text)  # 描述
    usage_count = db.Column(db.Integer, default=0)  # 使用次数
    is_active = db.Column(db.Boolean, default=True, index=True)  # 是否启用
    sort_order = db.Column(db.Integer, default=0, index=True)  # 排序
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, **kwargs):
        super(PhotoTemplate, self).__init__(**kwargs)
    
    @property
    def aspect_ratio(self):
        """宽高比"""
        if self.height > 0:
            return round(self.width / self.height, 3)
        return 0
    
    @property
    def size_display(self):
        """尺寸显示"""
        if self.width_mm and self.height_mm:
            return f"{self.width_mm}mm × {self.height_mm}mm"
        return f"{self.width}px × {self.height}px"
    
    def get_pixel_size(self, dpi=None):
        """根据DPI获取像素尺寸"""
        if not dpi:
            dpi = self.dpi
        
        if self.width_mm and self.height_mm:
            # 从毫米转换为像素
            width_px = int((float(self.width_mm) / 25.4) * dpi)
            height_px = int((float(self.height_mm) / 25.4) * dpi)
            return width_px, height_px
        
        return self.width, self.height
    
    def get_mm_size(self):
        """获取毫米尺寸"""
        if self.width_mm and self.height_mm:
            return float(self.width_mm), float(self.height_mm)
        
        # 从像素转换为毫米
        width_mm = (self.width / self.dpi) * 25.4
        height_mm = (self.height / self.dpi) * 25.4
        return round(width_mm, 2), round(height_mm, 2)
    
    def increment_usage(self):
        """增加使用次数"""
        self.usage_count = (self.usage_count or 0) + 1
    
    @classmethod
    def get_by_category(cls, category):
        """根据分类获取模板"""
        return cls.query.filter_by(category=category, is_active=True).order_by(cls.sort_order).all()
    
    @classmethod
    def get_popular_templates(cls, limit=10):
        """获取热门模板"""
        return cls.query.filter_by(is_active=True).order_by(cls.usage_count.desc()).limit(limit).all()
    
    @classmethod
    def search_templates(cls, keyword):
        """搜索模板"""
        return cls.query.filter(
            cls.is_active == True,
            db.or_(
                cls.name.contains(keyword),
                cls.description.contains(keyword)
            )
        ).order_by(cls.sort_order).all()
    
    def to_dict(self):
        """转换为字典"""
        width_mm, height_mm = self.get_mm_size()
        
        return {
            'id': self.id,
            'name': self.name,
            'category': self.category,
            'width': self.width,
            'height': self.height,
            'width_mm': width_mm,
            'height_mm': height_mm,
            'dpi': self.dpi,
            'background_color': self.background_color,
            'head_ratio': float(self.head_ratio) if self.head_ratio else 0.75,
            'description': self.description,
            'usage_count': self.usage_count,
            'aspect_ratio': self.aspect_ratio,
            'size_display': self.size_display,
            'sort_order': self.sort_order,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        return f'<PhotoTemplate {self.name}: {self.width}x{self.height}>' 