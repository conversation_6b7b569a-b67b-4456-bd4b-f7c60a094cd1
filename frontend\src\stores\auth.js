import { defineStore } from 'pinia'
import apiMethods from '@/api'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: JSON.parse(localStorage.getItem('user') || 'null'),
    token: localStorage.getItem('token'),
    refreshToken: localStorage.getItem('refreshToken'),
    isAuthenticated: !!localStorage.getItem('token')
  }),

  getters: {
    isLoggedIn: (state) => !!state.token && !!state.user && state.isAuthenticated,
    userRole: (state) => state.user?.role || 'guest',
    isVip: (state) => state.user?.is_vip || false,
    credits: (state) => state.user?.credits || 0,
    dailyUsage: (state) => state.user?.daily_usage || 0
  },

  actions: {
    async login(credentials) {
      const response = await apiMethods.auth.login(credentials)
      const { tokens, user } = response.data
      
      this.token = tokens.access_token
      this.refreshToken = tokens.refresh_token
      this.user = user
      this.isAuthenticated = true
      
      localStorage.setItem('token', tokens.access_token)
      localStorage.setItem('refreshToken', tokens.refresh_token)
      localStorage.setItem('user', JSON.stringify(user))
      
      return response.data
    },

    async register(userData) {
      const response = await apiMethods.auth.register(userData)
      return response.data
    },

    async logout() {
      try {
        if (this.token) {
          await apiMethods.auth.logout()
        }
      } catch (error) {
        console.warn('Logout API call failed:', error)
      } finally {
        this.token = null
        this.refreshToken = null
        this.user = null
        this.isAuthenticated = false
        
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('user')
      }
    },

    async refreshAccessToken() {
      try {
        if (!this.refreshToken) {
          throw new Error('No refresh token available')
        }
        
        const response = await apiMethods.auth.refresh()
        
        const { access_token } = response.data
        this.token = access_token
        localStorage.setItem('token', access_token)
        
        return access_token
      } catch (error) {
        this.logout()
        throw error
      }
    },

    async fetchUserInfo() {
      try {
        const response = await apiMethods.auth.getCurrentUser()
        this.user = response.data.user
        this.isAuthenticated = true
        localStorage.setItem('user', JSON.stringify(response.data.user))
        return response.data.user
      } catch (error) {
        this.logout()
        throw error
      }
    },

    async updateProfile(userData) {
      const response = await apiMethods.user.updateProfile(userData)
      this.user = { ...this.user, ...response.data }
      localStorage.setItem('user', JSON.stringify(this.user))
      return response.data
    },

    async changePassword(passwordData) {
      const response = await apiMethods.user.changePassword(passwordData)
      return response.data
    },

    // 初始化认证状态
    async initAuth() {
      console.log('初始化认证状态:', {
        hasToken: !!this.token,
        hasUser: !!this.user,
        isAuthenticated: this.isAuthenticated
      })
      
      if (this.token) {
        try {
          // 如果有token但没有用户信息，获取用户信息
          if (!this.user) {
          await this.fetchUserInfo()
          }
          console.log('认证状态初始化成功')
        } catch (error) {
          console.warn('认证状态初始化失败:', error)
          this.logout()
        }
      }
    },

    // 检查认证状态（路由守卫使用）
    async checkAuth() {
      // 如果有token但没有用户信息，尝试获取用户信息
      if (this.token && !this.user) {
        try {
          await this.fetchUserInfo()
          return true
        } catch (error) {
          console.warn('获取用户信息失败，清除认证状态:', error)
          this.logout()
          return false
        }
      }
      
      // 如果有token和用户信息，直接返回true（暂时跳过token验证）
      if (this.token && this.user) {
        return true
      }
      
      // 如果没有token或用户信息，返回登录状态
      return this.isLoggedIn
    },

    // 调试方法：打印当前认证状态
    debugAuthState() {
      console.log('=== 认证状态调试信息 ===')
      console.log('Token:', this.token ? '存在' : '不存在')
      console.log('RefreshToken:', this.refreshToken ? '存在' : '不存在')
      console.log('User:', this.user ? '存在' : '不存在')
      console.log('isAuthenticated:', this.isAuthenticated)
      console.log('isLoggedIn:', this.isLoggedIn)
      console.log('LocalStorage token:', localStorage.getItem('token') ? '存在' : '不存在')
      console.log('LocalStorage user:', localStorage.getItem('user') ? '存在' : '不存在')
      console.log('========================')
    }
  }
}) 
 