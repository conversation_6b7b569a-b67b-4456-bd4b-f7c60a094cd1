# 相片制作工具集 - 快速启动指南

## 🚀 环境要求

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **内存**: 8GB以上推荐
- **硬盘**: 20GB可用空间
- **网络**: 稳定的互联网连接

### 软件依赖
- **Docker**: 20.10+ 
- **Docker Compose**: 2.0+
- **Git**: 2.30+
- **Python**: 3.8+ (可选，用于本地开发)
- **Node.js**: 16+ (可选，用于本地开发)

## 📦 一键部署(推荐)

### 1. 克隆项目
```bash
git clone https://github.com/your-username/photo-maker.git
cd photo-maker
```

### 2. 启动全部服务
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 初始化数据库
```bash
# 数据库会自动初始化，如需手动执行：
docker-compose exec mysql mysql -u root -p photo_maker < /docker-entrypoint-initdb.d/init.sql
```

### 4. 访问服务
- **前端界面**: http://localhost
- **后端API**: http://localhost/api
- **API文档**: http://localhost/api/docs
- **任务监控**: http://localhost:5555 (Celery Flower)
- **数据库**: localhost:3306 (用户名: photo_user, 密码: photo_pass_2024)

## 🛠️ 本地开发模式

### 后端开发
```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 设置环境变量
cp .env.example .env
# 编辑 .env 文件配置数据库等信息

# 运行数据库迁移
flask db upgrade

# 启动开发服务器
python run.py

# 启动Celery工作进程(新终端)
celery -A app.celery worker --loglevel=info
```

### 前端开发
```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 构建生产版本
npm run build
```

## 🔧 配置说明

### 环境变量配置
创建 `backend/.env` 文件：
```env
# 数据库配置
MYSQL_HOST=localhost
MYSQL_USER=photo_user
MYSQL_PASSWORD=photo_pass_2024
MYSQL_DB=photo_maker

# Redis配置
REDIS_HOST=localhost
REDIS_PASSWORD=redis_pass_2024

# 应用配置
SECRET_KEY=your_secret_key_here_2024
JWT_SECRET_KEY=jwt_secret_key_here_2024
FLASK_ENV=development

# 文件存储
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# AI功能配置
ENABLE_FACE_BEAUTIFY=true
ENABLE_BACKGROUND_REMOVAL=true
```

### 前端配置
创建 `frontend/.env` 文件：
```env
VUE_APP_API_BASE_URL=http://localhost:5000
VUE_APP_UPLOAD_MAX_SIZE=16777216
VUE_APP_SUPPORTED_FORMATS=jpg,jpeg,png,bmp,gif
```

## 📱 小程序开发

### 使用uni-app开发小程序版本
```bash
# 安装uni-app CLI
npm install -g @vue/cli
vue add uni-app

# 创建uni-app项目
vue create -p dcloudio/uni-preset-vue miniprogram
cd miniprogram

# 安装依赖
npm install

# 运行微信小程序
npm run dev:mp-weixin

# 运行支付宝小程序
npm run dev:mp-alipay

# 运行H5
npm run dev:h5

# 运行APP
npm run dev:app-plus
```

## 🧪 测试

### 后端测试
```bash
cd backend
pytest tests/
```

### 前端测试
```bash
cd frontend
npm run test:unit
```

### 集成测试
```bash
# 确保所有服务运行后执行
python tests/integration_test.py
```

## 📊 监控和日志

### 查看服务状态
```bash
# 查看所有容器状态
docker-compose ps

# 查看特定服务日志
docker-compose logs backend
docker-compose logs celery
docker-compose logs frontend

# 实时跟踪日志
docker-compose logs -f backend
```

### 性能监控
- **Celery监控**: http://localhost:5555
- **Redis监控**: 使用Redis Commander或其他工具
- **MySQL监控**: 使用phpMyAdmin或其他数据库管理工具

## 🚨 常见问题

### 1. Docker服务启动失败
```bash
# 检查Docker是否运行
docker --version
docker-compose --version

# 重新构建镜像
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### 2. 数据库连接失败
- 检查MySQL容器是否正常运行
- 确认数据库配置信息正确
- 等待MySQL完全启动(约30-60秒)

### 3. 图像处理失败
- 检查OpenCV依赖是否正确安装
- 确认上传的图片格式支持
- 查看Celery工作进程日志

### 4. 前端页面无法访问
- 检查Nginx配置是否正确
- 确认前端构建是否成功
- 查看前端容器日志

### 5. 小程序开发问题
- 确保微信开发者工具已安装
- 检查小程序AppID配置
- 确认服务器域名已在小程序后台配置

## 🔒 安全建议

### 生产环境部署
1. **更改默认密码**: 修改数据库、Redis等默认密码
2. **HTTPS配置**: 使用SSL证书启用HTTPS
3. **防火墙设置**: 限制不必要的端口访问
4. **定期备份**: 配置数据库和文件自动备份
5. **访问控制**: 实现用户权限管理

### 数据保护
- 用户上传的图片定期清理
- 敏感信息加密存储
- 实现数据访问日志记录

## 📖 API文档

启动服务后，访问以下地址查看详细API文档：
- **Swagger UI**: http://localhost/api/docs
- **Postman集合**: 导入 `docs/postman_collection.json`

## 🤝 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📞 技术支持

- **问题反馈**: GitHub Issues
- **技术讨论**: GitHub Discussions
- **邮件支持**: <EMAIL>

---

## 🎯 下一步

1. **完成基础功能**: 按照指导文档实现核心功能
2. **优化用户体验**: 改进界面设计和交互流程
3. **扩展平台支持**: 适配更多平台和设备
4. **性能优化**: 提升图像处理速度和质量
5. **商业化部署**: 准备生产环境部署方案

开始开发愉快！🎉 