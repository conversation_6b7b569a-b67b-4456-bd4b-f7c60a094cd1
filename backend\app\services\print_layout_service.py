import os
import io
import math
from PIL import Image, ImageDraw, ImageFont
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class PrintLayoutService:
    """名片排版打印服务"""
    
    def __init__(self):
        # 标准名片尺寸（毫米）
        self.business_card_size = {
            'width_mm': 90,
            'height_mm': 54
        }
        
        # 常用纸张规格（毫米）
        self.paper_sizes = {
            'A4': {'width_mm': 210, 'height_mm': 297},
            'A5': {'width_mm': 148, 'height_mm': 210},
            'Letter': {'width_mm': 216, 'height_mm': 279}
        }
        
        # 打印DPI
        self.print_dpi = 300
        
        # 默认边距（毫米）
        self.default_margin = 10
    
    def calculate_layout(self, paper_size='A4', margin_mm=None):
        """
        计算纸张上可以排列的名片数量和位置
        
        Args:
            paper_size: 纸张规格
            margin_mm: 边距（毫米）
            
        Returns:
            dict: 布局信息
        """
        if margin_mm is None:
            margin_mm = self.default_margin
            
        # 获取纸张尺寸
        if isinstance(paper_size, str):
            paper = self.paper_sizes.get(paper_size, self.paper_sizes['A4'])
        else:
            paper = paper_size
        
        # 计算可用区域
        usable_width = paper['width_mm'] - (margin_mm * 2)
        usable_height = paper['height_mm'] - (margin_mm * 2)
        
        # 计算每行和每列可以放置的名片数量
        cards_per_row = int(usable_width // self.business_card_size['width_mm'])
        cards_per_col = int(usable_height // self.business_card_size['height_mm'])
        
        total_cards = cards_per_row * cards_per_col
        
        # 计算名片间距（居中排列）
        if cards_per_row > 1:
            remaining_width = usable_width - (cards_per_row * self.business_card_size['width_mm'])
            card_spacing_x = remaining_width / (cards_per_row - 1) if cards_per_row > 1 else 0
        else:
            card_spacing_x = 0
            
        if cards_per_col > 1:
            remaining_height = usable_height - (cards_per_col * self.business_card_size['height_mm'])
            card_spacing_y = remaining_height / (cards_per_col - 1) if cards_per_col > 1 else 0
        else:
            card_spacing_y = 0
        
        # 计算名片位置
        card_positions = []
        for row in range(cards_per_col):
            for col in range(cards_per_row):
                x = margin_mm + col * (self.business_card_size['width_mm'] + card_spacing_x)
                y = margin_mm + row * (self.business_card_size['height_mm'] + card_spacing_y)
                card_positions.append({'x': x, 'y': y})
        
        return {
            'paper_size': paper,
            'margin_mm': margin_mm,
            'usable_area': {
                'width_mm': usable_width,
                'height_mm': usable_height
            },
            'cards_per_row': cards_per_row,
            'cards_per_col': cards_per_col,
            'total_cards': total_cards,
            'card_spacing': {
                'x': card_spacing_x,
                'y': card_spacing_y
            },
            'card_positions': card_positions
        }
    
    def create_print_layout(self, business_card_html, business_card_css, layout_config, card_count=None):
        """
        创建打印排版图片
        
        Args:
            business_card_html: 名片HTML内容
            business_card_css: 名片CSS样式
            layout_config: 布局配置
            card_count: 名片数量（None表示填满整页）
            
        Returns:
            PIL Image对象
        """
        try:
            # 计算布局
            layout = self.calculate_layout(
                layout_config.get('paper_size', 'A4'),
                layout_config.get('margin_mm', self.default_margin)
            )
            
            # 确定要生成的名片数量
            if card_count is None:
                card_count = layout['total_cards']
            else:
                card_count = min(card_count, layout['total_cards'])
            
            # 计算图片尺寸（像素）
            paper_width_px = int(layout['paper_size']['width_mm'] * self.print_dpi / 25.4)
            paper_height_px = int(layout['paper_size']['height_mm'] * self.print_dpi / 25.4)
            
            # 创建背景图片
            layout_img = Image.new('RGB', (paper_width_px, paper_height_px), 'white')
            
            # 生成单个名片图片
            card_img = self._render_business_card_image(business_card_html, business_card_css)
            
            if card_img is None:
                raise Exception("名片渲染失败")
            
            # 计算名片在图片中的尺寸
            card_width_px = int(self.business_card_size['width_mm'] * self.print_dpi / 25.4)
            card_height_px = int(self.business_card_size['height_mm'] * self.print_dpi / 25.4)
            
            # 调整名片图片大小
            card_img = card_img.resize((card_width_px, card_height_px), Image.Resampling.LANCZOS)
            
            # 在布局中放置名片
            for i in range(card_count):
                if i >= len(layout['card_positions']):
                    break
                    
                pos = layout['card_positions'][i]
                
                # 转换位置到像素
                x_px = int(pos['x'] * self.print_dpi / 25.4)
                y_px = int(pos['y'] * self.print_dpi / 25.4)
                
                # 粘贴名片
                layout_img.paste(card_img, (x_px, y_px))
            
            # 添加裁切线（可选）
            if layout_config.get('show_cut_lines', False):
                layout_img = self._add_cut_lines(layout_img, layout)
            
            return layout_img
            
        except Exception as e:
            logger.error(f"创建打印排版失败: {str(e)}")
            raise
    
    def _render_business_card_image(self, html_content, css_content):
        """
        将HTML名片渲染为图片
        
        Args:
            html_content: HTML内容
            css_content: CSS样式
            
        Returns:
            PIL Image对象
        """
        try:
            # 这里使用简化的渲染方式
            # 在实际应用中，可以使用headless browser或HTML to image服务
            
            # 创建一个标准尺寸的名片图片
            card_width_px = int(self.business_card_size['width_mm'] * self.print_dpi / 25.4)
            card_height_px = int(self.business_card_size['height_mm'] * self.print_dpi / 25.4)
            
            # 创建基础图片
            card_img = Image.new('RGB', (card_width_px, card_height_px), 'white')
            draw = ImageDraw.Draw(card_img)
            
            # 添加边框
            border_color = '#dddddd'
            draw.rectangle([0, 0, card_width_px-1, card_height_px-1], outline=border_color)
            
            # 添加文本（简化版本）
            try:
                # 尝试加载系统字体
                font_path = self._get_system_font()
                font_large = ImageFont.truetype(font_path, 24) if font_path else ImageFont.load_default()
                font_medium = ImageFont.truetype(font_path, 16) if font_path else ImageFont.load_default()
                font_small = ImageFont.truetype(font_path, 12) if font_path else ImageFont.load_default()
            except:
                font_large = ImageFont.load_default()
                font_medium = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            # 在图片上绘制示例文本
            draw.text((20, 20), "名片样例", fill='black', font=font_large)
            draw.text((20, 50), "职位标题", fill='#666666', font=font_medium)
            draw.text((20, 70), "公司名称", fill='#333333', font=font_medium)
            draw.text((20, 100), "电话: 123-456-7890", fill='#666666', font=font_small)
            draw.text((20, 120), "邮箱: <EMAIL>", fill='#666666', font=font_small)
            
            return card_img
            
        except Exception as e:
            logger.error(f"渲染名片图片失败: {str(e)}")
            return None
    
    def _get_system_font(self):
        """获取系统字体路径"""
        import platform
        system = platform.system()
        
        if system == "Windows":
            return "C:/Windows/Fonts/msyh.ttc"  # 微软雅黑
        elif system == "Darwin":  # macOS
            return "/System/Library/Fonts/PingFang.ttc"
        else:  # Linux
            font_paths = [
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "/usr/share/fonts/TTF/DejaVuSans.ttf",
                "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
            ]
            for path in font_paths:
                if os.path.exists(path):
                    return path
        
        return None
    
    def _add_cut_lines(self, layout_img, layout):
        """添加裁切线"""
        try:
            draw = ImageDraw.Draw(layout_img)
            
            # 裁切线颜色和样式
            cut_line_color = '#cccccc'
            line_width = 1
            
            # 为每个名片位置添加裁切线
            for pos in layout['card_positions']:
                # 转换位置到像素
                x_px = int(pos['x'] * self.print_dpi / 25.4)
                y_px = int(pos['y'] * self.print_dpi / 25.4)
                
                card_width_px = int(self.business_card_size['width_mm'] * self.print_dpi / 25.4)
                card_height_px = int(self.business_card_size['height_mm'] * self.print_dpi / 25.4)
                
                # 绘制名片边框线
                draw.rectangle(
                    [x_px, y_px, x_px + card_width_px, y_px + card_height_px],
                    outline=cut_line_color,
                    width=line_width
                )
                
                # 添加角标记
                corner_length = 5
                corners = [
                    (x_px, y_px),  # 左上
                    (x_px + card_width_px, y_px),  # 右上
                    (x_px, y_px + card_height_px),  # 左下
                    (x_px + card_width_px, y_px + card_height_px)  # 右下
                ]
                
                for corner_x, corner_y in corners:
                    # 水平线
                    draw.line([corner_x - corner_length, corner_y, corner_x + corner_length, corner_y], 
                             fill=cut_line_color, width=line_width)
                    # 垂直线
                    draw.line([corner_x, corner_y - corner_length, corner_x, corner_y + corner_length], 
                             fill=cut_line_color, width=line_width)
            
            return layout_img
            
        except Exception as e:
            logger.error(f"添加裁切线失败: {str(e)}")
            return layout_img
    
    def create_pdf_layout(self, business_card_html, business_card_css, layout_config, card_count=None):
        """
        创建PDF格式的打印排版
        
        Args:
            business_card_html: 名片HTML内容
            business_card_css: 名片CSS样式
            layout_config: 布局配置
            card_count: 名片数量
            
        Returns:
            PDF字节数据
        """
        try:
            # 创建排版图片
            layout_img = self.create_print_layout(
                business_card_html, 
                business_card_css, 
                layout_config, 
                card_count
            )
            
            # 转换为PDF
            pdf_buffer = io.BytesIO()
            layout_img.save(pdf_buffer, format='PDF', resolution=self.print_dpi)
            pdf_bytes = pdf_buffer.getvalue()
            pdf_buffer.close()
            
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"创建PDF排版失败: {str(e)}")
            raise
    
    def get_layout_preview(self, paper_size='A4', margin_mm=None, show_guidelines=True):
        """
        获取排版预览信息
        
        Args:
            paper_size: 纸张规格
            margin_mm: 边距
            show_guidelines: 是否显示辅助线
            
        Returns:
            dict: 预览信息
        """
        layout = self.calculate_layout(paper_size, margin_mm)
        
        # 生成预览图片（小尺寸）
        preview_dpi = 72  # 屏幕显示DPI
        paper_width_px = int(layout['paper_size']['width_mm'] * preview_dpi / 25.4)
        paper_height_px = int(layout['paper_size']['height_mm'] * preview_dpi / 25.4)
        
        preview_img = Image.new('RGB', (paper_width_px, paper_height_px), 'white')
        
        if show_guidelines:
            draw = ImageDraw.Draw(preview_img)
            
            # 绘制边距线
            margin_px = int(layout['margin_mm'] * preview_dpi / 25.4)
            draw.rectangle([margin_px, margin_px, paper_width_px - margin_px, paper_height_px - margin_px], 
                          outline='#cccccc')
            
            # 绘制名片位置
            card_width_px = int(self.business_card_size['width_mm'] * preview_dpi / 25.4)
            card_height_px = int(self.business_card_size['height_mm'] * preview_dpi / 25.4)
            
            for pos in layout['card_positions']:
                x_px = int(pos['x'] * preview_dpi / 25.4)
                y_px = int(pos['y'] * preview_dpi / 25.4)
                
                draw.rectangle([x_px, y_px, x_px + card_width_px, y_px + card_height_px], 
                              outline='#3498db', fill='#ecf0f1')
        
        # 转换为base64
        buffer = io.BytesIO()
        preview_img.save(buffer, format='PNG')
        buffer.seek(0)
        
        import base64
        preview_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        buffer.close()
        
        return {
            'layout': layout,
            'preview_image': f"data:image/png;base64,{preview_base64}"
        } 