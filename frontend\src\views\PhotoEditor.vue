<template>
  <div class="photo-editor">
    <!-- 顶部工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <button class="btn-back" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          <span>返回</span>
        </button>
      </div>
      <div class="toolbar-center">
        <h1 class="page-title">证件照制作</h1>
      </div>
      <div class="toolbar-right">
        <button class="btn-save" @click="downloadPhoto" :disabled="!processedImage">
          <el-icon><Download /></el-icon>
          <span>保存</span>
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：图片预览区域 -->
      <div class="preview-panel">
        <div class="preview-container" v-if="!originalImage">
          <!-- 上传区域 -->
          <div 
            class="upload-zone" 
            @click="triggerFileInput" 
            @drop="handleDrop" 
            @dragover.prevent="handleDragOver"
            @dragenter.prevent="handleDragEnter"
            @dragleave.prevent="handleDragLeave"
          >
            <div class="upload-icon">
              <el-icon size="48"><Plus /></el-icon>
            </div>
            <h3>选择照片</h3>
            <p>拖放照片到此处或点击选择</p>
            <p class="upload-hint">支持 JPG、PNG 格式，最大 10MB</p>
          </div>
          <input 
            ref="fileInput" 
            type="file" 
            accept="image/*" 
            @change="handleFileSelect" 
            style="display: none"
          />
        </div>

        <div class="preview-container" v-else>
          <!-- 图片预览 -->
          <div class="image-preview">
            <div class="image-container" ref="imageContainer">
              <!-- 排版预览模式 -->
              <div v-if="layoutPreviewMode && layoutPreviewImage" class="layout-preview-container">
                <div class="layout-preview-image-wrapper">
                  <img :src="layoutPreviewImage" alt="排版预览" class="layout-preview-image" />
                </div>
              </div>
              
              <!-- 普通图片预览模式 -->
              <img 
                v-else
                :src="currentDisplayImage" 
                :alt="fileName"
                class="preview-image"
                @load="onImageLoad"
                draggable="false"
                ref="previewImage"
              />
              
              <!-- 手动裁剪系统 -->
              <div v-if="cropMode" class="crop-overlay">
                <!-- 遮罩层 -->
                <div class="crop-mask">
                  <!-- 上遮罩 -->
                  <div class="mask-top" :style="maskTopStyle"></div>
                  <!-- 下遮罩 -->
                  <div class="mask-bottom" :style="maskBottomStyle"></div>
                  <!-- 左遮罩 -->
                    <div class="mask-left" :style="maskLeftStyle"></div>
                  <!-- 右遮罩 -->
                    <div class="mask-right" :style="maskRightStyle"></div>
                </div>
                
                <!-- 裁剪框 -->
                <div 
                  class="crop-box"
                  :style="cropBoxStyle"
                  @mousedown="startDragBox"
                >
                  <!-- 裁剪框边框 -->
                  <div class="crop-border"></div>
                  
                  <!-- 网格线 -->
                  <div class="crop-grid">
                    <div class="grid-line grid-line-v" style="left: 33.33%"></div>
                    <div class="grid-line grid-line-v" style="left: 66.66%"></div>
                    <div class="grid-line grid-line-h" style="top: 33.33%"></div>
                    <div class="grid-line grid-line-h" style="top: 66.66%"></div>
                  </div>
                  
                  <!-- 调整手柄 -->
                  <div class="resize-handle nw" @mousedown.stop="startResize('nw', $event)"></div>
                  <div class="resize-handle ne" @mousedown.stop="startResize('ne', $event)"></div>
                  <div class="resize-handle sw" @mousedown.stop="startResize('sw', $event)"></div>
                  <div class="resize-handle se" @mousedown.stop="startResize('se', $event)"></div>
                  <div class="resize-handle n" @mousedown.stop="startResize('n', $event)"></div>
                  <div class="resize-handle s" @mousedown.stop="startResize('s', $event)"></div>
                  <div class="resize-handle w" @mousedown.stop="startResize('w', $event)"></div>
                  <div class="resize-handle e" @mousedown.stop="startResize('e', $event)"></div>
                </div>
                
                                  <!-- 裁剪提示 -->
                <div class="crop-tips">
                  <p>拖拽裁剪框调整位置和大小</p>
                  <p>点击"完成裁剪"应用更改</p>
                  <div class="crop-coords-info">
                    <span>裁剪区域: {{ Math.round(cropRect.x) }}, {{ Math.round(cropRect.y) }} - {{ Math.round(cropRect.width) }}×{{ Math.round(cropRect.height) }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 加载遮罩 -->
            <div class="loading-overlay" v-if="processing">
              <div class="loading-spinner"></div>
              <p>处理中...</p>
            </div>
          </div>


          <!-- 图片信息 -->
          <div class="image-info" v-if="!layoutPreviewMode">
            <div class="info-item">
              <span class="label">文件名:</span>
              <span class="value">{{ fileName }}</span>
            </div>
            <div class="info-item">
              <span class="label">尺寸:</span>
              <span class="value">{{ imageWidth }} × {{ imageHeight }}</span>
            </div>
            <div class="info-item">
              <span class="label">规格:</span>
              <span class="value">{{ selectedTemplate?.name || '未选择' }}</span>
            </div>
            <div class="info-item" v-if="currentPaperSize">
              <span class="label">纸张:</span>
              <span class="value">{{ currentPaperSize.name }} ({{ currentPaperSize.width_mm }}×{{ currentPaperSize.height_mm }}mm)</span>
            </div>
          </div>
          <!-- 排版信息 -->
          <div class="layout-info-summary" v-if="layoutPreviewMode && layoutInfo" style="display: flex; align-items: center; justify-content: space-between; gap: 18px;">
            <div class="layout-info-bar" style="display: flex; align-items: center; gap: 18px; flex-wrap: wrap;">
              <span class="label">布局:</span>
              <span class="value">{{ layoutInfo.rows }}行 × {{ layoutInfo.cols }}列</span>
              <span class="label">总数:</span>
              <span class="value">{{ layoutInfo.total_photos }}张</span>
              <span class="label">纸张:</span>
              <span class="value" v-if="currentPaperSize">{{ currentPaperSize.name }} ({{ currentPaperSize.width_mm }}×{{ currentPaperSize.height_mm }}mm)</span>
              <span class="label">规格:</span>
              <span class="value" v-if="selectedTemplate">{{ selectedTemplate.name }} ({{ selectedTemplate.width_mm }}×{{ selectedTemplate.height_mm }}mm)</span>
            </div>
            <div class="layout-preview-actions" style="display: flex; gap: 12px;">
              <el-button size="small" @click="exitLayoutPreview" type="default">返回编辑</el-button>
              <el-button size="small" type="primary" @click="generateLayout"><el-icon><Download /></el-icon>下载排版</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：操作面板 -->
      <div class="control-panel">
        <!-- 工作流进度指示器 -->
        <div class="workflow-progress">
          <div class="progress-item" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
            <div class="progress-number">1</div>
            <div class="progress-label">背景颜色</div>
          </div>
          <div class="progress-arrow">→</div>
          <div class="progress-item" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
            <div class="progress-number">2</div>
            <div class="progress-label">美颜优化</div>
                    </div>
          <div class="progress-arrow">→</div>
          <div class="progress-item" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
            <div class="progress-number">3</div>
            <div class="progress-label">证件规格</div>
                  </div>
          <div class="progress-arrow">→</div>
          <div class="progress-item" :class="{ active: currentStep >= 4, completed: currentStep > 4 }">
            <div class="progress-number">4</div>
            <div class="progress-label">图片裁剪</div>
                </div>
          <div class="progress-arrow">→</div>
          <div class="progress-item" :class="{ active: currentStep >= 5, completed: currentStep > 5 }">
            <div class="progress-number">5</div>
            <div class="progress-label">一键排版</div>
                </div>
        </div>

        <!-- 1. 背景颜色 -->
        <div class="control-section">
          <div class="section-header" @click="toggleSection('background')">
            <h3>
              <span class="step-number">1</span>
              背景颜色
              <span v-if="currentStep === 1" class="current-step-badge">当前步骤</span>
              <span v-if="currentStep > 1" class="completed-step-badge">已完成</span>
            </h3>
            <el-icon class="expand-icon" :class="{ expanded: expandedSections.background }">
              <ArrowDown />
            </el-icon>
          </div>
          <transition name="section-slide">
            <div class="section-content" v-if="expandedSections.background">
              <div class="color-grid">
                <div 
                  v-for="color in backgroundColors" 
                  :key="color.value"
                  class="color-option"
                  :class="{ active: selectedBackground === color.value }"
                  :style="{ backgroundColor: color.value }"
                  @click="selectBackground(color.value)"
                >
                  <div class="color-check" v-if="selectedBackground === color.value">
                    <el-icon><Check /></el-icon>
                  </div>
                </div>
                <!-- 自定义颜色 -->
                <div class="color-option custom" @click="showColorPicker = true">
                  <el-icon><Brush /></el-icon>
                </div>
              </div>
              
              <!-- 背景步骤完成按钮 -->
              <div class="step-action" v-if="currentStep === 1">
                <el-button @click="completeStep(1)" type="primary" size="small" style="width: 100%">
                  <el-icon><Check /></el-icon>
                  完成背景设置，进入下一步
                </el-button>
              </div>
            </div>
          </transition>
        </div>

        <!-- 2. 美颜优化 -->
        <div class="control-section" :class="{ disabled: currentStep < 2 }">
          <div class="section-header" @click="currentStep >= 2 && toggleSection('beauty')">
            <h3>
              <span class="step-number">2</span>
              美颜优化
              <span v-if="currentStep === 2" class="current-step-badge">当前步骤</span>
              <span v-if="currentStep > 2" class="completed-step-badge">已完成</span>
              <span v-if="currentStep < 2" class="disabled-step-badge">等待中</span>
            </h3>
            <el-icon class="expand-icon" :class="{ expanded: expandedSections.beauty }" v-if="currentStep >= 2">
              <ArrowDown />
            </el-icon>
          </div>
          <transition name="section-slide">
            <div class="section-content" v-if="expandedSections.beauty && currentStep >= 2">
              <div class="beauty-controls">
                <div class="control-item">
                  <label class="control-label">磨皮程度 ({{ beautySettings.smoothness }}%)</label>
                  <el-slider v-model="beautySettings.smoothness" :min="0" :max="100" @change="debounceProcess" />
                  </div>
                <div class="control-item">
                  <label class="control-label">美白程度 ({{ beautySettings.whitening }}%)</label>
                  <el-slider v-model="beautySettings.whitening" :min="0" :max="100" @change="debounceProcess" />
                    </div>
                <div class="control-item">
                  <label class="control-label">亮度调节 ({{ beautySettings.brightness > 0 ? '+' : '' }}{{ beautySettings.brightness }})</label>
                  <el-slider v-model="beautySettings.brightness" :min="-50" :max="50" @change="debounceProcess" />
                    </div>
                <div class="control-item">
                  <label class="control-label">对比度 ({{ beautySettings.contrast > 0 ? '+' : '' }}{{ beautySettings.contrast }})</label>
                  <el-slider v-model="beautySettings.contrast" :min="-50" :max="50" @change="debounceProcess" />
                  </div>
                </div>
              <div class="beauty-actions">
                <el-button @click="resetBeauty" size="small">重置美颜</el-button>
                <el-button type="primary" @click="applyBeautyAndContinue" size="small" v-if="currentStep === 2">
                  应用美颜并继续
                </el-button>
                <el-button type="primary" @click="processImage" size="small" v-else>应用效果</el-button>
              </div>

              <!-- 美颜步骤完成按钮 -->
              <div class="step-action" v-if="currentStep === 2">
                <el-button @click="completeStep(2)" type="primary" size="small" style="width: 100%">
                  <el-icon><Check /></el-icon>
                  完成美颜，进入下一步
                    </el-button>
                  </div>
                </div>
          </transition>
              </div>

        <!-- 3. 证件规格 -->
        <div class="control-section" :class="{ disabled: currentStep < 3 }">
          <div class="section-header" @click="currentStep >= 3 && toggleSection('template')">
            <h3>
              <span class="step-number">3</span>
              证件规格
              <span v-if="currentStep === 3" class="current-step-badge">当前步骤</span>
              <span v-if="currentStep > 3" class="completed-step-badge">已完成</span>
              <span v-if="currentStep < 3" class="disabled-step-badge">等待中</span>
            </h3>
            <el-icon class="expand-icon" :class="{ expanded: expandedSections.template }" v-if="currentStep >= 3">
              <ArrowDown />
            </el-icon>
                  </div>
          <transition name="section-slide">
            <div class="section-content" v-if="expandedSections.template && currentStep >= 3">
              <div class="template-categories">
                <div v-for="category in templateCategories" :key="category" class="template-category">
                  <!-- 可折叠的分类标题 -->
                  <div class="category-header" @click="toggleTemplateCategory(category)">
                    <h4 class="category-title">{{ category }}</h4>
                    <el-icon class="category-expand-icon" :class="{ expanded: expandedCategories[category] }">
                      <ArrowDown />
                    </el-icon>
              </div>

                  <!-- 折叠式模板网格 -->
                  <transition name="category-slide">
                    <div class="template-grid" v-if="expandedCategories[category]">
                      <div 
                        v-for="template in getTemplatesByCategory(category)" 
                        :key="template.id"
                        class="template-card"
                        :class="{ active: selectedTemplate?.id === template.id }"
                        @click="selectTemplate(template)"
                      >
                        <div class="template-preview">{{ template.name }}</div>
                        <div class="template-size">{{ template.width_mm }}×{{ template.height_mm }}mm</div>
                  </div>
                  </div>
                  </transition>
                    </div>
                    </div>
              
              <!-- 规格步骤完成按钮 -->
              <div class="step-action" v-if="currentStep === 3">
                <el-button @click="completeStep(3)" type="primary" size="small" style="width: 100%">
                  <el-icon><Check /></el-icon>
                  完成规格设置，进入下一步
                      </el-button>
                    </div>
            </div>
          </transition>
        </div>

        <!-- 4. 图片裁剪 -->
        <div class="control-section" :class="{ disabled: currentStep < 4 }">
          <div class="section-header" @click="currentStep >= 4 && toggleSection('crop')">
            <h3>
              <span class="step-number">4</span>
              图片裁剪
              <span v-if="currentStep === 4" class="current-step-badge">当前步骤</span>
              <span v-if="currentStep > 4" class="completed-step-badge">已完成</span>
              <span v-if="currentStep < 4" class="disabled-step-badge">等待中</span>
            </h3>
            <el-icon class="expand-icon" :class="{ expanded: expandedSections.crop }" v-if="currentStep >= 4">
              <ArrowDown />
            </el-icon>
          </div>
          <transition name="section-slide">
            <div class="section-content" v-if="expandedSections.crop && currentStep >= 4">
              <!-- 裁剪功能 -->
              <div class="edit-group">
                <div class="crop-controls">
                  <!-- 手动裁剪模式 -->
                  <div class="control-row">
                    <el-button @click="toggleCropMode" :type="cropMode ? 'primary' : ''" size="small">
                      <el-icon><svg viewBox="0 0 1024 1024"><path d="M832 64h64v128h-64V64zM192 832V704h-128v-64h128V128h64v512h512v64H320v128h-64z"/></svg></el-icon>
                      {{ cropMode ? '完成裁剪' : '手动裁剪' }}
                    </el-button>
                </div>
                  
                  <!-- 自动裁剪选项 -->
                  <div class="control-row" v-if="!cropMode">
                    <el-button @click="oneClickOptimize" size="small" type="primary" style="flex: 1">
                      <el-icon><svg viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"/></svg></el-icon>
                      一键优化
                    </el-button>
                </div>
                  
                  <div class="control-row" v-if="!cropMode">
                    <el-button @click="autoCrop" size="small" type="success">
                      <el-icon><MagicStick /></el-icon>
                      智能裁剪
                    </el-button>
                    <el-button @click="previewCropArea" size="small">
                      <el-icon><Aim /></el-icon>
                      预览区域
                    </el-button>
                </div>
                  
                  <div class="control-row" v-if="cropMode">
                    <el-button @click="testCropOnly" size="small" type="warning">
                      <el-icon><svg viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zM296 512a216 216 0 1 1 432 0 216 216 0 0 1-432 0z"/></svg></el-icon>
                      测试纯裁剪
                    </el-button>
                    <el-button @click="debugCropCoords" size="small" type="info">
                      <el-icon><svg viewBox="0 0 1024 1024"><path d="M512 64a448 448 0 1 1 0 896A448 448 0 0 1 512 64zm23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352-30.976 29.568-46.464 71.04-46.464 124.416l0 36.608 73.216 0 0-31.616c0-33.792 6.72-58.368 20.16-73.728 13.44-15.36 34.816-23.04 64.128-23.04 26.112 0 45.568 6.72 58.368 20.16 12.8 13.44 19.2 31.104 19.2 52.992 0 16.896-4.352 31.616-13.056 44.16-8.704 12.544-23.808 26.88-45.312 43.008-21.504 16.128-37.12 34.56-46.848 55.296C486.4 568.32 481.536 593.408 481.536 623.104l0 32.256 73.216 0 0-28.416c0-24.064 3.584-44.672 10.752-61.824 7.168-17.152 20.608-34.56 40.32-52.224 19.712-17.664 34.304-33.536 43.776-47.616 9.472-14.08 14.208-30.976 14.208-50.688 0-44.672-13.824-78.848-41.472-102.528C594.688 267.584 554.496 255.488 535.744 255.488zM512 704a48 48 0 1 0 0 96 48 48 0 0 0 0-96z"/></svg></el-icon>
                      调试坐标
                    </el-button>
                </div>
                  
                  <!-- 裁剪步骤完成按钮 -->
                  <div class="step-action" v-if="currentStep === 4">
                    <el-button @click="completeStep(4)" type="primary" size="small" style="width: 100%">
                      <el-icon><Check /></el-icon>
                      完成裁剪，进入下一步
                    </el-button>
              </div>
                  
                  <!-- 裁剪提示信息 -->
                  <div class="crop-tips" v-if="!cropMode">
                    <p class="tip-text">
                      <el-icon><svg viewBox="0 0 1024 1024"><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z"/></svg></el-icon>
                      裁剪框会根据所选证件规格自动调整比例，确保符合标准要求
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </div>

        <!-- 5. 一键排版 -->
        <div class="control-section" :class="{ disabled: currentStep < 5 }">
          <div class="section-header" @click="currentStep >= 5 && toggleSection('layout')">
            <h3>
              <span class="step-number">5</span>
              一键排版
              <span v-if="currentStep === 5" class="current-step-badge">当前步骤</span>
              <span v-if="currentStep > 5" class="completed-step-badge">已完成</span>
              <span v-if="currentStep < 5" class="disabled-step-badge">等待中</span>
            </h3>
            <el-icon class="expand-icon" :class="{ expanded: expandedSections.layout }" v-if="currentStep >= 5">
              <ArrowDown />
            </el-icon>
          </div>
          <transition name="section-slide">
            <div class="section-content" v-if="expandedSections.layout && currentStep >= 5">
              
              <!-- 相纸规格选择 -->
              <div class="paper-size-section">
                <h4 class="sub-section-title">相纸规格</h4>
                <div class="paper-categories">
                  <div v-for="category in paperSizeCategories" :key="category" class="paper-category">
                    <!-- 可折叠的分类标题 -->
                    <div class="category-header" @click="togglePaperCategory(category)">
                      <h5 class="category-title">{{ category }}</h5>
                      <el-icon class="category-expand-icon" :class="{ expanded: expandedPaperCategories[category] }">
                        <ArrowDown />
                      </el-icon>
                    </div>

                    <!-- 折叠式相纸网格 -->
                    <transition name="category-slide">
                      <div class="paper-grid" v-if="expandedPaperCategories[category]">
                        <div 
                          v-for="paperSize in getPaperSizesByCategory(category)" 
                          :key="paperSize.id"
                          class="paper-card"
                          :class="{ active: selectedPaperSize === paperSize.id }"
                          @click="selectPaperSize(paperSize)"
                        >
                          <div class="paper-preview">{{ paperSize.name }}</div>
                          <div class="paper-size">{{ paperSize.size_display }}</div>
                          <div class="paper-orientation">
                            <span :class="{ active: paperOrientation === 'portrait' }">纵向</span>
                            /
                            <span :class="{ active: paperOrientation === 'landscape' }">横向</span>
                          </div>
                        </div>
                      </div>
                    </transition>
                  </div>
                </div>
              </div>

              <!-- 纸张方向选择 -->
              <div class="orientation-section" v-if="selectedPaperSize">
                <h4 class="sub-section-title">纸张方向</h4>
                <div class="orientation-controls">
                  <el-radio-group v-model="paperOrientation" @change="onOrientationChange">
                    <el-radio-button label="portrait">
                      <el-icon><svg viewBox="0 0 24 24"><rect x="7" y="3" width="10" height="18" rx="1" fill="none" stroke="currentColor" stroke-width="2"/></svg></el-icon>
                      纵向
                    </el-radio-button>
                    <el-radio-button label="landscape">
                      <el-icon><svg viewBox="0 0 24 24"><rect x="3" y="7" width="18" height="10" rx="1" fill="none" stroke="currentColor" stroke-width="2"/></svg></el-icon>
                      横向
                    </el-radio-button>
                  </el-radio-group>
                </div>
              </div>

              <!-- 排版设置 -->
              <div class="layout-settings-section" v-if="selectedPaperSize">
                <h4 class="sub-section-title">排版设置</h4>
                
                <div class="control-item">
                  <label class="control-label">照片间距 ({{ layoutSpacing }}mm)</label>
                  <el-slider
                    v-model="layoutSpacing"
                    :min="0"
                    :max="10"
                    :step="0.5"
                    @change="calculateLayout"
                  />
                </div>
                
                <!-- 自动/手动排版切换 -->
                <div class="control-item">
                  <el-switch
                    v-model="useCustomLayout"
                    active-text="手动调整"
                    inactive-text="自动排版"
                    @change="onLayoutModeChange"
                  />
                </div>
                
                <!-- 手动调整行列数 -->
                <div v-if="useCustomLayout" class="custom-layout-controls">
                  <div class="control-item">
                    <label class="control-label">行数</label>
                    <el-input-number
                      v-model="customRows"
                      :min="1"
                      :max="maxRows"
                      size="small"
                      @change="updateCustomLayout"
                    />
                    <span class="control-hint">最大{{ maxRows }}行</span>
                  </div>
                  <div class="control-item">
                    <label class="control-label">列数</label>
                    <el-input-number
                      v-model="customCols"
                      :min="1"
                      :max="maxCols"
                      size="small"
                      @change="updateCustomLayout"
                    />
                    <span class="control-hint">最大{{ maxCols }}列</span>
                  </div>
                </div>
                
                <!-- 排版信息显示 -->
                <div class="layout-info" v-if="layoutInfo">
                  <div class="info-grid">
                    <div class="info-item">
                      <span class="info-label">排版：</span>
                      <span class="info-value">{{ layoutInfo.rows }}行 × {{ layoutInfo.cols }}列</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">总数：</span>
                      <span class="info-value">{{ layoutInfo.total_photos }}张</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">纸张：</span>
                      <span class="info-value">{{ currentPaperSize?.name }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">方向：</span>
                      <span class="info-value">{{ paperOrientation === 'portrait' ? '纵向' : '横向' }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 排版操作 -->
              <div class="layout-actions" v-if="selectedPaperSize && layoutInfo">
                <div class="control-row">
                  <el-button @click="previewLayout" :loading="layoutProcessing" size="small">
                    <el-icon><ViewIcon /></el-icon>
                    预览排版
                  </el-button>
                  <el-button @click="generateLayout" :loading="layoutProcessing" size="small" type="primary">
                    <el-icon><Download /></el-icon>
                    生成下载
                  </el-button>
                </div>
              </div>

              <!-- 排版步骤完成按钮 -->
              <div class="step-action" v-if="currentStep === 5">
                <el-button @click="completeStep(5)" type="primary" size="small" style="width: 100%">
                  <el-icon><Check /></el-icon>
                  完成排版制作
                </el-button>
              </div>

            </div>
          </transition>
        </div>

        <!-- 快速操作 -->
        <div class="workflow-actions" v-if="currentStep > 4">
          <div class="action-group">
            <h4>完成制作</h4>
            <p>所有步骤已完成，可以保存或重新调整</p>
            <div class="action-buttons">
              <el-button class="action-btn" @click="resetWorkflow">
            <el-icon><RefreshLeft /></el-icon>
                重新制作
          </el-button>
              <el-button class="action-btn" type="primary" @click="finalizeImage">
            <el-icon><MagicStick /></el-icon>
                完成制作
          </el-button>
        </div>
          </div>
          

        </div>


      </div>
    </div>

    <!-- 颜色选择器弹窗 -->
    <el-dialog 
      v-model="showColorPicker" 
      title="选择自定义颜色" 
      width="400px" 
      class="color-picker-dialog"
      :show-close="false"
    >
      <div class="color-picker-content">
        <el-color-picker 
          v-model="customColor" 
          show-alpha 
          @change="onCustomColorChange"
        />
        <div class="color-preview" :style="{ backgroundColor: customColor }">
          <span>预览效果</span>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showColorPicker = false">取消</el-button>
          <el-button type="primary" @click="confirmCustomColor">确定</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, nextTick, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ArrowLeft,
  Download,
  Plus,
  ArrowDown,
  Check,
  Brush,
  RefreshLeft,
  MagicStick,
  Aim,
  View as ViewIcon
} from '@element-plus/icons-vue'
import api from '../api'

export default {
  name: 'PhotoEditor',
  components: {
    ArrowLeft,
    Download,
    Plus,
    ArrowDown,
    Check,
    Brush,
    RefreshLeft,
    MagicStick,
    Aim,
    ViewIcon
  },
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const originalImage = ref(null)
    const processedImage = ref(null)
    const fileName = ref('')
    const imageWidth = ref(0)
    const imageHeight = ref(0)
    const processing = ref(false)
    const fileInput = ref(null)
    
    // 防抖定时器
    let processTimeout = null
    
    // 模板和背景
    const templates = ref([
      // 常用证件照规格
      { id: 1, name: '一寸照', width: 295, height: 413, width_mm: 25, height_mm: 35, category: '标准证件照' },
      { id: 2, name: '二寸照', width: 413, height: 531, width_mm: 35, height_mm: 45, category: '标准证件照' },
      { id: 3, name: '小二寸', width: 413, height: 472, width_mm: 35, height_mm: 40, category: '标准证件照' },
      { id: 4, name: '大二寸', width: 449, height: 531, width_mm: 38, height_mm: 45, category: '标准证件照' },
      { id: 5, name: '三寸照', width: 649, height: 826, width_mm: 55, height_mm: 70, category: '标准证件照' },
      { id: 6, name: '五寸照', width: 1181, height: 1476, width_mm: 100, height_mm: 125, category: '标准证件照' },
      
      // 护照签证类
      { id: 7, name: '护照照片', width: 354, height: 472, width_mm: 30, height_mm: 40, category: '护照签证' },
      { id: 8, name: '美国签证', width: 600, height: 600, width_mm: 51, height_mm: 51, category: '护照签证' },
      { id: 9, name: '欧洲签证', width: 413, height: 531, width_mm: 35, height_mm: 45, category: '护照签证' },
      { id: 10, name: '日本签证', width: 354, height: 472, width_mm: 30, height_mm: 40, category: '护照签证' },
      { id: 11, name: '韩国签证', width: 413, height: 531, width_mm: 35, height_mm: 45, category: '护照签证' },
      { id: 12, name: '英国签证', width: 413, height: 531, width_mm: 35, height_mm: 45, category: '护照签证' },
      
      // 考试报名类
      { id: 13, name: '高考报名', width: 480, height: 640, width_mm: 40.8, height_mm: 54.4, category: '考试报名' },
      { id: 14, name: '研究生报名', width: 480, height: 640, width_mm: 40.8, height_mm: 54.4, category: '考试报名' },
      { id: 15, name: '公务员考试', width: 413, height: 531, width_mm: 35, height_mm: 45, category: '考试报名' },
      { id: 16, name: '教师资格证', width: 295, height: 413, width_mm: 25, height_mm: 35, category: '考试报名' },
      { id: 17, name: '四六级考试', width: 240, height: 320, width_mm: 20, height_mm: 27, category: '考试报名' },
      
      // 职业资格类
      { id: 18, name: '身份证照片', width: 358, height: 441, width_mm: 30.4, height_mm: 37.4, category: '证件办理' },
      { id: 19, name: '驾驶证照片', width: 260, height: 378, width_mm: 22, height_mm: 32, category: '证件办理' },
      { id: 20, name: '工作证照片', width: 295, height: 413, width_mm: 25, height_mm: 35, category: '证件办理' },
      { id: 21, name: '社保卡照片', width: 358, height: 441, width_mm: 30.4, height_mm: 37.4, category: '证件办理' },
      
      // 医疗保健类
      { id: 22, name: '医师资格证', width: 295, height: 413, width_mm: 25, height_mm: 35, category: '医疗保健' },
      { id: 23, name: '护士资格证', width: 295, height: 413, width_mm: 25, height_mm: 35, category: '医疗保健' },
      
      // 求职应聘类
      { id: 24, name: '简历照片', width: 413, height: 531, width_mm: 35, height_mm: 45, category: '求职应聘' },
      { id: 25, name: '面试照片', width: 295, height: 413, width_mm: 25, height_mm: 35, category: '求职应聘' }
    ])
    
    const backgroundColors = ref([
      { name: '白色', value: '#ffffff' },
      { name: '蓝色', value: '#0066cc' },
      { name: '红色', value: '#cc0000' },
      { name: '浅蓝', value: '#e6f3ff' },
      { name: '浅灰', value: '#f5f5f5' },
      { name: '深蓝', value: '#003d82' }
    ])
    
    const selectedTemplate = ref(null)
    const selectedBackground = ref('#ffffff')
    const customColor = ref('#ffffff')
    const showColorPicker = ref(false)
    
    // 美颜设置
    const beautySettings = reactive({
      smoothness: 30,
      whitening: 20,
      brightness: 0,
      contrast: 0
    })
    
    // 尺寸设置
    const customSize = reactive({
      width: 295,
      height: 413
    })
    const keepAspectRatio = ref(true)
    
    // 图片变换状态
    const imageTransform = reactive({
      scale: 1,
      rotation: 0,
      translateX: 0,
      translateY: 0
    })
    
    // 裁剪状态
    const cropMode = ref(false)
    const cropRect = reactive({
      x: 50,       // 裁剪框左上角X坐标 (像素)
      y: 50,       // 裁剪框左上角Y坐标 (像素)
      width: 200,  // 裁剪框宽度 (像素)
      height: 250  // 裁剪框高度 (像素)
    })
    
    // 裁剪交互状态
    const cropState = reactive({
      isDragging: false,
      isResizing: false,
      resizeDirection: '',
      startX: 0,
      startY: 0,
      startRect: { x: 0, y: 0, width: 0, height: 0 }
    })
    
    // DOM引用
    const imageContainer = ref(null)
    const previewImage = ref(null)
    
    // 计算属性 - 裁剪框样式
    const cropBoxStyle = computed(() => ({
      position: 'absolute',
      left: `${cropRect.x}px`,
      top: `${cropRect.y}px`,
      width: `${cropRect.width}px`,
      height: `${cropRect.height}px`,
      cursor: cropState.isDragging ? 'move' : 'move'
    }))
    
    // 计算属性 - 遮罩样式
    const maskTopStyle = computed(() => ({
      position: 'absolute',
      top: '0',
      left: '0',
      right: '0',
      height: `${cropRect.y}px`,
      backgroundColor: 'rgba(0, 0, 0, 0.5)'
    }))
    
    const maskBottomStyle = computed(() => ({
      position: 'absolute',
      top: `${cropRect.y + cropRect.height}px`,
      left: '0',
      right: '0',
      bottom: '0',
      backgroundColor: 'rgba(0, 0, 0, 0.5)'
    }))
    
    const maskLeftStyle = computed(() => ({
      position: 'absolute',
      top: `${cropRect.y}px`,
      left: '0',
      width: `${cropRect.x}px`,
      height: `${cropRect.height}px`,
      backgroundColor: 'rgba(0, 0, 0, 0.5)'
    }))
    
    const maskRightStyle = computed(() => ({
      position: 'absolute',
      top: `${cropRect.y}px`,
      left: `${cropRect.x + cropRect.width}px`,
      right: '0',
      height: `${cropRect.height}px`,
      backgroundColor: 'rgba(0, 0, 0, 0.5)'
    }))
    
    // 当前应该显示的图片 - 优先显示每步骤的处理结果
    const currentDisplayImage = computed(() => {
      // 如果有临时处理结果（预览），始终优先显示
      if (processedImage.value) {
        return processedImage.value
      }
      
      // 按工作流步骤显示对应的最终图片
      if (currentStep.value > 4 && stepImages.step4) {
        // 工作流完成，显示最终裁剪结果
        return stepImages.step4
      }
      if (currentStep.value >= 4 && stepImages.step3) {
        // 当前在裁剪步骤，显示规格调整后的图片作为裁剪基础
        return stepImages.step3
      }
      if (currentStep.value >= 3 && stepImages.step2) {
        // 当前在规格步骤，显示美颜后的图片
        return stepImages.step2
      }
      if (currentStep.value >= 2 && stepImages.step1) {
        // 当前在美颜步骤，显示背景处理后的图片
        return stepImages.step1
      }
      
      // 当前在背景步骤且没有处理结果时，显示原始图片
      return originalImage.value
    })
    
    // 排版相关计算属性
    const paperSizeGroups = computed(() => {
      const groups = [
        { label: '常用相纸', options: [] },
        { label: '其他规格', options: [] }
      ]
      
      paperSizes.value.forEach(size => {
        if (size.is_common) {
          groups[0].options.push(size)
        } else {
          groups[1].options.push(size)
        }
      })
      
      return groups.filter(group => group.options.length > 0)
    })
    
    // 获取最终处理的图片（用于排版）
    const finalProcessedImage = computed(() => {
      console.log('=== 计算finalProcessedImage ===')
      console.log('当前步骤:', currentStep.value)
      console.log('stepImages状态:', {
        step1: stepImages.step1 ? '✓ 有背景处理图片' : '✗ 无背景处理图片',
        step2: stepImages.step2 ? '✓ 有美颜图片' : '✗ 无美颜图片',
        step3: stepImages.step3 ? '✓ 有规格调整图片' : '✗ 无规格调整图片',
        step4: stepImages.step4 ? '✓ 有裁剪图片' : '✗ 无裁剪图片'
      })
      
      // 在任何情况下，如果有裁剪后的图片，都优先使用
      if (stepImages.step4) {
        console.log('✅ 找到裁剪后图片，优先使用 step4')
        return stepImages.step4
      }
      
      // 当前在排版步骤且没有裁剪，使用规格调整后的图片
      if (currentStep.value >= 5 && stepImages.step3) {
        console.log('⚠️ 没有裁剪图片，使用规格调整后图片 step3')
        return stepImages.step3
      }
      
      // 如果在排版步骤但没有规格调整，使用美颜后的图片
      if (currentStep.value >= 5 && stepImages.step2) {
        console.log('⚠️ 没有规格调整图片，使用美颜后图片 step2')
        return stepImages.step2
      }
      
      // 如果在排版步骤但没有美颜，使用背景处理后的图片
      if (currentStep.value >= 5 && stepImages.step1) {
        console.log('⚠️ 没有美颜图片，使用背景处理后图片 step1')
        return stepImages.step1
      }
      
      // 最后使用当前显示的图片
      console.log('⚠️ 使用currentDisplayImage')
      return currentDisplayImage.value
    })


    // 模板分类
    const templateCategories = ref([
      '标准证件照',
      '护照签证', 
      '考试报名',
      '证件办理',
      '医疗保健',
      '求职应聘'
    ])
    
    // 展开状态 - 新顺序：背景→美颜→规格→裁剪→排版
    const expandedSections = reactive({
      background: true,  // 步骤1：背景颜色
      beauty: false,     // 步骤2：美颜优化
      template: false,   // 步骤3：证件规格
      crop: false,       // 步骤4：图片裁剪
      layout: false,     // 步骤5：一键排版
      advancedCrop: false
    })
    
    // 工作流状态管理 - 新顺序：背景→美颜→规格→裁剪
    const currentStep = ref(1) // 当前步骤：1-背景，2-美颜，3-规格，4-裁剪
    const stepImages = reactive({
      step1: null, // 背景替换后的图片
      step2: null, // 美颜后的图片  
      step3: null, // 规格调整后的图片
      step4: null  // 最终裁剪后的图片
    })
    
    // 排版功能相关状态
    const paperSizes = ref([])
    const selectedPaperSize = ref(null)
    const currentPaperSize = ref(null)
    const layoutSpacing = ref(2) // 照片间距，默认2mm
    const layoutInfo = ref(null)
    const layoutProcessing = ref(false)
    const layoutPreviewMode = ref(false)  // 排版预览模式
    const layoutPreviewImage = ref(null)
    // 手动调整行列数
    const customRows = ref(0)
    const customCols = ref(0)
    const useCustomLayout = ref(false)
    const maxRows = ref(0)
    const maxCols = ref(0)
    
    // 纸张方向选择
    const paperOrientation = ref('portrait') // 'portrait' | 'landscape'
    
    // 相纸分类
    const paperSizeCategories = ref([
      '常用相纸',
      '标准相纸'
    ])
    
    // 相纸分类展开状态
    const expandedPaperCategories = reactive({
      '常用相纸': true,
      '标准相纸': false
    })
    
    // 证件模板分类展开状态
    const expandedCategories = reactive({
      '标准证件照': true,
      '护照签证': false,
      '考试报名': false,
      '证件办理': false,
      '医疗保健': false,
      '求职应聘': false
    })
    
        // 自动裁剪选项
    const autoOptions = reactive({
      faceDetection: true,
      autoRotate: true,
      enhanceQuality: true,
      removeBackground: true,
      autoLevel: true,
      detectionLevel: 'normal'
    })

    // 监听当前显示图片的变化，在图片切换时重新初始化裁剪框
    watch(currentDisplayImage, (newImage, oldImage) => {
      if (newImage !== oldImage && cropMode.value) {
        console.log('检测到图片变化，重新初始化裁剪框')
        nextTick(() => {
          initCropRect()
        })
      }
    })

    // 在组件挂载时尝试加载保存的预设
    onMounted(() => {
      try {
        const saved = localStorage.getItem('autoCropPreset')
        if (saved) {
          const preset = JSON.parse(saved)
          // 只在预设不超过7天的情况下加载
          const savedTime = new Date(preset.timestamp || 0)
          const now = new Date()
          const daysDiff = (now - savedTime) / (1000 * 60 * 60 * 24)
          
          if (daysDiff < 7) {
            Object.assign(autoOptions, preset)
            console.log('已自动加载保存的裁剪预设')
          }
        }
      } catch (error) {
        console.log('加载裁剪预设失败:', error)
      }
      
      // 加载相纸规格数据
      loadPaperSizes()
    })
    
    // 方法
    const goBack = () => {
      router.push('/')
    }
    
    const triggerFileInput = () => {
      fileInput.value?.click()
    }
    
    const handleFileSelect = (event) => {
      const file = event.target.files[0]
      if (file) {
        loadImage(file)
      }
    }
    
    const handleDrop = (event) => {
      event.preventDefault()
      const file = event.dataTransfer.files[0]
      if (file && file.type.startsWith('image/')) {
        loadImage(file)
      }
      // 移除拖拽样式
      event.target.classList.remove('drag-over')
    }
    
    const handleDragOver = (event) => {
      event.preventDefault()
      event.dataTransfer.dropEffect = 'copy'
    }
    
    const handleDragEnter = (event) => {
      event.preventDefault()
      event.target.classList.add('drag-over')
    }
    
    const handleDragLeave = (event) => {
      event.preventDefault()
      // 检查是否真的离开了拖拽区域
      if (!event.target.contains(event.relatedTarget)) {
        event.target.classList.remove('drag-over')
      }
    }
    
    const loadImage = async (file) => {
      if (!file.type.startsWith('image/')) {
        ElMessage.error('请选择图片文件')
        return
      }

      // 提取文件基本信息
      fileName.value = file.name
      
      // 1. 先在本地显示图片以获得即时反馈
      const reader = new FileReader()
      reader.onload = (e) => {
        originalImage.value = e.target.result
        processedImage.value = null // 清除旧的处理结果
        
        // 创建Image对象以获取图片尺寸
        const img = new Image()
        img.onload = () => {
          imageWidth.value = img.width
          imageHeight.value = img.height
          console.log('图片尺寸:', img.width, 'x', img.height)
          
          // 在获取到尺寸后重置所有步骤
          resetAllSteps()
          ElMessage.success('图片已加载，开始你的创作吧！')
        }
        img.src = e.target.result
      }
      reader.readAsDataURL(file)

      // 2. 将文件上传到服务器
      try {
        // 调用上传API，直接传递file对象
        const response = await api.images.upload(file)
        
        if (response.data.success) {
          console.log('图片已成功同步到服务器:', response.data.image)
          
          // 更新文件信息（服务器可能会处理文件名等）
          const imageData = response.data.image
          if (imageData.original_filename) {
            fileName.value = imageData.original_filename
          }
          if (imageData.width && imageData.height) {
            imageWidth.value = imageData.width
            imageHeight.value = imageData.height
          }
        } else {
          ElMessage.error('图片同步到服务器失败，请检查网络后重试')
        }
      } catch (error) {
        console.error('上传图片失败:', error)
        ElMessage.error('图片上传至服务器失败: ' + (error.response?.data?.message || error.message))
      }
    }
    
    const onImageLoad = (event) => {
      // 图片加载完成后的处理
      console.log('图片加载完成')
      
      // 如果还没有图片尺寸信息，从加载的图片元素中获取
      if (event && event.target && (!imageWidth.value || !imageHeight.value)) {
        imageWidth.value = event.target.naturalWidth
        imageHeight.value = event.target.naturalHeight
        console.log('从图片元素获取尺寸:', event.target.naturalWidth, 'x', event.target.naturalHeight)
      }
      
      // 确保在图片完全加载后再初始化裁剪框
      nextTick(() => {
        if (cropMode.value) {
          console.log('图片加载完成，重新初始化裁剪框')
          initCropRect()
        }
      })
    }
    
    const toggleSection = (section) => {
      // 关闭所有其他菜单
      Object.keys(expandedSections).forEach(key => {
        expandedSections[key] = false
      })
      // 切换当前菜单
      expandedSections[section] = true
    }
    
    const selectTemplate = (template) => {
      selectedTemplate.value = template
      if (keepAspectRatio.value) {
        customSize.width = template.width
        customSize.height = template.height
      }
      
      // 选择模板后重新初始化裁剪框，使其符合证件规格比例
      if (cropMode.value) {
        setTimeout(() => {
          initCropRect()
        }, 100)
      }
      
      ElMessage.info(`已选择 ${template.name} 规格`)
      
      // 在证件规格步骤中，只选择模板不立即处理，避免尺寸变化
      if (currentStep.value !== 3) {
        debounceProcess()
      }
    }
    
    const selectBackground = async (color) => {
      selectedBackground.value = color
      ElMessage.info('背景颜色已更改')
      
      // 在工作流模式下，立即预览背景更换效果
      if (originalImage.value && currentStep.value >= 1) {
        try {
          processing.value = true
          await processBackground()
          ElMessage.success('背景预览已更新')
        } catch (error) {
          console.error('背景预览失败:', error)
          ElMessage.error('背景预览失败: ' + error.message)
        } finally {
          processing.value = false
        }
      }
    }
    
    const onCustomColorChange = (color) => {
      customColor.value = color
    }
    
    const confirmCustomColor = async () => {
      selectedBackground.value = customColor.value
      showColorPicker.value = false
      ElMessage.success('自定义颜色已应用')
      
      // 在工作流模式下，立即预览背景更换效果
      if (originalImage.value && currentStep.value >= 1) {
        try {
          processing.value = true
          await processBackground()
          ElMessage.success('背景预览已更新')
        } catch (error) {
          console.error('背景预览失败:', error)
          ElMessage.error('背景预览失败: ' + error.message)
        } finally {
          processing.value = false
        }
      }
    }
    
    const debounceProcess = () => {
      if (processTimeout) {
        clearTimeout(processTimeout)
      }
      processTimeout = setTimeout(async () => {
        // 在美颜步骤中使用美颜专用API，而不是完整的证件照处理API
        if (currentStep.value === 2) {
          try {
            await processBeauty()
          } catch (error) {
            // 错误已在processBeauty函数中处理
          }
        } else if (currentStep.value === 3) {
          // 在证件规格步骤中，只选择模板不立即处理，避免尺寸变化
          console.log('证件规格步骤：仅选择模板，不调用处理API')
        } else {
          processImage()
        }
      }, 500)
    }
    
    const resetBeauty = async () => {
      beautySettings.smoothness = 30
      beautySettings.whitening = 20
      beautySettings.brightness = 0
      beautySettings.contrast = 0
      ElMessage.info('美颜设置已重置')
      // 在美颜步骤中使用美颜专用API
      if (currentStep.value === 2) {
        try {
          await processBeauty()
        } catch (error) {
          // 错误已在processBeauty函数中处理
        }
      } else {
        processImage()
      }
    }
    
    const applySizeChange = () => {
      if (keepAspectRatio.value && selectedTemplate.value) {
        const ratio = selectedTemplate.value.width / selectedTemplate.value.height
        customSize.height = Math.round(customSize.width / ratio)
      }
      debounceProcess()
    }
    
    const onAspectRatioChange = () => {
      if (keepAspectRatio.value && selectedTemplate.value) {
        applySizeChange()
      }
    }
    
    const processImage = async () => {
      if (!originalImage.value) return
      
      processing.value = true
      try {
        // 调用后端API进行图片处理
        const result = await api.idPhoto.process({
          image_data: originalImage.value,
          template: selectedTemplate.value,
          background: selectedBackground.value,
          beauty: beautySettings,
          size: customSize,
          transform: imageTransform,
          auto_corrections: {
            auto_level: false,
            center_face: false
          }
        })
        
        if (result.data.success) {
          processedImage.value = result.data.processed_image
          ElMessage.success('图片处理完成')
        } else {
          throw new Error(result.data.message || '处理失败')
        }
      } catch (error) {
        console.error('图片处理失败:', error)
        ElMessage.error('图片处理失败: ' + (error.response?.data?.message || error.message))
      } finally {
        processing.value = false
      }
    }
    
    const resetAll = () => {
      ElMessageBox.confirm(
        '确定要重置所有设置吗？这将清除当前的所有调整。',
        '确认重置',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        selectedTemplate.value = null
        selectedBackground.value = '#ffffff'
        resetBeauty()
        customSize.width = imageWidth.value
        customSize.height = imageHeight.value
        processedImage.value = null
        ElMessage.success('所有设置已重置')
      }).catch(() => {
        ElMessage.info('已取消重置')
      })
    }
    
    const downloadPhoto = () => {
      if (!processedImage.value) {
        ElMessage.warning('请先处理图片')
        return
      }
      
      try {
        const link = document.createElement('a')
        const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
        link.download = `processed_${timestamp}_${fileName.value}`
        link.href = processedImage.value
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        ElMessage.success('图片已保存到下载文件夹')
      } catch (error) {
        ElMessage.error('保存失败: ' + error.message)
      }
    }
    
    // 排版功能相关方法
    const loadPaperSizes = async () => {
      try {
        const response = await api.layout.getPaperSizes()
        paperSizes.value = response.data.paper_sizes
        console.log('加载相纸规格成功:', paperSizes.value)
      } catch (error) {
        console.error('加载相纸规格失败:', error)
        ElMessage.error('加载相纸规格失败')
      }
    }
    
    const onPaperSizeChange = (sizeId) => {
      const size = paperSizes.value.find(s => s.id === sizeId)
      currentPaperSize.value = size
      if (size && selectedTemplate.value) {
        // 重置为自动模式
        useCustomLayout.value = false
        calculateLayout()
      }
    }
    
    // 计算最大行列数
    const calculateMaxRowsCols = () => {
      if (!currentPaperSize.value || !selectedTemplate.value) {
        maxRows.value = 0
        maxCols.value = 0
        return
      }

      // 确定纸张的长边和短边
      const dbWidth = effectivePrintableArea.value.width
      const dbHeight = effectivePrintableArea.value.height
      const longEdge = Math.max(dbWidth, dbHeight)
      const shortEdge = Math.min(dbWidth, dbHeight)

      // 根据用户选择的纸张方向（物理概念）来确定计算用的宽高
      let paperWidth, paperHeight
      if (paperOrientation.value === 'landscape') {
        // 横向：长边是宽度，短边是高度
        paperWidth = longEdge
        paperHeight = shortEdge
      } else {
        // 纵向：短边是宽度，长边是高度
        paperWidth = shortEdge
        paperHeight = longEdge
      }
      
      const photoWidth = selectedTemplate.value.width_mm
      const photoHeight = selectedTemplate.value.height_mm
      const spacing = layoutSpacing.value
      
      if (photoWidth <= 0 || photoHeight <= 0) {
        maxRows.value = 0
        maxCols.value = 0
        return
      }

      maxCols.value = Math.floor((paperWidth + spacing) / (photoWidth + spacing))
      maxRows.value = Math.floor((paperHeight + spacing) / (photoHeight + spacing))
      
      console.log(`最大行列数 (${paperOrientation.value}):`, maxRows.value, '行 ×', maxCols.value, '列')
    }
    
    // 前端最优排版计算（遵循用户指定的行列逻辑）
    const calculateOptimalLayout = () => {
      if (!currentPaperSize.value || !selectedTemplate.value) {
        return null
      }
      
      // 确定纸张的长边和短边
      const dbWidth = effectivePrintableArea.value.width
      const dbHeight = effectivePrintableArea.value.height
      const longEdge = Math.max(dbWidth, dbHeight)
      const shortEdge = Math.min(dbWidth, dbHeight)

      // 根据用户选择的纸张方向（物理概念）来确定计算用的宽高
      let paperWidth, paperHeight
      if (paperOrientation.value === 'landscape') {
        // 横向：长边是宽度，短边是高度
        paperWidth = longEdge
        paperHeight = shortEdge
      } else {
        // 纵向：短边是宽度，长边是高度
        paperWidth = shortEdge
        paperHeight = longEdge
      }
      
      const photoWidth = selectedTemplate.value.width_mm
      const photoHeight = selectedTemplate.value.height_mm
      const spacing = layoutSpacing.value

      // 根据用户逻辑直接计算行列数
      const cols = Math.floor((paperWidth + spacing) / (photoWidth + spacing))
      const rows = Math.floor((paperHeight + spacing) / (photoHeight + spacing))
      
      const usedWidth = cols * photoWidth + (cols > 1 ? (cols - 1) * spacing : 0)
      const usedHeight = rows * photoHeight + (rows > 1 ? (rows - 1) * spacing : 0)

      const result = {
        rows: rows,
        cols: cols,
        total_photos: rows * cols,
        photo_rotated: false, // 不再进行自动旋转
        used_width_mm: Math.round(usedWidth * 100) / 100,
        used_height_mm: Math.round(usedHeight * 100) / 100,
        offset_x_mm: Math.max(0, Math.round((paperWidth - usedWidth) / 2 * 100) / 100),
        offset_y_mm: Math.max(0, Math.round((paperHeight - usedHeight) / 2 * 100) / 100),
        spacing_mm: spacing
      }
      
      console.log('前端排版计算结果 (固定方向):', result)
      return result
    }

    const calculateLayout = async () => {
      if (!currentPaperSize.value || !selectedTemplate.value) {
        return
      }
      
      try {
        // 1. 始终为UI约束计算最大可能的行和列
        calculateMaxRowsCols()
        
        if (useCustomLayout.value) {
          // 2. 如果是手动模式，只需根据用户输入更新布局
          updateCustomLayout()
        } else {
          // 3. 自动模式：现在后端已足够智能，直接调用API
          try {
            console.log(`自动排版：调用后端API，方向: ${paperOrientation.value}`)
            const response = await api.layout.calculateLayout(currentPaperSize.value.id, {
              photo_width_mm: selectedTemplate.value.width_mm,
              photo_height_mm: selectedTemplate.value.height_mm,
              spacing_mm: layoutSpacing.value,
              orientation: paperOrientation.value // 传递当前方向
            })
            
            const layoutResult = response.data.layout
            
            if (layoutResult) {
              layoutInfo.value = layoutResult
              // 将自定义值与自动计算的值同步
              customRows.value = layoutInfo.value.rows
              customCols.value = layoutInfo.value.cols
            }
            console.log('后端API计算成功:', layoutResult)

          } catch (error) {
            console.error('排版计算失败，即使在新的API模式下:', error)
            ElMessage.error('排版计算失败: ' + (error.response?.data?.message || error.message))
          }
        }
      } catch (error) {
        console.error('排版计算流程错误:', error)
        ElMessage.error('排版计算失败')
      }
    }
    
    const updateCustomLayout = () => {
      if (!currentPaperSize.value || !selectedTemplate.value || !useCustomLayout.value) {
        return
      }
      
      // 确保行列数在有效范围内
      const rows = Math.min(Math.max(1, customRows.value), maxRows.value)
      const cols = Math.min(Math.max(1, customCols.value), maxCols.value)
      
      customRows.value = rows
      customCols.value = cols
      
      // 计算实际使用的空间和偏移
      const photoWidth = selectedTemplate.value.width_mm
      const photoHeight = selectedTemplate.value.height_mm
      const spacing = layoutSpacing.value
      
      const usedWidth = cols * photoWidth + (cols - 1) * spacing
      const usedHeight = rows * photoHeight + (rows - 1) * spacing
      
      // 确定纸张的长边和短边
      const dbWidth = effectivePrintableArea.value.width
      const dbHeight = effectivePrintableArea.value.height
      const longEdge = Math.max(dbWidth, dbHeight)
      const shortEdge = Math.min(dbWidth, dbHeight)

      // 根据用户选择的纸张方向（物理概念）来确定计算用的宽高
      let availableWidth, availableHeight
      if (paperOrientation.value === 'landscape') {
        availableWidth = longEdge
        availableHeight = shortEdge
      } else {
        availableWidth = shortEdge
        availableHeight = longEdge
      }
      
      const offsetX = (availableWidth - usedWidth) / 2
      const offsetY = (availableHeight - usedHeight) / 2
      
      // 手动设置layoutInfo
      layoutInfo.value = {
        rows: rows,
        cols: cols,
        total_photos: rows * cols,
        photo_rotated: false, // 手动模式下假定不旋转照片
        used_width_mm: Math.round(usedWidth * 100) / 100,
        used_height_mm: Math.round(usedHeight * 100) / 100,
        offset_x_mm: Math.round(offsetX * 100) / 100,
        offset_y_mm: Math.round(offsetY * 100) / 100,
        spacing_mm: spacing
      }
      
      console.log('手动排版更新:', layoutInfo.value)
    }
    
    // 监听纸张方向变化，重新计算排版
    watch(paperOrientation, (newValue, oldValue) => {
      if (newValue !== oldValue) {
        console.log(`纸张方向从 ${oldValue} 变为 ${newValue}，重新计算排版`)
        calculateLayout()
      }
    })
    
    const onLayoutModeChange = () => {
      if (useCustomLayout.value) {
        // 切换到手动模式时，使用当前的自动计算结果作为初始值
        if (layoutInfo.value) {
          customRows.value = layoutInfo.value.rows
          customCols.value = layoutInfo.value.cols
        }
        updateCustomLayout()
      } else {
        // 切换到自动模式时，重新计算
        calculateLayout()
      }
    }
    
    const formatSpacingTooltip = (value) => {
      return `${value}mm`
    }
    
    const previewLayout = async () => {
      if (!finalProcessedImage.value || !layoutInfo.value) {
        ElMessage.warning('请先完成图片处理和排版设置')
        return
      }
      
      try {
        layoutProcessing.value = true
        layoutPreviewMode.value = true
        layoutPreviewImage.value = null

        await nextTick() // 确保容器已在DOM中渲染

        const containerRect = imageContainer.value?.getBoundingClientRect()
        if (!containerRect || containerRect.width === 0) {
          throw new Error('无法获取预览容器尺寸，请重试')
        }

        // 获取纸张在当前方向下的完整像素尺寸
        const dbCanvasWidth = currentPaperSize.value.width_px
        const dbCanvasHeight = currentPaperSize.value.height_px
        const longEdgePx = Math.max(dbCanvasWidth, dbCanvasHeight)
        const shortEdgePx = Math.min(dbCanvasWidth, dbCanvasHeight)

        let paperWidthPx, paperHeightPx
        if (paperOrientation.value === 'landscape') {
          paperWidthPx = longEdgePx
          paperHeightPx = shortEdgePx
        } else {
          paperWidthPx = shortEdgePx
          paperHeightPx = longEdgePx
        }
        
        // 动态计算最佳缩放比例以适应容器
        const padding = 32 // 给予预览一些边距
        const maxPreviewWidth = containerRect.width - padding
        const maxPreviewHeight = containerRect.height - padding
        
        const scale = Math.min(
          maxPreviewWidth / paperWidthPx,
          maxPreviewHeight / paperHeightPx,
          1 // 不放大，只缩小
        )
        
        console.log(`动态计算预览缩放比例: ${scale.toFixed(3)}`)

        // 使用动态计算的比例生成预览图
        const canvas = await generateLayoutImage(scale)
        layoutPreviewImage.value = canvas.toDataURL('image/jpeg', 0.9)
        
        ElMessage.success(`排版预览生成完成 - ${layoutInfo.value.rows}行×${layoutInfo.value.cols}列，共${layoutInfo.value.total_photos}张`)
        
      } catch (error) {
        console.error('生成排版预览失败:', error)
        ElMessage.error('生成排版预览失败: ' + error.message)
        layoutPreviewMode.value = false
      } finally {
        layoutProcessing.value = false
      }
    }
    
    // 退出排版预览模式
    const exitLayoutPreview = () => {
      layoutPreviewMode.value = false
      layoutPreviewImage.value = null
    }
    
    // 相纸分类切换
    const togglePaperCategory = (category) => {
      expandedPaperCategories[category] = !expandedPaperCategories[category]
    }
    
    // 根据分类获取相纸规格
    const getPaperSizesByCategory = (category) => {
      return paperSizes.value.filter(size => {
        switch (category) {
          case '常用相纸':
            return size.is_common || ['A4', '6寸', '5寸'].includes(size.name)
          case '标准相纸':
            return !size.is_common && ['A5', '7寸', '8寸', '10寸'].includes(size.name)
          case '大幅面相纸':
            return ['11寸', '12寸', 'A3'].includes(size.name)
          default:
            return false
        }
      })
    }
    
    // 选择相纸规格
    const selectPaperSize = (paperSize) => {
      selectedPaperSize.value = paperSize.id
      currentPaperSize.value = paperSize
      calculateLayout()
    }
    
    // 纸张方向改变
    const onOrientationChange = () => {
      if (currentPaperSize.value) {
        // 交换宽高
        const temp = currentPaperSize.value.width_px
        currentPaperSize.value.width_px = currentPaperSize.value.height_px
        currentPaperSize.value.height_px = temp
        
        const tempMm = currentPaperSize.value.width_mm
        currentPaperSize.value.width_mm = currentPaperSize.value.height_mm
        currentPaperSize.value.height_mm = tempMm
        
        calculateLayout()
      }
    }
    
    const generateLayoutImage = async (scale = 1.0) => {
      if (!finalProcessedImage.value || !layoutInfo.value) {
        throw new Error('请先完成图片处理和排版设置')
      }
      
      if (currentStep.value < 5) {
        ElMessage.warning('请先完成所有制作步骤后再进行排版')
        throw new Error('工作流未完成')
      }
      
      // 详细的图片选择日志
      console.log('=== 排版图片选择详情 ===')
      console.log('当前步骤:', currentStep.value)
      console.log('stepImages状态:', {
        step1: stepImages.step1 ? '✓ 有背景处理图片' : '✗ 无背景处理图片',
        step2: stepImages.step2 ? '✓ 有美颜图片' : '✗ 无美颜图片',
        step3: stepImages.step3 ? '✓ 有规格调整图片' : '✗ 无规格调整图片',
        step4: stepImages.step4 ? '✓ 有裁剪图片' : '✗ 无裁剪图片'
      })
      console.log('finalProcessedImage选择的图片URL前缀:', finalProcessedImage.value ? finalProcessedImage.value.substring(0, 50) + '...' : 'null')
      
      // 强制检查是否应该使用裁剪后的图片
      let imageToUse = finalProcessedImage.value
      if (stepImages.step4) {
        console.log('⚠️ 发现裁剪后图片，强制使用 step4')
        imageToUse = stepImages.step4
      } else if (stepImages.step3) {
        console.log('⚠️ 未找到裁剪图片，使用规格调整后图片 step3')
        imageToUse = stepImages.step3
      } else {
        console.log('⚠️ 使用其他图片源')
      }
      
      // 创建Canvas进行排版
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      
      // 根据纸张方向调整画布尺寸
      const dbCanvasWidth = currentPaperSize.value.width_px
      const dbCanvasHeight = currentPaperSize.value.height_px
      const longEdgePx = Math.max(dbCanvasWidth, dbCanvasHeight)
      const shortEdgePx = Math.min(dbCanvasWidth, dbCanvasHeight)

      let canvasWidth, canvasHeight
      if (paperOrientation.value === 'landscape') {
        canvasWidth = longEdgePx
        canvasHeight = shortEdgePx
      } else {
        canvasWidth = shortEdgePx
        canvasHeight = longEdgePx
      }

      // 设置Canvas尺寸，应用传入的缩放比例
      canvas.width = canvasWidth * scale
      canvas.height = canvasHeight * scale
      
      // 填充白色背景
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      
      // 加载处理后的图片
      const img = new Image()
      img.crossOrigin = 'anonymous'
      
      console.log('加载排版图片:', imageToUse ? imageToUse.substring(0, 50) + '...' : 'null')
      
      await new Promise((resolve, reject) => {
        img.onload = () => {
          console.log('✓ 排版图片加载成功，尺寸:', img.width, '×', img.height)
          resolve()
        }
        img.onerror = (error) => {
          console.error('✗ 排版图片加载失败:', error)
          reject(error)
        }
        img.src = imageToUse
      })
      
      // 计算照片在相纸上的尺寸 (300 DPI)
      const photoMmWidth = selectedTemplate.value.width_mm
      const photoMmHeight = selectedTemplate.value.height_mm

      const photoWidthPx = (photoMmWidth / 25.4) * 300 * scale
      const photoHeightPx = (photoMmHeight / 25.4) * 300 * scale
      const spacingPx = (layoutSpacing.value / 25.4) * 300 * scale

      // 计算起始位置（居中），考虑特殊纸张的无边距情况
      const marginMm = effectivePrintableArea.value.margin
      const marginXPx = (marginMm / 25.4) * 300 * scale
      const marginYPx = (marginMm / 25.4) * 300 * scale
      const startX = marginXPx + (layoutInfo.value.offset_x_mm / 25.4) * 300 * scale
      const startY = marginYPx + (layoutInfo.value.offset_y_mm / 25.4) * 300 * scale

      // 排版照片
      for (let row = 0; row < layoutInfo.value.rows; row++) {
        for (let col = 0; col < layoutInfo.value.cols; col++) {
          const x = startX + col * (photoWidthPx + spacingPx)
          const y = startY + row * (photoHeightPx + spacingPx)
          ctx.drawImage(img, x, y, photoWidthPx, photoHeightPx)
        }
      }
      
      return canvas
    }
    
    const generateLayout = async () => {
      if (!finalProcessedImage.value || !layoutInfo.value) {
        ElMessage.warning('请先完成图片处理和排版设置')
        return
      }
      
      try {
        layoutProcessing.value = true
        
        const canvas = await generateLayoutImage(1.0) // 下载时使用1.0的完整比例
        
        // 生成下载链接
        canvas.toBlob((blob) => {
          const url = URL.createObjectURL(blob)
          const link = document.createElement('a')
          const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
          link.download = `layout_${currentPaperSize.value.name}_${timestamp}.jpg`
          link.href = url
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          URL.revokeObjectURL(url)
          
          ElMessage.success(`排版完成！已生成${layoutInfo.value.total_photos}张照片的排版文件`)
          
          // 如果在预览模式下，退出预览模式
          if (layoutPreviewMode.value) {
            exitLayoutPreview()
          }
        }, 'image/jpeg', 0.95)
        
      } catch (error) {
        console.error('生成排版失败:', error)
        ElMessage.error('生成排版失败: ' + error.message)
      } finally {
        layoutProcessing.value = false
      }
    }
    

    
    // 图片编辑相关方法
    const getTemplatesByCategory = (category) => {
      return templates.value.filter(template => template.category === category)
    }
    
    const scaleImage = (delta) => {
      const newScale = Math.max(0.1, Math.min(3, imageTransform.scale + delta))
      imageTransform.scale = Math.round(newScale * 10) / 10
      updateImageTransform()
    }
    
    const rotateImage = (delta) => {
      imageTransform.rotation = (imageTransform.rotation + delta) % 360
      updateImageTransform()
    }
    
    const moveImage = (deltaX, deltaY) => {
      imageTransform.translateX += deltaX
      imageTransform.translateY += deltaY
      updateImageTransform()
    }
    
    const resetPosition = () => {
      imageTransform.translateX = 0
      imageTransform.translateY = 0
      updateImageTransform()
      ElMessage.info('图片已居中')
    }
    
    const fitToTemplate = () => {
      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择证件照规格')
        return
      }
      
      // 计算合适的缩放比例
      const targetRatio = selectedTemplate.value.width / selectedTemplate.value.height
      const imageRatio = imageWidth.value / imageHeight.value
      
      if (imageRatio > targetRatio) {
        // 图片较宽，以高度为准
        imageTransform.scale = selectedTemplate.value.height / imageHeight.value
      } else {
        // 图片较高，以宽度为准
        imageTransform.scale = selectedTemplate.value.width / imageWidth.value
      }
      
      imageTransform.scale = Math.round(imageTransform.scale * 10) / 10
      resetPosition()
      ElMessage.success('已适应规格尺寸')
    }
    
    const resetTransform = () => {
      imageTransform.scale = 1
      imageTransform.rotation = 0
      imageTransform.translateX = 0
      imageTransform.translateY = 0
      updateImageTransform()
      ElMessage.info('变换已重置')
    }
    
    const updateImageTransform = () => {
      // 更新图片变换，触发重新处理
      debounceProcess()
    }
    
    // 裁剪功能
    const toggleCropMode = async () => {
      if (!originalImage.value) {
        ElMessage.warning('请先上传图片')
        return
      }

      if (!cropMode.value) {
        // 启用裁剪模式
        cropMode.value = true
        initCropRect()
        ElMessage.info('裁剪模式已启用，拖拽裁剪框调整位置和大小')
      } else {
        // 执行裁剪处理
        await performCrop()
        cropMode.value = false
      }
    }

    // 初始化裁剪框
    const initCropRect = () => {
      if (!previewImage.value) {
        console.warn('预览图片未加载，延迟初始化裁剪框')
        // 如果图片还没加载完成，等待一下再初始化
        setTimeout(initCropRect, 100)
        return
      }
      
      const img = previewImage.value
      
      // 等待图片完全加载
      if (!img.complete) {
        console.warn('图片还在加载中，延迟初始化裁剪框')
        setTimeout(initCropRect, 100)
        return
      }
      
      const displayWidth = img.offsetWidth || img.clientWidth
      const displayHeight = img.offsetHeight || img.clientHeight
      
      if (!displayWidth || !displayHeight) {
        console.warn('无法获取图片显示尺寸，延迟初始化')
        setTimeout(initCropRect, 100)
        return
      }
      
      console.log('=== 初始化裁剪框 ===')
      console.log('当前显示图片:', currentDisplayImage.value ? '处理后图片' : '原始图片')
      console.log('图片显示尺寸:', displayWidth, 'x', displayHeight)
      console.log('图片自然尺寸:', img.naturalWidth, 'x', img.naturalHeight)
      
      // 根据选中的模板计算裁剪框尺寸
      let cropWidth = 200
      let cropHeight = 250
      
      if (selectedTemplate.value) {
        const templateRatio = selectedTemplate.value.width_mm / selectedTemplate.value.height_mm
        cropWidth = Math.min(displayWidth * 0.6, Math.min(displayWidth - 40, 400))
        cropHeight = cropWidth / templateRatio
        
        // 确保裁剪框不超出显示区域
        if (cropHeight > displayHeight * 0.8) {
          cropHeight = displayHeight * 0.8
          cropWidth = cropHeight * templateRatio
        }
        
        // 再次检查宽度
        if (cropWidth > displayWidth * 0.9) {
          cropWidth = displayWidth * 0.9
          cropHeight = cropWidth / templateRatio
        }
      }
      
      // 确保最小尺寸
      cropWidth = Math.max(50, cropWidth)
      cropHeight = Math.max(50, cropHeight)
      
      // 居中放置裁剪框
      cropRect.x = Math.max(0, (displayWidth - cropWidth) / 2)
      cropRect.y = Math.max(0, (displayHeight - cropHeight) / 2)
      cropRect.width = cropWidth
      cropRect.height = cropHeight
      
      console.log('裁剪框初始化完成:', {
        x: cropRect.x,
        y: cropRect.y,
        width: cropRect.width,
        height: cropRect.height,
        template: selectedTemplate.value?.name,
        aspectRatio: selectedTemplate.value ? selectedTemplate.value.width_mm / selectedTemplate.value.height_mm : 1
      })
      console.log('=== 裁剪框初始化完成 ===')
    }

    // 开始拖拽裁剪框
    const startDragBox = (event) => {
      event.preventDefault()
      cropState.isDragging = true
      cropState.startX = event.clientX
      cropState.startY = event.clientY
      cropState.startRect = { ...cropRect }
      
      document.addEventListener('mousemove', dragBox)
      document.addEventListener('mouseup', stopDragBox)
    }

    // 拖拽裁剪框
    const dragBox = (event) => {
      if (!cropState.isDragging) return
      
      const deltaX = event.clientX - cropState.startX
      const deltaY = event.clientY - cropState.startY
      
      let newX = cropState.startRect.x + deltaX
      let newY = cropState.startRect.y + deltaY
      
      // 限制在图片范围内
      const img = previewImage.value
      if (img) {
        newX = Math.max(0, Math.min(newX, img.offsetWidth - cropRect.width))
        newY = Math.max(0, Math.min(newY, img.offsetHeight - cropRect.height))
      }
      
      cropRect.x = newX
      cropRect.y = newY
    }

    // 停止拖拽裁剪框
    const stopDragBox = () => {
      cropState.isDragging = false
      document.removeEventListener('mousemove', dragBox)
      document.removeEventListener('mouseup', stopDragBox)
    }

    // 开始调整大小
    const startResize = (direction, event) => {
      event.preventDefault()
      cropState.isResizing = true
      cropState.resizeDirection = direction
      cropState.startX = event.clientX
      cropState.startY = event.clientY
      cropState.startRect = { ...cropRect }
      
      document.addEventListener('mousemove', resizeBox)
      document.addEventListener('mouseup', stopResize)
    }

    // 调整大小
    const resizeBox = (event) => {
      if (!cropState.isResizing) return
      
      const deltaX = event.clientX - cropState.startX
      const deltaY = event.clientY - cropState.startY
      const direction = cropState.resizeDirection
      const img = previewImage.value
      
      if (!img) return
      
      let newX = cropState.startRect.x
      let newY = cropState.startRect.y
      let newWidth = cropState.startRect.width
      let newHeight = cropState.startRect.height
      
      // 获取证件照模板的宽高比
      let aspectRatio = 1
      if (selectedTemplate.value) {
        aspectRatio = selectedTemplate.value.width_mm / selectedTemplate.value.height_mm
      }
      
      // 根据方向调整大小，保持宽高比
      if (direction.includes('se') || direction.includes('nw')) {
        // 右下角或左上角：使用对角线调整，保持比例
        if (direction.includes('se')) {
          newWidth = cropState.startRect.width + deltaX
          newHeight = newWidth / aspectRatio
        } else { // nw
          newWidth = cropState.startRect.width - deltaX
          newHeight = newWidth / aspectRatio
          newX = cropState.startRect.x + deltaX
          newY = cropState.startRect.y + (cropState.startRect.height - newHeight)
        }
      } else if (direction.includes('ne') || direction.includes('sw')) {
        // 右上角或左下角：使用对角线调整，保持比例
        if (direction.includes('ne')) {
          newWidth = cropState.startRect.width + deltaX
          newHeight = newWidth / aspectRatio
          newY = cropState.startRect.y + (cropState.startRect.height - newHeight)
        } else { // sw
          newWidth = cropState.startRect.width - deltaX
          newHeight = newWidth / aspectRatio
          newX = cropState.startRect.x + deltaX
        }
      } else if (direction.includes('e') || direction.includes('w')) {
        // 左右边：按宽度调整，高度按比例
        if (direction.includes('e')) {
          newWidth = cropState.startRect.width + deltaX
        } else {
          newWidth = cropState.startRect.width - deltaX
          newX = cropState.startRect.x + deltaX
        }
        newHeight = newWidth / aspectRatio
        // 保持垂直居中
        newY = cropState.startRect.y + (cropState.startRect.height - newHeight) / 2
      } else if (direction.includes('n') || direction.includes('s')) {
        // 上下边：按高度调整，宽度按比例
        if (direction.includes('s')) {
          newHeight = cropState.startRect.height + deltaY
        } else {
          newHeight = cropState.startRect.height - deltaY
          newY = cropState.startRect.y + deltaY
        }
        newWidth = newHeight * aspectRatio
        // 保持水平居中
        newX = cropState.startRect.x + (cropState.startRect.width - newWidth) / 2
      }
      
      // 最小尺寸限制
      const minSize = 50
      if (newWidth < minSize || newHeight < minSize) return
      
      // 边界限制
      if (newX < 0) {
        const offset = -newX
        newX = 0
        newWidth -= offset
        newHeight = newWidth / aspectRatio
      }
      if (newY < 0) {
        const offset = -newY
        newY = 0
        newHeight -= offset
        newWidth = newHeight * aspectRatio
      }
      if (newX + newWidth > img.offsetWidth) {
        newWidth = img.offsetWidth - newX
        newHeight = newWidth / aspectRatio
      }
      if (newY + newHeight > img.offsetHeight) {
        newHeight = img.offsetHeight - newY
        newWidth = newHeight * aspectRatio
      }
      
      // 再次边界检查，确保调整后的尺寸不超出边界
      if (newX + newWidth > img.offsetWidth || newY + newHeight > img.offsetHeight) {
        return // 不更新，保持当前状态
      }
      
      cropRect.x = newX
      cropRect.y = newY
      cropRect.width = newWidth
      cropRect.height = newHeight
    }

    // 停止调整大小
    const stopResize = () => {
      cropState.isResizing = false
      document.removeEventListener('mousemove', resizeBox)
      document.removeEventListener('mouseup', stopResize)
    }

    const performCrop = async () => {
      if (!stepImages.step3 && !stepImages.step2 && !stepImages.step1 && !originalImage.value) {
        ElMessage.warning('没有可裁剪的图片')
        return
      }
      if (!selectedTemplate.value) {
        ElMessage.warning('请选择模板规格后再进行裁剪')
        return
      }

      processing.value = true
      try {
        const img = previewImage.value
        if (!img) throw new Error('无法获取图片信息')
        
        // 等待图片完全加载后再计算坐标
        await new Promise((resolve) => {
          if (img.complete) {
            resolve()
          } else {
            img.onload = resolve
          }
        })
        
        // 获取图片容器和图片的详细信息
        const imgContainer = img.parentElement
        const containerRect = imgContainer.getBoundingClientRect()
        
        // 获取图片的实际尺寸信息
        const naturalWidth = img.naturalWidth
        const naturalHeight = img.naturalHeight
        
        // 计算图片在容器中的实际显示区域
        // 考虑到CSS的object-fit: contain效果
        const containerAspect = containerRect.width / containerRect.height
        const imageAspect = naturalWidth / naturalHeight
        
        let actualDisplayWidth, actualDisplayHeight
        let offsetX = 0, offsetY = 0
        
        if (imageAspect > containerAspect) {
          // 图片宽高比大于容器，以宽度为准
          actualDisplayWidth = containerRect.width
          actualDisplayHeight = containerRect.width / imageAspect
          offsetY = (containerRect.height - actualDisplayHeight) / 2
        } else {
          // 图片宽高比小于容器，以高度为准  
          actualDisplayHeight = containerRect.height
          actualDisplayWidth = containerRect.height * imageAspect
          offsetX = (containerRect.width - actualDisplayWidth) / 2
        }
        
        // 计算从显示坐标到原图坐标的缩放比例
        const scaleX = naturalWidth / actualDisplayWidth
        const scaleY = naturalHeight / actualDisplayHeight
        
        console.log('=== 手动裁剪坐标计算 ===')
        console.log('容器尺寸:', containerRect.width, 'x', containerRect.height)
        console.log('图片原始尺寸:', naturalWidth, 'x', naturalHeight)
        console.log('图片实际显示尺寸:', actualDisplayWidth, 'x', actualDisplayHeight)
        console.log('显示偏移量:', offsetX, ',', offsetY)
        console.log('缩放比例:', scaleX, ',', scaleY)
        console.log('裁剪框相对容器坐标:', cropRect)
        
        // 将裁剪框坐标转换为相对于实际图片显示区域的坐标
        const relativeX = cropRect.x - offsetX
        const relativeY = cropRect.y - offsetY
        
        // 转换为原图坐标
        const cropData = {
          x: Math.max(0, Math.round(relativeX * scaleX)),
          y: Math.max(0, Math.round(relativeY * scaleY)),
          width: Math.max(1, Math.round(cropRect.width * scaleX)),
          height: Math.max(1, Math.round(cropRect.height * scaleY))
        }
        
        // 边界检查和修正
        cropData.x = Math.min(Math.max(0, cropData.x), naturalWidth - 1)
        cropData.y = Math.min(Math.max(0, cropData.y), naturalHeight - 1)
        
        // 确保裁剪区域不超出边界
        const maxWidth = naturalWidth - cropData.x
        const maxHeight = naturalHeight - cropData.y
        cropData.width = Math.min(cropData.width, maxWidth)
        cropData.height = Math.min(cropData.height, maxHeight)
        
        // 最终验证
        if (cropData.width < 1) cropData.width = 1
        if (cropData.height < 1) cropData.height = 1
        
        console.log('相对图片坐标:', relativeX, ',', relativeY)
        console.log('最终裁剪坐标:', cropData)
        console.log('=== 坐标计算完成 ===')

        // 使用最新处理的图片进行裁剪
        const baseImage = stepImages.step3 || stepImages.step2 || stepImages.step1 || originalImage.value
        let baseImageSource = stepImages.step3 ? '步骤3的图片（规格调整后）' : 
                             (stepImages.step2 ? '步骤2的图片（美颜后）' : 
                             (stepImages.step1 ? '步骤1的图片（背景处理后）' : '原始图片'))
        console.log('裁剪处理：使用基础图片', baseImageSource)
        
        // 调用纯裁剪API，传递模板信息进行最终尺寸调整
        const result = await api.idPhoto.pureCrop({
          image_data: baseImage,
          crop: cropData,
          template: selectedTemplate.value  // 传递模板信息，用于最终尺寸调整
        })

        if (result.data.success) {
          processedImage.value = result.data.processed_image
          // 自动保存裁剪后的图片到步骤4
          stepImages.step4 = result.data.processed_image
          console.log('手动裁剪完成，已自动保存到 stepImages.step4')
          ElMessage.success('手动裁剪完成')
          
          // 输出调试信息
          if (result.data.debug_info) {
            console.log('后端处理信息:', result.data.debug_info)
          }
        } else {
          throw new Error(result.data.message || '裁剪处理失败')
        }
      } catch (error) {
        console.error('裁剪处理失败:', error)
        ElMessage.error('裁剪处理失败: ' + (error.response?.data?.message || error.message))
      } finally {
        processing.value = false
      }
    }
    
    const autoCrop = async () => {
      if (!originalImage.value) {
        ElMessage.warning('请先上传图片')
        return
      }

      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择证件照规格')
        return
      }
      
      processing.value = true
      
      try {
        // 显示详细的处理提示
        ElMessage.info('正在进行智能裁剪，请稍候...')
        
        const result = await api.idPhoto.process({
          image_data: originalImage.value,
          template: selectedTemplate.value,
          background: selectedBackground.value,
          beauty: beautySettings,
          size: customSize,
          transform: imageTransform,
          auto_corrections: {
            smart_crop: true,                           // 启用智能裁剪
            auto_level: autoOptions.autoLevel,         // 自动色阶调整
            center_face: autoOptions.faceDetection,    // 人脸居中
            auto_rotate: autoOptions.autoRotate,       // 自动旋转纠正
            enhance_quality: autoOptions.enhanceQuality, // 增强图片质量
            remove_background: autoOptions.removeBackground, // 智能背景移除
            face_detection: autoOptions.faceDetection, // 人脸检测优化
            detection_level: autoOptions.detectionLevel // 检测精度级别
          }
        })
        
        if (result.data.success) {
          processedImage.value = result.data.processed_image
          // 自动保存智能裁剪后的图片到步骤4
          stepImages.step4 = result.data.processed_image
          console.log('智能裁剪完成，已自动保存到 stepImages.step4')
          ElMessage.success('智能裁剪完成！已自动优化人脸位置和图片质量')
          
          // 智能裁剪完成后，如果是裁剪模式，重新初始化裁剪框
          if (cropMode.value) {
            nextTick(() => {
              console.log('智能裁剪完成，重新初始化裁剪框')
              initCropRect()
            })
          }
          
          // 如果返回了裁剪信息，显示给用户
          if (result.data.crop_info) {
            console.log('裁剪信息:', result.data.crop_info)
          }
        } else {
          throw new Error(result.data.message || '智能裁剪失败')
        }
      } catch (error) {
        console.error('智能裁剪失败:', error)
        
        // 提供更详细的错误信息
        let errorMessage = '智能裁剪失败'
        if (error.response?.data?.message) {
          errorMessage = error.response.data.message
        } else if (error.message) {
          errorMessage = error.message
        }
        
        ElMessage.error(errorMessage)
        
        // 如果智能裁剪失败，可以提供备选方案
        setTimeout(() => {
          ElMessage.info('您可以尝试手动调整图片位置后再次处理')
        }, 2000)
      } finally {
        processing.value = false
      }
    }

    // 添加批量自动裁剪功能
    const batchAutoCrop = async (images) => {
      if (!images || images.length === 0) {
        ElMessage.warning('没有图片需要处理')
        return
      }

      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择证件照规格')
        return
      }

      processing.value = true
      const results = []

      try {
        ElMessage.info(`开始批量处理 ${images.length} 张图片...`)

        for (let i = 0; i < images.length; i++) {
          const image = images[i]
          
          try {
            const result = await api.idPhoto.process({
              image_data: image.data,
              template: selectedTemplate.value,
              background: selectedBackground.value,
              beauty: beautySettings,
              size: customSize,
              auto_corrections: {
                smart_crop: true,
                auto_level: true,
                center_face: true,
                auto_rotate: true,
                enhance_quality: true,
                remove_background: true,
                face_detection: true
              }
            })

            if (result.data.success) {
              results.push({
                original: image,
                processed: result.data.processed_image,
                success: true
              })
            } else {
              results.push({
                original: image,
                error: result.data.message,
                success: false
              })
            }

            // 更新进度
            ElMessage.info(`已处理 ${i + 1}/${images.length} 张图片`)
          } catch (error) {
            results.push({
              original: image,
              error: error.message,
              success: false
            })
          }
        }

        const successCount = results.filter(r => r.success).length
        ElMessage.success(`批量处理完成！成功处理 ${successCount}/${images.length} 张图片`)

        return results
      } catch (error) {
        ElMessage.error('批量处理失败: ' + error.message)
        return []
      } finally {
        processing.value = false
      }
    }

    // 预览裁剪区域功能
    const previewCropArea = async () => {
      if (!originalImage.value) {
        ElMessage.warning('请先上传图片')
        return
      }

      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择证件照规格')
        return
      }

      try {
        // 调用API获取建议的裁剪区域
        const result = await api.idPhoto.detectCropArea({
          image_data: originalImage.value,
          template: selectedTemplate.value
        })

        if (result.data.success && result.data.crop_area) {
          // 显示裁剪预览
          ElMessage.success('已检测到最佳裁剪区域')
          return result.data.crop_area
        } else {
          ElMessage.warning('无法检测到合适的裁剪区域')
          return null
        }
      } catch (error) {
        console.error('裁剪区域检测失败:', error)
        ElMessage.error('裁剪区域检测失败')
        return null
      }
    }

    // 重置自动裁剪选项
    const resetAutoOptions = () => {
      autoOptions.faceDetection = true
      autoOptions.autoRotate = true
      autoOptions.enhanceQuality = true
      autoOptions.removeBackground = true
      autoOptions.autoLevel = true
      autoOptions.detectionLevel = 'normal'
      ElMessage.success('设置已重置为默认值')
    }

    // 保存自动裁剪预设
    const saveAutoPreset = () => {
      try {
        const preset = {
          ...autoOptions,
          timestamp: new Date().toISOString(),
          templateId: selectedTemplate.value?.id
        }
        localStorage.setItem('autoCropPreset', JSON.stringify(preset))
        ElMessage.success('预设已保存')
      } catch (error) {
        ElMessage.error('保存预设失败')
      }
    }

    // 加载自动裁剪预设
    const loadAutoPreset = () => {
      try {
        const saved = localStorage.getItem('autoCropPreset')
        if (saved) {
          const preset = JSON.parse(saved)
          Object.assign(autoOptions, preset)
          ElMessage.success('预设已加载')
          return true
        }
        return false
      } catch (error) {
        ElMessage.error('加载预设失败')
        return false
      }
    }

    // 一键优化功能
    const oneClickOptimize = async () => {
      if (!originalImage.value) {
        ElMessage.warning('请先上传图片')
        return
      }

      if (!selectedTemplate.value) {
        ElMessage.warning('请先选择证件照规格')
        return
      }
      
      processing.value = true

      try {
        ElMessage.info('正在进行一键优化...')
        
        // 执行智能裁剪
        const result = await api.idPhoto.process({
          image_data: originalImage.value,
          template: selectedTemplate.value,
          background: selectedBackground.value,
          beauty: beautySettings,
          size: customSize,
          transform: imageTransform,
          auto_corrections: {
            smart_crop: true,
            auto_level: autoOptions.autoLevel,
            center_face: autoOptions.faceDetection,
            auto_rotate: autoOptions.autoRotate,
            enhance_quality: autoOptions.enhanceQuality,
            remove_background: autoOptions.removeBackground,
            face_detection: autoOptions.faceDetection,
            detection_level: autoOptions.detectionLevel,
            one_click_optimize: true  // 标记为一键优化
          }
        })
        
        if (result.data.success) {
          processedImage.value = result.data.processed_image
          ElMessage.success('一键优化完成！图片已自动调整到最佳状态')
          
          // 一键优化完成后，如果是裁剪模式，重新初始化裁剪框
          if (cropMode.value) {
            nextTick(() => {
              console.log('一键优化完成，重新初始化裁剪框')
              initCropRect()
            })
          }
        } else {
          throw new Error(result.data.message || '一键优化失败')
        }
      } catch (error) {
        console.error('一键优化失败:', error)
        ElMessage.error('一键优化失败: ' + (error.response?.data?.message || error.message))
      } finally {
        processing.value = false
      }
    }

        // 测试裁剪功能（仅裁剪，不做其他处理）
    const testCropOnly = async () => {
      if (!originalImage.value) {
        ElMessage.warning('请先上传图片')
        return
      }

      processing.value = true
      try {
        const img = previewImage.value
        if (!img) throw new Error('无法获取图片信息')
        
        // 等待图片完全加载后再计算坐标
        await new Promise((resolve) => {
          if (img.complete) {
            resolve()
          } else {
            img.onload = resolve
          }
        })
        
        // 获取图片容器和图片的详细信息  
        const imgContainer = img.parentElement
        const containerRect = imgContainer.getBoundingClientRect()
        
        // 获取图片的实际尺寸信息
        const naturalWidth = img.naturalWidth
        const naturalHeight = img.naturalHeight
        
        // 计算图片在容器中的实际显示区域
        const containerAspect = containerRect.width / containerRect.height
        const imageAspect = naturalWidth / naturalHeight
        
        let actualDisplayWidth, actualDisplayHeight
        let offsetX = 0, offsetY = 0
        
        if (imageAspect > containerAspect) {
          actualDisplayWidth = containerRect.width
          actualDisplayHeight = containerRect.width / imageAspect
          offsetY = (containerRect.height - actualDisplayHeight) / 2
        } else {
          actualDisplayHeight = containerRect.height
          actualDisplayWidth = containerRect.height * imageAspect
          offsetX = (containerRect.width - actualDisplayWidth) / 2
        }
        
        const scaleX = naturalWidth / actualDisplayWidth
        const scaleY = naturalHeight / actualDisplayHeight
        
        console.log('=== 测试裁剪坐标计算 ===')
        console.log('容器尺寸:', containerRect.width, 'x', containerRect.height)
        console.log('图片原始尺寸:', naturalWidth, 'x', naturalHeight)
        console.log('图片实际显示尺寸:', actualDisplayWidth, 'x', actualDisplayHeight)
        console.log('显示偏移量:', offsetX, ',', offsetY)
        console.log('缩放比例:', scaleX, ',', scaleY)
        console.log('裁剪框相对容器坐标:', cropRect)
        
        // 将裁剪框坐标转换为相对于实际图片显示区域的坐标
        const relativeX = cropRect.x - offsetX
        const relativeY = cropRect.y - offsetY
        
        // 转换为原图坐标
        const cropData = {
          x: Math.max(0, Math.round(relativeX * scaleX)),
          y: Math.max(0, Math.round(relativeY * scaleY)),
          width: Math.max(1, Math.round(cropRect.width * scaleX)),
          height: Math.max(1, Math.round(cropRect.height * scaleY))
        }
        
        // 边界检查和修正
        cropData.x = Math.min(Math.max(0, cropData.x), naturalWidth - 1)
        cropData.y = Math.min(Math.max(0, cropData.y), naturalHeight - 1)
        
        const maxWidth = naturalWidth - cropData.x
        const maxHeight = naturalHeight - cropData.y
        cropData.width = Math.min(cropData.width, maxWidth)
        cropData.height = Math.min(cropData.height, maxHeight)
        
        if (cropData.width < 1) cropData.width = 1
        if (cropData.height < 1) cropData.height = 1
        
        console.log('相对图片坐标:', relativeX, ',', relativeY)
        console.log('最终裁剪坐标:', cropData)
        console.log('=== 测试坐标计算完成 ===')

        // 使用新的纯裁剪API
        const response = await fetch('/api/processing/pure-crop', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            image_data: originalImage.value,
            crop_params: cropData
          })
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success) {
            processedImage.value = result.processed_image
            ElMessage.success('测试裁剪完成！这是纯裁剪结果')
            console.log('裁剪信息:', result.crop_info)
          } else {
            throw new Error(result.error || '测试裁剪失败')
          }
        } else {
          const errorData = await response.json()
          throw new Error(errorData.error || '测试裁剪失败')
        }
      } catch (error) {
        console.error('测试裁剪失败:', error)
        ElMessage.error('测试裁剪失败: ' + error.message)
      } finally {
        processing.value = false
      }
    }

    // 调试坐标功能
    const debugCropCoords = () => {
      if (!previewImage.value) {
        ElMessage.warning('请先上传图片')
        return
      }

      const img = previewImage.value
      const imgContainer = img.parentElement
      const containerRect = imgContainer.getBoundingClientRect()
      
      const naturalWidth = img.naturalWidth
      const naturalHeight = img.naturalHeight
      
      const containerAspect = containerRect.width / containerRect.height
      const imageAspect = naturalWidth / naturalHeight
      
      let actualDisplayWidth, actualDisplayHeight
      let offsetX = 0, offsetY = 0
      
      if (imageAspect > containerAspect) {
        actualDisplayWidth = containerRect.width
        actualDisplayHeight = containerRect.width / imageAspect
        offsetY = (containerRect.height - actualDisplayHeight) / 2
      } else {
        actualDisplayHeight = containerRect.height
        actualDisplayWidth = containerRect.height * imageAspect
        offsetX = (containerRect.width - actualDisplayWidth) / 2
      }
      
      const scaleX = naturalWidth / actualDisplayWidth
      const scaleY = naturalHeight / actualDisplayHeight
      
      const relativeX = cropRect.x - offsetX
      const relativeY = cropRect.y - offsetY
      
      const cropData = {
        x: Math.max(0, Math.round(relativeX * scaleX)),
        y: Math.max(0, Math.round(relativeY * scaleY)),
        width: Math.max(1, Math.round(cropRect.width * scaleX)),
        height: Math.max(1, Math.round(cropRect.height * scaleY))
      }

      const debugInfo = `
🖼️ 图片信息:
  - 原始尺寸: ${naturalWidth} × ${naturalHeight}
  - 容器尺寸: ${Math.round(containerRect.width)} × ${Math.round(containerRect.height)}
  - 实际显示: ${Math.round(actualDisplayWidth)} × ${Math.round(actualDisplayHeight)}
  - 显示偏移: (${Math.round(offsetX)}, ${Math.round(offsetY)})

📐 裁剪框信息:
  - 容器坐标: (${Math.round(cropRect.x)}, ${Math.round(cropRect.y)})
  - 容器尺寸: ${Math.round(cropRect.width)} × ${Math.round(cropRect.height)}
  - 相对坐标: (${Math.round(relativeX)}, ${Math.round(relativeY)})
  - 原图坐标: (${cropData.x}, ${cropData.y})
  - 原图尺寸: ${cropData.width} × ${cropData.height}

⚙️ 计算比例:
  - X轴缩放: ${scaleX.toFixed(3)}
  - Y轴缩放: ${scaleY.toFixed(3)}
      `

      ElMessageBox.alert(debugInfo, '坐标调试信息', {
        confirmButtonText: '确定',
        type: 'info'
      })

      console.log('=== 调试坐标信息 ===')
      console.log(debugInfo)
    }
    
    // 工作流控制方法 - 新顺序：背景→美颜→规格→裁剪
    const completeStep = async (step) => {
      try {
        processing.value = true
        
        switch(step) {
          case 1: // 完成背景设置
            await processBackground()
            if (processedImage.value) {
              stepImages.step1 = processedImage.value
              console.log('步骤1完成：已保存背景处理后的图片')
              processedImage.value = null // 清除临时处理结果
            } else {
              throw new Error('背景处理未产生有效结果')
            }
            currentStep.value = 2
            // 自动展开下一步
            Object.keys(expandedSections).forEach(key => {
              expandedSections[key] = false
            })
            expandedSections.beauty = true
            ElMessage.success('背景设置完成，进入美颜优化')
            break
            
          case 2: // 完成美颜
            await processBeauty()
            if (processedImage.value) {
              stepImages.step2 = processedImage.value
              console.log('步骤2完成：已保存美颜后的图片')
              processedImage.value = null // 清除临时处理结果
            } else {
              throw new Error('美颜操作未产生有效结果')
            }
            currentStep.value = 3
            Object.keys(expandedSections).forEach(key => {
              expandedSections[key] = false
            })
            expandedSections.template = true
            ElMessage.success('美颜完成，选择证件规格')
            break
            
          case 3: // 完成规格设置
            await processFinalSize()
            if (processedImage.value) {
              stepImages.step3 = processedImage.value
              console.log('步骤3完成：已保存规格处理后的图片')
              processedImage.value = null // 清除临时处理结果
            } else {
              throw new Error('规格处理未产生有效结果')
            }
            currentStep.value = 4
            Object.keys(expandedSections).forEach(key => {
              expandedSections[key] = false
            })
            expandedSections.crop = true
            ElMessage.success('规格设置完成，进行最终裁剪')
            break
            
          case 4: { // 完成裁剪
            processing.value = true
            try {
              if (cropMode.value) {
                await performCrop()
                cropMode.value = false
              }

              // 检查是否有最终可保存的图片
              const finalImageToSave = stepImages.step4 || (cropMode.value ? processedImage.value : null)
              
              if (finalImageToSave) {
                if (!cropMode.value) {
                  stepImages.step4 = finalImageToSave
                }
                console.log('步骤4完成：准备保存最终处理的图片')
                
                // 等待后台保存完成
                const response = await api.images.saveProcessed({ image_data: finalImageToSave })
                
                if (response.data.success) {
                  console.log('处理完成的图片已成功同步到服务器:', response.data.image)
                  
                  // 只有在保存成功后才进入下一步
                  currentStep.value = 5
                  Object.keys(expandedSections).forEach(key => {
                    expandedSections[key] = false
                  })
                  expandedSections.layout = true
                  ElMessage.success('裁剪完成，进入一键排版')
                } else {
                  // 如果后端明确返回失败，则抛出错误
                  throw new Error(response.data.message || '处理结果后台保存失败')
                }
              } else {
                // 如果没有需要保存的图片（例如，用户直接跳过裁剪）
                if (!stepImages.step4) {
                  stepImages.step4 = stepImages.step3 || currentDisplayImage.value
                  console.log('步骤4完成：用户跳过裁剪，使用上一步的图片')
                } else {
                  console.log('步骤4完成：检测到已存在的智能裁剪结果，直接进入下一步')
                }
                
                // 直接进入下一步
                currentStep.value = 5
                Object.keys(expandedSections).forEach(key => {
                  expandedSections[key] = false
                })
                expandedSections.layout = true
                ElMessage.success('裁剪完成，进入一键排版')
              }
            } catch (error) {
              console.error('完成裁剪步骤失败:', error)
              ElMessage.error('无法保存裁剪结果: ' + (error.response?.data?.message || error.message))
            } finally {
              processing.value = false
            }
            break
          }
            
          case 5: // 完成排版
            ElMessage.success('证件照制作完成！')
            // 收起本级菜单
            expandedSections.layout = false
            // 将步骤推进到"完成"状态
            currentStep.value = 6
            console.log('完成制作，已收起排版菜单，步骤状态更新为已完成')
            break
        }
      } catch (error) {
        ElMessage.error('处理失败: ' + error.message)
      } finally {
        processing.value = false
      }
    }
    
    const processBeauty = async () => {
      if (!stepImages.step1 && !originalImage.value) {
        throw new Error('没有可用的基础图片进行美颜处理')
      }
      
      const baseImage = stepImages.step1 || originalImage.value
      console.log('美颜处理：使用基础图片', stepImages.step1 ? '步骤1的图片（背景处理后）' : '原始图片')
      
      try {
        processing.value = true
        
        // 调用美颜处理API
        const result = await api.idPhoto.processBeauty({
          image_data: baseImage,
          beauty: beautySettings
        })
        
        if (result.data.success) {
          processedImage.value = result.data.processed_image
          console.log('美颜处理成功，已更新processedImage')
        } else {
          throw new Error(result.data.message || '美颜处理失败')
        }
      } catch (error) {
        console.error('美颜处理失败:', error)
        ElMessage.error('美颜处理失败: ' + (error.response?.data?.message || error.message))
        throw error
      } finally {
        processing.value = false
      }
    }
    
    const processBackground = async () => {
      if (!originalImage.value) {
        throw new Error('没有可用的原始图片进行背景处理')
      }
      
      const baseImage = originalImage.value
      console.log('背景处理：使用原始图片')
      
      // 调用背景处理API
      const result = await api.idPhoto.processBackground({
        image_data: baseImage,
        background: selectedBackground.value
      })
      
      if (result.data.success) {
        processedImage.value = result.data.processed_image
        console.log('背景处理成功，已更新processedImage')
      } else {
        throw new Error(result.data.message || '背景处理失败')
      }
    }
    
    const processFinalSize = async () => {
      if (!stepImages.step2 && !stepImages.step1 && !originalImage.value) {
        throw new Error('没有可用的基础图片进行规格处理')
      }
      if (!selectedTemplate.value) {
        throw new Error('请先选择证件照规格')
      }
      
      const baseImage = stepImages.step2 || stepImages.step1 || originalImage.value
      let baseImageSource = stepImages.step2 ? '步骤2的图片（美颜后）' : (stepImages.step1 ? '步骤1的图片（背景处理后）' : '原始图片')
      console.log('规格处理：使用基础图片', baseImageSource)
      
      // 调用最终尺寸处理API
      const result = await api.idPhoto.processFinalSize({
        image_data: baseImage,
        template: selectedTemplate.value
      })
      
      if (result.data.success) {
        processedImage.value = result.data.processed_image
        console.log('规格处理成功，已更新processedImage')
      } else {
        throw new Error(result.data.message || '规格处理失败')
      }
    }
    
    const applyBeautyAndContinue = async () => {
      await processBeauty()
      completeStep(2)
    }
    
    const toggleTemplateCategory = (category) => {
      expandedCategories[category] = !expandedCategories[category]
    }
    
    const resetWorkflow = () => {
      ElMessageBox.confirm(
        '确定要重新开始制作吗？这将清除所有步骤的进度。',
        '确认重新制作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        resetAllSteps()
        ElMessage.success('已重置所有步骤，可以重新开始制作了！')
      }).catch(() => {
        ElMessage.info('已取消重新制作')
      })
    }
    
    const resetAllSteps = () => {
      currentStep.value = 1
      Object.keys(stepImages).forEach(key => {
        stepImages[key] = null
      })
      processedImage.value = null
      layoutInfo.value = null
      
      // Reset expanded sections to the initial state
      Object.keys(expandedSections).forEach(key => {
        expandedSections[key] = false
      })
      expandedSections.background = true

      console.log('所有步骤已重置')
    }
    
    const finalizeImage = async () => {
      if (!processedImage.value) {
        ElMessage.warning('请先完成所有制作步骤')
        return
      }
      
      try {
        // 可以在这里添加最终的图片优化处理
        ElMessage.success('证件照制作完成！可以保存图片了')
        // 自动触发下载
        downloadPhoto()
      } catch (error) {
        ElMessage.error('最终处理失败: ' + error.message)
      }
    }

    const effectivePrintableArea = computed(() => {
      if (!currentPaperSize.value) {
        return { width: 0, height: 0, margin: 0 }
      }
      const paper = currentPaperSize.value
      // 检查纸张名称是否包含"5寸"或"6寸"
      if (paper.name.includes('5寸') || paper.name.includes('6寸')) {
        console.log(`检测到 ${paper.name}，使用全幅尺寸进行排版`)
        return {
          width: paper.width_mm,
          height: paper.height_mm,
          margin: 0
        }
      } else {
        return {
          width: paper.printable_width_mm,
          height: paper.printable_height_mm,
          margin: paper.margin_mm
        }
      }
    })

    return {
      originalImage,
      processedImage,
      fileName,
      imageWidth,
      imageHeight,
      processing,
      fileInput,
      templates,
      templateCategories,
      backgroundColors,
      selectedTemplate,
      selectedBackground,
      customColor,
      showColorPicker,
      beautySettings,
      customSize,
      keepAspectRatio,
      imageTransform,
      expandedSections,
      autoOptions,
      // 工作流状态
      currentStep,
      stepImages,
      expandedCategories,
      // 裁剪相关
      cropMode,
      cropRect,
      cropState,
      imageContainer,
      previewImage,
      // 计算属性
      cropBoxStyle,
      maskTopStyle,
      maskBottomStyle,
      maskLeftStyle,
      maskRightStyle,
      currentDisplayImage,
      // 原有方法
      goBack,
      triggerFileInput,
      handleFileSelect,
      handleDrop,
      handleDragOver,
      handleDragEnter,
      handleDragLeave,
      onImageLoad,
      toggleSection,
      selectTemplate,
      selectBackground,
      onCustomColorChange,
      confirmCustomColor,
      debounceProcess,
      resetBeauty,
      applySizeChange,
      onAspectRatioChange,
      processImage,
      resetAll,
      downloadPhoto,
      getTemplatesByCategory,
      scaleImage,
      rotateImage,
      moveImage,
      resetPosition,
      fitToTemplate,
      resetTransform,
      updateImageTransform,
      // 裁剪方法
      toggleCropMode,
      initCropRect,
      startDragBox,
      dragBox,
      stopDragBox,
      startResize,
      resizeBox,
      stopResize,
      performCrop,
      autoCrop,
      batchAutoCrop,
      previewCropArea,
      resetAutoOptions,
      saveAutoPreset,
      loadAutoPreset,
      oneClickOptimize,
      testCropOnly,
      debugCropCoords,
      // 工作流方法
      completeStep,
      processBeauty,
      processBackground,
      processFinalSize,
      applyBeautyAndContinue,
      toggleTemplateCategory,
      resetWorkflow,
      finalizeImage,
      // 排版功能方法
      paperSizes,
      selectedPaperSize,
      currentPaperSize,
      layoutSpacing,
      layoutInfo,
      layoutProcessing,
      layoutPreviewMode,
      layoutPreviewImage,
      paperSizeGroups,
      finalProcessedImage,
      loadPaperSizes,
      onPaperSizeChange,
      calculateLayout,
      calculateOptimalLayout,
      formatSpacingTooltip,
      previewLayout,
      exitLayoutPreview,
      generateLayout,
      generateLayoutImage,
      // 手动排版相关
      customRows,
      customCols,
      useCustomLayout,
      maxRows,
      maxCols,
      calculateMaxRowsCols,
      updateCustomLayout,
      onLayoutModeChange,
      // 新增排版方法
      paperOrientation,
      paperSizeCategories,
      expandedPaperCategories,
      togglePaperCategory,
      getPaperSizesByCategory,
      selectPaperSize,
      onOrientationChange,
      effectivePrintableArea
    }
  }
}
</script>

<style scoped>
.photo-editor {
  min-height: 100vh;
  background: #f5f5f7;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 工具栏 */
.toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 24px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.btn-back, .btn-save {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  background: none;
  color: #007aff;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-back:hover, .btn-save:hover:not(:disabled) {
  background: rgba(0, 122, 255, 0.1);
}

.btn-save:disabled {
  color: #c7c7cc;
  cursor: not-allowed;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

/* 主要内容 */
.main-content {
  display: flex;
  gap: 24px;
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  align-items: flex-start;
}

/* 预览面板 */
.preview-panel {
  flex: 1;
  min-width: 600px;
  display: flex;
  flex-direction: column;
}

.preview-container {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

/* 上传区域 */
.upload-zone {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500px;
  border: 2px dashed #c7c7cc;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 20px;
}

.upload-zone:hover, .upload-zone.drag-over {
  border-color: #007aff;
  background: rgba(0, 122, 255, 0.05);
}

.upload-zone.drag-over {
  border-style: solid;
  transform: scale(1.02);
  box-shadow: 0 8px 30px rgba(0, 122, 255, 0.3);
}

.upload-icon {
  width: 80px;
  height: 80px;
  background: #f2f2f7;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  color: #8e8e93;
  transition: all 0.3s ease;
}

.upload-zone:hover .upload-icon, .upload-zone.drag-over .upload-icon {
  background: rgba(0, 122, 255, 0.1);
  color: #007aff;
}

.upload-zone h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0 0 8px 0;
}

.upload-zone p {
  color: #8e8e93;
  margin: 0;
}

.upload-hint {
  font-size: 14px;
  margin-top: 8px !important;
}

/* 图片预览 */
.image-preview {
  position: relative;
  text-align: center;
  padding: 24px;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.image-container {
  position: relative;
  max-width: 100%;
  max-height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image {
  max-width: 500px;       /* 最大宽度限制 */
  max-height: 600px;      /* 最大高度限制 */
  width: auto;            /* 自动宽度 */
  height: auto;           /* 自动高度 */
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  user-select: none;
  pointer-events: auto;
  object-fit: contain;    /* 保持图片比例 */
  display: block;         /* 确保居中显示 */
}

/* 简化的裁剪控制样式 */
.crop-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 裁剪系统样式 */
.crop-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  pointer-events: auto;
}

.crop-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.crop-box {
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.3), inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  cursor: move;
  user-select: none;
}

.crop-border {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #fff;
  pointer-events: none;
}

.crop-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.grid-line {
  position: absolute;
  background: rgba(255, 255, 255, 0.5);
}

.grid-line-v {
  width: 1px;
  height: 100%;
}

.grid-line-h {
  width: 100%;
  height: 1px;
}

.resize-handle {
  position: absolute;
  background: #fff;
  border: 1px solid #333;
  width: 8px;
  height: 8px;
  z-index: 12;
}

.resize-handle.nw {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.resize-handle.ne {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.resize-handle.sw {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.resize-handle.se {
  bottom: -4px;
  right: -4px;
  cursor: se-resize;
}

.resize-handle.n {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.resize-handle.s {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.resize-handle.w {
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  cursor: w-resize;
}

.resize-handle.e {
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
  cursor: e-resize;
}

.crop-tips {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 11;
  pointer-events: none;
}

.crop-tips p {
  margin: 2px 0;
}

.crop-coords-info {
  font-size: 11px;
  color: #007aff;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-top: 4px;
  font-family: monospace;
}

/* 裁剪提示样式 */
.crop-tips {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #007aff;
}

.tip-text {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.tip-text:last-child {
  margin-bottom: 0;
}

.tip-text .el-icon {
  color: #007aff;
  font-size: 14px;
  flex-shrink: 0;
}

/* 高级裁剪设置样式 */
.advanced-crop-settings {
  margin-top: 16px;
  border: 1px solid #e5e5ea;
  border-radius: 8px;
  overflow: hidden;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.settings-header:hover {
  background: #f0f1f2;
}

.settings-title {
  font-size: 13px;
  font-weight: 600;
  color: #1d1d1f;
}

.settings-content {
  padding: 16px;
  background: white;
}

.setting-group {
  margin-bottom: 16px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group-title {
  font-size: 12px;
  font-weight: 600;
  color: #666;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.checkbox-group .el-checkbox {
  margin-right: 0;
}

 .checkbox-group .el-checkbox__label {
   font-size: 13px;
   color: #333;
 }

 .setting-actions {
   display: flex;
   justify-content: space-between;
   gap: 8px;
   margin-top: 16px;
   padding-top: 16px;
   border-top: 1px solid #f0f0f0;
 }

 .setting-actions .el-button {
  flex: 1;
   min-width: 0;
   font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .preview-panel {
    min-width: unset;
  }
  
  .control-panel {
    width: 100%;
  }
  
  .image-container {
    max-height: 400px;
  }
  
  .preview-image {
    max-height: 400px;
  }
  
  
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .toolbar {
    padding: 0 16px;
  }
  
  .image-container {
    max-height: 300px;
  }
  
  .preview-image {
    max-height: 300px;
  }
  
  .upload-zone {
    height: 300px;
    margin: 16px;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f2f2f7;
  border-top: 3px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: #1d1d1f;
  font-weight: 500;
  margin: 0;
}

/* 图片信息 */
.image-info {
  padding: 16px 24px;
  background: #f9f9fb;
  border-top: 1px solid #e5e5ea;
}

/* 排版信息 */
.layout-info-summary {
  padding: 16px 24px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%);
  border-top: 1px solid rgba(102, 126, 234, 0.2);
  border-left: 3px solid #667eea;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #8e8e93;
  font-size: 14px;
}

.value {
  color: #1d1d1f;
  font-size: 14px;
  font-weight: 500;
}

.layout-info-summary .label {
  color: #667eea;
  font-weight: 500;
}

.layout-info-summary .value {
  color: #4c4c4c;
  font-weight: 600;
}

/* 控制面板 */
.control-panel {
  width: 360px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 工作流进度指示器 */
.workflow-progress {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  font-size: 12px;
}

.progress-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  transition: all 0.3s ease;
  opacity: 0.5;
}

.progress-item.active {
  opacity: 1;
  transform: scale(1.1);
}

.progress-item.completed {
  opacity: 0.8;
}

.progress-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e5e5ea;
  color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
  transition: all 0.3s ease;
}

.progress-item.active .progress-number {
  background: #007aff;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.progress-item.completed .progress-number {
  background: #34c759;
  color: white;
}

.progress-label {
  font-size: 10px;
  color: #8e8e93;
  font-weight: 500;
  text-align: center;
  min-width: 40px;
}

.progress-item.active .progress-label {
  color: #007aff;
  font-weight: 600;
}

.progress-item.completed .progress-label {
  color: #34c759;
}

.progress-arrow {
  color: #c7c7cc;
  font-size: 12px;
  font-weight: bold;
}

/* 步骤状态标识 */
.step-number {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #e5e5ea;
  color: #8e8e93;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 8px;
}

.current-step-badge {
  background: linear-gradient(135deg, #007aff, #00d4ff);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  font-weight: 500;
}

.completed-step-badge {
  background: linear-gradient(135deg, #34c759, #4cd964);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  font-weight: 500;
}

.disabled-step-badge {
  background: #f2f2f7;
  color: #8e8e93;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 8px;
  font-weight: 500;
}

/* 禁用状态的控制区域 */
.control-section.disabled {
  opacity: 0.5;
  pointer-events: none;
}

.control-section.disabled .section-header {
  cursor: not-allowed;
}

/* 步骤完成按钮 */
.step-action {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f2f2f7;
}

.step-action .el-button {
  background: linear-gradient(135deg, #007aff, #00d4ff);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step-action .el-button:hover {
  background: linear-gradient(135deg, #0056d3, #00bfff);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
}

/* 证件规格折叠分类 */
.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  cursor: pointer;
  border-bottom: 1px solid #f2f2f7;
  transition: all 0.2s ease;
}

.category-header:hover {
  background: rgba(0, 122, 255, 0.05);
  border-radius: 8px;
  padding-left: 8px;
  padding-right: 8px;
}

.category-title {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.category-expand-icon {
  transition: transform 0.3s ease;
  color: #8e8e93;
}

.category-expand-icon.expanded {
  transform: rotate(180deg);
}

/* 折叠动画 */
.category-slide-enter-active,
.category-slide-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.category-slide-enter-from,
.category-slide-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.category-slide-enter-to,
.category-slide-leave-from {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
}

/* 工作流完成操作 */
.workflow-actions {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.action-group h4 {
  margin: 0 0 8px 0;
  color: #1d1d1f;
  font-size: 16px;
  font-weight: 600;
}

.action-group p {
  margin: 0 0 16px 0;
  color: #8e8e93;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.action-buttons .action-btn {
  flex: 1;
  min-width: 0;
}

.control-section {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.control-section:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  border-bottom: 1px solid #f2f2f7;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background: rgba(0, 122, 255, 0.05);
}

.section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #1d1d1f;
  margin: 0;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: #8e8e93;
}

.expand-icon.expanded {
  transform: rotate(180deg);
  color: #007aff;
}

/* 折叠动画 */
.section-slide-enter-active,
.section-slide-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.section-slide-enter-from,
.section-slide-leave-to {
  opacity: 0;
  max-height: 0;
}

.section-slide-enter-to,
.section-slide-leave-from {
  opacity: 1;
  max-height: 500px;
}

.section-content {
  padding: 20px;
}

/* 模板分类 */
.template-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.template-category {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.template-category .category-title {
  font-size: 14px;
  font-weight: 600;
  color: #007aff;
  margin: 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f2f2f7;
}

/* 模板网格 */
.template-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.template-card {
  padding: 16px;
  border: 2px solid #f2f2f7;
  border-radius: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.template-card:hover {
  border-color: #007aff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 122, 255, 0.2);
}

.template-card.active {
  border-color: #007aff;
  background: rgba(0, 122, 255, 0.05);
  box-shadow: 0 0 0 1px rgba(0, 122, 255, 0.2);
}

.template-preview {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 4px;
}

.template-size {
  font-size: 12px;
  color: #8e8e93;
}

/* 颜色网格 */
.color-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12px;
}

.color-option {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 2px solid #f2f2f7;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.color-option.custom {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
  color: white;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.color-option.active {
  border-color: #007aff;
  transform: scale(1.1);
  box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.3);
}

.color-check {
  position: absolute;
  color: white;
  text-shadow: 0 0 4px rgba(0, 0, 0, 0.5);
  font-size: 16px;
}

/* 美颜控制 */
.beauty-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.control-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  font-weight: 500;
  color: #1d1d1f;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.beauty-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.beauty-actions .el-button {
  flex: 1;
}

/* 图片编辑控制 */
.editor-controls {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.scale-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rotation-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.position-controls {
  display: flex;
  justify-content: center;
}

.position-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  width: fit-content;
}

.position-grid .el-button {
  width: 40px;
  height: 40px;
  padding: 0;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

.quick-actions .el-button {
  flex: 1;
}

/* 尺寸控制 */
.size-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  height: 44px;
  border-radius: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* 颜色选择器对话框 */
.color-picker-dialog {
  border-radius: 16px;
}

.color-picker-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.color-preview {
  width: 100%;
  height: 60px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  border: 1px solid #e5e5ea;
}

.dialog-footer {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 响应式 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
  }
  
  .preview-panel {
    min-width: auto;
  }
  
  .control-panel {
    width: 100%;
  }
  
  .template-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .color-grid {
    grid-template-columns: repeat(8, 1fr);
  }
}

@media (max-width: 768px) {
  .toolbar {
    padding: 0 16px;
  }
  
  .main-content {
    padding: 16px;
    gap: 16px;
  }
  
  .template-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .color-grid {
    grid-template-columns: repeat(6, 1fr);
  }
  
  .btn-back span,
  .btn-save span {
    display: none;
  }
}

/* 图片编辑功能样式 */
.edit-group {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f2f2f7;
}

.edit-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.group-title {
  font-size: 14px;
  font-weight: 600;
  color: #007aff;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #f2f2f7;
}

/* 排版功能样式 */
.layout-section {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f6f8fc 0%, #e8f0fe 100%);
  border-radius: 12px;
  border: 1px solid #e3f2fd;
}

.layout-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  color: #1565c0;
  font-size: 16px;
  font-weight: 600;
}

.layout-section p {
  color: #666;
  font-size: 14px;
  margin: 0 0 16px 0;
  line-height: 1.4;
}

.paper-size-selection {
  margin-bottom: 16px;
}

.selection-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.layout-settings {
  margin-bottom: 16px;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.setting-item label {
  font-size: 14px;
  color: #333;
  min-width: 80px;
}

.setting-value {
  font-size: 14px;
  color: #666;
  min-width: 40px;
}

.setting-hint {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.custom-layout-controls {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  border: 1px dashed #ddd;
}

.layout-preview-info {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}

.layout-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-size: 13px;
  color: #666;
}

.stat-value {
  font-size: 13px;
  font-weight: 600;
  color: #1565c0;
}

.layout-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.layout-btn {
  flex: 1;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.2s ease;
}

.preview-btn {
  background: white;
  border: 1px solid #ddd;
  color: #666;
}

.preview-btn:hover {
  background: #f8f9fa;
  border-color: #007aff;
  color: #007aff;
}

.generate-btn {
  background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
  border: none;
  color: white;
}

.generate-btn:hover {
  background: linear-gradient(135deg, #0056d3 0%, #004bb8 100%);
  transform: translateY(-1px);
}

/* 排版预览弹窗样式 */
.layout-preview-dialog {
  .el-dialog {
    border-radius: 16px;
  }
  
  .el-dialog__header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
  }
  
  .el-dialog__body {
    padding: 20px 24px;
  }
}

.layout-preview-content {
  display: flex;
  gap: 20px;
  min-height: 500px;
}

.preview-left {
  flex: 2;
  display: flex;
  flex-direction: column;
}

.preview-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.preview-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  height: 100%;
  overflow-y: auto;
}

.preview-info h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #007aff;
  padding-bottom: 8px;
}

.info-section {
  margin-bottom: 20px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h5 {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  font-weight: 600;
  padding-left: 8px;
  border-left: 3px solid #007aff;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.info-value.highlight {
  color: #007aff;
  font-weight: 700;
  font-size: 15px;
}

.preview-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.layout-preview-img {
  max-width: 100%;
  max-height: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #666;
}

.loading-icon {
  font-size: 32px;
  margin-bottom: 16px;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.preview-loading p {
  font-size: 16px;
  margin: 0;
}

/* 响应式布局 */
@media (max-width: 1024px) {
  .layout-preview-content {
    flex-direction: column;
  }
  
  .preview-left {
    flex: none;
  }
  
  .preview-right {
    flex: none;
  }
}

@media (max-width: 768px) {
  .layout-preview-dialog {
    width: 95% !important;
  }
  
  .custom-layout-controls {
    padding: 8px;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .setting-item label {
    min-width: auto;
  }
}

.control-row {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.control-row:last-child {
  margin-bottom: 0;
}



/* 图片容器交互 */
.image-container {
  position: relative;
  overflow: hidden;
  cursor: grab;
  user-select: none;
}

.image-container:active {
  cursor: grabbing;
}

.preview-image {
  max-width: 500px;       /* 最大宽度限制 */
  max-height: 600px;      /* 最大高度限制 */
  width: auto;            /* 自动宽度 */
  height: auto;           /* 自动高度 */
  display: block;
  transition: transform 0.3s ease;
  object-fit: contain;    /* 保持图片比例 */
}



/* 方向控制垫 */
.direction-pad {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  margin-top: 16px;
}

.pad-row {
  display: flex;
  gap: 4px;
  align-items: center;
}

.pad-spacer {
  width: 32px;
  height: 32px;
}

.pad-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pad-btn.center {
  background: #f2f2f7;
  border-color: #e5e5ea;
}

.pad-btn:hover {
  transform: scale(1.05);
}

/* 自动校正控制 */
.auto-correct-controls .control-row {
  justify-content: space-between;
}



/* 滑块样式优化 */
.control-item .el-slider {
  margin: 8px 0;
}

.control-item .el-slider__runway {
  background-color: #f2f2f7;
  border-radius: 4px;
  height: 6px;
}

.control-item .el-slider__bar {
  background-color: #007aff;
  border-radius: 4px;
}

.control-item .el-slider__button {
  border: 2px solid #007aff;
  background-color: white;
  width: 16px;
  height: 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* 排版预览样式 */
.layout-preview-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
  border-radius: 12px;
  overflow: hidden;
}

.layout-preview-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 5px;
}

.layout-preview-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 4px;
  max-width: none;
  max-height: none;
}

.layout-preview-info {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 200px;
  background: rgba(255, 255, 255, 0.92);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.1);
  text-align: center;
  z-index: 101;
  border: 1px solid rgba(255,255,255,0.3);
}

.preview-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1f2937;
  padding-bottom: 6px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 8px;
}

.layout-preview-info.processing {
  opacity: 0.8;
  cursor: not-allowed;
}

.layout-preview-overlay {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.layout-preview-overlay h4 {
  margin: 0 0 8px 0;
  color: #1d1d1f;
  font-size: 16px;
  font-weight: 600;
}

.layout-preview-overlay p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

/* 排版预览控制区域 */
.layout-preview-controls {
  margin-top: 16px;
  padding: 0 16px;
}

.preview-info-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 16px;
  color: white;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.preview-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 14px;
}

.preview-icon {
  font-size: 20px;
  margin-right: 10px;
  opacity: 0.9;
}

.preview-info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.layout-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  backdrop-filter: blur(10px);
}

.stat-label {
  display: block;
  font-size: 11px;
  opacity: 0.8;
  margin-bottom: 3px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
}

.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.action-btn {
  min-width: 100px;
  height: 36px;
  border-radius: 18px;
  font-weight: 500;
  transition: all 0.3s ease;
  font-size: 14px;
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.action-btn.primary {
  background: white;
  border: none;
  color: #667eea;
  font-weight: 600;
}

.action-btn.primary:hover {
  background: #f8f9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
}

/* 相纸规格选择样式 */
.paper-size-section {
  margin-bottom: 20px;
}

.sub-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 6px;
}

.paper-categories {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.paper-category {
  border: 1px solid #e5e5ea;
  border-radius: 8px;
  overflow: hidden;
  background: #ffffff;
}

.paper-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 8px;
  padding: 12px;
  background: #fafafa;
}

.paper-card {
  background: #ffffff;
  border: 2px solid #e5e5ea;
  border-radius: 8px;
  padding: 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.paper-card:hover {
  border-color: #007aff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.2);
}

.paper-card.active {
  border-color: #007aff;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
}

.paper-preview {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 4px;
}

.paper-size {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.paper-orientation {
  font-size: 11px;
  color: #999;
}

.paper-orientation span.active {
  color: #007aff;
  font-weight: 600;
}

/* 纸张方向选择样式 */
.orientation-section {
  margin-bottom: 20px;
}

.orientation-controls {
  display: flex;
  gap: 8px;
}

/* 排版设置样式 */
.layout-settings-section {
  margin-bottom: 20px;
}

.layout-info {
  background: rgba(0, 122, 255, 0.05);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
}

.info-label {
  color: #666;
}

.info-value {
  color: #1d1d1f;
  font-weight: 500;
}

/* 排版操作样式 */
.layout-actions {
  margin-top: 16px;
}

.control-row {
  display: flex;
  gap: 8px;
}

.control-row .el-button {
  flex: 1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .paper-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .layout-preview-overlay {
    position: static;
    margin-top: 16px;
  }
}

.layout-preview-image-wrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 16px;
}

.layout-preview-image {
  width: 100%;
  max-width: 900px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 2px 12px #4A90E222;
  background: #fff;
  object-fit: contain;
}

.layout-info-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 18px;
  font-size: 1.08rem;
  color: #333;
  margin: 12px 0 18px 0;
  font-weight: 500;
}

.layout-preview-actions {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-bottom: 8px;
}
</style>
